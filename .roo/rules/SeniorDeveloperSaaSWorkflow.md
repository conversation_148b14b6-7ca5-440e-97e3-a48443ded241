---
description: 
globs: 
alwaysApply: true
---
# SaaS 系統開發與 Roo Code IDE 最佳實踐指南

作為一名資深的 SaaS 系統開發工程師，我經常使用像 Roo Code 這樣強大的 IDE 來提升開發效率和程式碼品質。以下是我總結的一些經驗法則，希望能幫助你（AI 助手）更有效地協助處理 SaaS 專案的開發任務：

## 1. 需求理解與情境掌握 (Understand & Contextualize)

*   **深度理解需求**：在動手寫任何程式碼之前，務必徹底理解使用者提出的需求、業務邏輯以及預期成果。若有模糊不清的地方，應優先釐清。
*   **善用 Roo Code 情境分析**：
    *   利用 Roo Code 的「與程式碼庫對話」(Chat with codebase) 功能，快速了解現有模組、函式庫及架構。
    *   透過「跳至定義」(Go to Definition)、「尋找參考」(Find References) 等功能，追蹤程式碼邏輯與依賴關係。
    *   仔細研讀 `.roo/rules/` 目錄下的所有規則檔案，特別是 `ApplicationArchitectureGuide.md` 和 `AISystemGuide.md` 等核心架構文件，以確保你的建議和實作符合專案規範。

## 2. 策略規劃與架構思維 (Plan & Design)

*   **謀定而後動**：對於複雜的 SaaS 功能，先進行詳細規劃。將大任務拆解成小而可管理的部分。
*   **架構考量優先**：SaaS 系統特別注重擴展性 (Scalability)、安全性 (Security)、可維護性 (Maintainability) 及多租戶 (Multi-tenancy) 架構。在設計解決方案時，務必將這些因素納入考量，並參考相關的架構指南。
*   **預想邊界條件與錯誤處理**：提前思考可能的錯誤情境、無效輸入及邊界條件，並在設計中納入健全的錯誤處理機制。

## 3. AI 輔助的高效開發週期 (AI-Augmented Development)

*   **充分發揮 Roo Code AI 潛能**：
    *   **程式碼生成與建議**：對於重複性或樣板程式碼，勇敢使用 Roo Code 的程式碼生成功能 (例如，透過 inline chat 或 "Generate Code" 指令)。
    *   **重構與優化**：利用 AI 輔助進行程式碼重構，改善可讀性、效能或遵循設計模式。
    *   **除錯與問題分析**：當遇到錯誤或 bug 時，可以請 AI 協助分析問題原因並提供解決方案建議。
    *   **文件與註解**：讓 AI 協助產生 JSDoc、程式碼註解或甚至是功能的初步文件草稿。
*   **迭代與快速驗證**：利用 AI 快速產出原型或部分實作，以便進行早期驗證和調整。

## 4. 品質堅持與標準遵循 (Quality & Standards)

*   **撰寫高標準程式碼**：產出的程式碼應力求乾淨 (Clean)、可讀性高 (Readable)、易於維護 (Maintainable) 且經過良好測試 (Testable)。
*   **嚴格遵守專案規範**：
    *   **核心規則檔至上**：嚴格遵循 `.roo/rules/` 目錄下所有現存規則文件的指示。這些文件定義了專案的架構、編碼風格、設計原則等重要規範。
    *   **命名約定與風格一致**：遵循專案中已建立的命名約定和編碼風格 (例如 `ApplicationArchitectureGuide.md` 中定義的檔案與變數命名規則)。
    *   **型別安全**：在 TypeScript 環境下，確保所有程式碼都有明確的型別定義，避免使用 `any`。
*   **DRY (Don't Repeat Yourself)**：避免重複的程式碼，將共用邏輯提取到可重用的函式或模組中。

## 5. 嚴謹驗證與測試 (Verify & Test)

*   **測試是品質的基石**：高度重視單元測試 (Unit Tests)、整合測試 (Integration Tests) 及端對端測試 (End-to-End Tests)。
*   **AI 輔助測試案例**：可以請 Roo Code AI 協助根據函式規格或使用者故事產生初步的測試案例情境。
*   **確保覆蓋主要路徑與邊界條件**：測試不僅要覆蓋正常操作流程，也要包含異常路徑和邊界條件。

## 6. 偵錯與問題分析 (Debug & Troubleshoot)

### 6.1 重構優先策略 (Refactoring-First Strategy)
*   **完整重寫優於逐步修補**：當遇到之前已修復過的類似問題時，**優先考慮完整重新生成**相關程式碼檔案，而非逐行修補。
*   **智能重生成決策**：
    *   **已知錯誤模式** → 完整重新生成相關檔案
    *   **大範圍重構需求** → 直接產生完整的新版本
    *   **命名規範調整** → 重新生成涉及的所有檔案
*   **AI 助手應主動建議**：「基於這個錯誤的性質，我建議完整重新生成 [相關檔案/模組]，這樣可以確保邏輯一致性並避免後續的連鎖問題。」

### 6.2 系統化偵錯流程 (Systematic Debugging Process)
*   **問題重現 (Reproduce the Issue)**：確保能穩定重現 bug，了解觸發條件。
*   **縮小範圍 (Narrow Down the Scope)**：透過日誌、錯誤訊息、斷點等方式，逐步縮小問題發生的程式碼範圍。
*   **假設與驗證 (Formulate & Test Hypotheses)**：針對可能的原因提出假設，並透過修改程式碼、增加日誌或使用偵錯工具來驗證。
*   **根本原因分析 (Root Cause Analysis)**：找到問題的根本原因，而不僅僅是修復表面現象。
### 6.3 善用 IDE 偵錯工具 (Leverage IDE Debugging Tools)
    *   **斷點 (Breakpoints)**：在關鍵位置設定斷點，觀察變數狀態、呼叫堆疊。
    *   **單步執行 (Step Over, Step Into, Step Out)**：逐行追蹤程式碼執行流程。
    *   **監看式 (Watch Expressions)**：監控特定變數或表達式的值。
    *   **主控台日誌 (Console Logging)**：策略性地使用 `console.log` 或更進階的日誌函式庫，輸出關鍵資訊。
### 6.4 AI 輔助偵錯 (AI-Assisted Debugging)
    *   **錯誤訊息解讀 (Error Message Interpretation)**：將複雜或不常見的錯誤訊息貼給 Roo Code AI，請求解釋和可能的解決方向。
    *   **程式碼片段分析 (Code Snippet Analysis)**：選取可疑的程式碼區塊，詢問 AI 是否有潛在問題或邏輯缺陷。
    *   **提出偵錯策略建議 (Suggest Debugging Strategies)**：當自己卡關時，可以向 AI 描述問題，請求偵錯思路或可以嘗試的方法。
### 6.5 日誌分析 (Log Analysis)
    *   **查閱相關日誌**：SaaS 系統通常有詳細的應用程式日誌、伺服器日誌、資料庫日誌等。學會查閱並過濾這些日誌。
    *   **結構化日誌**：若專案使用結構化日誌 (例如 JSON 格式)，利用工具進行分析和查詢。
### 6.6 版本控制與偵錯 (Version Control for Debugging)
    *   **`git bisect`**：當懷疑某個 bug 是近期引入時，使用 `git bisect` 快速定位引入問題的 commit。
    *   **查閱提交歷史**：檢查相關檔案的變更歷史，尋找可能的線索。
### 6.7 隔離問題 (Isolate the Problem)
    *   **最小可重現範例 (Minimal Reproducible Example)**：嘗試建立一個最小化的程式碼範例來重現問題，這有助於排除不相關的因素。
    *   **模組化測試**：如果懷疑特定模組或函式庫，單獨測試它。
### 6.8 保持冷靜與耐心 (Stay Calm and Patient)
*   偵錯有時會很耗時且令人沮喪。保持冷靜，一步一步來。適時休息有助於轉換思路。

## 7. 持續學習與改進 (Learn & Improve)

*   **擁抱重構**：將重構視為日常工作的一部分，持續改善程式碼結構和設計。
*   **關注效能與安全**：利用 Roo Code 的分析能力或整合工具，主動尋找潛在的效能瓶頸或安全漏洞。
*   **從錯誤中學習**：若 AI 產生的程式碼或建議有誤，記錄下來並從中學習，避免未來重蹈覆轍。

透過遵循這些原則，你將能更有效地運用 Roo Code 的強大功能，為 HorizAI SaaS 專案貢獻高品質的程式碼與解決方案。
