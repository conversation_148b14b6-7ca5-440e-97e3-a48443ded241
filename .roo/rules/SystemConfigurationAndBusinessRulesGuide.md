---
description: 
globs: 
alwaysApply: true
---
# System Configuration and Business Rules Guide

## 0. Introduction

This document serves as the unified guide for system-level configurations, core business rules (particularly concerning subscription management and billing), and database management practices within the HorizAI SaaS project. Adherence to these guidelines is crucial for maintaining system integrity, operational consistency, and scalability.

---

## 1. General System Administration Settings

These settings are managed through the backend admin panel and control various operational aspects of the HorizAI SaaS platform. They are typically stored in a dedicated `SystemSetting` or similar model in the database.

### 1.1. General Settings
- **Application Name:** The display name of the application.
- **Application URL:** The primary URL for accessing the application.
- **Default Timezone:** System-wide default timezone.
- **Default Language:** System-wide default language for new users or public pages.
- **Maintenance Mode:** Enable/disable maintenance mode for the entire platform, with an option for a custom message.

### 1.2. Security Settings
- **Password Policies:** Minimum length, complexity requirements (uppercase, lowercase, numbers, symbols), password expiry, and history.
- **Multi-Factor Authentication (MFA):** Enable/disable MFA options (e.g., TOTP, SMS).
- **Session Management:** Session timeout duration, concurrent session limits.
- **Access Control Logging:** Detailed logging of administrative actions and permission changes.
- **IP Whitelisting/Blacklisting:** Options for restricting access to the admin panel or specific services.

### 1.3. Notification Settings
- **System Email Address:** The "From" address for system-generated emails.
- **Email Templates:** Management of templates for various system notifications (welcome emails, password resets, subscription alerts, etc.).
- **In-App Notification Preferences:** Global controls for enabling/disabling certain types of in-app notifications.

### 1.4. API Key Management (External Integrations)
- Management of API keys for external services that the SaaS platform integrates with (e.g., analytics, CRMs). This is distinct from `AiKey` which is for AI provider access.
- Secure storage and rotation mechanisms.

### 1.5. SMTP Server Configuration
- Host, port, username, password, encryption (SSL/TLS) for the outgoing mail server.
- Test email functionality.

### 1.6. File Storage Configuration
- **Default Storage Provider:** (e.g., local, AWS S3, Google Cloud Storage).
- **Credentials and Configuration:** Bucket names, access keys, regions, etc., for cloud storage providers.
- **Storage Quotas:** Default storage quotas for tenants/workspaces (can be overridden by plans).
- **Allowed File Types & Max Size:** System-wide restrictions on file uploads.

### 1.7. Monitoring and Logging
- **Log Level:** Configuration for application logging verbosity (debug, info, warn, error).
- **External Monitoring Service Integration:** (e.g., Sentry, Datadog) API keys and endpoints.

### 1.8. Theme and Branding (Optional - if centrally managed)
- **Logo Upload:** Admin ability to upload a system-wide logo.
- **Primary/Secondary Color Configuration:** Basic theming options if not fully tenant-specific.

### 1.9. Data Backup and Restore
- **Automated Backup Schedule:** Configuration for how often database and critical file backups are performed.
- **Backup Retention Policy:** How long backups are kept.
- **Manual Backup Trigger:** Ability for admins to initiate a backup manually.
- **Restore Procedures Documentation:** Clear guidelines on how to restore from a backup (may involve manual steps).

### 1.10. Language and Localization
- **Supported Languages:** List of languages the platform supports.
- **Translation Management Interface:** (If applicable) A way for admins to update UI translations.

---

## 2. Subscription Management and Billing

This section details the business rules and processes for managing customer subscriptions, pricing plans, and billing operations, based on the "Subscription Management and Payment Integration PRD".

### 2.1. Pricing Plans and Tiers
- **Plan Definition:** Each plan (e.g., Free, Basic, Pro, Enterprise) must have:
    - Name, Description
    - Price (monthly/annual)
    - Billing Cycle (monthly, annually)
    - Features and Resource Limits (e.g., number of users, storage, AI credits/tokens, access to specific modules).
    - `price_id` (from payment gateway for recurring billing)
- **Currency Support:** Clearly defined supported currencies (e.g., USD, EUR).
- **Add-ons:** Optional features or resource packs that can be added to a base plan for an additional cost.

### 2.2. Trial Period Management
- **Eligibility:** Rules for who is eligible for a trial (e.g., new tenants only).
- **Duration:** Standard trial period length (e.g., 14 days, 30 days).
- **Features Available During Trial:** Typically, trials grant access to a specific plan's features.
- **Conversion:** Process for converting a trial to a paid subscription (manual or automatic with payment method on file).
- **Post-Trial State:** What happens if a trial expires without conversion (e.g., account downgrade to free tier, restricted access).

### 2.3. Subscription Lifecycle
- **Activation:** Occurs upon successful payment or start of a trial.
- **Upgrade/Downgrade:**
    - Proration logic for changes mid-cycle (credit for unused time on old plan, immediate charge for difference on new plan).
    - Impact on resource limits and feature access.
- **Renewal:** Automatic renewal for recurring subscriptions. Notification prior to renewal.
- **Cancellation:**
    - User-initiated or admin-initiated.
    - Effective date of cancellation (end of current billing period vs. immediate with no refund).
    - Data retention policy post-cancellation.
- **Expiration/Suspension:**
    - Due to non-payment or end of a fixed-term plan.
    - Grace period before suspension.
    - Restricted access during suspension.
    - Data handling during and after suspension.
- **Reactivation:** Process for reactivating a cancelled or suspended subscription.

### 2.4. Billing and Payment Integration
- **Payment Gateway Integration:** (e.g., Stripe, PayPal) Secure handling of API keys and webhooks.
- **Supported Payment Methods:** (e.g., Credit/Debit Cards, Bank Transfers).
- **Invoicing:** Automatic generation of invoices (PDF format) with necessary details (line items, taxes, company info).
    - Invoice delivery (email, downloadable from portal).
- **Tax Calculation:** Integration with tax services or configurable tax rules based on region/country.
- **Payment Failures & Dunning:** Automated retry logic for failed payments and communication sequence (dunning emails) to customers.
- **Refunds:** Policy and process for issuing full or partial refunds.
- **Dispute Handling:** Process for managing chargebacks and payment disputes.

### 2.5. Usage Tracking and Quotas
- Real-time or near real-time tracking of resource usage against plan limits.
- Clear visibility for users/tenants on their current usage.
- Over-limit policies (e.g., hard stop, overage charges, notification to upgrade).

### 2.6. Notifications (Subscription-Related)
- **Automated Emails/In-App Messages for:**
    - Subscription activation, upcoming renewal, successful renewal, payment failure.
    - Trial nearing expiration, trial expired.
    - Plan changes (upgrade/downgrade confirmation).
    - Invoice availability.
    - Credit card expiration warnings.

### 2.7. Admin Management Features for Subscriptions
- **Dashboard:** Overview of subscription metrics (MRR, ARR, churn, active subscriptions, etc.).
- **View/Search Subscriptions:** Ability for admins to find and view details of any tenant's subscription.
- **Manual Actions:**
    - Create/cancel subscriptions.
    - Issue refunds.
    - Extend trial periods.
    - Apply discounts or credits.
    - Manually trigger renewals or payment attempts.
- **Plan Management:** Admin interface to create, edit, and deprecate pricing plans.

---

## 3. Database Management and Guidelines

These guidelines ensure consistency, reliability, and maintainability of the project's database across all environments.

### 3.1. Schema Change and Migration Flow
- **Migration Tool:** All database schema changes (tables, columns, indexes, constraints) MUST be managed via a migration tool (e.g., Prisma Migrate).
- **No Manual Changes:** Direct manual modification of the database schema in any environment (development, testing, production) is strictly prohibited.
- **Migration Files:** Each schema change must correspond to a new migration file, which is version-controlled (Git).
- **Naming and Description:** Migration files should have clear, descriptive names and include comments explaining the purpose of the change.
- **Idempotency:** Migrations should be written to be idempotent where possible, meaning they can be run multiple times without adverse effects if the change has already been applied.

### 3.2. Code and Schema Synchronization
- **Code Follows Migrations:** Application code (e.g., services, repositories) must only interact with database structures defined and applied through migrations.
- **Update Schema First:** If application code requires new database fields or tables, a migration must be created and applied successfully *before* the code utilizing these changes is deployed.
- **Seed Scripts:** Seed data scripts (e.g., `seed.ts`) must also adhere to this, only operating on schema elements already established by migrations.

### 3.3. Development and CI/CD Automation for DB Schema
- **Automated Migration Application:** Development startup scripts and CI/CD pipelines MUST automatically apply pending migrations (e.g., `pnpm db:apply`).
- **Schema Status Check:** CI/CD pipelines should include a step to check the database schema status against migrations (e.g., `pnpm db:status`). If the schema is not synchronized with migrations, the pipeline should fail.

### 3.4. Schema Validation and Checks
- **Migration Tool Validation:** The migration tool (e.g., Prisma) typically validates schema consistency during migration generation and application.
- **Manual Status Checks:** Developers can use commands like `pnpm db:status` to manually verify schema synchronization.
- **Static Analysis (Optional):** Advanced static analysis scripts can be developed to further ensure schema integrity, but the primary reliance should be on the migration tool's validation.

### 3.5. Permissions Synchronization
- **Source of Truth:** Permissions definitions (roles, abilities) are managed in designated files (e.g., within `prisma/templates/` or a dedicated permissions configuration directory).
- **Synchronization Script:** A dedicated script (e.g., triggered by `pnpm db:sync-perms --force`) is responsible for synchronizing these definitions with the database, typically populating or updating roles and permissions tables.
- **Consistency with Application Logic:** CASL ability definitions or similar authorization logic in the application code must remain consistent with these synchronized database permissions.

### 3.6. Seed Data Management
- **Purpose:** Seed data is used to populate the database with initial essential data (e.g., default admin users, system settings, predefined lists) required for the application to function correctly, especially in fresh environments.
- **Execution:** Seed scripts should be executable after migrations have been applied (e.g., `pnpm db:apply --seed`).
- **Synchronization:** Seed scripts must be kept synchronized with schema changes. If a table structure changes, corresponding seed scripts must be updated.
- **Environment-Specific Seeding:** Consider mechanisms for environment-specific seed data if necessary (e.g., different sample data for development vs. staging).

### 3.7. Multi-Environment Support
- **Configuration:** Database connection details and other environment-specific parameters should be managed via environment files (e.g., `.env.development`, `.env.production`, `.env.test`).
- **Tooling Support:** Database management scripts (`pnpm db:*`) should respect these environment configurations, often selectable via an `--env` flag or similar mechanism.

### 3.8. Common Database Commands (Examples using `pnpm`)
- **Check Migration Status:** `pnpm db:status` (Verifies if the database schema is synchronized with migration files).
- **Apply Migrations:** `pnpm db:apply` (Applies all pending migrations to the database).
- **Reset Database:** `pnpm db:reset` (Typically drops the database, recreates it, and applies all migrations. Use with caution, especially outside development).
- **Synchronize Permissions:** `pnpm db:sync-perms --force` (Runs the permission synchronization script).
- **Apply Migrations and Seed:** `pnpm db:apply --seed` (Applies migrations and then runs seed scripts).
- **Generate Migrations:** `pnpm prisma migrate dev --name <migration_name>` (Example for Prisma to generate a new migration file based on schema changes).

### 3.9. Documentation and Maintenance for Database Changes
- **Document Changes:** Significant schema changes, new initialization procedures, or updates to permission/seed logic must be documented (e.g., in a `db-migration-guide.md`, release notes, or relevant PR descriptions).
- **Changelog:** Major database changes should be noted in the project's changelog, especially if they require manual intervention or have implications for existing data.
- **Migration Guide:** For complex migrations (e.g., involving data transformation), a specific migration guide should be provided.

---

## 4. Cross-Cutting Concerns and Governance

### 4.1. Data Integrity and Consistency
- **Relational Integrity:** Utilize database constraints (foreign keys, unique constraints, not null) to enforce data integrity at the database level.
- **Transactional Operations:** Ensure that business operations involving multiple database writes are performed within transactions to maintain consistency.
- **Validation:** Implement robust data validation at the application layer (DTOs, service logic) in addition to database constraints.

### 4.2. Audit Trails
- **Log Critical Changes:** Implement audit trails for significant events, such as changes to system settings, subscription modifications, role/permission updates, and critical data modifications.
- **Audit Log Structure:** Audit logs should capture what changed, who made the change, when it was made, and the old/new values where applicable.

### 4.3. Compliance and Regulatory Considerations
- **Data Privacy:** Adhere to relevant data privacy regulations (e.g., GDPR, CCPA) in data handling, storage, and processing. This includes mechanisms for data subject access requests, data deletion, and consent management.
- **Security Standards:** Follow industry best practices for securing sensitive data both in transit and at rest.

This guide is a living document and should be updated as the system evolves. All development teams are expected to be familiar with and adhere to these principles.
