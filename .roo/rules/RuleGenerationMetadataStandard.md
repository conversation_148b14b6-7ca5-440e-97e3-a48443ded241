---
description: 本規則指示 AI 助手，在未來產生任何新的 Roo Code 規則檔案 (.md 檔案) 時，都必須在該新檔案的頂部加入一個標準化的元資料區塊 (包含 description, globs, alwaysApply 欄位)。
globs: 
alwaysApply: false
---
# Roo Code 規則元資料標準 (Rule Metadata Standard)

當您（AI 助手）被要求產生新的 Roo Code 規則檔案 (`.md` 檔案) 時，請務必在每個新規則檔案的最頂部包含以下標準元資料區塊 (metadata block)。

---
description: (在此處填寫對此規則的簡短描述，說明其目的和範圍)
globs: (可選，若此規則主要適用於特定檔案/目錄，請填寫 glob 模式陣列，例如 `["src/**/*.ts", "tests/**/*.spec.ts"]`。若規則為全域適用或不確定，可將此欄位留空或省略此行)
alwaysApply: true (此值通常應為 true，表示 AI 應始終載入並考慮此規則的內容)
---

**使用說明：**

*   `description`: 請在此處提供一個簡潔明瞭的描述，說明該規則的主要內容、目的或適用情境。
*   `globs`: (此欄位為選用) 如果該規則特別針對某些檔案類型或目錄結構，請使用 glob 模式陣列來指定。例如，`["apps/frontend/src/**/*.vue", "packages/ui-library/src/**/*.ts"]`。如果規則是普遍適用的，或者您不確定具體的適用範圍，可以將此欄位留空，或者完全省略 `globs:` 這一行。
*   `alwaysApply`: 此欄位通常應設定為 `true`。這表示 AI 系統在處理使用者請求時，應始終載入並將此規則納入考量。

**重要提示：**

請確保此元資料區塊嚴格按照上述格式呈現，並位於所產生之新規則檔案的最頂端，元資料區塊前後應使用 `---` 分隔符。

**範例情境：**

如果使用者指示：「請為 Vue 專案的狀態管理建立一條規則，說明 Pinia 的使用慣例。」

那麼，您產生的新規則檔案 (例如 `PiniaUsageGuide.md`) 的開頭應如下所示：

```markdown
---
description: HorizAI SaaS 專案中 Vue 3 應用程式使用 Pinia 進行狀態管理的標準慣例與最佳實踐。
globs: ["apps/frontend/src/stores/**/*.ts", "apps/frontend/src/views/**/*.vue", "apps/frontend/src/components/**/*.vue"]
alwaysApply: true
---

# Pinia 狀態管理慣例

(此處接續 Pinia 使用慣例的詳細規則內容...)
```

遵循此標準有助於確保所有 Roo Code 規則都具有一致的元資料結構，方便管理和 AI 理解其適用範圍。
