---
applyTo: "apps/backend/prisma/schema.prisma"
---

# Prisma Schema 指南 (`schema.prisma`)

本指南適用於 `apps/backend/prisma/schema.prisma` 檔案的撰寫。

## 通用原則

- 保持 schema 清晰、一致且易於理解。
- 所有變更都應透過 Prisma Migrate 產生對應的 migration 檔案。

## 模型 (Model) 定義

- **命名**: 模型名稱使用 `PascalCase` 單數形式 (例如 `User`, `TenantDepartment`, `RolePermission`)。
- **欄位 (Field) 命名**: 欄位名稱使用 `camelCase` (例如 `firstName`, `createdAt`)。
- **型別**: 為每個欄位選擇最合適的 Prisma 型別 (例如 `String`, `Int`, `Boolean`, `DateTime`, `Json`)。
- **主鍵 (Primary Key)**:
  - 通常使用 `id String @id @default(cuid())` 或 `id Int @id @default(autoincrement())`。
  - 若為 `cuid()` 或 `uuid()`，請確保其唯一性。
- **預設值 (`@default`)**: 為適當的欄位設定預設值 (例如 `createdAt DateTime @default(now())`)。
- **唯一約束 (`@unique`)**: 為需要保持唯一的欄位或欄位組合加上 `@unique`。
- **索引 (`@index`, `@@index`)**:
  - 為經常查詢或排序的欄位建立索引以提高效能。
  - 使用 `@@index([field1, field2])` 建立複合索引。
- **可選欄位 (`?`)**: 只有真正可選的欄位才標記為可選 (例如 `lastName String?`)。
- **列表/陣列 (`Type[]`)**: 用於儲存純量型別的列表 (例如 `tags String[]`)。

## 關聯 (Relations)

- **命名**:
  - 一對多關聯中，"一" 方的關聯欄位通常是複數形式的對方模型名稱 (例如 `posts Post[]`)。
  - "多" 方的關聯欄位通常是單數形式的對方模型名稱 (例如 `author User @relation(fields: [authorId], references: [id])`)。
  - "多" 方的外鍵欄位命名為 `[relatedModelNameInCamelCase]Id` (例如 `authorId String`)。
- **明確定義**:
  - 總是明確定義雙向關聯。
  - 使用 `@relation` 屬性，並指定 `fields` 和 `references`。
  - 考慮關聯操作 (例如 `onDelete`, `onUpdate`)，預設通常是 `Cascade` 或 `Restrict`，請根據業務需求選擇。
    - 例如：`author User @relation(fields: [authorId], references: [id], onDelete: Cascade)`
- **多對多關聯**:
  - 使用隱式多對多關聯 (Prisma 會自動產生關聯表) 或顯式多對多關聯 (手動定義中間表)。
  - 顯式關聯表允許在關聯上加入額外欄位。
  - 例如，`User` 和 `Role` 的多對多，中間表可能是 `UserRole`。

## Enum 定義

- Enum 名稱使用 `PascalCase` (例如 `UserRole`, `TenantStatus`)。
- Enum 值使用 `SCREAMING_SNAKE_CASE` (例如 `ADMIN`, `ACTIVE`)。
- Enum 應在 `schema.prisma` 中定義，以便 Prisma Client 產生對應的型別。

## 資料庫對應 (`@map`, `@@map`)

- **模型對應 (`@@map`)**: 如果資料庫中的表格名稱與模型名稱不同 (例如，資料庫表格使用 snake_case)，使用 `@@map("table_name")`。
  - 例如: `model User { ... @@map("users") }`
- **欄位對應 (`@map`)**: 如果資料庫中的欄位名稱與模型欄位名稱不同，使用 `@map("column_name")`。
  - 例如: `firstName String @map("first_name")`
- **慣例**: 專案中似乎傾向於在 Prisma schema 中使用 camelCase，然後透過 `@map` 和 `@@map` 對應到資料庫的 snake_case。請保持此慣例。

## 時間戳記

- 模型通常應包含 `createdAt` 和 `updatedAt` 欄位。
  - `createdAt DateTime @default(now())`
  - `updatedAt DateTime @updatedAt`

## 註解

- 在 `schema.prisma` 中使用 `///` (三斜線) 為模型和欄位加入註解，這些註解會出現在產生的 Prisma Client 型別中。
  ```prisma
  /// Represents a user in the system.
  model User {
    id String @id @default(cuid())
    /// The email address of the user. Must be unique.
    email String @unique
    // ...
  }
  ```

## 範例參考

參考現有的模型定義，例如 `User`, `Tenant`, `Role`, `Permission`, `TenantDepartment` 等，以保持一致性。
