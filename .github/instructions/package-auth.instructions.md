---
applyTo: "packages/@auth/**/*.{ts,tsx,json,md}"
---

# 共用套件指南 (`packages/@auth`)

本指南適用於 `packages/@auth/` 目錄下的所有程式碼和文件。

**核心目標**:

- 提供專案中前端 (`apps/frontend`) 和後端 (`apps/backend`) 共用的身份驗證相關邏輯、型別定義和工具函式。
- 保持此套件的獨立性和通用性，使其易於在不同部分中重用。
- 遵循 [通用編碼標準](../general-coding.instructions.md)。

## 套件結構

- **`src/`**: 原始碼目錄。
  - `composables/` (如果適用於 Vue Composition API 的共用邏輯)
  - `services/` (例如，用於解析 token 或處理通用驗證邏輯的服務)
  - `store/` (如果需要共用的狀態管理邏輯，例如 Pinia store 的一部分)
  - `types/`: 此套件內部使用的 TypeScript 型別。
  - `config.ts`: 設定相關。
  - `index.ts`: 套件的主要匯出入口。
- **`shared/`**: 跨前端和後端共用的程式碼，特別是型別定義。
  - `types.ts`: 核心的共用型別定義 (例如 `AuthenticatedUser`, `AuthTokenPayload`)。這些型別應被前端和後端引用。
  - `services/` (如果存在跨環境的服務邏輯)
- **`tests/`**: 單元測試和整合測試。
  - `auth.service.test.ts`: 範例測試檔案。
- `package.json`: 套件定義、相依性和建構腳本。
- `tsconfig.json`: TypeScript 設定。
- `vite.config.ts` (或其他建構工具設定檔): 用於建構此套件。

## 開發原則

- **型別安全**: 所有匯出的函式和資料結構都應有明確的 TypeScript 型別。
- **無副作用**: 共用函式應盡可能為純函式，避免直接的 DOM 操作或特定框架的相依性 (除非該函式明確為特定框架設計，例如 Vue composable)。
- **最小化相依**: 盡量減少對外部函式庫的相依，特別是大型函式庫。
- **文件化**: 所有公開的 API (函式、型別、常數) 都應有清晰的 JSDoc 註解。
- **測試**: 為核心邏輯撰寫單元測試，確保其正確性和穩定性。參考 `tests/auth.service.test.ts`。

## 與主應用程式的整合

- 前端 (`apps/frontend`) 和後端 (`apps/backend`) 應透過 pnpm workspace 依賴此套件。
- 匯入共用型別時，路徑應為 `@auth/shared/types` 或類似的套件內部路徑。
  ```typescript
  // 在 apps/frontend 或 apps/backend 中
  import type { AuthenticatedUser } from "@auth/shared/types";
  ```

## 建構與發佈 (Monorepo 環境)

- 此套件應可獨立建構。
- `package.json` 中的 `main`, `module`, `types` 欄位應正確設定，指向建構後的產物。
- 通常在 Monorepo 中，本地開發時會直接引用原始碼或建構後的本地版本，不需要發佈到 npm。

## 修改注意事項

- 修改 `shared/types.ts` 中的型別時，需同時考慮其對前端和後端的影響。
- 新增功能時，思考其通用性，是否適合放在此共用套件中。
- 執行 `pnpm run build` (或套件的建構指令) 來驗證變更。
- 執行 `pnpm run test` (或套件的測試指令) 來執行測試。
