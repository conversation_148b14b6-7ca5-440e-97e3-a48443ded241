---
applyTo: "**/*.{test,spec}.ts"
---

# 測試撰寫指南

本指南適用於專案中所有測試檔案 (`*.test.ts`, `*.spec.ts`) 的撰寫。

**核心原則**:

- 測試是程式碼品質的保證，應盡可能覆蓋核心邏輯。
- 測試應快速、可靠且易於理解。
- 遵循 [通用編碼標準](./general-coding.instructions.md)。

## 測試框架

- **主要測試框架**: (請根據專案實際使用的測試框架填寫，例如 Jest, Vitest, Mocha)
- **斷言庫**: (請根據專案實際使用的斷言庫填寫，例如 Chai, Jest expect)

## 測試類型

- **單元測試 (Unit Tests)**:
  - 專注於測試最小的可測試單元 (例如，單一函式、類別方法、Vue 元件的特定部分)。
  - 應模擬 (mock) 所有外部相依性 (例如，API 呼叫、資料庫存取、其他服務)。
- **整合測試 (Integration Tests)**:
  - 測試多個單元協同工作的正確性 (例如，控制器和服務的互動、元件和其 store 的互動)。
  - 可以模擬部分外部系統 (例如，HTTP 伺服器)，但應盡可能測試模組間的真實互動。
- **端對端測試 (E2E Tests)**: (若有)
  - 模擬真實使用者操作，測試整個應用程式流程。
  - 通常使用 Cypress, Playwright 等工具。

## 命名慣例

- **測試檔案**: `[filename].test.ts` 或 `[filename].spec.ts` (例如 `user.service.test.ts`, `UserProfile.spec.ts`)。
- **`describe` 區塊**: 描述被測試的單元或功能模組。
  - 例如: `describe('UserService', () => { ... });`
  - 例如: `describe('UserProfile Component', () => { ... });`
- **`it` 或 `test` 區塊**: 描述具體的測試案例，語意應清晰，說明預期行為。
  - 使用 "should [do something] when [condition]" 或 "returns [something] given [input]" 的格式。
  - 例如: `it('should return the user when a valid ID is provided', () => { ... });`
  - 例如: `it('renders the user name correctly', () => { ... });`

## 測試結構 (Arrange-Act-Assert)

每個測試案例應遵循 AAA 模式：

1.  **Arrange (安排)**: 設定測試所需的初始條件，包括模擬相依性和準備測試資料。
2.  **Act (執行)**: 執行被測試的程式碼。
3.  **Assert (斷言)**: 驗證執行的結果是否符合預期。

```typescript
describe("Calculator", () => {
  it("should add two numbers correctly", () => {
    // Arrange
    const calculator = new Calculator();
    const num1 = 5;
    const num2 = 10;
    const expectedSum = 15;

    // Act
    const result = calculator.add(num1, num2);

    // Assert
    expect(result).toBe(expectedSum);
  });
});
```

## Mocking 和 Stubbing

- **目的**: 隔離被測試單元，避免外部相依性的影響。
- **工具**: 使用測試框架內建的 mock 功能 (例如 Jest: `jest.fn()`, `jest.spyOn()`, `jest.mock()`)。
- **原則**:
  - 只 mock 必要的相依性。
  - Mock 的行為應盡可能簡單，只模擬測試案例所需的互動。
  - 確保在每個測試後清理或重置 mock (例如使用 `beforeEach` 或 `afterEach`)。

## Vue 元件測試 (範例使用 Vue Test Utils)

- **掛載元件**: 使用 `mount` 或 `shallowMount`。`shallowMount` 用於避免渲染子元件，更側重於單元測試。
- **互動**: 使用 `wrapper.find()`, `wrapper.trigger()`, `wrapper.vm` 等方法與元件互動。
- **斷言**: 檢查渲染的輸出、元件狀態、觸發的事件等。
- **Props 和 Slots**: 測試元件在不同 props 和 slots 下的行為。

## NestJS 測試

- **測試模組**: 使用 `@nestjs/testing` 中的 `Test.createTestingModule()` 建立測試模組。
- **模擬 Provider**: 在測試模組中提供 mock 的服務或相依性。
- **取得實例**: 使用 `module.get<T>()` 取得控制器或服務的實例進行測試。
- **HTTP 測試**: 可使用 `supertest` 測試 HTTP 端點。

## 撰寫良好測試的技巧

- **獨立性**: 測試案例之間不應相互依賴。
- **可重複性**: 測試結果在任何環境下都應一致。
- **快速執行**: 測試應盡可能快地執行完畢。
- **覆蓋重要路徑**: 優先測試核心業務邏輯、邊界條件和錯誤路徑。
- **可讀性**: 測試程式碼也應像產品程式碼一樣清晰易懂。
- **一個斷言原則 (Single Assertion Principle)**: 理想情況下，每個測試案例只驗證一件事情 (但可彈性處理)。

## 參考範例

- `packages/@auth/tests/auth.service.test.ts`
- (若專案中有其他測試檔案，可在此列出作為參考)
