---
applyTo: "{DevDoc/**/*.md,apps/backend/docs/**/*.md,README.md}"
---

# 文件撰寫指南

本指南適用於專案中所有 Markdown 文件 (`.md`) 的撰寫，包括 `DevDoc/` 目錄、`apps/backend/docs/` 目錄以及根目錄的 `README.md`。

**核心原則**:

- 文件應清晰、準確、完整且易於維護。
- 為不同的讀者群體 (開發者、產品經理、新成員) 提供有用的資訊。
- 遵循 [通用編碼標準](./general-coding.instructions.md) (其中適用於 Markdown 的部分)。

## 通用 Markdown 風格

- **標題**:
  - 使用 ATX 標題風格 (`# H1`, `## H2`, etc.)。
  - `# H1` 應為文件主標題，每個文件一個。
  - 邏輯上組織內容，使用適當的標題層級。
- **段落**: 段落之間用一個空行分隔。
- **清單**:
  - 無序清單使用 `-` 或 `*`。
  - 有序清單使用 `1.`。
  - 清單項目若包含多個段落或區塊，應適當縮排。
- **程式碼區塊**:
  - 使用三個反引號 (```) 包圍。
  - 明確指定程式碼語言 (例如 ` ```typescript `, ` ```bash `, ` ```json `) 以獲得正確的語法高亮。
  - 對於指令或簡短的程式碼片段，可使用行內程式碼 (`` `code` ``)。
- **連結**:
  - 使用描述性文字作為連結文字 (例如 `[Prisma Migrate 文件](https://www.prisma.io/docs/concepts/components/prisma-migrate)`)。
  - 內部文件連結應使用相對路徑。
- **圖片**:
  - 使用 `![替代文字](圖片路徑)`。
  - 圖片應存放於合理位置 (例如 `DevDoc/images/` 或相關模組的 `docs/images/`)。
- **表格**: 使用 Markdown 表格語法建立簡單表格。
- **強調**:
  - 使用 `*斜體*` 或 `_斜體_`。
  - 使用 `**粗體**` 或 `__粗體__`。
- **引用**: 使用 `> ` 進行引用。

## README.md (根目錄)

- **專案名稱和簡介**: 清晰說明專案是什麼，解決什麼問題。
- **技術棧**: 列出主要使用的技術和框架。
- **專案結構概覽**: 簡要介紹 Monorepo 中各個 app 和 package 的用途。
- **安裝與設定**:
  - 必要的環境準備 (例如 Node.js 版本, pnpm)。
  - 如何安裝相依性 (`pnpm install`)。
  - 環境變數設定說明 (如何建立 `.env` 檔案，哪些是必要變數)。
- **開發指令**:
  - 如何啟動前端開發伺服器 (`pnpm --filter frontend dev`)。
  - 如何啟動後端開發伺服器 (`pnpm --filter backend dev`)。
  - 如何執行測試 (`pnpm test`)。
  - 如何執行 lint (`pnpm lint`)。
  - 如何建構專案 (`pnpm build`)。
- **資料庫遷移**: 如何執行 Prisma migrations。
- **其他重要指令**: (例如 seed 資料庫)。
- **貢獻指南**: (若適用) 如何參與專案開發。

## `DevDoc/` 目錄

此目錄存放高階設計文件、產品需求文件 (PRD)、架構決策記錄 (ADR) 等。

- **PRD (例如 `ai-price-prd.md`, `auth-system-prd.md`)**:
  - 背景與目標
  - 使用者故事 (User Stories)
  - 功能規格 (Functional Specifications)
  - 非功能性需求 (Non-functional Requirements, 例如效能、安全性)
  - UI/UX 線框圖或設計稿連結 (若有)
  - 未來考量
- **技術設計/架構文件 (例如 `CaslAbilityFactory.md` 的存檔)**:
  - 問題描述/背景
  - 提案的解決方案 (可有多個，並比較優劣)
  - 最終決策及其理由
  - 系統架構圖 (若適用)
  - 資料庫結構設計 (若適用)
  - API 設計 (若適用)
- **TODO/重構計畫 (例如 `ai_module_refactor_todo.md`)**:
  - 待辦事項清單
  - 優先順序
  - 預期目標
  - 相關人員 (若有)

## `apps/backend/docs/` 目錄

此目錄存放後端特定技術指南和操作手冊。

- **`db-migration-guide.md`**:
  - 如何建立新的 migration。
  - 如何套用 migration。
  - 如何回滾 migration (若 Prisma 支援或有手動步驟)。
  - 常見問題與解決方案。
- **`db-export-import-guide.md`**: 資料庫匯出匯入步驟。
- **`encryption-guide.md`**: 加密相關策略和實作細節。
- **`env-sync-guide.md`**: 環境變數同步和管理指南。

## 文件維護

- 當程式碼或架構發生變更時，應及時更新相關文件。
- 定期審查文件，確保其準確性和實用性。
- 鼓勵團隊成員共同維護文件。
