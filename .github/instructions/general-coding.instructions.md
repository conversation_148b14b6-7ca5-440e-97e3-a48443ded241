---
applyTo: "**/*.{ts,vue,js,md}"
---

# 通用編碼標準

本文件定義了專案通用的編碼風格和最佳實踐。

## 命名慣例

- 變數與函式：`camelCase`
- 類別、介面、型別別名、Enum：`PascalCase`
- Vue 元件檔名與元件名稱：`PascalCase` (例如 `UserProfileCard.vue`)
- 其他檔案 (例如 services, composables, utils)：`kebab-case` (例如 `user-service.ts`, `use-auth-guard.ts`)
- 常數：`SCREAMING_SNAKE_CASE` (例如 `MAX_USERS`)
- 私有屬性和方法 (若語言支持或約定)：以下底線開頭 `_privateMember`

## 註解

- 公開的函式、類別、方法和複雜的邏輯區塊應有 JSDoc 風格的註解。
- 註解應解釋 "為什麼" 而不是 "做什麼" (除非程式碼本身不夠清晰)。
- 不允許在程式碼文件中使用 `// TODO:` 或類似的預留位置註解；必須提供完整的程式碼實作。
- 使用 `// FIXME:` 標記需要修復的問題。

## 程式碼風格

- **可讀性**: 優先考慮程式碼的清晰度和可維護性。
- **函式長度**: 函式應盡量簡短，並遵循單一職責原則。
- **避免魔法數字/字串**: 使用具名常數或設定檔取代。
- **錯誤處理**: 對可能失敗的操作進行適當的錯誤處理。
- **日誌記錄**: 在關鍵操作和錯誤處理中使用日誌記錄。

## TypeScript 特定

- 盡可能使用明確的型別，避免使用 `any`，除非絕對必要且有註解說明。
- 優先使用 `interface` 定義物件的形狀，使用 `type` 定義聯合型別、元組或更複雜的型別。
- 使用 `Readonly<T>` 或 `readonly` 修飾符來表示不可變資料。

## Markdown 文件

- 使用一致的標題層級。
- 程式碼區塊應標明語言。
