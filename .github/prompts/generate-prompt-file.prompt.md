---
mode: "agent"
description: "協助建立一個新的 Prompt file (.prompt.md) 以自訂 Copilot Chat 行為。"
---

# 建立新的 Prompt File

這個提示將引導您建立一個新的 `.prompt.md` 檔案，用於自訂 GitHub Copilot Chat 的行為。

請提供以下資訊：

1.  **新 Prompt File 的主要用途是什麼？** (例如：協助重構 Vue 元件、產生特定類型的 NestJS 服務、檢查程式碼風格等)
    `${input:purpose:描述新 Prompt file 的目標}`

2.  **新 Prompt File 的建議檔名 (不含副檔名，例如 `refactor-vue-component` 或 `generate-nestjs-service`)？**
    它將被建立在 `.github/prompts/` 目錄下，並自動加上 `.prompt.md` 副檔名。
    `${input:filename:my-custom-prompt}`

3.  **新 Prompt File 的 `mode` 是什麼？** (可選：`agent`, `edit`, 或 `ask`。預設為 `agent`)
    `${input:mode:agent}`

4.  **新 Prompt File 需要使用哪些 `tools`？** (例如：`codebase`, `terminal`；若無則留空，我會根據您的「主要用途」嘗試建議。多個工具請用逗號分隔)
    `${input:tools}`

5.  **新 Prompt File 的簡短 `description` (用於其 front matter)？**
    `${input:description:新 Prompt file 的簡短描述}`

6.  **請提供新 Prompt File 的主要提示內容 (Markdown 格式)。**
    請在此處詳細描述您希望 Copilot 如何回應，可以使用 `${variable}` 或 `${input:varName:placeholder}` 等語法。

    ```markdown
    ${input:promptContent:

    # 新提示標題 (例如：Vue 元件重構助手)

    這是新提示的主要內容...
    請告訴我關於 `${input:topic:某個主題}` 的資訊。
    }
    ```

---

請根據以上提供的資訊，產生名為 `${filename}.prompt.md` 的新 Prompt file 的完整內容。
該檔案應包含正確的 front matter。Front matter 應包含：

- `mode`: 使用者提供的 `${input:mode}`。
- `description`: 使用者提供的 `${input:description}`。
- `tools`:
  - 如果使用者在 `${input:tools}` 中提供了工具 (逗號分隔的字串)，則將其轉換為 YAML 列表格式 (例如，若輸入為 "tool1, tool2"，則輸出 `tools: ["tool1", "tool2"]`)。
  - 如果使用者將 `${input:tools}` 留空，請分析 `${input:purpose}` (新 Prompt File 的主要用途)。
    - 如果用途涉及讀取或修改程式碼，建議加入 `codebase`。
    - 如果用途涉及執行終端機指令，建議加入 `terminal`。
    - 如果不確定或用途較通用，可以設為 `tools: []` 或省略該欄位。
    - 在產生檔案前，請向使用者確認建議的工具。

以及使用者提供的主要提示內容 (`${input:promptContent}`)。
新檔案應建立在 `.github/prompts/` 目錄下。
