---
mode: "agent"
tools: ["codebase"]
description: "協助更新專案相關文件"
---

# 專案文件更新助手

我需要更新專案中的一份文件，使其能更準確地反映目前的專案狀態或包含最新的資訊。

**要更新的文件路徑**:
`${input:docPath:請輸入要更新的文件的完整路徑 (例如：DevDoc/auth-system-prd.md 或 README.md)}`

**更新的主要原因或目標**:
`${input:updateReason:例如：新增最近實作的功能說明、修正過時的設定步驟、補充 API 端點的變更等}`

**需要特別關注或修改的章節/內容**:
`${input:specificSections:請描述文件中哪些部分需要重點更新，或提供相關的程式碼變更 commit ID (如果適用)}`

請分析指定的專案文件 (`${docPath}`) 和相關的程式碼庫 (如果需要)，並提供以下協助：

1.  **內容審查與建議**:

    - 指出文件中可能過時或不準確的部分。
    - 根據 `${updateReason}` 和 `${specificSections}`，建議需要新增、修改或刪除的內容。

2.  **草擬更新內容**:

    - 為需要修改的部分提供更新後的文本草稿。
    - 如果需要新增內容，請提供新內容的建議結構和文字。

3.  **格式與風格**:
    - 確保更新後的內容符合文件原有的格式和風格 (例如 Markdown 語法)。

請盡可能提供具體且可直接使用的文字建議。
