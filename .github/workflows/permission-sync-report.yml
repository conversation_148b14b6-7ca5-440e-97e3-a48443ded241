name: 權限同步報告 CI

on:
  push:
    branches:
      - main
  workflow_dispatch:

jobs:
  permission-sync:
    runs-on: ubuntu-latest
    steps:
      - name: 檢出程式碼
        uses: actions/checkout@v3

      - name: 設定 pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 7

      - name: 安裝相依套件
        run: pnpm install

      - name: 執行權限同步 (強制覆蓋)
        run: pnpm db:sync-perms --force

      - name: 上傳 Markdown 報告為 artifact
        uses: actions/upload-artifact@v3
        with:
          name: permission-sync-report-md
          path: apps/backend/reports/permission-sync-report.md

      - name: 上傳 JSON 報告為 artifact
        uses: actions/upload-artifact@v3
        with:
          name: permission-sync-report-json
          path: apps/backend/reports/permission-sync-report.json
