#!/usr/bin/env node

/**
 * 登出功能測試腳本
 * 
 * 這個腳本可以用來測試登出功能是否正常工作
 * 使用方法：node scripts/test-logout.js
 */

const axios = require('axios');

// 配置
const BASE_URL = process.env.API_BASE_URL || 'http://localhost:3001';
const TEST_EMAIL = process.env.TEST_EMAIL || '<EMAIL>';
const TEST_PASSWORD = process.env.TEST_PASSWORD || 'admin123';

console.log('🧪 開始測試登出功能...');
console.log(`📍 API 基礎 URL: ${BASE_URL}`);

async function testLogout() {
  try {
    // 1. 登入獲取 token
    console.log('\n1️⃣ 嘗試登入...');
    const loginResponse = await axios.post(`${BASE_URL}/api/auth/login`, {
      email: TEST_EMAIL,
      password: TEST_PASSWORD
    }, {
      withCredentials: true,
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (loginResponse.status === 200) {
      console.log('✅ 登入成功');
      console.log(`👤 用戶: ${loginResponse.data.user.email}`);
      
      // 檢查是否有 Set-Cookie 標頭
      const setCookieHeader = loginResponse.headers['set-cookie'];
      if (setCookieHeader) {
        console.log('🍪 收到 cookies:', setCookieHeader);
      } else {
        console.log('⚠️  未收到 cookies');
      }
    } else {
      throw new Error(`登入失敗: ${loginResponse.status}`);
    }

    // 2. 測試登出
    console.log('\n2️⃣ 嘗試登出...');
    
    // 從登入回應中提取 cookies
    const cookies = loginResponse.headers['set-cookie'];
    let cookieHeader = '';
    if (cookies) {
      cookieHeader = cookies.map(cookie => cookie.split(';')[0]).join('; ');
    }

    const logoutResponse = await axios.post(`${BASE_URL}/api/auth/logout`, {}, {
      withCredentials: true,
      headers: {
        'Cookie': cookieHeader,
        'Content-Type': 'application/json'
      }
    });

    if (logoutResponse.status === 200 || logoutResponse.status === 201) {
      console.log('✅ 登出成功');
      console.log('📄 回應:', logoutResponse.data);
      
      // 檢查是否有清除 cookies 的標頭
      const setCookieHeader = logoutResponse.headers['set-cookie'];
      if (setCookieHeader) {
        console.log('🍪 收到清除 cookies 指令:', setCookieHeader);
        
        // 檢查是否包含過期時間
        const hasExpiredCookies = setCookieHeader.some(cookie => 
          cookie.includes('expires=Thu, 01 Jan 1970') || 
          cookie.includes('Max-Age=0')
        );
        
        if (hasExpiredCookies) {
          console.log('✅ Cookies 已正確設置為過期');
        } else {
          console.log('⚠️  Cookies 可能未正確清除');
        }
      } else {
        console.log('⚠️  未收到清除 cookies 的指令');
      }
    } else {
      throw new Error(`登出失敗: ${logoutResponse.status}`);
    }

    // 3. 測試登出後是否無法存取需要認證的端點
    console.log('\n3️⃣ 測試登出後的認證狀態...');
    
    try {
      const meResponse = await axios.get(`${BASE_URL}/api/auth/me`, {
        withCredentials: true,
        headers: {
          'Cookie': cookieHeader
        }
      });
      
      if (meResponse.status === 200) {
        console.log('❌ 登出後仍然可以存取認證端點，登出可能未完全生效');
        console.log('👤 當前用戶:', meResponse.data);
      }
    } catch (error) {
      if (error.response && error.response.status === 401) {
        console.log('✅ 登出後無法存取認證端點，登出功能正常');
      } else {
        console.log('⚠️  測試認證狀態時發生未預期的錯誤:', error.message);
      }
    }

    console.log('\n🎉 登出功能測試完成！');

  } catch (error) {
    console.error('\n❌ 測試過程中發生錯誤:');
    
    if (error.response) {
      console.error(`HTTP ${error.response.status}: ${error.response.statusText}`);
      console.error('回應內容:', error.response.data);
    } else if (error.request) {
      console.error('無法連接到伺服器:', error.message);
    } else {
      console.error('錯誤:', error.message);
    }
    
    process.exit(1);
  }
}

// 執行測試
testLogout().catch(error => {
  console.error('測試執行失敗:', error);
  process.exit(1);
});
