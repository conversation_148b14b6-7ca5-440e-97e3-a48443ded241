# 登出功能測試指南

## 修復內容總結

### 後端修復
1. **修復登出 API**：在 `/api/auth/logout` 端點中添加了 cookie 清除邏輯
   - 清除 `auth_token`、`access_token`、`refresh_token` cookies
   - 使用正確的 cookie 選項（domain、path、secure 等）
   - 即使發生錯誤也會嘗試清除 cookies

2. **統一 cookie 名稱**：
   - 將 Google OAuth 和 LINE OAuth 的 cookie 名稱統一為 `auth_token`
   - 確保所有登入方式使用相同的 cookie 名稱和選項

### 前端修復
1. **改進 TokenService**：
   - 增強 `clearAuth()` 方法，清除更多可能的 cookies 和本地儲存
   - 嘗試清除不同路徑和域名的 cookies
   - 同時清除 localStorage 和 sessionStorage

2. **改進 AuthService**：
   - 重構登出邏輯，確保即使 API 失敗也能清除前端狀態
   - 添加 `performCompleteCleanup()` 方法進行徹底清理
   - 防止在登出過程中觸發 token 刷新

3. **改進 AuthStore**：
   - 確保無論 API 呼叫是否成功都會重置前端狀態
   - 清除錯誤狀態

4. **改進組件登出處理**：
   - UserMenu.vue 和 NoTenant.vue 中的登出處理更加健壯
   - 即使登出失敗也會執行重導向
   - 提供適當的用戶反饋

5. **改進重導向函數**：
   - 清除更多認證相關的本地儲存項目
   - 嘗試清除不同路徑和域名的 cookies

## 測試步驟

### 1. 基本登出測試
1. 登入系統
2. 點擊登出按鈕
3. 確認：
   - 頁面重導向到登入頁面
   - 瀏覽器開發者工具中 cookies 已被清除
   - localStorage 和 sessionStorage 已被清除
   - 無法直接訪問需要認證的頁面

### 2. 頁面重新載入測試
1. 登入系統
2. 點擊登出
3. 重新載入頁面
4. 確認仍然顯示為未登入狀態

### 3. 直接 URL 訪問測試
1. 登入系統
2. 點擊登出
3. 嘗試直接在網址列輸入需要認證的頁面 URL
4. 確認被重導向到登入頁面

### 4. 多標籤頁測試
1. 在多個標籤頁中開啟系統
2. 在其中一個標籤頁登出
3. 切換到其他標籤頁並重新載入
4. 確認所有標籤頁都顯示為未登入狀態

### 5. 網路錯誤測試
1. 登入系統
2. 中斷網路連線
3. 點擊登出按鈕
4. 確認：
   - 前端狀態仍然被清除
   - 頁面重導向到登入頁面
   - 恢復網路後無法存取需要認證的頁面

### 6. OAuth 登入測試
1. 使用 Google 或 LINE 登入
2. 點擊登出
3. 確認登出功能正常運作

## 檢查項目

### 瀏覽器開發者工具檢查
1. **Application > Cookies**：
   - `auth_token` cookie 應該被清除
   - `access_token` cookie 應該被清除（如果存在）
   - `refresh_token` cookie 應該被清除（如果存在）

2. **Application > Local Storage**：
   - `auth.session.exists` 應該被清除
   - `auth.user` 應該被清除
   - `user` 應該被清除
   - 其他認證相關項目應該被清除

3. **Application > Session Storage**：
   - 所有認證相關項目應該被清除

### 網路請求檢查
1. **Network > XHR**：
   - 登出請求應該成功發送到 `/api/auth/logout`
   - 回應應該包含適當的 Set-Cookie 標頭清除 cookies

### 路由保護檢查
1. 嘗試訪問以下需要認證的路由：
   - `/workspace/*`
   - `/admin/*`
   - `/profile/*`
2. 確認都被重導向到 `/auth/login`

## 預期行為

### 成功登出後應該：
1. 所有 HTTP-only cookies 被後端清除
2. 所有前端儲存的認證資料被清除
3. 頁面重導向到登入頁面
4. 無法存取需要認證的頁面
5. 頁面重新載入後仍然保持登出狀態
6. 多標籤頁同步登出狀態

### 錯誤處理：
1. 即使後端 API 失敗，前端狀態仍然被清除
2. 即使網路錯誤，前端狀態仍然被清除
3. 提供適當的用戶反饋訊息

## 故障排除

如果登出後仍然顯示為已登入：
1. 檢查瀏覽器開發者工具中的 cookies 是否真的被清除
2. 檢查 localStorage 和 sessionStorage 是否被清除
3. 檢查網路請求是否成功發送到後端
4. 檢查後端日誌是否有錯誤訊息
5. 嘗試硬重新整理頁面（Ctrl+F5 或 Cmd+Shift+R）
6. 清除瀏覽器快取和 cookies
