# Task ID: 7
# Title: User Management System
# Status: pending
# Dependencies: 6
# Priority: medium
# Description: Implement comprehensive user management for system and tenant users
# Details:
Create separate user management for system_users and tenant_users. Implement user invitation system with email verification using nodemailer 6+. Create user profile management, password change, and account deactivation features. Implement user search and filtering capabilities. Add user activity logging and last login tracking. Support bulk user operations for tenant admins.

# Test Strategy:
Test user creation, invitation flow, profile updates, and verify email notifications are sent correctly
