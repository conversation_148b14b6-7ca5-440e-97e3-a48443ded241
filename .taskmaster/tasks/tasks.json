{"tasks": [{"id": 1, "title": "Setup Project Repository and Development Environment", "description": "Initialize the project repository with proper structure, development tools, and CI/CD pipeline configuration", "details": "Create monorepo structure with separate frontend (Vue 3 + TypeScript) and backend (NestJS + TypeScript) directories. Setup package.json with latest dependencies: Vue 3.4+, NestJS 10+, TypeScript 5+, Prisma 5+, Tailwind CSS 3.4+. Configure ESLint, <PERSON><PERSON><PERSON>, <PERSON><PERSON> for code quality. Setup Docker containers for PostgreSQL 15+ and Redis 7+. Initialize Git repository with proper .gitignore and branch protection rules.", "testStrategy": "Verify all dependencies install correctly, Docker containers start successfully, and development servers run without errors", "priority": "high", "dependencies": [], "status": "done", "subtasks": []}, {"id": 2, "title": "Database Schema Design and Migration Setup", "description": "Design and implement the complete database schema for multi-tenant architecture with Prisma ORM", "details": "Create Prisma schema with multi-tenant architecture: system_users, tenants, tenant_users, workspaces, workspace_members, roles, permissions, role_permissions, ai_bots, ai_keys, ai_models, ai_usage_logs, plans, subscriptions, orders tables. Implement proper foreign key relationships, indexes for performance, and data isolation constraints. Use Prisma 5.7+ with PostgreSQL provider. Setup migration scripts and seed data for development.", "testStrategy": "Run migrations successfully, verify all relationships work correctly, test data isolation between tenants, and validate schema constraints", "priority": "high", "dependencies": [1], "status": "done", "subtasks": [{"id": 1, "title": "Design Core Entities Schema", "description": "Develop the database schema for core entities such as users, tenants, and workspaces, ensuring proper relationships and data isolation in a multi-tenant architecture.", "dependencies": [], "details": "Implement a schema that accommodates multiple tenants, ensuring data isolation and scalability. Consider using a shared database with separate schemas for each tenant to balance resource utilization and data isolation. ([jomatt.io](https://jomatt.io/how-to-design-a-multi-tenant-saas-solution/?utm_source=openai))", "status": "done"}, {"id": 2, "title": "Design AI-Re<PERSON>a", "description": "Create the database schema for AI-related entities, including bots, models, and usage logs, ensuring they integrate seamlessly with the core entities and support multi-tenant requirements.", "dependencies": [1], "details": "Extend the core schema to include AI-specific entities, ensuring they are tenant-aware and support efficient querying and data retrieval. Implement row-level security to enforce data isolation between tenants. ([aloa.co](https://aloa.co/blog/multi-tenant-saas?utm_source=openai))", "status": "done"}, {"id": 3, "title": "Design Subscription and Billing Schema", "description": "Develop the database schema for managing subscriptions and billing, ensuring it supports multiple tenants and integrates with the core and AI-related schemas.", "dependencies": [1], "details": "Implement a schema that tracks subscription plans, billing cycles, and payment histories, associating them with the appropriate tenants and users. Ensure data integrity and support for complex billing scenarios. ([aloa.co](https://aloa.co/blog/multi-tenant-saas?utm_source=openai))", "status": "done"}, {"id": 4, "title": "Define Relationships and Constraints", "description": "Establish relationships and constraints between the core, AI-related, and subscription schemas to maintain data integrity and support complex queries.", "dependencies": [1, 2, 3], "details": "Define foreign keys, unique constraints, and indexes to enforce data integrity and optimize query performance. Ensure that relationships support multi-tenant data isolation and scalability. ([aloa.co](https://aloa.co/blog/multi-tenant-saas?utm_source=openai))", "status": "done"}, {"id": 5, "title": "Create <PERSON><PERSON>", "description": "Develop migration scripts to set up the database schemas and apply any necessary changes over time, ensuring smooth transitions and data integrity.", "dependencies": [1, 2, 3, 4], "details": "Write scripts to create and modify database schemas, including tables, indexes, and constraints. Ensure that migrations are idempotent and can be applied in any environment. ([aloa.co](https://aloa.co/blog/multi-tenant-saas?utm_source=openai))", "status": "done"}, {"id": 6, "title": "Set Up Seed Data and Testing", "description": "Implement seed data and testing setups to validate the database schemas and ensure they function correctly in a multi-tenant environment.", "dependencies": [1, 2, 3, 4, 5], "details": "Create sample data for users, tenants, workspaces, bots, models, and billing records. Develop unit and integration tests to verify data integrity, relationships, and performance under load. ([aloa.co](https://aloa.co/blog/multi-tenant-saas?utm_source=openai))", "status": "done"}]}, {"id": 3, "title": "Backend Core Architecture Setup", "description": "Establish NestJS backend foundation with modules, guards, and middleware", "details": "Setup NestJS 10+ with TypeScript 5+. Create core modules: AuthModule, UsersModule, TenantsModule, WorkspacesModule, AiModule. Implement global exception filters, validation pipes using class-validator 0.14+, and request logging middleware. Setup Swagger/OpenAPI documentation with @nestjs/swagger 7+. Configure environment variables with @nestjs/config 3+. Implement health check endpoints.", "testStrategy": "Verify all modules load correctly, API documentation generates properly, health checks respond, and error handling works as expected", "priority": "high", "dependencies": [2], "status": "done", "subtasks": []}, {"id": 4, "title": "JWT Authentication System Implementation", "description": "Implement secure JWT-based authentication with access and refresh token mechanism", "details": "Implement JWT authentication using @nestjs/jwt 10+ and @nestjs/passport 10+. Create access tokens (15min expiry) and refresh tokens (7 days expiry). Implement JwtAuthGuard, LocalAuthGuard, and RefreshTokenGuard. Setup password hashing with bcrypt 5+. Create login, logout, refresh token, and password reset endpoints. Store refresh tokens securely in database with rotation mechanism.", "testStrategy": "Test login/logout flows, token refresh mechanism, password reset functionality, and verify tokens expire correctly", "priority": "high", "dependencies": [3], "status": "done", "subtasks": []}, {"id": 5, "title": "Multi-Tenant Architecture Implementation", "description": "Implement core multi-tenant functionality with data isolation and tenant context", "details": "Create TenantGuard to extract tenant context from JWT tokens. Implement TenantService for tenant CRUD operations. Create tenant-scoped Prisma queries using middleware to ensure data isolation. Implement tenant creation workflow with initial workspace setup. Create tenant switching mechanism for system admins. Use tenant_id in all relevant database queries automatically.", "testStrategy": "Verify complete data isolation between tenants, test tenant creation workflow, and validate tenant context is properly maintained throughout requests", "priority": "high", "dependencies": [4], "status": "pending", "subtasks": [{"id": 1, "title": "Tenant Context Extraction and Guard Implementation", "description": "Develop mechanisms to extract tenant context from incoming requests and implement guards to ensure data isolation and security across tenants.", "dependencies": [], "details": "Implement middleware or context extraction functions to identify the tenant from each request, such as by parsing subdomains, headers, or authentication tokens. Develop guards to enforce tenant-specific data access controls, ensuring that each tenant can only access their own data and preventing unauthorized cross-tenant access. This approach is crucial for maintaining data security and privacy in a multi-tenant environment. ([workos.com](https://workos.com/blog/what-is-multi-tenancy-pros-cons-best-practices?utm_source=openai))", "status": "done"}, {"id": 2, "title": "Tenant Service with CRUD Operations", "description": "Create a tenant service that provides Create, Read, Update, and Delete operations, ensuring that all data manipulations are scoped to the correct tenant context.", "dependencies": [1], "details": "Design a service layer that performs CRUD operations while automatically scoping all database queries to the current tenant. This can be achieved by including a tenant identifier in each database query, ensuring that data operations are isolated per tenant. Utilizing Prisma's middleware capabilities can assist in dynamically adding the tenant context to each query, maintaining data integrity and isolation. ([dev.to](https://dev.to/zenstack/multi-tenant-implementation-approaches-with-prisma-and-zenstack-4p20?utm_source=openai))", "status": "done"}, {"id": 3, "title": "Prisma Middleware for Automatic Tenant Scoping", "description": "Implement Prisma middleware to automatically scope database queries to the current tenant, reducing the need for manual tenant context management in each query.", "dependencies": [2], "details": "Develop Prisma middleware that intercepts all database queries and appends the current tenant identifier to the query conditions. This ensures that all data access is automatically scoped to the correct tenant, simplifying the codebase and reducing the risk of cross-tenant data access. It's important to handle complex queries and relationships carefully to maintain data integrity. ([dev.to](https://dev.to/zenstack/multi-tenant-implementation-approaches-with-prisma-and-zenstack-4p20?utm_source=openai))", "status": "done"}, {"id": 4, "title": "Tenant Creation Workflow and Initial Setup", "description": "Design and implement a workflow for creating new tenants, including initial setup tasks such as database schema creation, configuration, and resource allocation.", "dependencies": [3], "details": "Develop an automated process for onboarding new tenants, which includes setting up tenant-specific databases or schemas, configuring initial settings, and allocating necessary resources. This workflow should ensure that each tenant's environment is properly isolated and configured, facilitating a smooth onboarding experience and maintaining data security. ([relevant.software](https://relevant.software/blog/multi-tenant-architecture/?utm_source=openai))", "status": "done"}, {"id": 5, "title": "Tenant Switching Mechanism for Admins", "description": "Implement a mechanism that allows administrators to switch between tenant contexts for management and support purposes, ensuring that tenant data remains secure during these operations.", "dependencies": [4], "details": "Create a secure and controlled method for administrators to switch between tenant contexts, enabling them to perform management tasks, support activities, and troubleshooting without compromising tenant data security. This mechanism should include proper authentication, authorization checks, and logging to maintain accountability and prevent unauthorized access. ([workos.com](https://workos.com/blog/what-is-multi-tenancy-pros-cons-best-practices?utm_source=openai))", "status": "done"}, {"id": 6, "title": "Data Isolation Testing and Validation", "description": "Conduct thorough testing and validation to ensure that data isolation mechanisms are functioning correctly and that tenant data is secure and inaccessible to unauthorized users.", "dependencies": [5], "details": "Perform comprehensive testing to verify that all data isolation mechanisms are effective, including unit tests, integration tests, and security audits. Validate that tenant data is properly isolated and that there are no vulnerabilities that could lead to unauthorized data access. Regularly review and update security protocols to address emerging threats and maintain compliance with data protection regulations. ([workos.com](https://workos.com/blog/what-is-multi-tenancy-pros-cons-best-practices?utm_source=openai))", "status": "done"}]}, {"id": 6, "title": "RBAC/ABAC Permission System with CASL", "description": "Implement a flexible role-based and attribute-based access control system using CASL. This update focuses on detailing the implementation requirements, including addressing the `TODO(\"實現完整的權限邏輯\")` in `auth.service.ts` by fully implementing the `getUnifiedUserPermissions` method, extending CASL rules for different user types, and ensuring robust permission checks in controllers.", "status": "pending", "dependencies": [5], "priority": "high", "details": "Integrate `@casl/ability 6+` and `@casl/prisma 1+` for permission management.\nKey implementation aspects include:\n1.  **Defining abilities** for different user roles: `SuperAdmin`, `SystemAdmin`, `TenantAdmin`, `TenantUser`, with specific attention to **extending CASL rules to support distinct permission requirements for `SystemUser` and `TenantUser`**.\n2.  **Fully implementing the `getUnifiedUserPermissions` method** in `auth.service.ts` to retrieve unified user permissions, addressing the `TODO(\"實現完整的權限邏輯\")`.\n3.  Creating `PermissionsGuard` to check abilities before controller actions and implementing permission decorators.\n4.  Implementing services for role assignment and management.\n5.  **Providing detailed implementation for both role-based and attribute-based permission checks**, including resource-based permissions using `@casl/prisma`.\n6.  **Updating permission-related endpoints in `auth.controller.ts`** to use the new permission logic and guards.", "testStrategy": "Test all permission combinations, verify role inheritance works correctly, and ensure unauthorized access is properly blocked. **Specifically, test the `getUnifiedUserPermissions` method, distinct permissions for `SystemUser` vs. `TenantUser`, the behavior of role-based and attribute-based checks, and the security of updated `auth.controller.ts` endpoints.**", "subtasks": [{"id": 1, "title": "CASL Ability Definitions and Role Hierarchy", "description": "Define user abilities and establish a role hierarchy using CASL to manage permissions effectively, including specific rules for SystemUser and TenantUser.", "dependencies": [], "details": "Utilize CASL's `AbilityBuilder` to define user abilities for roles like `SuperAdmin`, `SystemAdmin`, `TenantAdmin`, `TenantUser`. **Crucially, extend CASL rules to support the distinct permission requirements for `SystemUser` and `TenantUser` roles.** Establish a role hierarchy. This work will contribute to resolving the `TODO(\"實現完整的權限邏輯\")` in `auth.service.ts` concerning permission definitions. ([npmjs.com](https://www.npmjs.com/package/%40casl/ability?utm_source=openai))", "status": "pending"}, {"id": 2, "title": "Permission Guard Implementation and Decorators", "description": "Implement permission guards and decorators to enforce access control, with detailed logic for role-based checks.", "dependencies": [1], "details": "Develop `PermissionsGuard` to check CASL abilities before controller actions. Create permission decorators for easy controller protection. **Provide detailed implementation for robust role-based permission checks within the guard and decorators.** This involves creating middleware to validate tenant context if applicable and using decorators to specify required permissions for routes. ([medium.com](https://medium.com/%40taofiqaiyelabegan/building-a-multi-tenant-e-commerce-api-fine-grained-authorization-with-nest-js-and-permit-io-ddd2ca77d19d?utm_source=openai))", "status": "pending"}, {"id": 3, "title": "Role Assignment and Management Services", "description": "Create services to assign and manage user roles within the application.", "dependencies": [1], "details": "Develop services to assign and manage user roles, ensuring that users have appropriate permissions based on their roles. This includes creating functions to assign roles to users and manage role hierarchies. ([codeproject.com](https://www.codeproject.com/Articles/151527/Access-Control-in-Multi-Tenant-Applications-with-V?utm_source=openai))", "status": "pending"}, {"id": 4, "title": "Resource-Based Permission Checking", "description": "Implement resource-based permission checks, including detailed attribute-based logic, to control access to specific resources.", "dependencies": [1], "details": "Implement resource-based permission checks to control access to specific resources. **Provide detailed implementation for attribute-based permission checks, leveraging `@casl/prisma` where appropriate.** This involves defining permissions for individual resources and checking user abilities against resource attributes before granting access. ([fullstack.com](https://www.fullstack.com/labs/resources/blog/role-based-user-authorization-in-javascript-with-casl?utm_source=openai))", "status": "pending"}, {"id": 5, "title": "Permission Testing and Validation Framework", "description": "Develop a framework to test and validate permission configurations, covering new methods and specific user type scenarios.", "dependencies": [1, 2, 4, 6, 7], "details": "Develop a framework to test and validate permission configurations. **Include test cases for the `getUnifiedUserPermissions` method, distinct permissions for `SystemUser` and `TenantUser`, comprehensive role-based and attribute-based checks, and the updated `auth.controller.ts` endpoints.** Ensure that access control rules are correctly enforced and unauthorized access is blocked. ([dev.to](https://dev.to/samueloseh/mastering-role-based-access-control-with-casl-part-one-5d1a?utm_source=openai))", "status": "pending"}, {"id": 6, "title": "Implement `getUnifiedUserPermissions` in `auth.service.ts`", "description": "Fully implement the `getUnifiedUserPermissions` method within `auth.service.ts` to retrieve unified user permissions, addressing a key part of the `TODO(\"實現完整的權限邏輯\")`.", "dependencies": [1], "details": "This method should aggregate permissions based on user roles (including `SystemUser`, `TenantUser`), direct assignments, and other relevant attributes to construct a comprehensive CASL `Ability` instance for the user. Ensure it correctly reflects the defined CASL rules and hierarchy.", "status": "pending"}, {"id": 7, "title": "Update `auth.controller.ts` Endpoints with New Permission Logic", "description": "Integrate the new CASL permission system into `auth.controller.ts` by applying guards and decorators to relevant endpoints.", "dependencies": [2, 6], "details": "Refactor existing permission-related endpoints in `auth.controller.ts`. Apply the `PermissionsGuard` and appropriate permission decorators to protect these endpoints. Ensure that the controller actions correctly leverage the CASL abilities provided by the updated `auth.service.ts`.", "status": "pending"}]}, {"id": 7, "title": "User Management System", "description": "Implement comprehensive user management for system and tenant users", "details": "Create separate user management for system_users and tenant_users. Implement user invitation system with email verification using nodemailer 6+. Create user profile management, password change, and account deactivation features. Implement user search and filtering capabilities. Add user activity logging and last login tracking. Support bulk user operations for tenant admins.", "testStrategy": "Test user creation, invitation flow, profile updates, and verify email notifications are sent correctly", "priority": "medium", "dependencies": [6], "status": "pending", "subtasks": []}, {"id": 8, "title": "Frontend Core Architecture Setup", "description": "Establish Vue 3 frontend foundation with routing, state management, and UI components", "details": "Setup Vue 3.4+ with Composition API, TypeScript 5+, and Vite 5+. Configure Vue Router 4+ with route guards for authentication and permissions. Setup Pinia 2+ for state management. Install and configure Shadcn-Vue with Tailwind CSS 3.4+. Create base layout components, navigation, and responsive design system. Setup axios interceptors for API calls with automatic token refresh.", "testStrategy": "Verify routing works correctly, state management persists data, UI components render properly, and API integration functions", "priority": "high", "dependencies": [1], "status": "pending", "subtasks": []}, {"id": 9, "title": "Authentication Frontend Implementation", "description": "Create login, registration, and authentication UI components", "details": "Create login form with email/password validation using VeeValidate 4+. Implement registration flow with email verification. Create password reset and change password components. Implement automatic token refresh and logout on token expiry. Create authentication store with Pinia for managing user state. Add loading states and error handling for all auth operations.", "testStrategy": "Test all authentication flows, verify form validation works, and ensure proper error messages are displayed", "priority": "high", "dependencies": [8, 4], "status": "pending", "subtasks": []}, {"id": 10, "title": "Tenant Management Frontend", "description": "Create tenant management interface for system administrators", "details": "Create tenant listing page with search, filter, and pagination using TanStack Table. Implement tenant creation form with validation. Create tenant details page with edit capabilities. Add tenant status management (active/inactive). Implement tenant user management interface. Create tenant analytics dashboard showing usage statistics.", "testStrategy": "Test tenant CRUD operations, verify data updates correctly, and ensure proper permission checks are in place", "priority": "medium", "dependencies": [9, 5], "status": "pending", "subtasks": []}, {"id": 11, "title": "Workspace Management System", "description": "Implement workspace creation, management, and member invitation system", "details": "Create workspace CRUD operations with proper tenant scoping. Implement workspace member management with role assignments. Create workspace invitation system with email notifications. Add workspace settings and configuration options. Implement workspace switching for users with multiple workspace access. Create workspace analytics and usage tracking.", "testStrategy": "Test workspace creation, member invitations, role assignments, and verify workspace isolation works correctly", "priority": "medium", "dependencies": [7], "status": "pending", "subtasks": []}, {"id": 12, "title": "Workspace Management Frontend", "description": "Create workspace management UI with member management and settings", "details": "Create workspace dashboard with overview statistics. Implement workspace creation and editing forms. Create member management interface with role assignment. Add workspace settings page with customization options. Implement workspace switching dropdown in navigation. Create workspace invitation flow with email preview.", "testStrategy": "Test workspace UI interactions, member management functions, and verify workspace switching works properly", "priority": "medium", "dependencies": [10, 11], "status": "pending", "subtasks": []}, {"id": 13, "title": "AI Service Integration Architecture", "description": "Create flexible AI service integration layer supporting multiple providers", "details": "Create AiProviderFactory with support for OpenAI 4+, An<PERSON><PERSON>, Google Gemini APIs. Implement provider abstraction layer for unified API calls. Create AI key management system with encryption using crypto-js 4+. Implement usage tracking and rate limiting. Add support for local model integration with OpenAI-compatible endpoints. Create provider health checking and failover mechanisms.", "testStrategy": "Test integration with multiple AI providers, verify usage tracking accuracy, and ensure failover mechanisms work correctly", "priority": "high", "dependencies": [3], "status": "pending", "subtasks": [{"id": 1, "title": "Design AI Provider Factory and Abstraction Layer", "description": "Develop a factory pattern to manage multiple AI providers and create an abstraction layer to standardize interactions with different AI models, enhancing modularity and scalability.", "dependencies": [], "details": "Implement an abstract factory pattern to encapsulate the creation of AI model instances, allowing for easy addition of new providers without altering existing code. Design an abstraction layer to provide a uniform interface for interacting with various AI models, facilitating seamless integration and maintenance.", "status": "pending"}, {"id": 2, "title": "Integrate OpenAI API", "description": "Establish a connection with OpenAI's API to access their language models, enabling functionalities such as text generation and conversation modeling.", "dependencies": [1], "details": "Utilize OpenAI's API endpoints to incorporate their language models into the system. Ensure compliance with OpenAI's usage policies and implement necessary authentication mechanisms to secure API access.", "status": "pending"}, {"id": 3, "title": "Integrate Anthropic's <PERSON>", "description": "Connect to Anthropic's Claude API to leverage their language models, providing additional AI capabilities within the application.", "dependencies": [1], "details": "Implement integration with Anthropic's Claude API, ensuring adherence to their API documentation and security protocols. Utilize the Model Context Protocol (MCP) for standardized communication between the application and Claude models. ([en.wikipedia.org](https://en.wikipedia.org/wiki/Model_Context_Protocol?utm_source=openai))", "status": "pending"}, {"id": 4, "title": "Integrate Google Gemini API", "description": "Set up integration with Google's Gemini API to incorporate their advanced language models into the system.", "dependencies": [1], "details": "Establish a connection with Google's Gemini API, ensuring compliance with their API usage guidelines and implementing appropriate authentication methods. Utilize the Model Context Protocol (MCP) for standardized communication between the application and Gemini models. ([en.wikipedia.org](https://en.wikipedia.org/wiki/Model_Context_Protocol?utm_source=openai))", "status": "pending"}, {"id": 5, "title": "Implement AI Key Management with Encryption", "description": "Develop a secure system for managing API keys, including encryption and access control, to protect sensitive credentials.", "dependencies": [1], "details": "Implement a secure key management system that encrypts API keys both at rest and in transit. Store keys in a secure location, such as a dedicated key management service or secure vault, and enforce access control policies based on the least privilege principle. Regularly rotate and revoke keys to maintain security. ([wordraptor.com](https://www.wordraptor.com/blog/google-gemini-api-key?utm_source=openai))", "status": "pending"}, {"id": 6, "title": "Develop Usage Tracking and Rate Limiting System", "description": "Create a system to monitor API usage and enforce rate limits to prevent abuse and ensure fair usage among users.", "dependencies": [1], "details": "Implement monitoring tools to track API usage patterns and detect any anomalies. Set up rate limiting mechanisms to control the number of requests per user or application, preventing overuse and ensuring equitable access to resources. Regularly audit usage logs to identify and address potential issues promptly.", "status": "pending"}]}, {"id": 14, "title": "AI Bot Management System", "description": "Implement AI bot configuration, management, and execution system", "details": "Create AI bot CRUD operations with prompt template management. Implement bot configuration with model selection, temperature, max tokens settings. Create bot execution service with conversation context management. Add bot performance monitoring and analytics. Implement bot sharing and permissions within workspaces. Create bot testing and validation tools.", "testStrategy": "Test bot creation, configuration changes, execution with different providers, and verify conversation context is maintained", "priority": "high", "dependencies": [13], "status": "pending", "subtasks": []}, {"id": 15, "title": "AI Usage Tracking and Cost Management", "description": "Implement comprehensive AI usage logging and cost calculation system", "details": "Create ai_usage_logs table with detailed tracking: tokens used, cost, provider, model, confidence scores. Implement real-time usage calculation and quota enforcement. Create cost estimation for different providers and models. Add usage analytics and reporting features. Implement usage alerts and notifications. Create cost optimization recommendations.", "testStrategy": "Verify usage logging accuracy, test quota enforcement, and validate cost calculations against provider pricing", "priority": "medium", "dependencies": [14], "status": "pending", "subtasks": []}, {"id": 16, "title": "AI Chat Interface Frontend", "description": "Create interactive AI chat interface with bot selection and conversation management", "details": "Create chat interface using Vue 3 with real-time messaging. Implement bot selection dropdown with workspace-available bots. Add conversation history management with local storage backup. Create message formatting with markdown support using marked 9+. Implement typing indicators and message status. Add file upload support for AI analysis. Create conversation export functionality.", "testStrategy": "Test chat functionality, bot switching, message formatting, and verify conversation history persists correctly", "priority": "medium", "dependencies": [12, 14], "status": "pending", "subtasks": []}, {"id": 17, "title": "AI Solution Architecture Implementation", "description": "Implement the unified AI Solution system with Bot and Workflow integration", "details": "Create ai_solutions table with key, name, type (BOT|WORKFLOW), targetId fields. Implement AiSolutionService with unified execution interface. Create solution key-based routing system. Add solution versioning and configuration management. Implement solution templates and cloning functionality. Create solution performance monitoring with confidence tracking.", "testStrategy": "Test solution creation, key-based execution, routing between bots and workflows, and verify performance tracking works", "priority": "high", "dependencies": [15], "status": "pending", "subtasks": [{"id": 1, "title": "Design AI Solution Data Model and Service Layer", "description": "Develop a comprehensive data model and service layer to manage interactions between AI components and external systems, ensuring scalability and maintainability.", "dependencies": [], "details": "This involves creating a structured data model that accurately represents the entities and relationships within the AI system. The service layer will act as an intermediary, handling data processing, API management, and communication between the AI models and external applications. Key considerations include data quality, security, and efficient data flow management. Utilizing a service-oriented architecture (SOA) can facilitate the development of reusable and composable services, enhancing the system's flexibility and scalability. ([en.wikipedia.org](https://en.wikipedia.org/wiki/Service_layer_pattern?utm_source=openai))", "status": "pending"}, {"id": 2, "title": "Implement Unified Execution Interface for Bots and Workflows", "description": "Create a standardized interface to orchestrate and manage the execution of bots and workflows, promoting consistency and ease of integration.", "dependencies": [1], "details": "Develop an execution interface that allows seamless orchestration of bots and workflows, ensuring they can be managed and monitored through a unified platform. This interface should support dynamic pipeline generation and be capable of handling complex dependencies and scheduling. Leveraging tools like Apache Airflow can provide a robust framework for workflow management, offering features such as dynamic pipeline generation and extensibility. ([en.wikipedia.org](https://en.wikipedia.org/wiki/Apache_Airflow?utm_source=openai))", "status": "pending"}, {"id": 3, "title": "Develop Solution Key-Based Routing System", "description": "Establish a routing mechanism that directs requests to the appropriate AI models or services based on predefined keys, optimizing resource utilization and response times.", "dependencies": [1], "details": "Implement a routing system that uses unique keys to determine the appropriate AI model or service to handle incoming requests. This system should be designed for high availability and low latency, ensuring efficient processing of requests. Incorporating load balancing and resource management strategies will enhance the system's performance and reliability. Utilizing a service-oriented architecture (SOA) can facilitate the development of reusable and composable services, enhancing the system's flexibility and scalability. ([en.wikipedia.org](https://en.wikipedia.org/wiki/Service_layer_pattern?utm_source=openai))", "status": "pending"}, {"id": 4, "title": "Establish Solution Versioning and Configuration Management", "description": "Implement a version control and configuration management system to track changes, manage deployments, and ensure consistency across the AI solution components.", "dependencies": [1], "details": "Set up a versioning system to manage updates and changes to the AI models and services, ensuring that all components are synchronized and compatible. This includes defining clear versioning strategies, maintaining detailed change logs, and implementing deployment pipelines that support rollback and forward deployment capabilities. Utilizing tools like Git for version control and configuration management can streamline this process, providing a clear history of changes and facilitating collaboration among development teams. ([towardsdatascience.com](https://towardsdatascience.com/building-a-secure-and-scalable-data-and-ai-platform-074e191b291f/?utm_source=openai))", "status": "pending"}, {"id": 5, "title": "Implement Performance Monitoring with Confidence Tracking", "description": "Set up monitoring mechanisms to track the performance of AI models and services, including confidence levels, to ensure optimal operation and identify areas for improvement.", "dependencies": [1], "details": "Develop a monitoring system that continuously tracks the performance metrics of AI models and services, focusing on confidence levels to assess the reliability of outputs. This system should provide real-time insights into performance, enabling proactive adjustments and improvements. Integrating monitoring tools with alerting capabilities will facilitate prompt responses to performance degradation or anomalies, ensuring the AI solution maintains high standards of accuracy and reliability. ([towardsdatascience.com](https://towardsdatascience.com/building-a-secure-and-scalable-data-and-ai-platform-074e191b291f/?utm_source=openai))", "status": "pending"}]}, {"id": 18, "title": "AI Workflow Editor Backend Architecture", "description": "Implement backend support for dynamic workflow node management and execution", "details": "Create workflow_definitions and workflow_executions tables. Implement dynamic node type registry with JSON schema validation. Create workflow execution engine with step-by-step processing. Add workflow versioning and rollback capabilities. Implement workflow templates and sharing. Create execution monitoring and debugging tools. Support conditional logic and data transformation nodes.", "testStrategy": "Test workflow creation, execution with different node types, versioning, and verify execution monitoring provides accurate data", "priority": "high", "dependencies": [17], "status": "pending", "subtasks": [{"id": 1, "title": "Design Workflow Data Models and Schema", "description": "Develop comprehensive data models and schemas to represent workflows, ensuring they capture all necessary entities, relationships, and attributes for effective workflow management.", "dependencies": [], "details": "This task involves creating data models that accurately represent workflows, including entities like tasks, transitions, and conditions, and defining schemas that facilitate efficient storage and retrieval of workflow data. The design should support scalability and flexibility to accommodate various workflow types and complexities.", "status": "pending"}, {"id": 2, "title": "Implement Dynamic Node Type Registry with Validation", "description": "Create a dynamic registry for node types within workflows, incorporating validation mechanisms to ensure node configurations are correct and consistent.", "dependencies": [1], "details": "This subtask focuses on developing a registry system that allows for the dynamic addition, removal, and modification of node types in workflows. It includes implementing validation processes to check for semantic correctness and adherence to predefined rules, ensuring that nodes function as intended within the workflow context. This approach enhances the flexibility and adaptability of the workflow system.", "status": "pending"}, {"id": 3, "title": "Develop Workflow Execution Engine", "description": "Build an execution engine capable of processing workflows, managing task execution, and handling state transitions efficiently.", "dependencies": [1, 2], "details": "This task involves creating an engine that can interpret and execute workflows defined by the data models and node types. The engine should manage the execution flow, handle task dependencies, and ensure that state transitions occur correctly. It must be optimized for performance and reliability to support complex workflows with multiple concurrent tasks.", "status": "pending"}, {"id": 4, "title": "Establish Workflow Versioning and Rollback System", "description": "Implement a system to manage different versions of workflows and provide rollback capabilities to previous versions when necessary.", "dependencies": [1, 3], "details": "This subtask focuses on developing a version control system for workflows, allowing for the tracking of changes, management of multiple versions, and the ability to revert to previous versions if needed. This system ensures that workflow modifications are controlled and that any issues can be addressed by rolling back to a stable state.", "status": "pending"}, {"id": 5, "title": "Create Execution Monitoring and Debugging Tools", "description": "Develop tools to monitor workflow execution in real-time and provide debugging capabilities to identify and resolve issues promptly.", "dependencies": [3], "details": "This task involves building monitoring tools that provide real-time insights into workflow execution, including task statuses, performance metrics, and error logs. Debugging tools should be integrated to allow for the identification and resolution of issues during workflow execution, facilitating smoother operations and quicker issue resolution.", "status": "pending"}, {"id": 6, "title": "Support Conditional Logic and Data Transformation in Workflows", "description": "Integrate support for conditional logic and data transformation within workflows to enable dynamic decision-making and data manipulation.", "dependencies": [3], "details": "This subtask focuses on incorporating mechanisms that allow workflows to perform conditional operations based on specific criteria and to transform data as needed during execution. This includes implementing decision nodes, conditional branches, and data transformation functions to enhance the flexibility and functionality of workflows.", "status": "pending"}]}, {"id": 19, "title": "Dynamic Node Type Management System", "description": "Implement the super node manifest system for zero-coding workflow editor", "details": "Create node_type_definitions table with complete node specifications. Implement GET /api/admin/ai/workflows/node-types endpoint returning node manifests. Create node type CRUD operations for admins. Add node configuration schema validation using ajv 8+. Implement node categorization and search functionality. Create node type versioning and migration system.", "testStrategy": "Test node type creation, manifest API responses, schema validation, and verify admin can manage node types without code changes", "priority": "high", "dependencies": [18], "status": "pending", "subtasks": []}, {"id": 20, "title": "AI Workflow Editor Frontend - Generic Rendering Engine", "description": "Create the universal workflow editor with dynamic node rendering", "details": "Create DraggableNode.vue component for universal node rendering. Implement NodeLibraryModal.vue with searchable node catalog. Create ImmersiveConfigPanel.vue for dynamic node configuration. Use Vue Flow 1.3+ for workflow visualization. Implement drag-and-drop functionality with proper connection validation. Add workflow execution visualization with real-time data flow display.", "testStrategy": "Test node creation from library, configuration panel functionality, workflow execution visualization, and verify all node types render correctly", "priority": "high", "dependencies": [16, 19], "status": "pending", "subtasks": [{"id": 1, "title": "Develop Universal Node Rendering Component", "description": "Create a flexible component capable of rendering various node types with dynamic content and styles.", "dependencies": [], "details": "This component should support dynamic content rendering, customizable styles, and be easily extendable to accommodate new node types in the future.", "status": "pending"}, {"id": 2, "title": "Implement Node Library Modal with Search Functionality", "description": "Design a modal interface that allows users to search and select nodes from a library to add to the workflow.", "dependencies": [1], "details": "The modal should feature a search bar, categorized node listings, and a preview option to assist users in selecting the appropriate node for their workflow.", "status": "pending"}, {"id": 3, "title": "Implement Dynamic Configuration Panel", "description": "Develop a panel that dynamically adjusts its content and controls based on the selected node, enabling real-time configuration changes.", "dependencies": [1], "details": "The panel should display relevant settings and options for the selected node, allowing users to modify properties and behaviors seamlessly.", "status": "pending"}, {"id": 4, "title": "Integrate Vue Flow with Drag-and-Drop Functionality", "description": "Incorporate Vue Flow to manage the workflow canvas, enabling drag-and-drop interactions for nodes and edges.", "dependencies": [1, 2], "details": "Vue Flow will handle the rendering and management of nodes and edges, supporting drag-and-drop actions to create and connect nodes. Ensure compatibility with Vue 3 and implement necessary configurations for smooth interactions.", "status": "pending"}, {"id": 5, "title": "Implement Connection Validation and Workflow Visualization", "description": "Add features to validate connections between nodes and provide visual feedback on the workflow's structure and status.", "dependencies": [4], "details": "Implement logic to check for valid connections, such as ensuring correct node types and preventing circular dependencies. Visual indicators should be provided to highlight valid and invalid connections, enhancing user experience and workflow integrity.", "status": "pending"}, {"id": 6, "title": "Develop Real-Time Execution Visualization", "description": "Create a visualization that displays the real-time execution status of the workflow, including progress indicators and logs.", "dependencies": [5], "details": "This visualization should update dynamically as the workflow executes, providing users with immediate feedback on the process's progress and any issues encountered during execution.", "status": "pending"}]}, {"id": 21, "title": "Project Management Core System", "description": "Implement comprehensive project management with tasks, milestones, and collaboration", "details": "Create projects, tasks, milestones, and project_members tables. Implement project lifecycle management with status tracking. Create task assignment and dependency management. Add project templates and cloning functionality. Implement project timeline and Gantt chart data. Create project collaboration features with comments and file attachments.", "testStrategy": "Test project creation, task management, member collaboration, and verify project data is properly scoped to workspaces", "priority": "medium", "dependencies": [11], "status": "pending", "subtasks": []}, {"id": 22, "title": "Project Management Frontend Interface", "description": "Create comprehensive project management UI with Kanban boards and timeline views", "details": "Create project dashboard with overview statistics. Implement Kanban board using Vue Draggable Plus 0.4+. Create Gantt chart view using dhtmlx-gantt or similar. Add project creation and editing forms with template selection. Implement task management with drag-and-drop status updates. Create project collaboration interface with real-time updates using Socket.io 4+.", "testStrategy": "Test project UI interactions, Kanban board functionality, timeline views, and verify real-time collaboration works correctly", "priority": "medium", "dependencies": [20, 21], "status": "pending", "subtasks": []}, {"id": 23, "title": "AI Business Integration Service", "description": "Implement unified AI business analysis features with solution-based architecture", "details": "Extend ai-business-integration.service.ts with solution-based routing. Implement projectAnalysis, photoAnalysis, workflowOptimization features. Create intelligent bot selection logic (Feature → System → Tenant). Add confidence parameter support (0-1 backend, 50-100% frontend). Implement unified error handling and retry mechanisms. Create feature-level usage tracking integration.", "testStrategy": "Test all business analysis features, solution routing, confidence parameter handling, and verify usage tracking captures all interactions", "priority": "high", "dependencies": [17], "status": "pending", "subtasks": [{"id": 1, "title": "Design and Implement Solution-Based Routing System", "description": "Develop a routing system that intelligently directs tasks to appropriate AI models based on predefined solutions, ensuring efficient processing and resource utilization.", "dependencies": [], "details": "This subtask involves creating a routing mechanism that evaluates incoming tasks and selects the most suitable AI model to handle them, optimizing performance and accuracy. The system should be adaptable to various business domains and capable of handling complex tasks. Reference: ([arxiv.org](https://arxiv.org/abs/2505.12566?utm_source=openai))", "status": "pending"}, {"id": 2, "title": "Implement Business Analysis Features", "description": "Develop features that analyze business data to provide actionable insights, supporting decision-making processes across different business domains.", "dependencies": [], "details": "This subtask focuses on creating analytical tools that process business data, identify trends, and generate reports. These features should be customizable to cater to the specific needs of various business sectors. Reference: ([relevanceai.com](https://relevanceai.com/agent-templates-tasks/feature-usage-analytics?utm_source=openai))", "status": "pending"}, {"id": 3, "title": "Develop Intelligent Bot Selection Logic", "description": "Create a system that selects the most appropriate AI bot for a given task, considering factors like task complexity, bot capabilities, and confidence levels.", "dependencies": [1, 2], "details": "This subtask involves designing an algorithm that evaluates available AI bots and assigns tasks based on their suitability, ensuring optimal performance and resource efficiency. Reference: ([arxiv.org](https://arxiv.org/abs/2503.07686?utm_source=openai))", "status": "pending"}, {"id": 4, "title": "Establish Confidence Parameter Handling System", "description": "Implement a system that manages confidence parameters to assess the reliability of AI model outputs, guiding decision-making processes.", "dependencies": [3], "details": "This subtask focuses on creating a framework that quantifies and interprets the confidence levels of AI models, enabling informed decisions based on output reliability. Reference: ([arxiv.org](https://arxiv.org/abs/2502.11021?utm_source=openai))", "status": "pending"}, {"id": 5, "title": "Integrate Usage Tracking for Business Features", "description": "Implement a system to monitor and analyze the usage of business features, providing insights into user engagement and feature performance.", "dependencies": [2], "details": "This subtask involves setting up tracking mechanisms that collect data on feature usage, analyze user interactions, and generate reports to inform product development and marketing strategies. Reference: ([fastercapital.com](https://fastercapital.com/articles/10-AI-Tools-for-Performance-Tracking-in-Businesses.html?utm_source=openai))", "status": "pending"}]}, {"id": 24, "title": "AI Analysis Frontend Components", "description": "Create unified AI analysis interface components for business features", "details": "Create AiAnalysisSettings.vue for solution configuration management. Implement AiAnalysisDialog.vue with unified analysis interface. Create AiAnalysisDemo.vue for feature demonstration. Add solution selection with real-time parameter adjustment. Implement result visualization with confidence indicators. Create analysis history and export functionality.", "testStrategy": "Test analysis interface functionality, solution configuration, result visualization, and verify confidence indicators display correctly", "priority": "medium", "dependencies": [22, 23], "status": "pending", "subtasks": []}, {"id": 25, "title": "Privacy-Preserving AI Architecture Foundation", "description": "Establish foundation for local AI model integration with OpenAI-compatible interface", "details": "Extend AiProviderFactory to support local model endpoints. Create LocalAiProvider class with OpenAI-compatible API interface. Implement sensitive data detection service using regex patterns and ML classification. Create data routing logic for automatic local/external provider selection. Add privacy compliance logging and audit trails. Prepare infrastructure for 7B+ parameter model deployment.", "testStrategy": "Test local provider integration, sensitive data detection accuracy, routing logic, and verify privacy compliance logging works", "priority": "high", "dependencies": [13], "status": "pending", "subtasks": [{"id": 1, "title": "Local AI Provider Implementation", "description": "Set up and configure a local AI provider to handle data processing and model inference within the organization's infrastructure, ensuring compliance with privacy regulations.", "dependencies": [], "details": "Implementing a local AI provider involves selecting appropriate hardware and software components, setting up the necessary infrastructure, and ensuring that the system operates within the organization's privacy and security policies. This approach minimizes data exposure by processing sensitive information on-premises, aligning with data protection laws such as GDPR and CCPA. ([erikrasin.io](https://www.erikrasin.io/blog/Local-AI-Models?utm_source=openai))", "status": "pending"}, {"id": 2, "title": "Sensitive Data Detection Service", "description": "Develop or integrate a service capable of identifying and classifying sensitive data within the organization's datasets to prevent unauthorized access and ensure compliance with data protection laws.", "dependencies": [1], "details": "A sensitive data detection service utilizes techniques like data anonymization, pseudonymization, and encryption to safeguard personal information. Implementing such a service helps in adhering to privacy regulations by ensuring that sensitive data is properly managed and protected. ([datasecurityintegrations.com](https://www.datasecurityintegrations.com/guides/essential-tips-protecting-sensitive-data/?utm_source=openai))", "status": "pending"}, {"id": 3, "title": "Data Routing Logic Implementation", "description": "Design and implement data routing logic to direct sensitive data through secure channels, ensuring that it is processed and stored in compliance with privacy regulations.", "dependencies": [2], "details": "Data routing logic involves creating pathways that ensure sensitive data is handled securely throughout its lifecycle. This includes routing data to appropriate storage locations, processing systems, and ensuring that data access is restricted to authorized personnel only. ([cybsoftware.com](https://cybsoftware.com/5-ways-organizations-can-protect-their-ai-models-during-data-ingestion-training-and-inference/?utm_source=openai))", "status": "pending"}, {"id": 4, "title": "Privacy Compliance Logging System", "description": "Establish a logging system to monitor and record data access and processing activities, ensuring transparency and accountability in line with privacy compliance requirements.", "dependencies": [3], "details": "A privacy compliance logging system tracks and records all interactions with sensitive data, providing an audit trail that can be reviewed to ensure compliance with privacy laws. This system helps in identifying unauthorized access and potential data breaches, facilitating timely responses to security incidents. ([cybsoftware.com](https://cybsoftware.com/5-ways-organizations-can-protect-their-ai-models-during-data-ingestion-training-and-inference/?utm_source=openai))", "status": "pending"}, {"id": 5, "title": "Local Model Deployment Infrastructure Preparation", "description": "Prepare the necessary infrastructure for deploying AI models locally, ensuring that the environment supports secure and efficient model operation while maintaining compliance with privacy standards.", "dependencies": [1], "details": "Preparing the local model deployment infrastructure involves setting up hardware and software environments that can securely host AI models. This includes ensuring that the infrastructure supports the computational requirements of the models and adheres to privacy and security standards. ([erikrasin.io](https://www.erikrasin.io/blog/Local-AI-Models?utm_source=openai))", "status": "pending"}]}, {"id": 26, "title": "Intelligent Data Routing System", "description": "Implement smart routing system for sensitive data to local models", "details": "Create data sensitivity classification engine with configurable rules. Implement automatic routing decision logic based on data classification. Add routing override capabilities for admin users. Create routing analytics and monitoring dashboard. Implement fallback mechanisms for local model unavailability. Add GDPR compliance validation and reporting.", "testStrategy": "Test data classification accuracy, routing decisions, fallback mechanisms, and verify GDPR compliance reporting is accurate", "priority": "high", "dependencies": [25], "status": "pending", "subtasks": [{"id": 1, "title": "Design Data Sensitivity Classification Engine", "description": "Develop an engine to automatically classify data based on sensitivity levels, utilizing machine learning and natural language processing techniques to ensure accurate categorization of sensitive information.", "dependencies": [], "details": "The classification engine should be capable of identifying and labeling data as low, medium, high, or restricted sensitivity, aligning with organizational policies and compliance requirements. It should integrate with existing data storage systems and support various data formats, including unstructured data. Regular updates and training of the classification models are essential to adapt to evolving data patterns and regulatory changes.", "status": "pending"}, {"id": 2, "title": "Implement Automatic Routing Decision Logic", "description": "Create a system that automatically routes data to appropriate destinations based on its sensitivity classification, ensuring secure handling and compliance with data protection regulations.", "dependencies": [1], "details": "The routing logic should consider data sensitivity levels, access controls, and compliance requirements to determine the appropriate storage, processing, and access permissions for each data type. It should integrate seamlessly with the data classification engine and support dynamic decision-making based on real-time data attributes and contextual information.", "status": "pending"}, {"id": 3, "title": "Develop Admin Routing Override Capabilities", "description": "Provide administrative controls to override automatic routing decisions when necessary, allowing for manual intervention in exceptional cases while maintaining overall system integrity.", "dependencies": [2], "details": "Admin override capabilities should include user authentication, logging of override actions, and the ability to temporarily or permanently modify routing decisions. These controls should be designed to minimize the risk of unauthorized access or data mishandling and should be subject to regular audits to ensure compliance with organizational policies and regulatory standards.", "status": "pending"}, {"id": 4, "title": "Establish Routing Analytics and Monitoring", "description": "Implement analytics and monitoring tools to track data routing activities, assess system performance, and identify potential issues or inefficiencies in the routing process.", "dependencies": [2], "details": "The analytics system should provide real-time dashboards, historical reporting, and alerting mechanisms to monitor data routing metrics, such as processing times, error rates, and compliance adherence. It should support drill-down capabilities to investigate anomalies and facilitate continuous improvement of the routing logic and overall system performance.", "status": "pending"}, {"id": 5, "title": "Ensure GDPR Compliance Validation and Reporting", "description": "Develop mechanisms to validate and report on GDPR compliance, ensuring that data handling practices meet regulatory requirements and can be audited effectively.", "dependencies": [1, 2, 4], "details": "Compliance validation should include automated checks for data subject rights, data minimization, and lawful processing. Reporting tools should generate comprehensive reports detailing compliance status, data handling practices, and any identified non-compliance issues, facilitating audits and supporting continuous compliance efforts.", "status": "pending"}]}, {"id": 27, "title": "AI Bot Intelligence System - Project Assistant", "description": "Implement intelligent project analysis and management AI bot", "details": "Create Project Intelligence Bot with risk analysis capabilities. Implement project bottleneck identification using task dependency analysis. Add intelligent task assignment based on team member skills and availability. Create project completion time prediction using historical data. Integrate with project management modules for real-time analysis. Add automated project health scoring.", "testStrategy": "Test project analysis accuracy, task assignment logic, completion time predictions, and verify integration with project data works correctly", "priority": "medium", "dependencies": [24], "status": "pending", "subtasks": []}, {"id": 28, "title": "AI Bot Intelligence System - Design Requirements Analyzer", "description": "Implement AI bot for design requirement analysis and recommendation", "details": "Create Design Requirements Ana<PERSON><PERSON>t with NLP capabilities for requirement parsing. Implement design style recommendation engine based on client preferences. Add budget estimation with material cost analysis. Create design suggestion system with image generation integration. Implement client requirement validation and clarification prompts. Add design trend analysis and recommendations.", "testStrategy": "Test requirement parsing accuracy, design recommendations quality, budget estimations, and verify integration with client management works", "priority": "medium", "dependencies": [27], "status": "pending", "subtasks": []}, {"id": 29, "title": "AI Bot Intelligence System - Progress Monitoring", "description": "Implement AI bot for construction progress monitoring and quality assessment", "details": "Create Progress Monitoring Bot with computer vision capabilities for photo analysis. Implement construction quality assessment using image recognition. Add progress percentage calculation based on visual analysis. Create risk identification and early warning system. Implement automated progress report generation. Add quality score tracking and trend analysis.", "testStrategy": "Test photo analysis accuracy, quality assessment reliability, progress calculations, and verify automated reporting generates correctly", "priority": "medium", "dependencies": [28], "status": "pending", "subtasks": [{"id": 1, "title": "Develop Computer Vision Photo Analysis System", "description": "Design and implement a computer vision system capable of analyzing construction site photos to assess quality and progress.", "dependencies": [], "details": "This system should utilize advanced image processing techniques to detect structural defects, measure construction elements, and monitor progress over time. Integration with AI/ML models will enhance accuracy and efficiency in identifying issues and tracking developments.", "status": "pending"}, {"id": 2, "title": "Implement Construction Quality Assessment", "description": "Establish a framework to evaluate construction quality based on data from the computer vision system.", "dependencies": [1], "details": "Utilize the outputs from the photo analysis system to assess compliance with construction standards, identify defects, and ensure structural integrity. This may involve developing algorithms to quantify quality metrics and generate actionable insights for improvement.", "status": "pending"}, {"id": 3, "title": "Calculate Construction Progress Percentage", "description": "Develop a method to compute the percentage of construction progress using data from the photo analysis system.", "dependencies": [1], "details": "Analyze sequential construction site images to estimate the completion status of various project components. This involves comparing current images with project plans to determine the extent of work completed and remaining tasks.", "status": "pending"}, {"id": 4, "title": "Create Risk Identification and Warning System", "description": "Design a system to identify potential risks on construction sites and provide timely warnings.", "dependencies": [1, 2], "details": "Leverage data from the photo analysis and quality assessment systems to detect hazards such as structural weaknesses, safety violations, or environmental concerns. Implement real-time alert mechanisms to notify stakeholders of identified risks, enabling prompt mitigation actions.", "status": "pending"}, {"id": 5, "title": "Automate Report Generation", "description": "Develop an automated reporting system that compiles analysis results into comprehensive construction reports.", "dependencies": [1, 2, 3, 4], "details": "Integrate the outputs from the photo analysis, quality assessment, progress calculation, and risk identification systems to generate detailed reports. These reports should summarize findings, highlight issues, and provide recommendations, facilitating informed decision-making among project stakeholders.", "status": "pending"}]}, {"id": 30, "title": "AI Bot Intelligence System - Smart Scheduling", "description": "Implement intelligent scheduling and resource optimization AI bot", "details": "Create Smart Scheduling Bot with resource conflict detection. Implement optimal work sequence recommendation using constraint programming. Add resource allocation optimization based on availability and skills. Create schedule adjustment suggestions for delays or changes. Implement team workload balancing algorithms. Add schedule risk assessment and mitigation suggestions.", "testStrategy": "Test scheduling optimization accuracy, resource conflict detection, workload balancing, and verify schedule recommendations are practical", "priority": "medium", "dependencies": [29], "status": "pending", "subtasks": [{"id": 1, "title": "Resource Conflict Detection System", "description": "Develop a system to identify and manage resource conflicts in scheduling, utilizing constraint programming techniques to model and solve combinatorial problems.", "dependencies": [], "details": "Implement a system that uses constraint programming to detect and resolve resource conflicts in scheduling tasks. This involves defining constraints over variables representing resources and tasks, and employing methods like backtracking and constraint propagation to find feasible solutions. ([en.wikipedia.org](https://en.wikipedia.org/wiki/Constraint_programming?utm_source=openai))", "status": "pending"}, {"id": 2, "title": "Optimal Work Sequence Recommendation", "description": "Create an algorithm to recommend the optimal sequence of tasks, employing combinatorial optimization methods to determine the most efficient order.", "dependencies": [1], "details": "Utilize combinatorial optimization techniques, such as genetic algorithms, to recommend the optimal sequence of tasks. This approach involves representing scheduling solutions as genomes and applying crossover and mutation operations to evolve solutions that satisfy constraints and optimize objectives. ([en.wikipedia.org](https://en.wikipedia.org/wiki/Genetic_algorithm_scheduling?utm_source=openai))", "status": "pending"}, {"id": 3, "title": "Resource Allocation Optimization", "description": "Design an optimization model to allocate resources efficiently, applying linear programming or integer programming methods to maximize or minimize specific objectives.", "dependencies": [1], "details": "Formulate a resource allocation problem as a linear or integer programming model, where the objective is to optimize resource usage subject to constraints. Utilize algorithms like the simplex method or branch and bound to find optimal solutions. ([en.wikipedia.org](https://en.wikipedia.org/wiki/Linear_programming?utm_source=openai))", "status": "pending"}, {"id": 4, "title": "Schedule Adjustment Suggestions", "description": "Develop a system to suggest adjustments to schedules, using constraint satisfaction problem techniques to propose feasible modifications.", "dependencies": [1, 2], "details": "Implement a system that analyzes existing schedules and suggests adjustments by solving constraint satisfaction problems. This involves identifying conflicts or inefficiencies and proposing changes that satisfy all constraints. ([en.wikipedia.org](https://en.wikipedia.org/wiki/Constraint_satisfaction_problem?utm_source=openai))", "status": "pending"}, {"id": 5, "title": "Workload Balancing Algorithms", "description": "Create algorithms to balance workloads across resources, employing combinatorial optimization methods to achieve equitable distribution.", "dependencies": [1, 3], "details": "Design algorithms that apply combinatorial optimization techniques to balance workloads across resources. This may involve solving problems like the bin packing problem or the assignment problem to ensure efficient and fair distribution of tasks. ([en.wikipedia.org](https://en.wikipedia.org/wiki/Combinatorial_optimization?utm_source=openai))", "status": "pending"}]}, {"id": 31, "title": "LINE Bot Integration Foundation", "description": "Establish LINE Bot integration with webhook handling and basic messaging", "details": "Setup LINE Messaging API integration using @line/bot-sdk 8+. Create webhook endpoint for LINE message handling. Implement message routing and user identification system. Create basic message response handlers with rich menu support. Add multimedia file handling and storage. Implement user session management for LINE conversations.", "testStrategy": "Test LINE webhook integration, message routing, rich menu functionality, and verify multimedia file handling works correctly", "priority": "medium", "dependencies": [30], "status": "pending", "subtasks": []}, {"id": 32, "title": "LINE Bot - Customer Service Bot", "description": "Implement customer service LINE bot with project status and appointment features", "details": "Create customer service bot with project status inquiry functionality. Implement appointment scheduling with designer availability checking. Add automated progress notifications and milestone alerts. Create quick problem reporting with photo upload support. Implement FAQ system with intelligent response matching. Add customer satisfaction survey integration.", "testStrategy": "Test project status queries, appointment scheduling, notification delivery, and verify problem reporting workflow functions correctly", "priority": "medium", "dependencies": [31], "status": "pending", "subtasks": []}, {"id": 33, "title": "LINE Bot - On-site Construction Bot", "description": "Implement construction site LINE bot for worker progress reporting", "details": "Create construction bot with photo upload and progress reporting. Implement digital checklist completion with validation. Add real-time problem reporting with priority classification. Create material request system with approval workflow. Implement worker check-in/check-out functionality. Add safety compliance checking and reporting.", "testStrategy": "Test photo upload functionality, checklist validation, problem reporting workflow, and verify material request system works", "priority": "medium", "dependencies": [32], "status": "pending", "subtasks": []}, {"id": 34, "title": "<PERSON><PERSON><PERSON> - Designer Mobile Assistant", "description": "Implement designer assistant <PERSON><PERSON><PERSON> bot for task management and client communication", "details": "Create designer assistant with task reminder and status update features. Implement quick task status changes with confirmation. Add client communication logging and history tracking. Create on-site measurement data upload with GPS tagging. Implement design file sharing and client approval workflow. Add calendar integration with appointment management.", "testStrategy": "Test task management features, client communication logging, measurement data upload, and verify calendar integration works", "priority": "medium", "dependencies": [33], "status": "pending", "subtasks": []}, {"id": 35, "title": "LINE Bot - Supplier Collaboration Bot", "description": "Implement supplier collaboration LINE bot for procurement and logistics", "details": "Create supplier bot with automated quotation request system. Implement delivery status tracking with real-time updates. Add digital invoice and receipt processing with OCR. Create supplier quality rating and feedback system. Implement material availability checking and reservation. Add procurement workflow automation with approval chains.", "testStrategy": "Test quotation system, delivery tracking, invoice processing, and verify procurement workflow automation functions correctly", "priority": "medium", "dependencies": [34], "status": "pending", "subtasks": []}, {"id": 36, "title": "Cross-Platform Workflow Automation Engine", "description": "Implement intelligent workflow automation connecting AI bots and LINE bots", "details": "Create workflow automation engine with event-driven architecture. Implement intelligent work order generation from LINE bot inputs. Add AI-driven progress tracking automation with photo analysis. Create smart design requirement collection workflow. Implement cross-platform notification and alert system. Add workflow performance monitoring and optimization suggestions.", "testStrategy": "Test workflow automation triggers, cross-platform data synchronization, notification delivery, and verify workflow optimization suggestions are relevant", "priority": "high", "dependencies": [35], "status": "pending", "subtasks": [{"id": 1, "title": "Design Event-Driven Workflow Automation Engine", "description": "Develop an event-driven architecture to automate workflows, enabling real-time responses to specific events or conditions.", "dependencies": [], "details": "Implement an event-driven architecture (EDA) to automate workflows, allowing systems to react in real-time to events. This involves defining event producers, consumers, and channels to facilitate communication. EDA enhances system responsiveness and scalability by decoupling components and enabling asynchronous processing. ([en.wikipedia.org](https://en.wikipedia.org/wiki/Event-driven_architecture?utm_source=openai))", "status": "pending"}, {"id": 2, "title": "Implement Intelligent Work Order Generation", "description": "Create a system that generates work orders intelligently based on predefined criteria and real-time data inputs.", "dependencies": [1], "details": "Develop a system that intelligently generates work orders by analyzing predefined criteria and real-time data inputs. This system should integrate with existing workflows to ensure seamless task assignment and execution. Utilizing AI and machine learning can enhance decision-making processes and improve efficiency. ([markovml.com](https://www.markovml.com/glossary/event-driven-workflow-automation?utm_source=openai))", "status": "pending"}, {"id": 3, "title": "Develop AI-Driven Progress Tracking Automation", "description": "Create an AI-powered system to monitor and track the progress of tasks and workflows automatically.", "dependencies": [2], "details": "Implement an AI-driven system that monitors and tracks the progress of tasks and workflows in real-time. This system should analyze data to predict potential delays, identify bottlenecks, and provide insights for optimization. Integration with existing workflow management tools is essential for comprehensive monitoring. ([infoq.com](https://www.infoq.com/articles/events-workflow-automation/?utm_source=openai))", "status": "pending"}, {"id": 4, "title": "Establish Cross-Platform Notification System", "description": "Set up a notification system that delivers alerts and updates across multiple platforms to stakeholders.", "dependencies": [3], "details": "Develop a cross-platform notification system that delivers alerts and updates to stakeholders across various platforms, including email, SMS, and mobile applications. This system should be integrated with the workflow automation engine to provide timely and relevant information. ([markovml.com](https://www.markovml.com/glossary/event-driven-workflow-automation?utm_source=openai))", "status": "pending"}, {"id": 5, "title": "Implement Workflow Performance Monitoring", "description": "Set up monitoring tools to assess the performance and efficiency of workflows continuously.", "dependencies": [4], "details": "Implement monitoring tools to continuously assess the performance and efficiency of workflows. These tools should provide real-time analytics, identify performance issues, and offer insights for improvement. Integration with existing workflow management platforms is crucial for effective monitoring. ([en.wikipedia.org](https://en.wikipedia.org/wiki/Workflow_engine?utm_source=openai))", "status": "pending"}, {"id": 6, "title": "Develop Optimization Suggestion System", "description": "Create a system that analyzes workflow data to suggest optimizations and improvements automatically.", "dependencies": [5], "details": "Develop a system that analyzes workflow data to automatically suggest optimizations and improvements. This system should utilize AI and machine learning algorithms to identify inefficiencies and recommend actionable changes. Integration with the workflow automation engine ensures that suggestions can be implemented seamlessly. ([infoq.com](https://www.infoq.com/articles/events-workflow-automation/?utm_source=openai))", "status": "pending"}]}, {"id": 37, "title": "Subscription and Billing System Backend", "description": "Implement comprehensive subscription management and billing system", "details": "Create subscription plans with feature limitations and usage quotas. Implement Stripe integration using stripe 14+ for payment processing. Create subscription lifecycle management with upgrades/downgrades. Add usage-based billing calculation and invoice generation. Implement payment webhook handling and subscription status updates. Create billing analytics and revenue reporting.", "testStrategy": "Test subscription creation, Stripe payment processing, usage billing calculations, and verify webhook handling updates subscription status correctly", "priority": "medium", "dependencies": [15], "status": "pending", "subtasks": [{"id": 1, "title": "Subscription Plan and Quota Management", "description": "Design and implement subscription plans with defined quotas, including free trials, billing cycles, and usage limits, utilizing Stripe's subscription management features.", "dependencies": [], "details": "Utilize Stripe's API to create and manage subscription plans, set up billing cycles, and define usage limits. Implement logic to handle plan upgrades, downgrades, and cancellations, ensuring accurate tracking of customer subscriptions and their associated quotas. ([docs.stripe.com](https://docs.stripe.com/subscriptions?utm_source=openai))", "status": "pending"}, {"id": 2, "title": "Stripe Payment Integration", "description": "Integrate Stripe's payment gateway to securely process customer payments, handle various payment methods, and manage payment failures and retries.", "dependencies": [1], "details": "Set up Stripe's payment processing system to handle one-time and recurring payments, ensuring compliance with PCI DSS standards. Implement webhook listeners to monitor payment events, such as successful payments, failures, and disputes, and develop appropriate responses to these events. ([stripe.com](https://stripe.com/billing?utm_source=openai))", "status": "pending"}, {"id": 3, "title": "Subscription Lifecycle Management", "description": "Manage the entire lifecycle of subscriptions, including creation, updates, renewals, and cancellations, ensuring seamless customer experiences.", "dependencies": [1, 2], "details": "Utilize Stripe's subscription APIs to create, update, and cancel subscriptions based on customer actions. Implement logic to handle proration during plan changes, manage trial periods, and ensure accurate billing cycles. Set up automated notifications to inform customers of subscription changes and upcoming renewals. ([docs.stripe.com](https://docs.stripe.com/subscriptions?utm_source=openai))", "status": "pending"}, {"id": 4, "title": "Usage-Based Billing Calculation", "description": "Implement usage-based billing by tracking customer usage metrics and integrating them into billing calculations, leveraging Stripe's usage-based billing features.", "dependencies": [1, 2], "details": "Set up meters in Stripe to track specific usage metrics, such as API calls or data storage. Record usage events in real-time and associate them with the appropriate subscription items. Configure pricing models that charge customers based on their usage, including flat fees, tiered pricing, or per-unit charges. ([docs.stripe.com](https://docs.stripe.com/billing/subscriptions/usage-based/implementation-guide?utm_source=openai))", "status": "pending"}, {"id": 5, "title": "Webhook Handling and Billing Analytics", "description": "Set up webhook listeners to handle Stripe events and implement billing analytics to monitor revenue, churn, and other key metrics.", "dependencies": [2, 3, 4], "details": "Configure webhooks to receive notifications from <PERSON><PERSON> about events such as invoice creation, payment success or failure, subscription updates, and usage records. Develop handlers to process these events and update the system accordingly. Implement analytics tools to track billing metrics, analyze revenue trends, monitor churn rates, and generate reports for financial analysis. ([stripe.com](https://stripe.com/billing?utm_source=openai))", "status": "pending"}]}, {"id": 38, "title": "Subscription Management Frontend", "description": "Create subscription management interface with plan selection and billing", "details": "Create subscription plan comparison and selection interface. Implement Stripe Elements integration for secure payment processing. Add subscription management dashboard with usage tracking. Create billing history and invoice download functionality. Implement plan upgrade/downgrade workflow with prorated billing. Add payment method management and automatic renewal settings.", "testStrategy": "Test plan selection, payment processing, subscription management, and verify billing calculations display correctly", "priority": "medium", "dependencies": [36, 37], "status": "pending", "subtasks": [{"id": 1, "title": "Design and Implement Plan Comparison and Selection Interface", "description": "Create a user-friendly interface that allows customers to compare and select subscription plans, including features, pricing, and billing cycles.", "dependencies": [], "details": "Utilize Stripe's product and pricing APIs to define subscription plans. Implement a comparison table highlighting key features and pricing differences. Ensure the interface is responsive and accessible across devices.", "status": "pending"}, {"id": 2, "title": "Integrate Stripe Elements for Secure Payment Processing", "description": "Embed Stripe Elements into the checkout process to securely collect payment information and handle transactions.", "dependencies": [1], "details": "Use Stripe's Payment Element to support multiple payment methods and ensure PCI-DSS compliance. Customize the UI to match the site's branding and provide a seamless checkout experience. Implement error handling and input validation to enhance user experience. ([stripe.com](https://stripe.com/payments/elements?utm_source=openai))", "status": "pending"}, {"id": 3, "title": "Develop Subscription Dashboard with Usage Tracking", "description": "Create a dashboard where customers can view their subscription details, monitor usage, and manage their accounts.", "dependencies": [2], "details": "Integrate Stripe's Billing and Customer Portal features to allow users to view invoices, update payment methods, and manage subscriptions. Implement usage tracking to display consumption metrics relevant to the subscription plan. Ensure the dashboard is intuitive and provides real-time data. ([stripe.com](https://stripe.com/resources/more/subscription-management-features-explained-and-how-to-choose-a-software-solution?utm_source=openai))", "status": "pending"}, {"id": 4, "title": "Implement Billing History and Payment Method Management", "description": "Provide customers with access to their billing history and the ability to manage payment methods within their account settings.", "dependencies": [3], "details": "Utilize Stripe's Invoicing API to retrieve and display past invoices. Allow users to add, update, or remove payment methods securely. Ensure compliance with PCI-DSS standards and provide clear instructions for managing payment information. ([stripe.com](https://stripe.com/en-si/billing/features?utm_source=openai))", "status": "pending"}]}, {"id": 39, "title": "OAuth Integration and Third-Party Authentication", "description": "Implement Google OAuth and LINE Login integration", "details": "Implement Google OAuth 2.0 integration using @nestjs/passport and passport-google-oauth20. Add LINE Login integration with proper scope handling. Create account linking functionality for existing users. Implement social login user profile synchronization. Add OAuth token refresh and revocation handling. Create unified user profile management across authentication methods.", "testStrategy": "Test Google and LINE login flows, account linking, profile synchronization, and verify token refresh mechanisms work correctly", "priority": "medium", "dependencies": [9], "status": "pending", "subtasks": []}, {"id": 40, "title": "System Monitoring, Analytics, and Deployment", "description": "Implement comprehensive monitoring, analytics, and production deployment setup", "details": "Setup application monitoring using Winston 3+ for logging and health checks. Implement user analytics and usage tracking with privacy compliance. Create system performance monitoring with alerts. Setup Docker production deployment with docker-compose. Implement automated backup system for PostgreSQL. Create deployment pipeline with CI/CD using GitHub Actions. Add error tracking and performance monitoring integration.", "testStrategy": "Test monitoring alerts, analytics data collection, deployment process, backup restoration, and verify CI/CD pipeline deploys successfully", "priority": "high", "dependencies": [38, 39], "status": "pending", "subtasks": []}, {"id": 41, "title": "Comprehensive MFA Integration with User Architecture and LINE OAuth Finalization", "description": "Integrate existing Multi-Factor Authentication (MFA) functionality with the new SystemUser and TenantUser architecture, ensuring compatibility across all authentication methods including traditional login and OAuth. This task also includes finalizing LINE OAuth integration by implementing authentication URL generation, user unbinding, and updating login time tracking.", "details": "1. **MFA Core Integration with User Models (SystemUser/TenantUser):** Review and adapt the current MFA system (e.g., TOTP) to fully support `SystemUser` and `TenantUser` models. Securely store MFA configurations (secrets, recovery codes, enabled methods) associated with the correct user type. Implement MFA enrollment, verification, and recovery processes for both user types.\n2. **MFA Enforcement Across Authentication Methods:** Ensure MFA is triggered post-primary authentication if enabled for the user. For traditional login, this is after password validation. For OAuth (Google, LINE), after successful authentication with the OAuth provider and user linkage in our system, our system's MFA should be prompted if active for that user.\n3. **LINE OAuth Finalization (building on Task 39):** \n    a. **Authentication URL Generation:** Develop an API endpoint (e.g., `GET /api/auth/line/url`) to dynamically generate the LINE OAuth authorization URL. This URL must include `client_id`, `redirect_uri`, `scope` (e.g., `profile openid email`), `response_type=code`, and a secure, unique `state` parameter for CSRF protection.\n    b. **User Unbinding for LINE:** Implement an API endpoint (e.g., `POST /api/users/me/oauth/line/unbind`) for authenticated users to disconnect their LINE account. This should remove the LINE ID association from their user profile in the database and, if possible, revoke relevant LINE tokens via their API.\n    c. **Callback Handler Enhancement:** Ensure the LINE OAuth callback handler (`GET /api/auth/line/callback`) robustly exchanges the authorization code for tokens, fetches user profile data, and correctly links to or logs in the `SystemUser` or `TenantUser`.\n4. **Login Timestamp Update:** Implement or update logic to record the `last_login_at` timestamp for both `SystemUser` and `TenantUser` entities upon any successful authentication event (traditional, OAuth, MFA completion).\n5. **API Endpoint Design Considerations:** Define clear API endpoints for MFA management (setup, verification, enable/disable, recovery code access) and the new LINE OAuth functionalities.\n6. **Security & UX:** Prioritize secure storage of MFA secrets, protection against MFA bypass/brute-force, and provide a clear user experience for MFA setup, usage, and recovery. Ensure proper session management post-MFA.", "testStrategy": "1. **MFA Setup & Management:** Verify `SystemUser` and `TenantUser` can enroll in MFA (e.g., TOTP setup), enable/disable it, and generate/use recovery codes.\n2. **Traditional Login with MFA:** Test successful login for both `SystemUser` and `TenantUser` with correct credentials and MFA code. Test login failure with incorrect/missing MFA code.\n3. **OAuth Login with System MFA:** For users with MFA enabled in our system:\n    a. **LINE OAuth:** Authenticate via LINE, then verify our system prompts for and validates MFA. Test successful and failed MFA attempts.\n    b. **Google OAuth:** (If applicable) Authenticate via Google, then verify our system prompts for and validates MFA.\n    c. For users without MFA enabled, verify direct access after successful OAuth.\n4. **LINE OAuth Specifics:**\n    a. **URL Generation:** Call the API to get the LINE auth URL. Validate its structure and parameters.\n    b. **Full Flow:** Test the end-to-end LINE login: initiation, redirection, LINE authentication, callback processing, user creation/linking, and session establishment.\n    c. **User Unbinding:** Verify an authenticated user can unbind their LINE account. Confirm data removal and inability to log in via that LINE account subsequently, while other login methods remain functional.\n5. **Login Timestamp Verification:** After each successful login type (traditional, OAuth, with/without MFA), confirm the `last_login_at` field is accurately updated for the respective `SystemUser` or `TenantUser`.\n6. **Multi-Tenant Isolation:** Confirm MFA settings for a `TenantUser` in one tenant do not impact users in other tenants or `SystemUser` configurations.\n7. **Security Penetration Tests:** Attempt common MFA bypass techniques. Verify CSRF protection on LINE OAuth `state` parameter. Confirm MFA secrets are not stored in plaintext.", "status": "pending", "dependencies": [4, 5, 39], "priority": "medium", "subtasks": [{"id": 1, "title": "Integrate MFA Core with SystemUser/TenantUser Models", "description": "Adapt the existing MFA system (e.g., TOTP) to fully support `SystemUser` and `TenantUser` models. This includes securely storing MFA configurations (secrets, recovery codes, enabled methods) associated with the correct user type and implementing MFA enrollment, verification, and recovery processes for both user types.", "dependencies": [], "details": "1. Modify database schemas or user models (`SystemUser`, `TenantUser`) to include fields for MFA secrets (encrypted), recovery codes (hashed or encrypted), enabled MFA methods (e.g., 'TOTP', 'SMS'), and MFA status (e.g., 'enrolled', 'not_enrolled').\n2. Update MFA service logic to handle both `SystemUser` and `TenantUser` types for: generating/storing TOTP secrets, generating/storing recovery codes, verifying TOTP codes, verifying recovery codes, and enabling/disabling MFA.\n3. Ensure MFA secrets are stored securely (e.g., encrypted at rest using AES-256 or similar).\n4. Implement API endpoints for MFA setup (e.g., `POST /api/users/me/mfa/setup` to get secret/QR code, `POST /api/users/me/mfa/verify` to confirm setup with a valid TOTP code), and managing recovery codes (e.g., `GET /api/users/me/mfa/recovery-codes` to view, `POST /api/users/me/mfa/recovery-codes/regenerate` to generate new ones).", "status": "pending", "testStrategy": "Unit tests for MFA logic (secret generation, code verification, storage) for both `SystemUser` and `TenantUser`. Integration tests for MFA enrollment, verification, and recovery code generation/usage flows. Manual testing of MFA setup UX and recovery processes."}, {"id": 2, "title": "Implement LINE OAuth Auth URL Generation & Enhance Callback Handler", "description": "Develop an API endpoint to dynamically generate the LINE OAuth authorization URL and enhance the existing LINE OAuth callback handler to robustly manage token exchange, user profile fetching, and linking/login for `SystemUser` or `TenantUser`.", "dependencies": [], "details": "1. Create `GET /api/auth/line/url` endpoint: Dynamically construct the LINE authorization URL including `client_id`, `redirect_uri`, `scope` ('profile openid email'), `response_type=code`, and a secure, unique, server-side stored `state` parameter for CSRF protection. Return the URL to the client.\n2. Enhance `GET /api/auth/line/callback` handler: Verify the received `state` parameter. Exchange authorization `code` for tokens (access, ID, refresh) with LINE. Fetch user profile from LINE. Implement logic to find existing `SystemUser`/`TenantUser` by LINE ID or email, or create a new user. Link LINE ID to the user profile. Log in the user and issue session tokens.", "status": "pending", "testStrategy": "Unit tests for URL generation logic and `state` parameter handling. Integration tests for the full LINE OAuth flow: redirect to LINE, login, callback handling, token exchange, user creation/linking. Test CSRF protection by manipulating the `state` parameter."}, {"id": 3, "title": "Implement LINE User Unbinding and Unified Login Timestamp Update", "description": "Implement an API endpoint for authenticated users to disconnect their LINE account from their profile and update/implement logic to record the `last_login_at` timestamp for both `SystemUser` and `TenantUser` entities upon any successful authentication event.", "dependencies": [], "details": "1. **LINE User Unbinding:** Create `POST /api/users/me/oauth/line/unbind` endpoint. Requires user authentication. Remove LINE ID and associated LINE tokens from the user's profile in the database. If possible, call LINE's API to revoke relevant tokens.\n2. **Login Timestamp Update:** Identify all successful authentication points (traditional login, OAuth login completion, MFA completion if it's the final step). Update the `last_login_at` field (UTC) for the `SystemUser` or `TenantUser` in the database at these points. Ensure this logic is centralized or consistently applied.", "status": "pending", "testStrategy": "API tests for LINE unbinding endpoint (success, failure, unauthorized). Verify database changes after unbinding. Test `last_login_at` update after traditional login, LINE login, and any other OAuth provider login, both with and without MFA."}, {"id": 4, "title": "Enforce MFA Across All Authentication Methods (Traditional & OAuth)", "description": "Ensure MFA is triggered post-primary authentication if enabled for the user. This applies to traditional login (after password validation) and OAuth logins (e.g., Google, LINE) after successful authentication with the provider and user linkage in our system.", "dependencies": [], "details": "1. **Traditional Login:** After successful password validation, check if MFA is enabled for the user. If yes, do not issue a full session token; instead, prompt for MFA verification. Implement an endpoint like `POST /api/auth/mfa/verify-login`.\n2. **OAuth Login (LINE, Google, etc.):** After successful OAuth provider authentication and user identification/linkage in our system (via callback), check if MFA is enabled for the user. If yes, prompt for MFA verification before granting full access or issuing a full session token.\n3. Manage intermediate authentication state securely (e.g., user passed primary auth, pending MFA). This might involve a temporary token or session state indicating partial authentication.", "status": "pending", "testStrategy": "Integration tests for login flows: traditional login with MFA enabled/disabled; LINE OAuth login with MFA enabled/disabled; Google OAuth login (if applicable) with MFA enabled/disabled. Test scenarios where MFA is required but user attempts to bypass it or provides incorrect MFA codes."}, {"id": 5, "title": "Finalize APIs, Implement Security Hardening & Conduct Comprehensive Testing", "description": "Define and document all new API endpoints for MFA management and LINE OAuth. Implement security best practices, including protection against MFA bypass/brute-force, and ensure a clear UX. Conduct comprehensive security and functional testing of all integrated features.", "dependencies": [], "details": "1. **API Documentation:** Review and finalize Swagger/OpenAPI documentation for all new/modified endpoints related to MFA (setup, verify, disable, recovery codes) and LINE OAuth (auth URL, callback, unbind).\n2. **Security Hardening:** Implement rate limiting on MFA verification attempts and recovery code usage. Ensure secure session management post-MFA. Log security-sensitive events (MFA actions, OAuth linking/unlinking, failed attempts). Review for MFA bypass vulnerabilities.\n3. **User Experience (UX):** Ensure clear instructions, feedback, and error messages for MFA setup, login, recovery, and LINE account linking/unlinking.\n4. **Comprehensive Testing:** Perform end-to-end testing of all authentication flows with MFA. Conduct security testing (e.g., review against OWASP Top 10, MFA-specific vulnerabilities). Test edge cases and error handling for all new functionalities.", "status": "pending", "testStrategy": "End-to-end manual testing of all user flows including MFA enrollment, login with MFA (all methods), LINE OAuth connect/disconnect. Automated security scans (e.g., SAST/DAST if available). Review of security logs under various test conditions. Usability testing for MFA and LINE OAuth processes."}]}]}