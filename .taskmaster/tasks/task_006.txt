# Task ID: 6
# Title: RBAC/ABAC Permission System with CASL
# Status: pending
# Dependencies: 5
# Priority: high
# Description: Implement a flexible role-based and attribute-based access control system using CASL. This update focuses on detailing the implementation requirements, including addressing the `TODO("實現完整的權限邏輯")` in `auth.service.ts` by fully implementing the `getUnifiedUserPermissions` method, extending CASL rules for different user types, and ensuring robust permission checks in controllers.
# Details:
Integrate `@casl/ability 6+` and `@casl/prisma 1+` for permission management.
Key implementation aspects include:
1.  **Defining abilities** for different user roles: `SuperAdmin`, `SystemAdmin`, `TenantAdmin`, `TenantUser`, with specific attention to **extending CASL rules to support distinct permission requirements for `SystemUser` and `TenantUser`**.
2.  **Fully implementing the `getUnifiedUserPermissions` method** in `auth.service.ts` to retrieve unified user permissions, addressing the `TODO("實現完整的權限邏輯")`.
3.  Creating `PermissionsGuard` to check abilities before controller actions and implementing permission decorators.
4.  Implementing services for role assignment and management.
5.  **Providing detailed implementation for both role-based and attribute-based permission checks**, including resource-based permissions using `@casl/prisma`.
6.  **Updating permission-related endpoints in `auth.controller.ts`** to use the new permission logic and guards.

# Test Strategy:
Test all permission combinations, verify role inheritance works correctly, and ensure unauthorized access is properly blocked. **Specifically, test the `getUnifiedUserPermissions` method, distinct permissions for `SystemUser` vs. `TenantUser`, the behavior of role-based and attribute-based checks, and the security of updated `auth.controller.ts` endpoints.**

# Subtasks:
## 1. CASL Ability Definitions and Role Hierarchy [pending]
### Dependencies: None
### Description: Define user abilities and establish a role hierarchy using CASL to manage permissions effectively, including specific rules for SystemUser and TenantUser.
### Details:
Utilize CASL's `AbilityBuilder` to define user abilities for roles like `SuperAdmin`, `SystemAdmin`, `TenantAdmin`, `TenantUser`. **Crucially, extend CASL rules to support the distinct permission requirements for `SystemUser` and `TenantUser` roles.** Establish a role hierarchy. This work will contribute to resolving the `TODO("實現完整的權限邏輯")` in `auth.service.ts` concerning permission definitions. ([npmjs.com](https://www.npmjs.com/package/%40casl/ability?utm_source=openai))

## 2. Permission Guard Implementation and Decorators [pending]
### Dependencies: 6.1
### Description: Implement permission guards and decorators to enforce access control, with detailed logic for role-based checks.
### Details:
Develop `PermissionsGuard` to check CASL abilities before controller actions. Create permission decorators for easy controller protection. **Provide detailed implementation for robust role-based permission checks within the guard and decorators.** This involves creating middleware to validate tenant context if applicable and using decorators to specify required permissions for routes. ([medium.com](https://medium.com/%40taofiqaiyelabegan/building-a-multi-tenant-e-commerce-api-fine-grained-authorization-with-nest-js-and-permit-io-ddd2ca77d19d?utm_source=openai))

## 3. Role Assignment and Management Services [pending]
### Dependencies: 6.1
### Description: Create services to assign and manage user roles within the application.
### Details:
Develop services to assign and manage user roles, ensuring that users have appropriate permissions based on their roles. This includes creating functions to assign roles to users and manage role hierarchies. ([codeproject.com](https://www.codeproject.com/Articles/151527/Access-Control-in-Multi-Tenant-Applications-with-V?utm_source=openai))

## 4. Resource-Based Permission Checking [pending]
### Dependencies: 6.1
### Description: Implement resource-based permission checks, including detailed attribute-based logic, to control access to specific resources.
### Details:
Implement resource-based permission checks to control access to specific resources. **Provide detailed implementation for attribute-based permission checks, leveraging `@casl/prisma` where appropriate.** This involves defining permissions for individual resources and checking user abilities against resource attributes before granting access. ([fullstack.com](https://www.fullstack.com/labs/resources/blog/role-based-user-authorization-in-javascript-with-casl?utm_source=openai))

## 5. Permission Testing and Validation Framework [pending]
### Dependencies: 6.1, 6.2, 6.4, 6.6, 6.7
### Description: Develop a framework to test and validate permission configurations, covering new methods and specific user type scenarios.
### Details:
Develop a framework to test and validate permission configurations. **Include test cases for the `getUnifiedUserPermissions` method, distinct permissions for `SystemUser` and `TenantUser`, comprehensive role-based and attribute-based checks, and the updated `auth.controller.ts` endpoints.** Ensure that access control rules are correctly enforced and unauthorized access is blocked. ([dev.to](https://dev.to/samueloseh/mastering-role-based-access-control-with-casl-part-one-5d1a?utm_source=openai))

## 6. Implement `getUnifiedUserPermissions` in `auth.service.ts` [pending]
### Dependencies: 6.1
### Description: Fully implement the `getUnifiedUserPermissions` method within `auth.service.ts` to retrieve unified user permissions, addressing a key part of the `TODO("實現完整的權限邏輯")`.
### Details:
This method should aggregate permissions based on user roles (including `SystemUser`, `TenantUser`), direct assignments, and other relevant attributes to construct a comprehensive CASL `Ability` instance for the user. Ensure it correctly reflects the defined CASL rules and hierarchy.

## 7. Update `auth.controller.ts` Endpoints with New Permission Logic [pending]
### Dependencies: 6.2, 6.6
### Description: Integrate the new CASL permission system into `auth.controller.ts` by applying guards and decorators to relevant endpoints.
### Details:
Refactor existing permission-related endpoints in `auth.controller.ts`. Apply the `PermissionsGuard` and appropriate permission decorators to protect these endpoints. Ensure that the controller actions correctly leverage the CASL abilities provided by the updated `auth.service.ts`.

