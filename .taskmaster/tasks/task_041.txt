# Task ID: 41
# Title: Comprehensive MFA Integration with User Architecture and LINE OAuth Finalization
# Status: pending
# Dependencies: 4, 5, 39
# Priority: medium
# Description: Integrate existing Multi-Factor Authentication (MFA) functionality with the new SystemUser and TenantUser architecture, ensuring compatibility across all authentication methods including traditional login and OAuth. This task also includes finalizing LINE OAuth integration by implementing authentication URL generation, user unbinding, and updating login time tracking.
# Details:
1. **MFA Core Integration with User Models (SystemUser/TenantUser):** Review and adapt the current MFA system (e.g., TOTP) to fully support `SystemUser` and `TenantUser` models. Securely store MFA configurations (secrets, recovery codes, enabled methods) associated with the correct user type. Implement MFA enrollment, verification, and recovery processes for both user types.
2. **MFA Enforcement Across Authentication Methods:** Ensure MFA is triggered post-primary authentication if enabled for the user. For traditional login, this is after password validation. For OAuth (Google, LINE), after successful authentication with the OAuth provider and user linkage in our system, our system's MFA should be prompted if active for that user.
3. **LINE OAuth Finalization (building on Task 39):** 
    a. **Authentication URL Generation:** Develop an API endpoint (e.g., `GET /api/auth/line/url`) to dynamically generate the LINE OAuth authorization URL. This URL must include `client_id`, `redirect_uri`, `scope` (e.g., `profile openid email`), `response_type=code`, and a secure, unique `state` parameter for CSRF protection.
    b. **User Unbinding for LINE:** Implement an API endpoint (e.g., `POST /api/users/me/oauth/line/unbind`) for authenticated users to disconnect their LINE account. This should remove the LINE ID association from their user profile in the database and, if possible, revoke relevant LINE tokens via their API.
    c. **Callback Handler Enhancement:** Ensure the LINE OAuth callback handler (`GET /api/auth/line/callback`) robustly exchanges the authorization code for tokens, fetches user profile data, and correctly links to or logs in the `SystemUser` or `TenantUser`.
4. **Login Timestamp Update:** Implement or update logic to record the `last_login_at` timestamp for both `SystemUser` and `TenantUser` entities upon any successful authentication event (traditional, OAuth, MFA completion).
5. **API Endpoint Design Considerations:** Define clear API endpoints for MFA management (setup, verification, enable/disable, recovery code access) and the new LINE OAuth functionalities.
6. **Security & UX:** Prioritize secure storage of MFA secrets, protection against MFA bypass/brute-force, and provide a clear user experience for MFA setup, usage, and recovery. Ensure proper session management post-MFA.

# Test Strategy:
1. **MFA Setup & Management:** Verify `SystemUser` and `TenantUser` can enroll in MFA (e.g., TOTP setup), enable/disable it, and generate/use recovery codes.
2. **Traditional Login with MFA:** Test successful login for both `SystemUser` and `TenantUser` with correct credentials and MFA code. Test login failure with incorrect/missing MFA code.
3. **OAuth Login with System MFA:** For users with MFA enabled in our system:
    a. **LINE OAuth:** Authenticate via LINE, then verify our system prompts for and validates MFA. Test successful and failed MFA attempts.
    b. **Google OAuth:** (If applicable) Authenticate via Google, then verify our system prompts for and validates MFA.
    c. For users without MFA enabled, verify direct access after successful OAuth.
4. **LINE OAuth Specifics:**
    a. **URL Generation:** Call the API to get the LINE auth URL. Validate its structure and parameters.
    b. **Full Flow:** Test the end-to-end LINE login: initiation, redirection, LINE authentication, callback processing, user creation/linking, and session establishment.
    c. **User Unbinding:** Verify an authenticated user can unbind their LINE account. Confirm data removal and inability to log in via that LINE account subsequently, while other login methods remain functional.
5. **Login Timestamp Verification:** After each successful login type (traditional, OAuth, with/without MFA), confirm the `last_login_at` field is accurately updated for the respective `SystemUser` or `TenantUser`.
6. **Multi-Tenant Isolation:** Confirm MFA settings for a `TenantUser` in one tenant do not impact users in other tenants or `SystemUser` configurations.
7. **Security Penetration Tests:** Attempt common MFA bypass techniques. Verify CSRF protection on LINE OAuth `state` parameter. Confirm MFA secrets are not stored in plaintext.

# Subtasks:
## 1. Integrate MFA Core with SystemUser/TenantUser Models [pending]
### Dependencies: None
### Description: Adapt the existing MFA system (e.g., TOTP) to fully support `SystemUser` and `TenantUser` models. This includes securely storing MFA configurations (secrets, recovery codes, enabled methods) associated with the correct user type and implementing MFA enrollment, verification, and recovery processes for both user types.
### Details:
1. Modify database schemas or user models (`SystemUser`, `TenantUser`) to include fields for MFA secrets (encrypted), recovery codes (hashed or encrypted), enabled MFA methods (e.g., 'TOTP', 'SMS'), and MFA status (e.g., 'enrolled', 'not_enrolled').
2. Update MFA service logic to handle both `SystemUser` and `TenantUser` types for: generating/storing TOTP secrets, generating/storing recovery codes, verifying TOTP codes, verifying recovery codes, and enabling/disabling MFA.
3. Ensure MFA secrets are stored securely (e.g., encrypted at rest using AES-256 or similar).
4. Implement API endpoints for MFA setup (e.g., `POST /api/users/me/mfa/setup` to get secret/QR code, `POST /api/users/me/mfa/verify` to confirm setup with a valid TOTP code), and managing recovery codes (e.g., `GET /api/users/me/mfa/recovery-codes` to view, `POST /api/users/me/mfa/recovery-codes/regenerate` to generate new ones).

## 2. Implement LINE OAuth Auth URL Generation & Enhance Callback Handler [pending]
### Dependencies: None
### Description: Develop an API endpoint to dynamically generate the LINE OAuth authorization URL and enhance the existing LINE OAuth callback handler to robustly manage token exchange, user profile fetching, and linking/login for `SystemUser` or `TenantUser`.
### Details:
1. Create `GET /api/auth/line/url` endpoint: Dynamically construct the LINE authorization URL including `client_id`, `redirect_uri`, `scope` ('profile openid email'), `response_type=code`, and a secure, unique, server-side stored `state` parameter for CSRF protection. Return the URL to the client.
2. Enhance `GET /api/auth/line/callback` handler: Verify the received `state` parameter. Exchange authorization `code` for tokens (access, ID, refresh) with LINE. Fetch user profile from LINE. Implement logic to find existing `SystemUser`/`TenantUser` by LINE ID or email, or create a new user. Link LINE ID to the user profile. Log in the user and issue session tokens.

## 3. Implement LINE User Unbinding and Unified Login Timestamp Update [pending]
### Dependencies: None
### Description: Implement an API endpoint for authenticated users to disconnect their LINE account from their profile and update/implement logic to record the `last_login_at` timestamp for both `SystemUser` and `TenantUser` entities upon any successful authentication event.
### Details:
1. **LINE User Unbinding:** Create `POST /api/users/me/oauth/line/unbind` endpoint. Requires user authentication. Remove LINE ID and associated LINE tokens from the user's profile in the database. If possible, call LINE's API to revoke relevant tokens.
2. **Login Timestamp Update:** Identify all successful authentication points (traditional login, OAuth login completion, MFA completion if it's the final step). Update the `last_login_at` field (UTC) for the `SystemUser` or `TenantUser` in the database at these points. Ensure this logic is centralized or consistently applied.

## 4. Enforce MFA Across All Authentication Methods (Traditional & OAuth) [pending]
### Dependencies: None
### Description: Ensure MFA is triggered post-primary authentication if enabled for the user. This applies to traditional login (after password validation) and OAuth logins (e.g., Google, LINE) after successful authentication with the provider and user linkage in our system.
### Details:
1. **Traditional Login:** After successful password validation, check if MFA is enabled for the user. If yes, do not issue a full session token; instead, prompt for MFA verification. Implement an endpoint like `POST /api/auth/mfa/verify-login`.
2. **OAuth Login (LINE, Google, etc.):** After successful OAuth provider authentication and user identification/linkage in our system (via callback), check if MFA is enabled for the user. If yes, prompt for MFA verification before granting full access or issuing a full session token.
3. Manage intermediate authentication state securely (e.g., user passed primary auth, pending MFA). This might involve a temporary token or session state indicating partial authentication.

## 5. Finalize APIs, Implement Security Hardening & Conduct Comprehensive Testing [pending]
### Dependencies: None
### Description: Define and document all new API endpoints for MFA management and LINE OAuth. Implement security best practices, including protection against MFA bypass/brute-force, and ensure a clear UX. Conduct comprehensive security and functional testing of all integrated features.
### Details:
1. **API Documentation:** Review and finalize Swagger/OpenAPI documentation for all new/modified endpoints related to MFA (setup, verify, disable, recovery codes) and LINE OAuth (auth URL, callback, unbind).
2. **Security Hardening:** Implement rate limiting on MFA verification attempts and recovery code usage. Ensure secure session management post-MFA. Log security-sensitive events (MFA actions, OAuth linking/unlinking, failed attempts). Review for MFA bypass vulnerabilities.
3. **User Experience (UX):** Ensure clear instructions, feedback, and error messages for MFA setup, login, recovery, and LINE account linking/unlinking.
4. **Comprehensive Testing:** Perform end-to-end testing of all authentication flows with MFA. Conduct security testing (e.g., review against OWASP Top 10, MFA-specific vulnerabilities). Test edge cases and error handling for all new functionalities.

