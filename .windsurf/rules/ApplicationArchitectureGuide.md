---
trigger: always_on
description: 
globs: 
---
# HorizAI SaaS - 應用程式架構統一指南 (Application Architecture Unified Guide)

**版本**: 1.0
**基於**: `project-structure.mdc`, `backend-architecture.mdc`, `frontend-architecture.mdc`, `api-integration.mdc`, `tenant-relationship.mdc` (部分)

## 1. 引言

### 1.1 文件目的

本文件旨在統一並詳細定義 HorizAI SaaS 平台的整體應用程式架構，包括專案結構、前後端模組劃分、API 設計與整合標準、以及檔案與目錄命名規範。本文檔為開發團隊提供一個共同的架構藍圖，以確保開發的一致性、可維護性和可擴展性。

本文檔將取代並整合以下舊有規則文件：
*   `project-structure.mdc`
*   `backend-architecture.mdc`
*   `frontend-architecture.mdc`
*   `api-integration.mdc`
*   `tenant-relationship.mdc` (關於資料模型關係的部分已整合，其他強化方案等內容應參考 `AuthAndAccessControlGuide.mdc` 或獨立處理)

### 1.2 核心原則

*   **模組化 (Modularity)**: 前後端應用程式均應劃分為高內聚、低耦合的模組。
*   **分層架構 (Layered Architecture)**: 遵循清晰的邏輯分層，如表現層、業務邏輯層、資料存取層。
*   **標準化與一致性 (Standardization & Consistency)**: 遵循統一的命名約定、編碼風格、API 設計模式。
*   **可測試性 (Testability)**: 架構設計應易於進行單元測試、整合測試和端到端測試。
*   **關注點分離 (Separation of Concerns)**: 明確劃分不同技術或業務領域的職責。

## 2. 整體專案結構 (Monorepo)

本專案採用 pnpm workspaces 管理的 monorepo 結構。

### 2.1 主要目錄

*   **`apps/`**: 包含主要的應用程式。
    *   `frontend/`: Vue 3 + TypeScript 前端應用程式。
    *   `backend/`: NestJS + TypeScript 後端應用程式。
*   **`packages/`**: 包含共享的套件。
    *   `@auth/`: 身份驗證與授權相關的共享邏輯、狀態管理 (如 `useAuthStore`)、類型定義等。
    *   `@shared/` (或類似名稱): 跨前後端共享的通用工具函式、核心類型定義 (非特定於某個應用程式的)。
*   **`prisma/`** (位於 `apps/backend/prisma`): 後端 Prisma schema 定義、資料庫遷移檔案、Seeder 腳本。
*   **`.github/`**: GitHub Actions workflows, issue/PR templates, 以及本架構指南等規則文件 (`prompts/` 或 `rules/` 子目錄)。
*   **`docs/`** (或 `apps/DevDoc/`): 專案級別的開發者文件、PRD、設計文檔等。

### 2.2 根目錄設定檔

*   `package.json`: Monorepo 根目錄的 `package.json`。
*   `pnpm-workspace.yaml`: 定義 pnpm workspaces。
*   `tsconfig.base.json`: 基礎 TypeScript 設定，被各個 app/package 繼承和擴展。
*   `.eslintrc.js`, `.prettierrc.js`: 全局 ESlint 和 Prettier 設定。

## 3. 後端架構 (NestJS)

### 3.1 核心技術

*   **框架 (Framework)**: NestJS
*   **ORM**: Prisma
*   **身份驗證 (Authentication)**: JWT (Access Token + Refresh Token) + CASL (用於權限定義)

### 3.2 模組結構 (`apps/backend/src/modules`)

後端模組主要分為以下幾類：

1.  **核心模組 (`core/`)**:
    *   **職責**: 處理應用程式的基礎核心功能。
    *   **子模組範例**:
        *   `auth/`: 身份驗證 (JWT 策略, `AuthService`, `AuthController`, Guards like `JwtAuthGuard`)。
        *   `prisma/`: Prisma Client 封裝與服務 (`PrismaService`)。
        *   `casl/`: CASL 能力工廠 (`CaslAbilityFactory`) 及相關定義。
        *   `encryption/`: 加密解密服務 (如用於 API Keys)。
        *   `mail/`: 郵件發送服務。
        *   `storage/`: 文件儲存服務 (本地或雲端)。
        *   `config/`: 應用程式配置讀取與管理。
    *   **API 路由**: 通常提供基礎服務，如 `/api/auth`。

2.  **管理員模組 (`admin/`)**:
    *   **職責**: 管理系統級的任務和資源，通常需要 `SYSTEM_ADMIN` 或 `SUPER_ADMIN` 權限。
    *   **子模組範例**:
        *   `tenants/`: 租戶管理。
        *   `users/system/`: 系統使用者管理。
        *   `roles/`: 系統級角色與權限管理 (`SYSTEM` scope)。
        *   `settings/`: 系統全局設定。
        *   `plans/`: 服務方案管理。
        *   `orders/`: 訂單管理。
        *   `ai/keys/`, `ai/models/`, `ai/bots/` (SYSTEM scope), `ai/feature-configs/`, `ai/global-settings/`: AI 相關的系統級管理。
        *   `system-logs/`: 系統日誌查看。
    *   **API 路由**: 前綴為 `/api/admin` (例如 `/api/admin/tenants`)。

3.  **租戶管理模組 (`tenant-admin/` 或整合進 `admin/` 及 `workspace/` 特定路由)**:
    *   **職責**: 租戶管理員管理其租戶範圍內的設定和資源。
    *   **子模組範例**:
        *   `users/tenant/`: 管理本租戶下的使用者。
        *   `roles/tenant/`: 管理本租戶下的角色 (`TENANT`, `WORKSPACE` scope)。
        *   `settings/tenant/`: 租戶特定設定。
        *   `ai/bots/tenant-template/`: 租戶 AI Bot 範本管理。
    *   **API 路由**: 例如 `/api/admin/tenants/:tenantId/users` 或 `/api/tenant-admin/...` (需與 `AuthAndAccessControlGuide.mdc` 協調)。

4.  **工作區模組 (`workspace/`)**:
    *   **職責**: 處理特定於使用者工作區的功能和資料，通常需要使用者是該工作區的成員。
    *   **子模組範例**:
        *   `projects/`: 專案管理。
        *   `members/`: 工作區成員管理。
        *   `settings/workspace/`: 工作區設定。
        *   `ai/bots/workspace/`: 工作區 AI Bot 管理與執行。
        *   `chat/`: 聊天功能。
    *   **API 路由**: 前綴為 `/api/workspace/:workspaceId` (例如 `/api/workspace/clxxxxxx/projects`)，其中 `:workspaceId` 通常從 URL 參數獲取，並透過 Guard 驗證使用者對此工作區的存取權限。

5.  **通用模組 (`common/` 或位於 `apps/backend/src/common`)**:
    *   **職責**: 提供跨模組共享的裝飾器、過濾器、管道、攔截器、工具函式等。
    *   **範例**: 全局異常過濾器, 資料轉換管道, 日誌攔截器。

### 3.3 主要應用程式模組 (`apps/backend/src/app.module.ts`)

所有業務模組和核心模組都在 `AppModule` 中註冊。

### 3.4 後端開發原則與慣例

*   **NestJS 最佳實踐**: 遵循標準的 NestJS 模組、控制器、服務、提供者模式。
*   **DTO 驗證**: 所有外部輸入 (request body, query params, path params) 都必須使用 DTO 並配合 `class-validator` 和 `class-transformer` 進行驗證和轉換。
*   **Prisma 使用**: 所有資料庫操作應通過 Prisma Client 進行，並封裝在服務層。
*   **錯誤處理**: 實現全局異常過濾器，將錯誤轉換為標準化的 HTTP 回應格式。
*   **序列化**: 使用 `@Exclude()`, `@Expose()` 或 `ClassSerializerInterceptor` 控制 API 回應中暴露的欄位。
*   **日誌記錄**: 在關鍵操作和錯誤處理中加入適當的日誌記錄。
*   **設定管理**: 使用 NestJS ConfigModule (`@nestjs/config`) 集中管理環境變數和應用程式設定。
*   **API 文件化 (API Documentation)**: 使用 `@nestjs/swagger` 套件來註解 DTOs 和 Controllers，以便自動生成 OpenAPI (Swagger) 規格文檔。這份文檔應部署並提供給前端和外部開發者參考。

## 4. 前端架構 (Vue 3)

### 4.1 核心技術

*   **框架 (Framework)**: Vue 3 (Composition API, `<script setup>`)
*   **狀態管理 (State Management)**: Pinia
*   **路由 (Routing)**: Vue Router
*   **UI 元件庫 (UI Components)**: Shadcn-Vue (基於 Radix Vue)
*   **樣式 (Styling)**: Tailwind CSS
*   **HTTP 客戶端 (HTTP Client)**: 統一使用 `httpService`

### 4.1.1 HTTP 客戶端規範 (httpService)

*   **命名規範**:
    *   統一使用 `httpService` 作為 HTTP 客戶端服務的名稱
    *   禁止使用其他名稱如 `ApiService`, `http` 等，以保持一致性
*   **引入方式**:
    ```typescript
    import { httpService } from '@/services/http.service'
    ```
*   **使用方式**:
    *   直接使用 composition pattern 而非繼承
    *   在 service 層中直接調用 `httpService` 的方法
    *   示例：
    ```typescript
    export class UserService {
      static async getProfile(): Promise<IUserProfile> {
        return httpService.get<IUserProfile>('/auth/me')
      }
    }
    ```
*   **錯誤處理**:
    *   統一由 `httpService` 處理通用錯誤（如 401, 403, 500 等）
    *   業務邏輯錯誤在各 service 中處理
*   **Token 管理**:
    *   Access Token 和 Refresh Token 的管理統一由 `httpService` 處理
    *   自動處理 token 過期和更新邏輯

### 4.2 目錄結構 (`apps/frontend/src`)

*   `main.ts`: 應用程式入口點，初始化 Vue app, Pinia, Router, 全局組件/插件。
*   `App.vue`: 根組件，通常包含 `<RouterView />` 和全局佈局元素 (如 Toaster, FlashMessage)。
*   `router/`:
    *   `index.ts`: Vue Router 實例化和路由定義。
    *   `guards.ts`: 全局導航守衛 (身份驗證、權限檢查)。
    *   `routes/`: (可選) 按模組拆分的路由配置文件。
*   `stores/`: Pinia stores (除了 `@horizai/auth` 中的 `authStore` 外的其他全局狀態)。
*   `composables/`:
    *   `shared/`: 跨功能共享的 Composition Functions (例如 `useNotification`, `useFlashMessage`)。
    *   `features/` (或按模組名): 特定功能的 Composition Functions (例如 `useProjectManagement`, `useChat`)。
*   `services/`: API 服務封裝 (例如 `project.service.ts`, `user.service.ts`)。
*   `components/`:
    *   `ui/`: Shadcn-Vue UI 元件 (通常是 CLI 生成的)。
    *   `common/`: 專案內可重用的通用業務組件 (基於 UI 元件封裝)。
    *   `layouts/`: 頁面佈局組件 (例如 `AdminLayout.vue`, `WorkspaceLayout.vue`)。
    *   `features/` (或按模組名): 特定功能的組件 (例如 `ProjectCard.vue`, `ChatWindow.vue`)。
*   `views/` (或 `pages/`):
    *   路由對應的頁面級組件，通常按模組組織 (例如 `views/admin/DashboardPage.vue`, `views/workspace/project/ProjectDetailsPage.vue`)。
*   `assets/`: 靜態資源 (圖片, 字體, 全局樣式如 `index.css` 或 `tailwind.css`)。
*   `types/` (或 `models/`):
    *   `api/` 或 `dto/`: 與後端 DTO 對應的請求/回應類型。
    *   `entities/` 或 `models/`: 前端內部使用的核心業務實體模型。
    *   `components/`: 組件 props 或 emits 的類型定義。
*   `utils/`: 通用工具函式 (日期格式化, 字串處理等)。
*   `config/`: 專案設定 (例如 API Base URL, 功能開關常數)。
*   `constants/`: 應用程式常數。
*   `lib/`: 對第三方庫的封裝或擴展。

### 4.3 前端開發原則與慣例

*   **組件結構標準**:
    *   單一組件行數限制 (建議不超過 300-500 行，超長則拆分)。
    *   邏輯分層: Template (UI) -> Composables (業務邏輯) -> Services (API 互動) -> Utils (純函式)。
*   **Composables 使用標準**:
    *   封裝狀態管理 (reactive refs)、相關方法、計算屬性。
    *   優先將業務邏輯提取到 Composables 中，保持組件輕量。
*   **狀態管理 (Pinia)**:
    *   核心身份驗證狀態由 `@horizai/auth` 提供。
    *   其他全局或跨組件共享的狀態使用 Pinia store 管理。
    *   模組化 store 設計。
*   **API 互動**: 
    *   在 `services/` 目錄中封裝 API 請求。
    *   服務方法應返回 Promise 並處理基本的請求/回應。
    *   在 Composables 或組件中調用服務，並管理載入狀態 (loading) 和錯誤狀態 (error)。
*   **錯誤處理**: 
    *   服務層捕獲 API 錯誤，Composables/組件層處理錯誤狀態並透過全局通知系統顯示用戶友好的錯誤訊息。
*   **類型安全**: 
    *   所有程式碼都應使用 TypeScript 並有明確的類型定義。
    *   為 API 請求/回應、props、emits、store state/actions 定義類型。
*   **環境變數**: API Base URL 等設定應使用環境變數 (`import.meta.env`)。
*   **路由與導航守衛**: 嚴格使用導航守衛進行頁面訪問控制。
*   **表單處理**: 使用如 `vee-validate` 結合 `zod` (或類似庫) 進行表單驗證。

## 5. API 設計與整合標準

### 5.1 後端 API 設計 (NestJS)

*   **RESTful 原則**: 嚴格遵循 RESTful 設計，使用 HTTP 動詞 (GET, POST, PUT, DELETE, PATCH) 對應資源操作。
*   **路由 (Routing)**: 清晰、資源導向的路由 (例如 `/api/users`, `/api/projects/:id`)。路由前綴應符合第 3.2 節的模組劃分。
*   **DTOs**: 請求/回應使用 DTO，並進行驗證。
*   **回應結構**: 標準化 HTTP 狀態碼。成功回應通常包含在 `data` 屬性中，或直接返回陣列/物件。錯誤回應應包含 `statusCode`, `message`, `errors` (詳細錯誤信息)。
*   **版本控制 (可選)**: 若 API 有重大變更，可考慮路徑版本控制 (例如 `/api/v1/users`)。
*   **身份驗證與授權**: 所有非公開端點都必須受 `JwtAuthGuard` 和 `PoliciesGuard` (CASL) 保護。
*   **序列化**: 控制回應數據，避免洩漏敏感資訊。
*   **API 文件 (Swagger/OpenAPI)**: 強烈建議使用 `@nestjs/swagger` 整合 Swagger UI，自動從 Controller 和 DTO 的裝飾器生成互動式 API 文件。此文件應作為 API 的標準參考。

### 5.2 前端 API 整合 (Vue 3)

*   **服務層封裝**: 在 `src/services` 中建立服務類或函式，封裝 API 呼叫邏輯。
*   **類型安全**: 前端定義與後端 DTO 匹配的 TypeScript 類型。
*   **狀態管理**: 在 Composables 或 Pinia store 中管理 API 請求的生命週期 (loading, data, error)。推薦使用 `@tanstack/vue-query` (Vue Query) 或 VueUse 的 `useAsyncState` 進行數據獲取和快取。
*   **錯誤處理**: 捕獲並處理 API 錯誤，透過全局通知系統向用戶顯示友善提示，並在控制台記錄詳細錯誤。
*   **身份驗證 Token**: 自動將 JWT Access Token 附加到需要身份驗證的請求中 (通常透過 Axios interceptor 或 Fetch 封裝實現)。

### 5.3 通用整合原則

*   **一致性**: 前後端之間保持命名約定、資料結構的一致性。
*   **文件化**: 後端 API 應使用 `@nestjs/swagger` 自動生成並提供 OpenAPI (Swagger) 文件。前端服務方法應有清晰的 JSDoc 註釋。
*   **完整性**: 所有 API 整合都應完整實現，包括請求處理、回應處理、錯誤管理和狀態更新。

## 6. 檔案與目錄命名規範

(本節內容直接整合自原 `project-structure.mdc` 的 "File and Directory Naming Rules" 部分，確保其在統一指南中的延續性)

### 6.1 基本命名原則
1.  **可讀性優先**: 檔案和目錄名稱應清楚表達其用途和內容。
2.  **一致性**: 整個專案使用統一的命名風格。
3.  **簡潔性**: 避免過於冗長或不必要的名稱。
4.  **避免特殊字元**: 除指定的符號 (如連字號 `-`, 點 `.`) 外，避免使用特殊字元。

### 6.2 檔案命名規則

#### 6.2.1 TypeScript/JavaScript 檔案
*   **通用規則**:
    *   所有檔案使用 `.ts` (首選) 或 `.js` 副檔名。
    *   使用 `kebab-case` 命名約定 (例如 `user-profile.ts`, `auth.service.ts`)。
    *   檔案名稱應能表達該檔案的主要功能或內容。
*   **模型與類型定義**:
    *   模型檔案 (包含介面、類型、enums) 使用 `.model.ts` 或 `.types.ts` 後綴 (例如 `user.model.ts`, `project.types.ts`)。建議統一使用 `.model.ts` 以表示與 Prisma 模型相關或業務實體相關的類型定義。
*   **Vue 元件檔案**:
    *   使用 `PascalCase` 命名 (例如 `UserProfile.vue`, `BaseButton.vue`)。
    *   元件檔案名稱應與其匯出的元件名稱一致。
    *   頁面級組件 (通常直接對應一個路由) 的命名應清晰反映其功能與層級，可選用如 `Page`, `View` 或組合名的方式來與一般組件區分 (例如 `UserProfilePage.vue`, `SettingsDashboardView.vue`, 或直接使用 `ProjectDetails.vue` 如果其在 `views/project/` 目錄下已表明其頁面屬性)。
*   **功能性檔案**:
    *   Composables (Vue Composition API) 使用 `use` 前綴，`camelCase` 或 `kebab-case` (例如 `useAuth.ts`, `use-project-filters.ts`)。
    *   工具函式檔案 (Utils) 使用 `kebab-case` (例如 `date-formatter.ts`)。
    *   常數檔案使用 `.constants.ts` 後綴 (例如 `user.constants.ts`)。
    *   服務檔案 (Services) 使用 `.service.ts` 後綴 (例如 `auth.service.ts`)。
    *   Pinia Store 檔案使用 `.store.ts` 後綴 (例如 `cart.store.ts`)。
    *   路由設定檔使用 `.routes.ts` 或 `router.ts`。

#### 6.2.2 樣式檔案
*   使用 `.css`, `.scss`, `.less` 等副檔名。
*   若為獨立樣式檔 (非 Tailwind)，使用 `kebab-case` 命名 (例如 `main-layout.scss`)。
*   通常 Tailwind CSS 不需要獨立的樣式檔案，樣式直接寫在組件中或全局 `tailwind.css` / `index.css`。

#### 6.2.3 測試檔案
*   使用 `.spec.ts` 或 `.test.ts` 後綴。
*   檔案名稱應與被測試的檔案對應 (例如 `user.service.ts` -> `user.service.spec.ts`)。

### 6.3 目錄命名規則
*   **通用規則**:
    *   使用 `kebab-case` 命名 (例如 `user-profiles/`, `feature-modules/`)。
    *   目錄名稱應能表達其內容物。
    *   避免縮寫，除非是廣為接受的 (例如 `ui/` 代表 User Interface, `api/` 代表 API 相關)。
*   **特定用途目錄** (參考第 3.2 和 4.2 節的結構描述):
    *   `components/`, `composables/`, `services/`, `stores/`, `utils/`, `views/` (或 `pages/`), `assets/`, `types/` (或 `models/`), `router/`, `layouts/`, `config/`, `constants/`, `lib/` 等。
*   **模組結構**:
    *   功能模組使用其功能名稱 (例如 `auth/`, `dashboard/`, `projects/`)。
    *   共享資源使用 `shared/` 或 `common/`。

### 6.4 索引檔案規則 (`index.ts`)
*   建議在需要匯出多個模組的目錄中使用 `index.ts` 作為統一匯出點，簡化外部引用路徑。
    *   例如，`composables/shared/index.ts` 可以匯出所有共享的 composables。

## 7. 開發與維護原則

*   **遵守規範**: 所有開發活動都應遵循本架構指南。
*   **文件同步**: 架構發生重大變更時，應及時更新本指南。
*   **完整性**: 確保所有架構組件和功能都完整實現，無 TODO 或未完成部分 (指最終交付狀態)。
*   **程式碼審查**: 透過程式碼審查確保架構原則的遵循。

---









