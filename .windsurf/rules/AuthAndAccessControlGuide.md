---
trigger: always_on
description:
globs:
---

# HorizAI SaaS - 身份驗證與權限控制統一指南 (Auth & Access Control Unified Guide)

**版本**: 1.0 (持續更新以反映重構進度)
**基於**: `auth-system-prd.md`, `auth-system-refactor-todo.md`, `default-roles-permissions-map.md`, 原 `auth-system.mdc`, 原 `admin-permissions.mdc`

## 1. 引言

### 1.1 文件目的

本文件旨在統一並詳細定義 HorizAI SaaS 平台的身份驗證 (Authentication) 與授權 (Authorization) 系統的核心設計原則、架構、功能需求、技術規格及實施指南。本系統是平台安全的基石，確保只有合法的用戶才能存取其被授權的資源與功能，並為未來的擴展奠定堅實基礎。

本文檔將取代並整合以下舊有規則文件：

- `auth-system.mdc`
- `admin-permissions.mdc`

### 1.2 核心目標

1.  **安全性 (Security)**: 建立強固的安全屏障，符合業界標準，防範未經授權的存取、資料洩漏和潛在攻擊。
2.  **精細化權限控制 (Granular Access Control)**: 提供基於角色 (RBAC)、資源屬性 (ABAC/Conditions via CASL) 和潛在欄位級別的靈活權限管理。
3.  **易用性 (Usability)**: 為終端用戶提供流暢的身份驗證體驗，為管理員提供清晰易操作的權限管理介面。
4.  **可維護性與可擴展性 (Maintainability & Scalability)**: 系統設計應模組化、清晰，易於維護、擴展新功能及整合新的認證/授權機制。
5.  **多租戶支援 (Multi-Tenancy)**: 完美支援多租戶架構，確保租戶間的資料和權限隔離。
6.  **標準化 (Standardization)**: 遵循 JWT, OAuth 2.0 概念等業界最佳實踐和標準。

### 1.3 名詞定義

(沿用 `auth-system-prd.md` 中的名詞定義，此處不再贅述，重點詞彙如：Authentication, Authorization, CASL, Role, Permission, Subject, Action, Conditions, JWT, Tenant, Workspace)

## 2. 系統架構

### 2.1 後端架構 (NestJS + Prisma + CASL)

#### 2.1.1 資料庫模型 (Prisma Schema)

核心模型 (詳見 `apps/backend/prisma/schema.prisma`):

- `User`: 儲存使用者基本資訊、狀態、登入相關時間戳。
- `Tenant`: 租戶資訊。
- `Workspace`: 工作區資訊。
- `Role`: 角色定義。
  - `id`: String (cuid)
  - `name`: String (角色名稱，特定 scope 下應唯一)
  - `description`: String? (角色描述)
  - `scope`: Enum `RoleScope` (`SYSTEM`, `TENANT`, `WORKSPACE`) - 定義角色的適用範圍。
  - `is_system_defined`: Boolean (是否為系統預設角色，不可刪除/修改核心屬性)
  - `tenant_id`: String? (當 scope 為 `TENANT` 或 `WORKSPACE` 時關聯的租戶 ID)
  - `permissions`: `RolePermissionMapping[]` (關聯的權限)
  - `users`: `UserRoleMapping[]` (關聯的使用者)
- `Permission`: 權限定義。
  - `id`: String (cuid)
  - `action`: String (操作，如 `create`, `read`, `manage`)
  - `subject`: String (主體/資源，如 `User`, `Project`, `TenantSetting`)
  - `conditions`: Json? (CASL 條件，JSON 格式，用於屬性級控制，例如 `"{ \\"authorId\\": \\"${user.id}\\" }"`)
  - `fields`: String[]? (CASL 欄位級控制，限制可讀/寫的欄位)
  - `description`: String? (權限描述)
  - `scope`: Enum `PermissionScope` (`SYSTEM`, `TENANT`, `WORKSPACE`, `GLOBAL`) - 權限適用範圍或分類。
- `RolePermissionMapping`: 角色與權限的多對多關聯表。
- `UserRoleMapping`: 使用者與角色的多對多關聯表。
- `AuthRefreshToken`: 儲存 Refresh Token 的雜湊值、過期時間、使用者關聯等，用於安全管理。
- `LineAuthState`: Line 登入相關狀態。
- `LoginLog`: 登入日誌。
- `SystemLog`: 系統操作日誌 (審計)。

#### 2.1.2 核心服務

- `AuthService` (`apps/backend/src/modules/core/auth/auth.service.ts`):
  - 處理使用者登入、註冊、登出。
  - JWT (Access Token, Refresh Token) 的簽發、驗證、刷新。
  - 密碼管理 (雜湊比對, 重設邏輯)。
  - 第三方登入 (Google, Line) 邏輯。
- `UsersService` (System, Tenant, Workspace scopes): 使用者帳戶管理。
- `RolesService` (`apps/backend/src/modules/admin/roles/roles.service.ts` 或各 scope 下):
  - 角色的 CRUD 操作。
  - 角色與權限的關聯管理。
- `PermissionsService` (`apps/backend/src/modules/admin/roles/permissions.service.ts` 或各 scope 下):
  - 權限定義的 CRUD 操作 (若權限為完全資料庫驅動)。
  - 提供權限列表查詢。
- `CaslAbilityFactory` (`apps/backend/src/casl/casl-ability.factory.ts`):
  - 根據使用者 ID、角色、直接指派的權限以及 `RolePermissionMapping`。
  - 生成 CASL `AppAbility` 實例 (包含 `can` 和 `cannot` 方法)。
  - 處理 `conditions` 和 `fields` 的解析與應用。
  - 是後端權限檢查的核心。
- `AuditLogService` / `SystemLogService`: 記錄重要的身份驗證和授權相關事件。

#### 2.1.3 API 端點 (主要路由)

- **認證相關 (`/api/auth`)**:
  - `POST /login`: Email/密碼登入。
  - `POST /register`: (若開放) 自助註冊。
  - `POST /logout`: 登出。
  - `GET /me`: 獲取當前登入使用者資訊 (包含角色和 CASL 權限規則 JSON)。
  - `POST /refresh-token`: 使用 Refresh Token 刷新 Access Token。
  - `POST /forgot-password`: 發起忘記密碼請求。
  - `POST /reset-password`: 使用 token 重設密碼。
  - `GET /google`, `GET /google/callback`: Google OAuth 登入。
  - `GET /line`, `GET /line/callback`: Line OAuth 登入。
- **管理員權限相關 (`/api/admin`)**:
  - `/roles`: 系統角色 CRUD (`SYSTEM` scope)。
  - `/permissions`: 系統權限 CRUD (若資料庫驅動)。
  - `/users/system`: 系統使用者管理。
  - (其他如 `admin-permissions.mdc` 所列，但會根據新架構調整 subject 和 action)
- **租戶管理員權限相關 (`/api/workspace-admin/:workspaceId` 或 `/api/tenant-admin`)**:
  - `/roles`: 租戶/工作區角色 CRUD (`TENANT`, `WORKSPACE` scope)。
  - `/users`: 租戶/工作區使用者管理。
- 其他業務 API 端點將透過 Guards 進行權限保護。

#### 2.1.4 後端保護機制

- `JwtAuthGuard`: 驗證 Access Token，確保請求者已登入。用於保護需要登入的 API。
- `PoliciesGuard` (或自定義 CASL Guard):
  - 依賴 `CaslAbilityFactory` 生成的 `AppAbility`。
  - 配合 `@CheckPolicies()` decorator (或類似機制) 使用於 Controller 的 handler 上。
  - Decorator 應能指定 `(action, subject)` 或一個處理函數來檢查權限。
  - 無權限的請求應返回 `403 Forbidden`。

### 2.2 前端架構 (Vue 3 + Pinia + @casl/vue)

#### 2.2.1 身份驗證狀態管理 (`@horizai/auth/src/store/auth.store.ts`)

- **State**:
  - `user: User | null` (當前登入使用者物件)
  - `accessToken: string | null`
  - `refreshTokenPresent: boolean` (標記 Refresh Token 是否存在於 Cookie)
  - `isAuthenticated: boolean`
  - `ability: AppAbility | null` (CASL `AppAbility` 實例)
  - `loading: boolean` (處理初始化、登入過程中的載入狀態)
- **Actions**:
  - `login(credentials)`: 呼叫登入 API，成功後更新 state，儲存 token，初始化 `ability`。
  - `logout()`: 呼叫登出 API，清除 state 和 token。
  - `fetchCurrentUser()`: 呼叫 `/api/auth/me`，恢復使用者狀態和權限，通常在應用初始化時調用。
  - `refreshToken()`: 呼叫刷新 token API。
  - `initAuth()`: 應用啟動時的核心函式，嘗試 `fetchCurrentUser`，設置 HTTP client 的 interceptors 處理 token 刷新和 401 錯誤。
  - `updateAbility(rules)`: 使用從後端獲取的 CASL 規則更新 `ability` 實例。
- **Getters**:
  - `isLoggedIn: boolean`
  - `currentUser: User | null`
  - `can(action, subject, field?): boolean` (直接使用 `ability` 實例的方法)

#### 2.2.2 前端 UI 保護

- **路由保護 (Navigation Guards)** (`apps/frontend/src/router/guards.ts`):
  - Vue Router `beforeEach` 全域守衛。
  - 檢查路由 `meta` 字段：
    - `requiresAuth: boolean`: 是否需要登入。
    - `requiresGuest: boolean`: 是否僅限訪客訪問 (已登入用戶將被重定向)。
    - `permission: { action: string, subject: string } | (ability: AppAbility) => boolean`: 權限要求。
  - 未登入訪問受保護路由 -> 重定向到登入頁 (可帶 `redirect` 參數)。
  - 已登入訪問訪客路由 -> 重定向到預設儀表板。
  - 權限不足 -> 重定向到 403 頁面或首頁，並提示訊息。
- **UI 元素顯隱/禁用**:
  - 使用 `@casl/vue` 提供的 `$can` (template) 或 `useAbility().can` (script) 方法。
  - 考慮使用 `<Can I='...' A='...'>...</Can>` 組件。
  - 範例: `<button v-if="$can('create', 'Project')">Create Project</button>`
- **權限狀態同步**:
  - 登入或 `fetchCurrentUser` 成功後，從 API (`/api/auth/me` 的回應或 Access Token payload 中解析) 獲取序列化的 CASL 權限規則 (JSON)。
  - 將這些規則傳遞給 `updateAbility` action 來實例化/更新前端的 `AppAbility`。
  - 確保 `AppAbility` 是響應式的，以便 UI 能自動更新。

## 3. 用戶角色與職責 (FR3)

(詳細定義參考 `auth-system-prd.md` Section 3 和 `default-roles-permissions-map.md`)

- **超級管理員 (SuperAdmin / SUPER_ADMIN)**: 平台擁有者，最高權限。
- **系統管理員 (SystemAdmin / SYSTEM_ADMIN)**: 管理平台，租戶，系統級設定。
- **租戶管理員 (TenantAdmin / TENANT_ADMIN)**: 管理其租戶內的資源、用戶、設定。
- **工作區管理員 (WorkspaceAdmin / WORKSPACE_ADMIN)**: (可選的細分角色) 管理特定工作區。
- **租戶用戶/工作區成員 (TenantUser / TENANT_USER)**: 使用平台功能。
- **訪客/未驗證用戶 (Guest)**: 瀏覽公開資訊。

**預設角色及其核心權限映射表請參考 `apps/DevDoc/default-roles-permissions-map.md`。** 此表應作為 Prisma Seeder 初始化預設角色和權限的基礎。

## 4. 功能需求 (FR4)

**注意**: 以下許多功能點的進度追蹤於 `apps/DevDoc/auth-system-refactor-todo.md`。本指南描述的是目標狀態。

### 4.1 用戶註冊與創建 (FR4.1)

- **FR4.1.1** 系統管理員建立租戶 (及租戶管理員)。
- **FR4.1.2** 租戶管理員邀請使用者 (Email 邀請連結)。
- **FR4.1.3** (可選) 使用者自助註冊 (需明確定義流程，預設關閉)。
- **FR4.1.4** 使用者資料收集與儲存 (姓名、Email、密碼 bcryptjs 雜湊)。

### 4.2 用戶登入 (FR4.2)

- **FR4.2.1** Email 和密碼登入。
  - "記住我" 選項 (延長 Refresh Token 效期)。
  - 清晰的登入失敗錯誤提示。
  - 登入嘗試次數限制 (Throttle)。
- **FR4.2.2 (未來/部分實現)** 第三方登入 (Social Login)。
  - Google (後端已部分實現)。
  - Line (規劃中)。
  - 帳戶綁定/建立流程。
- **FR4.2.3 (未來)** 單點登入 (SSO - SAML, OAuth 2.0/OIDC)。

### 4.3 密碼管理 (FR4.3)

- **FR4.3.1** 忘記密碼/密碼重設 (Email token 驗證)。
- **FR4.3.2** 修改密碼 (需驗證舊密碼)。
- **FR4.3.3** 密碼複雜性策略 (後端強制)。

### 4.4 Session 管理與 JWT (FR4.4)

- **FR4.4.1** JWT 簽發:
  - Access Token (短效期，HttpOnly Cookie `auth_token` 或 Authorization Header)。Payload 包含 `userId`, `username`, `roles`, `permissions` (CASL rules JSON), `iss`, `sub`, `aud`, `exp`, `iat`, `jti`。
  - Refresh Token (長效期，HttpOnly Cookie `refresh_token`，其雜湊值存於後端 `AuthRefreshToken` 表)。
- **FR4.4.2** Access Token 驗證 (後端 `JwtAuthGuard`)。
- **FR4.4.3** Token 刷新 (前端 `http.service.ts` 攔截器，後端 `/api/auth/refresh-token` 端點)。
- **FR4.4.4** Token 失效 (吊銷):
  - 登出時使 Refresh Token 失效 (後端資料庫標記)。
  - 修改密碼時使所有 Refresh Tokens 失效。

### 4.5 用戶登出 (FR4.5)

- 前端清除狀態 (Pinia store)。
- 後端失效 Refresh Token。
- 重定向。

### 4.6 角色管理 (RBAC) (FR4.6)

- **FR4.6.1** 角色定義:
  - 預設角色: `SUPER_ADMIN`, `SYSTEM_ADMIN`, `TENANT_ADMIN`, `TENANT_USER` (透過 Prisma Seeder 初始化)。
  - 管理員可 CRUD 自定義角色 (在其 scope 內)。
  - 角色欄位: `name`, `description`, `scope`, `tenant_id`。
- **FR4.6.2 (目前未實現)** 角色層級與繼承。

### 4.7 權限管理 (FR4.7)

- **FR4.7.1** 權限定義 (Permissions):
  - `action`: 如 `create`, `read`, `update`, `delete`, `manage`, 及特定業務操作 (如 `execute_ai_bot`)。
  - `subject`: Prisma 模型名稱或自定義業務主體。
  - `conditions` (JSON): 屬性級控制。
  - `fields` (String[]): 欄位級控制。
- **FR4.7.2** 權限列表維護:
  - 基於 `Permission` 資料庫表，提供相應管理介面。
  - 系統核心權限可透過 Seeder 初始化。
  - 參考 `admin-permissions.mdc` 和 `default-roles-permissions-map.md` 作為初始權限清單。
- **FR4.7.3** 權限 UI 管理: 管理員查看/管理權限定義。

### 4.8 角色-權限指派 (FR4.8)

- **FR4.8.1** 指派機制: 管理員將權限指派給角色 (`RolePermissionMapping` 表)。
- **FR4.8.2** UI 操作: 角色管理介面中修改角色權限。

### 4.9 使用者-角色指派 (FR4.9)

- **FR4.9.1** 指派機制: 管理員將角色指派給使用者 (`UserRoleMapping` 表)，使用者可擁有多個角色。
- **FR4.9.2** UI 操作: 使用者管理介面中修改使用者角色。

### 4.10 API 端點保護 (後端) (FR4.10)

- **FR4.10.1** 身份驗證保護 (`JwtAuthGuard`)。
- **FR4.10.2** 權限檢查保護 (`PoliciesGuard` + `@CheckPolicies()`)。

### 4.11 前端 UI 保護 (FR4.11)

- **FR4.11.1** 路由保護 (Navigation Guards)。
- **FR4.11.2** UI 元素顯隱/禁用 (`@casl/vue`)。
- **FR4.11.3** 權限狀態同步 (登入後獲取 CASL 規則，實例化 `AppAbility`)。

### 4.12 (未來) 多因子認證 (MFA) (FR4.12)

- TOTP 設定、驗證、備用碼。

### 4.13 審計日誌 (Audit Logging) (FR4.13)

- **FR4.13.1** 記錄範圍: 登入/登出，密碼操作，角色/權限變動，重要操作等。
- **FR4.13.2** 日誌內容: 時間戳, 使用者ID, IP, 事件類型, 對象, 結果等。
- **FR4.13.3** 儲存與查詢 (`SystemLog` 表，提供管理員查詢介面)。

## 5. 權限設計與管理原則

(參考 `default-roles-permissions-map.md` Section 5)

### 5.1 設計階段

- **最小權限原則 (Principle of Least Privilege)**。
- **權限粒度** 適中。
- **Scope 一致性** (`RoleScope`, `PermissionScope`)。
- **Subject 命名** 清晰。
- **Conditions** 的合理使用。
- 新功能同步規劃權限。

### 5.2 開發階段

- **Prisma Seeder 腳本 (`apps/backend/prisma/seed.ts`)**: 初始化預設角色、權限、映射關係，具備冪等性。字串與程式碼嚴格一致。
- **`CaslAbilityFactory.ts`**: 權限檢查核心，正確處理角色、直接權限、條件。
- **`@CheckPolicies` Decorator / `PoliciesGuard`**: 嚴格保護所有需授權的 API 端點。
- **前端權限使用 (`@casl/vue`, 路由守衛)**: 與後端權限同步。
- **錯誤處理**: `401 Unauthorized`, `403 Forbidden`。
- **完整測試**: 單元測試、整合測試、端到端測試。

### 5.3 維護階段

- **同步更新**: 模型、API、角色職責變更時，同步更新權限定義、Seeder、本指南。
- **定期審查**: 確保權限符合當前需求和最小權限原則。
- **日誌監控**: 監控審計日誌中的權限相關事件。
- **版本控制**: 核心權限程式碼和 Seeder 腳本。
- **文件更新**: 保持本指南及相關 PRD、TODO 文件最新。

## 6. 安全考量 (NFR5.1)

(詳細參考 `auth-system-prd.md` Section 5.1 和 `auth-system-refactor-todo.md` NFR5.1)

- **OWASP Top 10 風險緩解**。
- **數據加密**: 傳輸中 (HTTPS), 靜態加密 (密碼雜湊, API 金鑰等敏感資訊使用 `@EncryptionService`)。
- **輸入驗證與輸出編碼**: Class-validator, ORM 防 SQL Injection, Vue template 防 XSS。
- **依賴安全**: 定期掃描與更新 (Snyk, npm audit)。
- **Session 安全**:
  - JWT 強簽名算法 (RS256 優先，HS256 作為備選)，密鑰安全管理 (環境變數, Vault)。
  - Refresh Token 安全儲存 (HttpOnly, Secure, SameSite=Strict cookie; 後端雜湊儲存)。
  - CSRF 防護 (若使用 Cookie 儲存 Token，需考慮 Double Submit Cookie 或 Synchronizer Token Pattern)。NestJS 預設可能有一些防護。
- **錯誤處理**: 不洩漏敏感系統細節。
- **速率限制 (Rate Limiting / Throttling)**: 防止暴力破解和 DoS 攻擊 (NestJS ThrottlerModule)。
- **安全 Headers**: `helmet` 或手動設置 (X-Content-Type-Options, X-Frame-Options, CSP 等)。

## 7. 待辦與重構中事項

本指南旨在描述目標狀態。許多功能的具體實現和細化仍在進行中，進度請參考：

- `apps/DevDoc/auth-system-refactor-todo.md`

開發團隊應持續更新該 TODO 文件，並在本指南的相關章節或附錄中註明已完成/進行中/待規劃的狀態，以確保所有人對當前系統能力和未來方向有一致的理解。

## 8. 附錄 (可選)

- 詳細的 API 端點權限需求表 (可從 `default-roles-permissions-map.md` 的前端路由權限表擴展)。
- 更詳細的 CASL `conditions` 和 `fields` 使用範例。

---

確保所有身份驗證和權限相關功能完整實作，無 TODO 或未完成部分 (指最終交付狀態，開發過程中允許 TODO)。
本文檔應作為身份驗證與授權系統開發與維護的**唯一真實來源 (Single Source of Truth)**。
