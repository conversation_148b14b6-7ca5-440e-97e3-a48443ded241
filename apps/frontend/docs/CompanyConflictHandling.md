# 公司唯一性衝突處理系統

**建立日期**: 2025-01-27  
**版本**: 1.0  
**狀態**: ✅ 已完成

## 📋 功能概覽

當使用者在兩階段註冊流程中建立新公司時，如果提交的公司資訊與現有公司發生衝突（公司名稱重複或網域重複），系統會自動跳轉到專門的衝突處理頁面，讓使用者選擇合適的解決方案。

## 🏗️ 系統架構

### 前端組件

- **主頁面**: `apps/frontend/src/views/auth/CompanyConflictView.vue`
- **路由**: `/auth/company-conflict`
- **觸發來源**: `CreateCompanyView.vue` 在檢測到衝突時跳轉

### 後端 API

- **唯一性檢查**: `POST /api/auth/validate-company`
- **創建公司**: `POST /api/auth/stage-two/create-company`
- **申請加入**: `POST /api/auth/stage-two/join-company`

## 🔄 處理流程

### 1. 衝突檢測

```typescript
// 在 CreateCompanyView.vue 中
const uniquenessResult = await httpService.post("/api/auth/validate-company", {
  companyName: formData.name.trim(),
  subdomain: formData.domain.trim() || "",
});

if (!uniquenessResult.available) {
  // 跳轉到衝突處理頁面
  router.push({
    name: "company-conflict",
    query: {
      /* 衝突詳情 */
    },
  });
}
```

### 2. 衝突類型

- **公司名稱衝突** (`name`): 相同公司名稱已存在
- **網域衝突** (`domain`): 相同網域已被使用

### 3. 解決方案選項

#### 選項 A: 申請加入現有公司

- 顯示所有發生衝突的現有公司
- 允許使用者選擇要申請加入的公司
- 收集申請資訊：部門、職稱、申請訊息
- 提交申請並跳轉到等待審核頁面

#### 選項 B: 修改公司資訊

- 返回 `CreateCompanyView.vue`
- 預填原始資料供使用者修改
- 避免衝突後重新提交

#### 選項 C: 使用系統建議

- 自動生成替代的公司名稱
- 包含地區後綴、業務類型後綴、數字後綴
- 一鍵使用建議名稱創建公司

## 🎨 使用者介面設計

### 視覺特色

- **顏色主題**: 橙色到紅色漸層背景，象徵警告和注意
- **動態效果**: 衝突粒子動畫，營造動態感
- **佈局**: 左側說明區域 + 右側操作區域

### 互動元素

- **衝突詳情卡片**: 顯示具體的衝突類型和現有公司資訊
- **選項卡片**: 可點擊的解決方案選項，支援單選
- **表單展開**: 選擇申請加入時顯示額外申請資訊表單
- **建議列表**: 系統生成的替代方案列表

## 📝 資料流轉

### URL 參數傳遞

```typescript
interface ConflictPageQuery {
  token: string; // stageOneToken
  conflicts: string; // JSON 編碼的衝突詳情
  original: string; // JSON 編碼的原始公司資料
  suggestions: string; // JSON 編碼的建議方案
}
```

### 衝突資料結構

```typescript
interface ConflictInfo {
  type: "name" | "domain";
  message: string;
  existingCompany: {
    id: string;
    name: string;
    domain?: string | null;
  };
}
```

### 建議方案結構

```typescript
interface Suggestion {
  name: string;
  domain?: string;
}
```

## 🔧 技術實作細節

### 唯一性檢查邏輯

```typescript
// 在提交前檢查
const uniquenessResult = await httpService.post("/api/auth/validate-company", {
  companyName: formData.name.trim(),
  subdomain: formData.domain.trim() || "",
});

// 處理衝突
if (!uniquenessResult.available) {
  const suggestions = generateSuggestions(formData.name.trim());
  // 跳轉邏輯...
}
```

### 建議生成算法

```typescript
function generateSuggestions(originalName: string) {
  return [
    { name: `${originalName} 台灣` }, // 地區後綴
    { name: `${originalName} 設計` }, // 業務類型後綴
    { name: `${originalName} 2024` }, // 年份後綴
  ];
}
```

### 錯誤處理

- **前端驗證**: 檢查必要參數和資料格式
- **API 錯誤**: 統一使用 `notification.toast.error()` 顯示錯誤
- **回退機制**: 資料解析失敗時自動返回上一頁

## 🚀 部署狀態

### ✅ 已完成

- [x] 前端頁面實作 (`CompanyConflictView.vue`)
- [x] 路由配置 (`/auth/company-conflict`)
- [x] 建立公司流程整合 (`CreateCompanyView.vue`)
- [x] 唯一性檢查 API 整合
- [x] 三種解決方案的完整實作
- [x] 錯誤處理和使用者體驗優化
- [x] 編譯測試通過

### 🔄 待後端完善

- [ ] 後端 `validateCompanyUniqueness` 返回完整衝突詳情
- [ ] Email 驗證流程 (EmailVerificationView.vue 已準備就緒)

## 📋 測試場景

### 基本流程測試

1. 使用者填寫公司資訊
2. 提交時檢測到名稱衝突
3. 自動跳轉到衝突處理頁面
4. 選擇申請加入現有公司
5. 填寫申請資訊並提交
6. 跳轉到等待審核頁面

### 邊界案例測試

- 同時有名稱和網域衝突
- 沒有衝突資料時的錯誤處理
- 網路請求失敗時的重試機制
- 建議方案的生成和選擇

## 🎯 未來擴展

### 可能的改進方向

- **智能建議**: 基於 AI 的更智能公司名稱建議
- **批量衝突**: 處理多個衝突的批量解決方案
- **即時檢查**: 在輸入時即時檢查唯一性
- **歷史記錄**: 保存使用者的衝突處理歷史

---

**實作完成**: 2025-01-27  
**負責開發**: AI Assistant  
**相關文件**: `LoginSystemRefactorTODO.md`
