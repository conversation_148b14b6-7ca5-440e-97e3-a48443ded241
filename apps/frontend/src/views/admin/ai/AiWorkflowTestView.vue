<template>
  <div class="container py-6 space-y-6">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold tracking-tight">AI 工作流程測試</h1>
        <p class="text-muted-foreground">
          測試和驗證 {{ workflow?.name || "載入中..." }} 工作流程
        </p>
      </div>
      <div class="flex items-center space-x-3">
        <Button @click="navigateToWorkflows" variant="outline" size="sm">
          <ArrowLeft class="h-4 w-4 mr-2" />
          返回列表
        </Button>
        <Button @click="navigateToEditor" variant="outline" size="sm">
          <Pencil class="h-4 w-4 mr-2" />
          編輯工作流程
        </Button>
        <Button
          @click="validateWorkflow"
          variant="outline"
          size="sm"
          :disabled="validating"
        >
          <CheckCircle class="h-4 w-4 mr-2" />
          驗證工作流程
        </Button>
      </div>
    </div>

    <!-- 測試面板 -->
    <Card>
      <CardHeader>
        <CardTitle>測試工作流程</CardTitle>
        <CardDescription>設定輸入參數並測試工作流程的執行結果</CardDescription>
      </CardHeader>
      <CardContent>
        <div class="flex gap-4">
          <!-- 輸入面板 -->
          <div class="flex-1 space-y-3">
            <Label>輸入數據 (JSON 格式)</Label>
            <Textarea
              v-model="inputData"
              rows="10"
              placeholder='{"message": "你好，AI"}'
              class="font-mono text-sm"
            />
            <Button
              @click="executeTest"
              variant="default"
              :disabled="executing"
            >
              <Play class="h-4 w-4 mr-2" />
              {{ executing ? "執行中..." : "執行測試" }}
            </Button>
          </div>

          <!-- 輸出面板 -->
          <div class="flex-1 space-y-3">
            <Label>執行結果</Label>
            <div
              class="min-h-[258px] border rounded-md p-3 overflow-auto bg-muted font-mono text-sm whitespace-pre-wrap"
            >
              <div
                v-if="executing"
                class="flex justify-center items-center h-full"
              >
                <div
                  class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"
                ></div>
              </div>
              <div
                v-else-if="executionError"
                class="text-destructive whitespace-pre-wrap"
              >
                {{ executionError }}
              </div>
              <pre v-else-if="executionResult" class="text-sm">{{
                formatJson(executionResult)
              }}</pre>
              <div
                v-else
                class="text-muted-foreground flex justify-center items-center h-full"
              >
                點擊「執行測試」按鈕來測試工作流程
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 執行歷史 -->
    <Card>
      <CardHeader>
        <CardTitle>執行歷史</CardTitle>
        <CardDescription>最近的工作流程執行記錄</CardDescription>
      </CardHeader>
      <CardContent>
        <div v-if="loadingHistory" class="py-8 flex justify-center">
          <div
            class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"
          ></div>
        </div>
        <div
          v-else-if="executionHistory.length === 0"
          class="py-8 text-center text-muted-foreground"
        >
          沒有執行記錄
        </div>
        <div v-else class="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>執行時間</TableHead>
                <TableHead>狀態</TableHead>
                <TableHead>執行時長</TableHead>
                <TableHead>輸入</TableHead>
                <TableHead>輸出</TableHead>
                <TableHead class="w-[100px]">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow
                v-for="execution in executionHistory"
                :key="execution.id"
              >
                <TableCell>{{ formatDate(execution.created_at) }}</TableCell>
                <TableCell>
                  <Badge :variant="getStatusVariant(execution.status)">
                    {{ getStatusText(execution.status) }}
                  </Badge>
                </TableCell>
                <TableCell>{{
                  execution.execution_time
                    ? `${execution.execution_time}ms`
                    : "---"
                }}</TableCell>
                <TableCell class="max-w-[200px] truncate font-mono text-xs">
                  {{ JSON.stringify(execution.input_data) }}
                </TableCell>
                <TableCell class="max-w-[200px] truncate font-mono text-xs">
                  {{
                    execution.output_data
                      ? JSON.stringify(execution.output_data)
                      : "---"
                  }}
                </TableCell>
                <TableCell>
                  <Button
                    @click="viewExecutionDetails(execution.id)"
                    variant="ghost"
                    size="sm"
                  >
                    <Eye class="h-4 w-4" />
                  </Button>
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>

    <!-- 驗證結果對話框 -->
    <Dialog v-model:open="showValidationDialog">
      <DialogContent>
        <DialogHeader>
          <DialogTitle>工作流程驗證結果</DialogTitle>
          <DialogDescription>
            驗證工作流程的結構和配置是否正確
          </DialogDescription>
        </DialogHeader>
        <div v-if="validating" class="py-6 flex justify-center">
          <div
            class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"
          ></div>
        </div>
        <div v-else-if="validationResult">
          <div v-if="validationResult.valid" class="py-6 text-center">
            <CheckCircle class="h-12 w-12 mx-auto text-green-500 mb-3" />
            <p class="text-lg font-medium text-green-600">工作流程驗證通過</p>
            <p class="text-muted-foreground">所有節點和連接都配置正確</p>
          </div>
          <div v-else class="py-6">
            <Alert variant="destructive" class="mb-4">
              <AlertCircle class="h-4 w-4 mr-2" />
              <AlertTitle>工作流程驗證失敗</AlertTitle>
              <AlertDescription> 發現以下問題: </AlertDescription>
            </Alert>
            <ul class="list-disc pl-6 space-y-2">
              <li
                v-for="(issue, index) in validationResult.issues"
                :key="index"
                class="text-sm"
              >
                {{ issue }}
              </li>
            </ul>
          </div>
        </div>
        <DialogFooter>
          <Button @click="showValidationDialog = false">關閉</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>

    <!-- 執行詳情對話框 -->
    <Dialog v-model:open="showExecutionDialog">
      <DialogContent class="max-w-3xl">
        <DialogHeader>
          <DialogTitle>執行詳情</DialogTitle>
          <DialogDescription> 查看工作流程執行的詳細信息 </DialogDescription>
        </DialogHeader>
        <div v-if="loadingExecution" class="py-6 flex justify-center">
          <div
            class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"
          ></div>
        </div>
        <div v-else-if="selectedExecution" class="space-y-4">
          <!-- 執行資訊 -->
          <div class="grid grid-cols-2 gap-4">
            <div>
              <Label>執行 ID</Label>
              <div class="p-2 bg-muted rounded-md text-xs font-mono">
                {{ selectedExecution.id }}
              </div>
            </div>
            <div>
              <Label>狀態</Label>
              <div class="p-2">
                <Badge :variant="getStatusVariant(selectedExecution.status)">
                  {{ getStatusText(selectedExecution.status) }}
                </Badge>
              </div>
            </div>
            <div>
              <Label>開始時間</Label>
              <div class="p-2">
                {{ formatDate(selectedExecution.created_at) }}
              </div>
            </div>
            <div>
              <Label>執行時間</Label>
              <div class="p-2">
                {{
                  selectedExecution.execution_time
                    ? `${selectedExecution.execution_time}ms`
                    : "---"
                }}
              </div>
            </div>
          </div>

          <!-- 輸入輸出 -->
          <div class="grid grid-cols-2 gap-4">
            <div>
              <Label>輸入數據</Label>
              <div
                class="p-2 bg-muted rounded-md font-mono text-xs overflow-auto max-h-[200px]"
              >
                <pre>{{ formatJson(selectedExecution.input_data) }}</pre>
              </div>
            </div>
            <div>
              <Label>輸出結果</Label>
              <div
                class="p-2 bg-muted rounded-md font-mono text-xs overflow-auto max-h-[200px]"
              >
                <pre v-if="selectedExecution.output_data">{{
                  formatJson(selectedExecution.output_data)
                }}</pre>
                <div
                  v-else-if="selectedExecution.error"
                  class="text-destructive"
                >
                  {{ selectedExecution.error }}
                </div>
                <div v-else class="text-muted-foreground">無輸出結果</div>
              </div>
            </div>
          </div>
        </div>
        <DialogFooter>
          <Button @click="showExecutionDialog = false">關閉</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from "vue";
import { useRoute, useRouter } from "vue-router";
import {
  ArrowLeft,
  Pencil,
  CheckCircle,
  Play,
  Eye,
  AlertCircle,
} from "lucide-vue-next";
import {
  useAiWorkflowsApi,
  type WorkflowExecution,
  type AiWorkflow,
} from "@/composables/useAiWorkflowsApi";
import { useNotification } from "@/composables/shared/useNotification";
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Table, TableHeader, TableBody, TableHead, TableCell, TableRow } from "@/components/ui/table";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert";

// 組件狀態
const route = useRoute();
const router = useRouter();
const notification = useNotification();
const workflowId = computed(() => route.params.id as string);

const {
  getWorkflow,
  executeWorkflow,
  getWorkflowExecutionHistory,
  getWorkflowExecution,
  validateWorkflow: apiValidateWorkflow,
  loading,
} = useAiWorkflowsApi();

// 工作流程數據
const workflow = ref<AiWorkflow | null>(null);
const inputData = ref('{\n  "message": "你好，AI"\n}');
const executing = ref(false);
const executionResult = ref<any>(null);
const executionError = ref<string | null>(null);

// 執行歷史
const executionHistory = ref<WorkflowExecution[]>([]);
const loadingHistory = ref(false);
const selectedExecution = ref<WorkflowExecution | null>(null);
const loadingExecution = ref(false);

// 驗證相關
const validating = ref(false);
const validationResult = ref<{ valid: boolean; issues?: string[] } | null>(
  null
);
const showValidationDialog = ref(false);
const showExecutionDialog = ref(false);

// 載入工作流程資料
const loadWorkflow = async () => {
  try {
    workflow.value = await getWorkflow(workflowId.value);
    loadExecutionHistory();
  } catch (error) {
    console.error("Failed to load workflow:", error);
    notification.toast.error("無法載入工作流程");
  }
};

// 載入執行歷史
const loadExecutionHistory = async () => {
  loadingHistory.value = true;
  try {
    const history = await getWorkflowExecutionHistory(workflowId.value);
    executionHistory.value = history.executions;
  } catch (error) {
    console.error("Failed to load execution history:", error);
    notification.toast.error("無法載入執行歷史");
  } finally {
    loadingHistory.value = false;
  }
};

// 執行測試
const executeTest = async () => {
  executing.value = true;
  executionResult.value = null;
  executionError.value = null;

  try {
    // 解析輸入 JSON
    let inputDataObj;
    try {
      inputDataObj = JSON.parse(inputData.value);
    } catch (e) {
      executionError.value = "輸入資料格式錯誤，請確保是有效的 JSON";
      return;
    }

    // 執行工作流程
    const result = await executeWorkflow(workflowId.value, inputDataObj);
    executionResult.value = result;

    // 重新載入執行歷史
    loadExecutionHistory();
  } catch (error: any) {
    console.error("Failed to execute workflow:", error);
    executionError.value = `執行錯誤: ${error.message || "未知錯誤"}`;
  } finally {
    executing.value = false;
  }
};

// 驗證工作流程
const validateWorkflow = async () => {
  validating.value = true;
  validationResult.value = null;
  showValidationDialog.value = true;

  try {
    validationResult.value = await apiValidateWorkflow(workflowId.value);
  } catch (error) {
    console.error("Failed to validate workflow:", error);
    notification.toast.error("工作流程驗證失敗");
  } finally {
    validating.value = false;
  }
};

// 查看執行詳情
const viewExecutionDetails = async (executionId: string) => {
  loadingExecution.value = true;
  selectedExecution.value = null;
  showExecutionDialog.value = true;

  try {
    selectedExecution.value = await getWorkflowExecution(executionId);
  } catch (error) {
    console.error("Failed to load execution details:", error);
    notification.toast.error("無法載入執行詳情");
  } finally {
    loadingExecution.value = false;
  }
};

// 導航到工作流程列表
const navigateToWorkflows = () => {
  router.push("/admin/ai-workflows");
};

// 導航到工作流程編輯器
const navigateToEditor = () => {
  router.push(`/admin/ai-creator-studio?workflowId=${workflowId.value}`);
};

// 格式化日期
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleString();
};

// 格式化 JSON
const formatJson = (json: any): string => {
  return JSON.stringify(json, null, 2);
};

// 獲取狀態變體
const getStatusVariant = (status: string) => {
  switch (status) {
    case "COMPLETED":
      return "success";
    case "RUNNING":
      return "warning";
    case "PENDING":
      return "secondary";
    case "FAILED":
      return "destructive";
    default:
      return "default";
  }
};

// 獲取狀態文字
const getStatusText = (status: string) => {
  switch (status) {
    case "COMPLETED":
      return "已完成";
    case "RUNNING":
      return "執行中";
    case "PENDING":
      return "等待中";
    case "FAILED":
      return "失敗";
    default:
      return status;
  }
};

// 生命週期鉤子
onMounted(() => {
  loadWorkflow();
});
</script>
