<template>
  <div class="space-y-4">
    <!-- Table Header with Search and Actions -->
    <div v-if="showHeader" class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <div class="flex items-center space-x-2">
        <h3 v-if="title" class="text-lg font-semibold">{{ title }}</h3>
        <Badge v-if="totalCount !== undefined" variant="secondary">
          {{ totalCount }} 項目
        </Badge>
      </div>
      
      <div class="flex flex-col sm:flex-row items-stretch sm:items-center gap-2">
        <!-- Search Input -->
        <div v-if="searchable" class="relative">
          <Search class="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            v-model="searchQuery"
            placeholder="搜尋..."
            class="pl-8 w-full sm:w-64"
            @input="handleSearch"
          />
        </div>
        
        <!-- Action Slot -->
        <slot name="actions" />
      </div>
    </div>

    <!-- Table -->
    <div class="rounded-md border overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead
              v-for="column in columns"
              :key="column.key"
              :class="column.headerClass"
            >
              <div class="flex items-center space-x-2">
                <span>{{ column.title }}</span>
                <Button
                  v-if="column.sortable"
                  variant="ghost"
                  size="sm"
                  class="h-4 w-4 p-0"
                  @click="handleSort(column.key)"
                >
                  <ArrowUpDown class="h-3 w-3" />
                </Button>
              </div>
            </TableHead>
          </TableRow>
        </TableHeader>
        
        <TableBody>
          <TableRow v-if="loading">
            <TableCell :colspan="columns.length" class="text-center py-8">
              <LoadingSpinner text="載入中..." centered />
            </TableCell>
          </TableRow>
          
          <TableRow v-else-if="!data.length">
            <TableCell :colspan="columns.length" class="text-center py-8">
              <EmptyState
                title="沒有數據"
                description="目前沒有可顯示的項目"
                size="sm"
              />
            </TableCell>
          </TableRow>
          
          <TableRow
            v-else
            v-for="(item, index) in data"
            :key="getRowKey(item, index)"
            :class="rowClass?.(item, index)"
          >
            <TableCell
              v-for="column in columns"
              :key="column.key"
              :class="column.cellClass"
            >
              <slot
                :name="`cell-${column.key}`"
                :item="item"
                :value="getNestedValue(item, column.key)"
                :index="index"
              >
                {{ formatCellValue(item, column) }}
              </slot>
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </div>

    <!-- Pagination -->
    <div v-if="pagination && totalCount !== undefined" class="flex items-center justify-between">
      <div class="text-sm text-muted-foreground">
        顯示 {{ (currentPage - 1) * pageSize + 1 }} 到 {{ Math.min(currentPage * pageSize, totalCount) }} 項，
        共 {{ totalCount }} 項
      </div>
      
      <Pagination
        :total="totalCount"
        :page-size="pageSize"
        :current-page="currentPage"
        @update:current-page="handlePageChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Search, ArrowUpDown } from 'lucide-vue-next'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Pagination } from '@/components/ui/pagination'
import LoadingSpinner from './LoadingSpinner.vue'
import EmptyState from './EmptyState.vue'

interface Column {
  key: string
  title: string
  sortable?: boolean
  formatter?: (value: any, item: any) => string
  headerClass?: string
  cellClass?: string
}

interface Props {
  data: any[]
  columns: Column[]
  loading?: boolean
  title?: string
  searchable?: boolean
  pagination?: boolean
  totalCount?: number
  currentPage?: number
  pageSize?: number
  showHeader?: boolean
  rowKey?: string | ((item: any, index: number) => string)
  rowClass?: (item: any, index: number) => string
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  searchable: false,
  pagination: false,
  currentPage: 1,
  pageSize: 10,
  showHeader: true,
  rowKey: 'id'
})

const emit = defineEmits<{
  search: [query: string]
  sort: [key: string, direction: 'asc' | 'desc']
  'page-change': [page: number]
}>()

const searchQuery = ref('')

const handleSearch = () => {
  emit('search', searchQuery.value)
}

const handleSort = (key: string) => {
  // Simple toggle between asc/desc
  emit('sort', key, 'asc') // You can implement more sophisticated sorting logic
}

const handlePageChange = (page: number) => {
  emit('page-change', page)
}

const getRowKey = (item: any, index: number) => {
  if (typeof props.rowKey === 'function') {
    return props.rowKey(item, index)
  }
  return item[props.rowKey] || index
}

const getNestedValue = (obj: any, path: string) => {
  return path.split('.').reduce((current, key) => current?.[key], obj)
}

const formatCellValue = (item: any, column: Column) => {
  const value = getNestedValue(item, column.key)
  if (column.formatter) {
    return column.formatter(value, item)
  }
  return value
}
</script> 