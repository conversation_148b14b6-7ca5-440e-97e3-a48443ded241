<template>
  <div :class="containerClass">
    <div class="flex items-center justify-between">
      <!-- Left side: Title and breadcrumb -->
      <div class="min-w-0 flex-1">
        <!-- Breadcrumb -->
        <nav v-if="breadcrumbs?.length" class="mb-2">
          <Breadcrumb>
            <BreadcrumbList>
              <template v-for="(crumb, index) in breadcrumbs" :key="index">
                <BreadcrumbItem>
                  <BreadcrumbLink
                    v-if="crumb.href && index < breadcrumbs.length - 1"
                    :href="crumb.href"
                    class="text-muted-foreground hover:text-foreground"
                  >
                    {{ crumb.label }}
                  </BreadcrumbLink>
                  <BreadcrumbPage v-else>
                    {{ crumb.label }}
                  </BreadcrumbPage>
                </BreadcrumbItem>
                <BreadcrumbSeparator v-if="index < breadcrumbs.length - 1" />
              </template>
            </BreadcrumbList>
          </Breadcrumb>
        </nav>

        <!-- Title Section -->
        <div class="flex items-center gap-3">
          <div v-if="icon || $slots.icon" class="flex-shrink-0">
            <slot name="icon">
              <component :is="icon" :class="iconClass" />
            </slot>
          </div>
          
          <div class="min-w-0 flex-1">
            <h1 :class="titleClass">
              {{ title }}
            </h1>
            <p v-if="description" :class="descriptionClass">
              {{ description }}
            </p>
          </div>
        </div>
      </div>

      <!-- Right side: Actions -->
      <div v-if="$slots.actions" class="flex-shrink-0 ml-4">
        <slot name="actions" />
      </div>
    </div>

    <!-- Bottom content -->
    <div v-if="$slots.default" class="mt-6">
      <slot />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { cn } from '@/lib/utils'
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb'

interface BreadcrumbItem {
  label: string
  href?: string
}

interface Props {
  title: string
  description?: string
  icon?: any
  breadcrumbs?: BreadcrumbItem[]
  size?: 'sm' | 'md' | 'lg'
  class?: string
}

const props = withDefaults(defineProps<Props>(), {
  size: 'md'
})

const containerClass = computed(() => cn(
  'border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60',
  {
    'px-4 py-4': props.size === 'sm',
    'px-6 py-6': props.size === 'md',
    'px-8 py-8': props.size === 'lg'
  },
  props.class
))

const iconClass = computed(() => cn(
  'text-muted-foreground',
  {
    'h-5 w-5': props.size === 'sm',
    'h-6 w-6': props.size === 'md',
    'h-7 w-7': props.size === 'lg'
  }
))

const titleClass = computed(() => cn(
  'font-semibold tracking-tight text-foreground',
  {
    'text-xl': props.size === 'sm',
    'text-2xl': props.size === 'md',
    'text-3xl': props.size === 'lg'
  }
))

const descriptionClass = computed(() => cn(
  'text-muted-foreground mt-1',
  {
    'text-sm': props.size === 'sm',
    'text-base': props.size === 'md',
    'text-lg': props.size === 'lg'
  }
))
</script> 