<template>
  <div :class="containerClass">
    <div class="flex flex-col items-center justify-center text-center">
      <!-- Icon -->
      <div v-if="icon || $slots.icon" :class="iconContainerClass">
        <slot name="icon">
          <component :is="icon" :class="iconClass" />
        </slot>
      </div>

      <!-- Title -->
      <h3 v-if="title" :class="titleClass">
        {{ title }}
      </h3>

      <!-- Description -->
      <p v-if="description" :class="descriptionClass">
        {{ description }}
      </p>

      <!-- Action Button -->
      <div v-if="actionText || $slots.action" class="mt-6">
        <slot name="action">
          <Button
            v-if="actionText"
            :variant="actionVariant"
            @click="$emit('action')"
          >
            {{ actionText }}
          </Button>
        </slot>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import type { Component } from 'vue'

interface Props {
  title?: string
  description?: string
  icon?: Component
  actionText?: string
  actionVariant?: 'default' | 'outline' | 'secondary' | 'ghost'
  size?: 'sm' | 'md' | 'lg'
  class?: string
}

const props = withDefaults(defineProps<Props>(), {
  actionVariant: 'default',
  size: 'md'
})

defineEmits<{
  action: []
}>()

const containerClass = computed(() => cn(
  'flex items-center justify-center',
  {
    'min-h-[200px] p-6': props.size === 'sm',
    'min-h-[300px] p-8': props.size === 'md',
    'min-h-[400px] p-12': props.size === 'lg'
  },
  props.class
))

const iconContainerClass = computed(() => cn(
  'flex items-center justify-center rounded-full bg-muted mb-4',
  {
    'h-12 w-12': props.size === 'sm',
    'h-16 w-16': props.size === 'md',
    'h-20 w-20': props.size === 'lg'
  }
))

const iconClass = computed(() => cn(
  'text-muted-foreground',
  {
    'h-6 w-6': props.size === 'sm',
    'h-8 w-8': props.size === 'md',
    'h-10 w-10': props.size === 'lg'
  }
))

const titleClass = computed(() => cn(
  'font-semibold text-foreground mb-2',
  {
    'text-lg': props.size === 'sm',
    'text-xl': props.size === 'md',
    'text-2xl': props.size === 'lg'
  }
))

const descriptionClass = computed(() => cn(
  'text-muted-foreground max-w-sm',
  {
    'text-sm': props.size === 'sm',
    'text-base': props.size === 'md',
    'text-lg': props.size === 'lg'
  }
))
</script> 