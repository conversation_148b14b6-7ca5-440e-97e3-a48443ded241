<template>
  <Badge :variant="badgeVariant" :class="badgeClass">
    <div v-if="showDot" :class="dotClass" />
    <slot>{{ label }}</slot>
  </Badge>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { cn } from '@/lib/utils'
import { Badge } from '@/components/ui/badge'

interface Props {
  status: 'success' | 'warning' | 'error' | 'info' | 'pending' | 'inactive'
  label?: string
  showDot?: boolean
  size?: 'sm' | 'md' | 'lg'
  class?: string
}

const props = withDefaults(defineProps<Props>(), {
  showDot: false,
  size: 'md'
})

const badgeVariant = computed(() => {
  const variantMap = {
    success: 'default' as const,
    warning: 'secondary' as const,
    error: 'destructive' as const,
    info: 'outline' as const,
    pending: 'secondary' as const,
    inactive: 'outline' as const
  }
  return variantMap[props.status]
})

const badgeClass = computed(() => cn(
  'inline-flex items-center gap-1.5',
  {
    // Status-specific colors
    'bg-green-100 text-green-800 hover:bg-green-100 dark:bg-green-900/20 dark:text-green-400': 
      props.status === 'success',
    'bg-yellow-100 text-yellow-800 hover:bg-yellow-100 dark:bg-yellow-900/20 dark:text-yellow-400': 
      props.status === 'warning',
    'bg-red-100 text-red-800 hover:bg-red-100 dark:bg-red-900/20 dark:text-red-400': 
      props.status === 'error',
    'bg-blue-100 text-blue-800 hover:bg-blue-100 dark:bg-blue-900/20 dark:text-blue-400': 
      props.status === 'info',
    'bg-orange-100 text-orange-800 hover:bg-orange-100 dark:bg-orange-900/20 dark:text-orange-400': 
      props.status === 'pending',
    'bg-gray-100 text-gray-800 hover:bg-gray-100 dark:bg-gray-900/20 dark:text-gray-400': 
      props.status === 'inactive',
    
    // Size variants
    'text-xs px-2 py-0.5': props.size === 'sm',
    'text-sm px-2.5 py-0.5': props.size === 'md',
    'text-base px-3 py-1': props.size === 'lg'
  },
  props.class
))

const dotClass = computed(() => cn(
  'rounded-full',
  {
    // Size variants
    'h-1.5 w-1.5': props.size === 'sm',
    'h-2 w-2': props.size === 'md',
    'h-2.5 w-2.5': props.size === 'lg',
    
    // Status colors for dot
    'bg-green-600 dark:bg-green-400': props.status === 'success',
    'bg-yellow-600 dark:bg-yellow-400': props.status === 'warning',
    'bg-red-600 dark:bg-red-400': props.status === 'error',
    'bg-blue-600 dark:bg-blue-400': props.status === 'info',
    'bg-orange-600 dark:bg-orange-400': props.status === 'pending',
    'bg-gray-600 dark:bg-gray-400': props.status === 'inactive'
  }
))
</script> 