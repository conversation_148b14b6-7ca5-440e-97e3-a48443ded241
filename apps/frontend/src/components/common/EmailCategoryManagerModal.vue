<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { useNotification } from '@/composables/shared/useNotification'

interface EmailCategory {
  id: string
  name: string
  description?: string
}

const categories = ref<EmailCategory[]>([])
const isLoading = ref(false)
const editingId = ref<string | null>(null)
const form = ref<Partial<EmailCategory>>({ name: '', description: '' })
const notification = useNotification()

const emit = defineEmits(['close'])

const fetchCategories = async () => {
  isLoading.value = true
  try {
    const res = await fetch('/api/email-categories')
    categories.value = await res.json()
  } catch {
    notification.toast.error('載入分類失敗')
  } finally {
    isLoading.value = false
  }
}

const handleEdit = (cat: EmailCategory) => {
  editingId.value = cat.id
  form.value = { ...cat }
}
const handleCancel = () => {
  editingId.value = null
  form.value = { name: '', description: '' }
}
const handleSave = async () => {
  if (!form.value.name) return notification.toast.error('分類名稱必填')
  try {
    if (editingId.value) {
      await fetch(`/api/email-categories/${editingId.value}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(form.value)
      })
      notification.toast.success('分類已更新')
    } else {
      await fetch('/api/email-categories', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(form.value)
      })
      notification.toast.success('分類已新增')
    }
    await fetchCategories()
    handleCancel()
  } catch {
    notification.toast.error('儲存失敗')
  }
}
const handleDelete = async (id: string) => {
  if (!confirm('確定要刪除此分類？')) return
  try {
    await fetch(`/api/email-categories/${id}`, { method: 'DELETE' })
    notification.toast.success('分類已刪除')
    await fetchCategories()
  } catch {
    notification.toast.error('刪除失敗')
  }
}
onMounted(fetchCategories)
</script>

<template>
  <div class="max-w-xl mx-auto p-6">
    <div class="flex items-center justify-between mb-4">
      <h2 class="text-xl font-bold">郵件模板分類管理</h2>
      <Button variant="ghost" @click="$emit('close')">關閉</Button>
    </div>
    <form @submit.prevent="handleSave" class="space-y-2 mb-6">
      <div>
        <Label>分類名稱</Label>
        <Input v-model="form.name" required placeholder="如：邀請、通知" />
      </div>
      <div>
        <Label>描述（可選）</Label>
        <Input v-model="form.description" placeholder="分類用途說明" />
      </div>
      <div class="flex gap-2">
        <Button type="submit">{{ editingId ? '儲存變更' : '新增分類' }}</Button>
        <Button type="button" variant="outline" v-if="editingId" @click="handleCancel">取消</Button>
      </div>
    </form>
    <div class="border rounded divide-y">
      <div v-for="cat in categories" :key="cat.id" class="flex items-center gap-2 p-3">
        <div class="flex-1">
          <div class="font-semibold">{{ cat.name }}</div>
          <div class="text-xs text-muted-foreground">{{ cat.description }}</div>
        </div>
        <Button size="sm" variant="outline" @click="handleEdit(cat)">編輯</Button>
        <Button size="sm" variant="destructive" @click="handleDelete(cat.id)">刪除</Button>
      </div>
    </div>
  </div>
</template> 