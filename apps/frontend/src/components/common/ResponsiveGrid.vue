<template>
  <div :class="gridClass">
    <slot />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { cn } from '@/lib/utils'
import { useBreakpoints, type Breakpoint } from '@/composables/useBreakpoints'

interface Props {
  cols?: Partial<Record<Breakpoint, number>>
  gap?: Partial<Record<Breakpoint, string>>
  rows?: Partial<Record<Breakpoint, number>>
  autoFit?: boolean
  minItemWidth?: string
  class?: string
}

const props = withDefaults(defineProps<Props>(), {
  cols: () => ({ xs: 1, sm: 2, md: 3, lg: 4 }),
  gap: () => ({ xs: '4', sm: '4', md: '6', lg: '6' }),
  autoFit: false,
  minItemWidth: '250px'
})

const { getResponsiveClass, getGridCols } = useBreakpoints()

const gridClass = computed(() => cn(
  'grid',
  
  // 自動適應網格
  props.autoFit ? [
    `grid-cols-[repeat(auto-fit,minmax(${props.minItemWidth},1fr))]`
  ] : [
    // 響應式列數
    getResponsiveClass(
      Object.fromEntries(
        Object.entries(props.cols || {}).map(([bp, value]) => [bp, `grid-cols-${value}`])
      )
    )
  ],
  
  // 響應式行數
  props.rows ? getResponsiveClass(
    Object.fromEntries(
      Object.entries(props.rows).map(([bp, value]) => [bp, `grid-rows-${value}`])
    )
  ) : '',
  
  // 響應式間距
  getResponsiveClass(
    Object.fromEntries(
      Object.entries(props.gap || {}).map(([bp, value]) => [bp, `gap-${value}`])
    )
  ),
  
  props.class
))

// 導出當前列數供父組件使用
const currentCols = computed(() => getGridCols(props.cols || {}))

defineExpose({
  currentCols
})
</script> 