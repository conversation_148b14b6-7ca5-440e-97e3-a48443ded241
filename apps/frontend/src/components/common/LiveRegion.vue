<template>
  <!-- 緊急公告區域 -->
  <div
    aria-live="assertive"
    aria-atomic="true"
    class="sr-only"
    role="status"
  >
    <div v-for="(message, index) in announcements" :key="`urgent-${index}`">
      {{ message }}
    </div>
  </div>

  <!-- 禮貌公告區域 -->
  <div
    aria-live="polite"
    aria-atomic="true"
    class="sr-only"
    role="status"
  >
    <div v-for="(message, index) in politeAnnouncements" :key="`polite-${index}`">
      {{ message }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAnnouncer } from '@/composables/useAnnouncer'

const { announcements, politeAnnouncements } = useAnnouncer()
</script> 