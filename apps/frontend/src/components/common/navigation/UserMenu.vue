<template>
  <DropdownMenu>
    <DropdownMenuTrigger class="focus:outline-none">
      <div class="flex items-center gap-2">
        <Avatar class="h-8 w-8">
          <AvatarImage :src="user?.avatar || ''" :alt="user?.name || ''" />
          <AvatarFallback>{{ user?.name?.charAt(0) || 'U' }}</AvatarFallback>
        </Avatar>
        <span class="text-sm font-medium hidden md:inline-block">{{ user?.name || '使用者' }}</span>
      </div>
    </DropdownMenuTrigger>
    <DropdownMenuContent align="end" class="w-56">
      <DropdownMenuLabel>
        <div class="flex flex-col space-y-1">
          <p class="text-sm font-medium leading-none">{{ user?.name || '使用者' }}</p>
          <p class="text-xs leading-none text-muted-foreground">
            {{ user?.email || '' }}
          </p>
          <p class="text-xs leading-none text-muted-foreground">
            角色: {{ getUserRoleText(user?.role) }}
          </p>
        </div>
      </DropdownMenuLabel>
      <DropdownMenuSeparator />
      <!-- 區域切換選項 -->
      <DropdownMenuLabel>切換區域</DropdownMenuLabel>
      <DropdownMenuItem @click="router.push('/admin')">
        <div class="flex items-center">
          <LayoutDashboard class="mr-2 h-4 w-4" />
          <span>管理後台</span>
        </div>
      </DropdownMenuItem>
      <DropdownMenuItem @click="handleWorkspaceRedirect">
        <div class="flex items-center">
          <Briefcase class="mr-2 h-4 w-4" />
          <span>工作區域</span>
        </div>
      </DropdownMenuItem>
      <DropdownMenuItem v-if="isTenantAdmin" @click="router.push('/tenant-admin')">
        <div class="flex items-center">
          <Users class="mr-2 h-4 w-4" />
          <span>租戶管理</span>
        </div>
      </DropdownMenuItem>
      <DropdownMenuSeparator />
      <DropdownMenuItem @click="router.push('/profile')">
        <div class="flex items-center">
          <UserIcon class="mr-2 h-4 w-4" />
          <span>個人資料</span>
        </div>
      </DropdownMenuItem>
      <DropdownMenuItem @click="router.push('/settings')">
        <div class="flex items-center">
          <Settings class="mr-2 h-4 w-4" />
          <span>設定</span>
        </div>
      </DropdownMenuItem>
      <DropdownMenuSeparator />
      <DropdownMenuItem @click="handleLogout">
        <div class="flex items-center">
          <LogOut class="mr-2 h-4 w-4" />
          <span>登出</span>
        </div>
      </DropdownMenuItem>
    </DropdownMenuContent>
  </DropdownMenu>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useAuth } from '@horizai/auth';
import { useRouter } from 'vue-router';
import { useNotification } from '@/composables/shared/useNotification';
import { handleLogoutRedirect } from '@/utils/redirect';
import type { User } from '@horizai/auth';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  LayoutDashboard,
  Briefcase,
  Users,
  User as UserIcon,
  Settings,
  LogOut,
} from 'lucide-vue-next';

const auth = useAuth();
const router = useRouter();
const notification = useNotification();
const user = ref<User | null>(null);
const currentWorkspaceId = ref<string | null>(null);

// 計算屬性：判斷是否為租戶管理員
const isTenantAdmin = computed(() => {
  return user.value?.role === 'TENANT_ADMIN';
});

// 轉換角色顯示文字
const getUserRoleText = (role?: string) => {
  const roleMap: Record<string, string> = {
    SUPER_ADMIN: '超級管理員',
    SYSTEM_ADMIN: '系統管理員',
    TENANT_ADMIN: '租戶管理員',
    TENANT_USER: '租戶使用者',
  };
  return roleMap[role || ''] || '一般使用者';
};

// 處理工作區域跳轉
const handleWorkspaceRedirect = async () => {
  try {
    // 如果已有 currentWorkspaceId，直接跳轉
    if (currentWorkspaceId.value) {
      router.push(`/workspace/${currentWorkspaceId.value}/dashboard`);
      return;
    }

    // 如果沒有 currentWorkspaceId，先跳轉到工作區選擇頁面
    router.push('/workspace-select');
  } catch (error) {
    console.error('Workspace redirect error:', error);
    notification.toast.error('無法進入工作區域');
  }
};

const handleLogout = async () => {
  try {
    await auth.logout();
    // 使用改進的登出重定向函數
    const target = handleLogoutRedirect();
    router.push(target);
  } catch (error: any) {
    // 即使登出失敗，也要清除前端狀態並重定向
    console.error('登出失敗:', error);
    const target = handleLogoutRedirect();
    router.push(target);
    notification.toast.error(error.message || '登出失敗');
  }
};

onMounted(async () => {
  try {
    const userData = await auth.getUser();
    if (userData) {
      user.value = {
        ...userData,
        avatar: userData.avatar || '',
        role: userData.role || 'TENANT_USER',
        name: userData.name || `使用者${Math.random().toString(36).slice(2, 6)}`,
        email: userData.email || '',
      } as User;
    }
  } catch (error) {
    console.error('Failed to get user:', error);
    notification.toast.error('無法取得使用者資訊');
    user.value = {
      name: `訪客${Math.random().toString(36).slice(2, 6)}`,
      role: 'TENANT_USER',
      avatar: '',
      email: '',
    } as User;
  }
});
</script>
