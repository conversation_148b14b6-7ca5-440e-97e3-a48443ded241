<template>
  <Sidebar ref="sidebarRef" collapsible="icon">
    <!-- LOGO 區塊 -->
    <div
      class="h-12 flex items-center justify-center px-1.5 border-b border-border/50"
    >
      <!-- 收合狀態時顯示的LOGO圖示 -->
      <div
        class="w-8 h-8 flex items-center justify-center group-data-[collapsible=icon]:flex hidden"
      >
        <img
          src="@/assets/images/logo-icon.svg"
          alt="HorizAI Logo"
          class="w-6 h-6 hidden dark:block text-white"
        />
        <img
          src="@/assets/images/logo-icon.svg"
          alt="HorizAI Logo"
          class="w-6 h-6 block dark:hidden text-emerald-600"
        />
      </div>

      <!-- 展開狀態時顯示的完整LOGO -->
      <div
        class="h-6 w-full group-data-[collapsible=icon]:hidden flex items-center justify-center"
      >
        <img
          src="@/assets/images/logo-light.svg"
          alt="HorizAI Logo"
          class="h-full hidden dark:block"
        />
        <img
          src="@/assets/images/logo.svg"
          alt="HorizAI Logo"
          class="h-full block dark:hidden text-zinc-800"
        />
      </div>
    </div>

    <!-- 選單內容區域 -->
    <SidebarContent class="pt-1">
      <template v-for="(group, idx) in menu" :key="group.group">
        <SidebarGroup
          v-if="group.items.some((i) => i.show)"
          class="group-data-[collapsible=icon]:mt-0 mt-0"
        >
          <SidebarGroupLabel
            class="group-data-[collapsible=icon]:hidden px-3 text-xs font-medium text-muted-foreground mb-0.5"
          >
            {{ group.group }}
          </SidebarGroupLabel>
          <SidebarGroupContent
            class="group-data-[collapsible=icon]:space-y-0 group-data-[collapsible=icon]:pt-0 space-y-0"
          >
            <SidebarMenu>
              <SidebarMenuItem
                v-for="item in group.items.filter((i) => i.show)"
                :key="item.title"
                class="group-data-[collapsible=icon]:mb-0"
              >
                <TooltipProvider v-if="isIconMode">
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <SidebarMenuButton asChild :active="isActive(item.to)">
                        <router-link
                          :to="item.to"
                          class="flex items-center gap-2 w-full py-1.5 px-2"
                        >
                          <component
                            :is="item.icon"
                            class="h-4 w-4 flex-shrink-0"
                          />
                          <span
                            class="truncate group-data-[collapsible=icon]:hidden text-sm"
                            >{{ item.title }}</span
                          >
                        </router-link>
                      </SidebarMenuButton>
                    </TooltipTrigger>
                    <TooltipContent side="right" class="font-normal">
                      {{ item.title }}
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                <SidebarMenuButton v-else asChild :active="isActive(item.to)">
                  <router-link
                    :to="item.to"
                    class="flex items-center gap-2 w-full py-1.5 px-2"
                  >
                    <component :is="item.icon" class="h-4 w-4 flex-shrink-0" />
                    <span
                      class="truncate group-data-[collapsible=icon]:hidden text-sm"
                      >{{ item.title }}</span
                    >
                  </router-link>
                </SidebarMenuButton>
              </SidebarMenuItem>
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
        <SidebarSeparator
          v-if="idx < menu.length - 1"
          class="group-data-[collapsible=icon]:my-px my-px border-border/50"
        />
      </template>
    </SidebarContent>
  </Sidebar>
</template>

<script setup lang="ts">
import { usePermission } from "@/composables/admin/usePermission";
import { useRoute } from "vue-router";
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarSeparator,
} from "@/components/ui/sidebar";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  LayoutDashboard,
  Building2,
  Users as UsersIcon,
  FolderKanban,
  CreditCard,
  Receipt as ReceiptIcon,
  BrainCircuit,
  BotMessageSquare,
  Sparkles,
  Settings2,
  UserCog,
  Shield,
  Lock,
  WrenchIcon,
  FileText,
  RefreshCw,
  Workflow,
} from "lucide-vue-next";
import { ref, computed, onMounted, onBeforeUnmount } from "vue";
import { Actions, Subjects } from "@horizai/permissions";

const { can } = usePermission();
const route = useRoute();
const isSuperAdmin = computed(() => can(Actions.MANAGE, "all"));
const isSystemAdmin = computed(() => can(Actions.MANAGE, Subjects.SYSTEM));
function isActive(path: string) {
  return route.path === path;
}

const menu = [
  // 1. 概覽
  {
    group: "概覽",
    items: [
      {
        title: "儀表板",
        icon: LayoutDashboard,
        to: "/admin/dashboard",
        show: true,
      },
    ],
  },

  // 2. 租戶與使用者管理
  {
    group: "租戶與使用者",
    items: [
      { title: "租戶列表", icon: Building2, to: "/admin/tenants", show: true },
      {
        title: "租戶使用者",
        icon: UsersIcon,
        to: "/admin/tenant-users",
        show:
          isSuperAdmin.value ||
          isSystemAdmin.value ||
          can(Actions.MANAGE, Subjects.TENANT_USER),
      },
      {
        title: "工作區管理",
        icon: FolderKanban,
        to: "/admin/workspaces",
        show: true,
      },
    ],
  },

  // 3. 訂閱與收費
  {
    group: "訂閱管理",
    items: [
      {
        title: "訂閱方案",
        icon: CreditCard,
        to: "/admin/plans",
        show:
          isSuperAdmin.value ||
          isSystemAdmin.value ||
          can(Actions.MANAGE, Subjects.PLAN),
      },
      {
        title: "訂閱訂單",
        icon: ReceiptIcon,
        to: "/admin/orders",
        show:
          isSuperAdmin.value ||
          isSystemAdmin.value ||
          can(Actions.MANAGE, Subjects.ORDER),
      },
    ],
  },

  // 4. AI 功能管理
  {
    group: "AI 智慧應用",
    items: [
      {
        title: "AI應用管理",
        icon: Sparkles,
        to: "/admin/ai-settings",
        show:
          isSuperAdmin.value ||
          isSystemAdmin.value ||
          can(Actions.MANAGE, Subjects.SYSTEM_SETTINGS),
      },
      {
        title: "AI 工作流程管理",
        icon: BrainCircuit,
        to: "/admin/ai-workflows",
        show:
          isSuperAdmin.value ||
          isSystemAdmin.value ||
          can(Actions.MANAGE, Subjects.SYSTEM_SETTINGS),
      },
      {
        title: "AI 工作流程監控",
        icon: RefreshCw,
        to: "/admin/ai-workflows/monitor",
        show:
          isSuperAdmin.value ||
          isSystemAdmin.value ||
          can(Actions.MANAGE, Subjects.SYSTEM_SETTINGS),
      },
      {
        title: "AI 工作流程編輯器",
        icon: Workflow,
        to: "/admin/ai-creator-studio",
        show:
          isSuperAdmin.value ||
          isSystemAdmin.value ||
          can(Actions.MANAGE, Subjects.SYSTEM_SETTINGS),
      },
      {
        title: "Line 應用管理",
        icon: Settings2,
        to: "/admin/line-settings",
        show:
          isSuperAdmin.value ||
          isSystemAdmin.value ||
          can(Actions.MANAGE, Subjects.SYSTEM_SETTINGS),
      },
    ],
  },

  // 5. 系統設定
  {
    group: "系統管理",
    items: [
      {
        title: "系統使用者",
        icon: UserCog,
        to: "/admin/system-users",
        show:
          isSuperAdmin.value ||
          isSystemAdmin.value ||
          can(Actions.MANAGE, Subjects.SYSTEM_USER),
      },
      {
        title: "角色管理",
        icon: Shield,
        to: "/admin/roles",
        show:
          isSuperAdmin.value ||
          isSystemAdmin.value ||
          can(Actions.MANAGE, Subjects.ROLE),
      },
      {
        title: "權限設定",
        icon: Lock,
        to: "/admin/permissions",
        show:
          isSuperAdmin.value ||
          isSystemAdmin.value ||
          can(Actions.MANAGE, Subjects.ROLE),
      },
      {
        title: "權限同步",
        icon: RefreshCw,
        to: "/admin/permissions/sync",
        show:
          isSuperAdmin.value ||
          isSystemAdmin.value ||
          can(Actions.MANAGE, Subjects.PERMISSION),
      },
      {
        title: "系統設定",
        icon: WrenchIcon,
        to: "/admin/settings",
        show:
          isSuperAdmin.value ||
          isSystemAdmin.value ||
          can(Actions.MANAGE, Subjects.SYSTEM_SETTINGS),
      },
      {
        title: "系統日誌",
        icon: FileText,
        to: "/admin/system-logs",
        show:
          isSuperAdmin.value ||
          isSystemAdmin.value ||
          can(Actions.READ, Subjects.SYSTEM_LOG),
      },
    ],
  },
];

// 取得 Sidebar 是否為 icon 狀態
const sidebarRef = ref<any>(null);
const isIconMode = ref(false);

function updateIconMode() {
  const el = sidebarRef.value?.$el ?? sidebarRef.value;
  if (el && typeof el.getAttribute === "function") {
    isIconMode.value = el.getAttribute("data-collapsible") === "icon";
  }
}

onMounted(() => {
  updateIconMode();
  const el = sidebarRef.value?.$el ?? sidebarRef.value;
  if (el && typeof el.getAttribute === "function") {
    const observer = new MutationObserver(updateIconMode);
    observer.observe(el, {
      attributes: true,
      attributeFilter: ["data-collapsible"],
    });
    onBeforeUnmount(() => observer.disconnect());
  }
});
</script>

<style scoped>
:deep(svg) {
  color: currentColor;
}
</style>
