<template>
  <component
    :is="tag"
    :class="textClass"
  >
    <slot />
  </component>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { cn } from '@/lib/utils'
import { useBreakpoints, type Breakpoint } from '@/composables/useBreakpoints'

interface Props {
  tag?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'p' | 'span' | 'div'
  size?: Partial<Record<Breakpoint, string>>
  weight?: Partial<Record<Breakpoint, string>>
  color?: string
  align?: Partial<Record<Breakpoint, 'left' | 'center' | 'right' | 'justify'>>
  lineHeight?: Partial<Record<Breakpoint, string>>
  letterSpacing?: Partial<Record<Breakpoint, string>>
  truncate?: boolean | number
  class?: string
}

const props = withDefaults(defineProps<Props>(), {
  tag: 'p',
  color: 'text-foreground',
  truncate: false
})

const { getResponsiveClass } = useBreakpoints()

const textClass = computed(() => cn(
  // 基礎顏色
  props.color,
  
  // 響應式字體大小
  props.size ? getResponsiveClass(
    Object.fromEntries(
      Object.entries(props.size).map(([bp, value]) => [bp, `text-${value}`])
    )
  ) : '',
  
  // 響應式字體粗細
  props.weight ? getResponsiveClass(
    Object.fromEntries(
      Object.entries(props.weight).map(([bp, value]) => [bp, `font-${value}`])
    )
  ) : '',
  
  // 響應式文字對齊
  props.align ? getResponsiveClass(
    Object.fromEntries(
      Object.entries(props.align).map(([bp, value]) => [bp, `text-${value}`])
    )
  ) : '',
  
  // 響應式行高
  props.lineHeight ? getResponsiveClass(
    Object.fromEntries(
      Object.entries(props.lineHeight).map(([bp, value]) => [bp, `leading-${value}`])
    )
  ) : '',
  
  // 響應式字母間距
  props.letterSpacing ? getResponsiveClass(
    Object.fromEntries(
      Object.entries(props.letterSpacing).map(([bp, value]) => [bp, `tracking-${value}`])
    )
  ) : '',
  
  // 文字截斷
  {
    'truncate': props.truncate === true,
    'text-ellipsis-2': props.truncate === 2,
    'text-ellipsis-3': props.truncate === 3
  },
  
  props.class
))
</script> 