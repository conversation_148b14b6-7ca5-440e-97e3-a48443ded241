<template>
  <div :class="containerClass">
    <div :class="spinnerClass">
      <div class="animate-spin rounded-full border-2 border-current border-t-transparent" />
    </div>
    <p v-if="text" :class="textClass">
      {{ text }}
    </p>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { cn } from '@/lib/utils'

interface Props {
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  variant?: 'primary' | 'secondary' | 'muted'
  text?: string
  centered?: boolean
  fullScreen?: boolean
  class?: string
}

const props = withDefaults(defineProps<Props>(), {
  size: 'md',
  variant: 'primary',
  centered: false,
  fullScreen: false
})

const containerClass = computed(() => cn(
  'flex items-center gap-3',
  {
    'justify-center': props.centered,
    'fixed inset-0 z-50 bg-background/80 backdrop-blur-sm': props.fullScreen,
    'flex-col': props.text
  },
  props.class
))

const spinnerClass = computed(() => cn(
  'flex items-center justify-center',
  {
    // Size variants
    'h-4 w-4': props.size === 'xs',
    'h-5 w-5': props.size === 'sm',
    'h-6 w-6': props.size === 'md',
    'h-8 w-8': props.size === 'lg',
    'h-12 w-12': props.size === 'xl',
    
    // Color variants
    'text-primary': props.variant === 'primary',
    'text-secondary': props.variant === 'secondary',
    'text-muted-foreground': props.variant === 'muted'
  }
))

const textClass = computed(() => cn(
  'text-sm font-medium',
  {
    'text-primary': props.variant === 'primary',
    'text-secondary': props.variant === 'secondary',
    'text-muted-foreground': props.variant === 'muted'
  }
))
</script> 