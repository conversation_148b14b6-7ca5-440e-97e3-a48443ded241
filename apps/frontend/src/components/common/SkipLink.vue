<template>
  <a
    :href="href"
    :class="linkClass"
    @focus="isVisible = true"
    @blur="isVisible = false"
  >
    {{ text }}
  </a>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { cn } from '@/lib/utils'

interface Props {
  href: string
  text?: string
  class?: string
}

const props = withDefaults(defineProps<Props>(), {
  text: '跳到主要內容'
})

const isVisible = ref(false)

const linkClass = computed(() => cn(
  // 基礎樣式
  'absolute top-0 left-0 z-[9999] px-4 py-2 text-sm font-medium',
  'bg-primary text-primary-foreground rounded-md shadow-lg',
  'transition-transform duration-200 ease-in-out',
  'focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
  // 預設隱藏，聚焦時顯示
  {
    '-translate-y-full': !isVisible.value,
    'translate-y-2': isVisible.value
  },
  props.class
))
</script> 