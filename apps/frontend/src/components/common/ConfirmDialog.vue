<template>
  <AlertDialog :open="open" @update:open="$emit('update:open', $event)">
    <AlertDialogContent class="max-w-md">
      <AlertDialogHeader>
        <AlertDialogTitle class="flex items-center gap-2">
          <component 
            v-if="icon" 
            :is="icon" 
            :class="iconClass"
          />
          {{ title }}
        </AlertDialogTitle>
        <AlertDialogDescription>
          {{ description }}
        </AlertDialogDescription>
      </AlertDialogHeader>
      
      <AlertDialogFooter>
        <AlertDialogCancel 
          :disabled="loading"
          @click="handleCancel"
        >
          {{ cancelText }}
        </AlertDialogCancel>
        <AlertDialogAction
          :variant="confirmVariant"
          :disabled="loading"
          @click="handleConfirm"
        >
          <LoadingSpinner v-if="loading" size="xs" class="mr-2" />
          {{ confirmText }}
        </AlertDialogAction>
      </AlertDialogFooter>
    </AlertDialogContent>
  </AlertDialog>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { cn } from '@/lib/utils'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import LoadingSpinner from './LoadingSpinner.vue'
import { AlertTriangle, Trash2, Info, HelpCircle } from 'lucide-vue-next'

interface Props {
  open: boolean
  title: string
  description: string
  confirmText?: string
  cancelText?: string
  variant?: 'destructive' | 'warning' | 'info' | 'default'
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  confirmText: '確認',
  cancelText: '取消',
  variant: 'default',
  loading: false
})

const emit = defineEmits<{
  'update:open': [value: boolean]
  confirm: []
  cancel: []
}>()

const icon = computed(() => {
  const iconMap = {
    destructive: Trash2,
    warning: AlertTriangle,
    info: Info,
    default: HelpCircle
  }
  return iconMap[props.variant]
})

const iconClass = computed(() => cn(
  'h-5 w-5',
  {
    'text-destructive': props.variant === 'destructive',
    'text-yellow-500': props.variant === 'warning',
    'text-blue-500': props.variant === 'info',
    'text-muted-foreground': props.variant === 'default'
  }
))

const confirmVariant = computed(() => {
  return props.variant === 'destructive' ? 'destructive' : 'default'
})

const handleConfirm = () => {
  emit('confirm')
}

const handleCancel = () => {
  emit('cancel')
  emit('update:open', false)
}
</script> 