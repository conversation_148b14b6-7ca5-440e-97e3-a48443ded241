<template>
  <component
    :is="tag"
    :class="containerClass"
  >
    <slot />
  </component>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { cn } from '@/lib/utils'
import { useBreakpoints, type Breakpoint } from '@/composables/useBreakpoints'

interface Props {
  tag?: string
  maxWidth?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full' | 'none'
  padding?: Partial<Record<Breakpoint, string>>
  margin?: Partial<Record<Breakpoint, string>>
  center?: boolean
  fluid?: boolean
  class?: string
}

const props = withDefaults(defineProps<Props>(), {
  tag: 'div',
  maxWidth: 'none',
  center: true,
  fluid: false
})

const { getResponsiveClass } = useBreakpoints()

const containerClass = computed(() => cn(
  // 基礎樣式
  'w-full',
  
  // 最大寬度
  {
    'max-w-xs': props.maxWidth === 'xs',
    'max-w-sm': props.maxWidth === 'sm',
    'max-w-md': props.maxWidth === 'md',
    'max-w-lg': props.maxWidth === 'lg',
    'max-w-xl': props.maxWidth === 'xl',
    'max-w-2xl': props.maxWidth === '2xl',
    'max-w-full': props.maxWidth === 'full',
    'max-w-none': props.maxWidth === 'none'
  },
  
  // 居中
  {
    'mx-auto': props.center
  },
  
  // 流體容器
  {
    'container-fluid': props.fluid,
    'container': !props.fluid && props.maxWidth !== 'none'
  },
  
  // 響應式內邊距
  props.padding ? getResponsiveClass(
    Object.fromEntries(
      Object.entries(props.padding).map(([bp, value]) => [bp, `p-${value}`])
    )
  ) : '',
  
  // 響應式外邊距
  props.margin ? getResponsiveClass(
    Object.fromEntries(
      Object.entries(props.margin).map(([bp, value]) => [bp, `m-${value}`])
    )
  ) : '',
  
  props.class
))
</script> 