<template>
  <div
    ref="containerRef"
    :class="props.class"
    @keydown="handleKeydown"
  >
    <!-- 開始焦點守衛 -->
    <div
      ref="startGuardRef"
      tabindex="0"
      @focus="focusLastElement"
      class="sr-only"
    />
    
    <slot />
    
    <!-- 結束焦點守衛 -->
    <div
      ref="endGuardRef"
      tabindex="0"
      @focus="focusFirstElement"
      class="sr-only"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'

interface Props {
  active?: boolean
  class?: string
}

const props = withDefaults(defineProps<Props>(), {
  active: true
})

const containerRef = ref<HTMLElement>()
const startGuardRef = ref<HTMLElement>()
const endGuardRef = ref<HTMLElement>()

// 可聚焦元素的選擇器
const FOCUSABLE_SELECTORS = [
  'button:not([disabled])',
  'input:not([disabled])',
  'select:not([disabled])',
  'textarea:not([disabled])',
  'a[href]',
  '[tabindex]:not([tabindex="-1"])',
  '[contenteditable="true"]'
].join(', ')

const getFocusableElements = (): HTMLElement[] => {
  if (!containerRef.value) return []
  
  const elements = containerRef.value.querySelectorAll(FOCUSABLE_SELECTORS)
  return Array.from(elements).filter((el) => {
    const element = el as HTMLElement
    return element.offsetWidth > 0 && element.offsetHeight > 0
  }) as HTMLElement[]
}

const focusFirstElement = () => {
  if (!props.active) return
  
  const focusableElements = getFocusableElements()
  if (focusableElements.length > 0) {
    focusableElements[0].focus()
  }
}

const focusLastElement = () => {
  if (!props.active) return
  
  const focusableElements = getFocusableElements()
  if (focusableElements.length > 0) {
    focusableElements[focusableElements.length - 1].focus()
  }
}

const handleKeydown = (event: KeyboardEvent) => {
  if (!props.active || event.key !== 'Tab') return
  
  const focusableElements = getFocusableElements()
  if (focusableElements.length === 0) return
  
  const firstElement = focusableElements[0]
  const lastElement = focusableElements[focusableElements.length - 1]
  
  if (event.shiftKey) {
    // Shift + Tab: 向前循環
    if (document.activeElement === firstElement) {
      event.preventDefault()
      lastElement.focus()
    }
  } else {
    // Tab: 向後循環
    if (document.activeElement === lastElement) {
      event.preventDefault()
      firstElement.focus()
    }
  }
}

onMounted(async () => {
  if (props.active) {
    await nextTick()
    focusFirstElement()
  }
})

// 導出方法供父組件使用
defineExpose({
  focusFirstElement,
  focusLastElement,
  getFocusableElements
})
</script> 