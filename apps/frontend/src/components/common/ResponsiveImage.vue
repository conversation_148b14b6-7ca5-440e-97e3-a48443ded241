<template>
  <picture v-if="sources?.length" :class="containerClass">
    <source
      v-for="source in sources"
      :key="source.media"
      :media="source.media"
      :srcset="source.srcset"
      :type="source.type"
    />
    <img
      :src="src"
      :alt="alt"
      :class="imageClass"
      :loading="loading"
      :decoding="decoding"
      @load="handleLoad"
      @error="handleError"
    />
  </picture>
  
  <div v-else :class="containerClass">
    <img
      :src="src"
      :alt="alt"
      :class="imageClass"
      :loading="loading"
      :decoding="decoding"
      @load="handleLoad"
      @error="handleError"
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { cn } from '@/lib/utils'
import { useBreakpoints, type Breakpoint } from '@/composables/useBreakpoints'

interface ImageSource {
  media: string
  srcset: string
  type?: string
}

interface Props {
  src: string
  alt: string
  sources?: ImageSource[]
  aspectRatio?: 'square' | 'video' | 'portrait' | 'landscape' | string
  objectFit?: 'contain' | 'cover' | 'fill' | 'none' | 'scale-down'
  sizes?: Partial<Record<Breakpoint, string>>
  loading?: 'lazy' | 'eager'
  decoding?: 'async' | 'sync' | 'auto'
  rounded?: boolean | string
  class?: string
  containerClass?: string
}

const props = withDefaults(defineProps<Props>(), {
  aspectRatio: 'auto',
  objectFit: 'cover',
  loading: 'lazy',
  decoding: 'async',
  rounded: false
})

const emit = defineEmits<{
  load: [event: Event]
  error: [event: Event]
}>()

const { getResponsiveClass } = useBreakpoints()

const containerClass = computed(() => cn(
  'relative overflow-hidden',
  
  // 長寬比
  {
    'aspect-square': props.aspectRatio === 'square',
    'aspect-video': props.aspectRatio === 'video',
    'aspect-[3/4]': props.aspectRatio === 'portrait',
    'aspect-[4/3]': props.aspectRatio === 'landscape'
  },
  
  // 自定義長寬比
  props.aspectRatio && !['square', 'video', 'portrait', 'landscape', 'auto'].includes(props.aspectRatio) 
    ? `aspect-[${props.aspectRatio}]` 
    : '',
  
  // 圓角
  {
    'rounded': props.rounded === true,
    'rounded-md': props.rounded === 'md',
    'rounded-lg': props.rounded === 'lg',
    'rounded-xl': props.rounded === 'xl',
    'rounded-full': props.rounded === 'full'
  },
  
  // 自定義圓角
  typeof props.rounded === 'string' && !['md', 'lg', 'xl', 'full'].includes(props.rounded)
    ? props.rounded
    : '',
  
  props.containerClass
))

const imageClass = computed(() => cn(
  'w-full h-full',
  
  // 物件適應
  {
    'object-contain': props.objectFit === 'contain',
    'object-cover': props.objectFit === 'cover',
    'object-fill': props.objectFit === 'fill',
    'object-none': props.objectFit === 'none',
    'object-scale-down': props.objectFit === 'scale-down'
  },
  
  // 響應式尺寸
  props.sizes ? getResponsiveClass(
    Object.fromEntries(
      Object.entries(props.sizes).map(([bp, value]) => [bp, value])
    )
  ) : '',
  
  props.class
))

const handleLoad = (event: Event) => {
  emit('load', event)
}

const handleError = (event: Event) => {
  emit('error', event)
}
</script> 