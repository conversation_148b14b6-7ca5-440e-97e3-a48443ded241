<template>
  <Button
    :variant="variant"
    :size="size"
    @click="toggleTheme"
    :class="cn('relative', props.class)"
  >
    <Sun
      v-if="!isDark"
      :class="iconClass"
    />
    <Moon
      v-else
      :class="iconClass"
    />
    <span v-if="showLabel" class="ml-2">
      {{ isDark ? '深色模式' : '淺色模式' }}
    </span>
    <span class="sr-only">切換主題</span>
  </Button>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Sun, Moon } from 'lucide-vue-next'
import { useDark, useToggle } from '@vueuse/core'

interface Props {
  variant?: 'default' | 'outline' | 'ghost' | 'secondary'
  size?: 'default' | 'sm' | 'lg' | 'icon'
  showLabel?: boolean
  class?: string
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'ghost',
  size: 'icon',
  showLabel: false
})

const isDark = useDark()
const toggleTheme = useToggle(isDark)

const iconClass = computed(() => cn(
  'transition-all duration-300',
  {
    'h-4 w-4': props.size === 'sm' || props.size === 'icon',
    'h-5 w-5': props.size === 'default',
    'h-6 w-6': props.size === 'lg'
  }
))
</script> 