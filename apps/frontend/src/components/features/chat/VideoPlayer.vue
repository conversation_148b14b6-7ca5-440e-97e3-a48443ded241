<template>
  <div class="relative max-w-sm rounded-lg overflow-hidden bg-black">
    <video
      ref="videoRef"
      :src="src"
      :poster="thumbnail"
      controls
      class="w-full h-auto"
      preload="metadata"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

interface Props {
  src: string
  thumbnail?: string
}

defineProps<Props>()

const videoRef = ref<HTMLVideoElement>()
</script> 