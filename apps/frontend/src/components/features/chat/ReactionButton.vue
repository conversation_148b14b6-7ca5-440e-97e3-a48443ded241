<template>
  <button
    @click="$emit('click')"
    class="inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs transition-colors"
    :class="{
      'bg-primary/10 text-primary border border-primary/20': isReacted,
      'bg-muted hover:bg-muted/80 text-muted-foreground': !isReacted
    }"
    :title="usersText"
  >
    <span>{{ emoji }}</span>
    <span>{{ count }}</span>
  </button>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  emoji: string
  count: number
  users: string[]
  isReacted: boolean
}

interface Emits {
  (e: 'click'): void
}

const props = defineProps<Props>()
defineEmits<Emits>()

const usersText = computed(() => {
  if (props.users.length === 0) return ''
  if (props.users.length === 1) return props.users[0]
  if (props.users.length === 2) return `${props.users[0]} 和 ${props.users[1]}`
  return `${props.users[0]} 和其他 ${props.users.length - 1} 人`
})
</script> 