<template>
  <div
    :class="cn(
      'group relative px-4 py-2 transition-colors',
      {
        'bg-accent/20': isSelected,
        'opacity-60': message.status === 'failed'
      }
    )"
    @click="handleMessageClick"
  >
    <!-- 選擇框 -->
    <div v-if="showSelection" class="absolute left-2 top-2">
      <Checkbox
        :checked="isSelected"
        @update:checked="$emit('select', message.id)"
      />
    </div>

    <!-- 訊息容器 -->
    <div
      :class="cn(
        'flex gap-3 max-w-4xl',
        {
          'flex-row-reverse ml-auto': isOwnMessage,
          'mr-auto': !isOwnMessage
        }
      )"
    >
      <!-- 頭像 -->
      <div
        v-if="!isOwnMessage"
        class="flex-shrink-0"
      >
        <Avatar class="h-8 w-8">
          <AvatarImage
            v-if="message.senderAvatar"
            :src="message.senderAvatar"
            :alt="message.senderName || '未知用戶'"
          />
        <AvatarFallback>
            {{ getInitials(message.senderName) }}
        </AvatarFallback>
      </Avatar>
      </div>

      <!-- 訊息泡泡 -->
      <div
        :class="cn(
          'flex flex-col min-w-0 max-w-md',
          {
            'items-end': isOwnMessage,
            'items-start': !isOwnMessage
          }
        )"
      >
        <!-- 發送者名稱和時間 (只在非自己訊息時顯示) -->
        <div
          v-if="!isOwnMessage"
          class="flex items-center gap-2 mb-1 px-1"
        >
          <span class="font-medium text-sm text-foreground">
            {{ message.senderName || '未知用戶' }}
          </span>
          <span class="text-xs text-muted-foreground">
            {{ formatTime(message.createdAt) }}
          </span>
          <Badge v-if="message.isEdited" variant="secondary" class="text-xs">
            已編輯
          </Badge>
        </div>

        <!-- 回覆訊息 -->
        <div
          v-if="message.replyToMessage"
          :class="cn(
            'mb-2 p-3 border-l-4 bg-muted/30 rounded-r text-sm max-w-full',
            {
              'border-r-4 border-l-0 rounded-l rounded-r-none border-primary-foreground/50': isOwnMessage,
              'border-primary': !isOwnMessage
            }
          )"
        >
          <div class="flex items-center gap-2 mb-1">
            <Reply class="h-3 w-3 text-muted-foreground" />
            <span class="font-medium text-xs text-muted-foreground">
              回覆 {{ message.replyToMessage.senderName || '未知用戶' }}
            </span>
          </div>
          <div class="text-muted-foreground line-clamp-2 text-xs">
            {{ getReplyPreview(message.replyToMessage) }}
          </div>
        </div>

        <!-- 訊息泡泡內容 -->
        <div
          :class="cn(
            'relative rounded-2xl px-4 py-2 shadow-sm',
            {
              // 自己的訊息 - 藍色泡泡
              'bg-primary text-primary-foreground': isOwnMessage && !isSystemMessage,
              // 他人的訊息 - 灰色泡泡
              'bg-muted text-foreground': !isOwnMessage && !isSystemMessage,
              // 系統訊息 - 透明背景
              'bg-transparent text-muted-foreground italic text-center': isSystemMessage
            }
          )"
        >
          <!-- 文字訊息 -->
          <div
            v-if="isTextMessage"
            class="text-sm whitespace-pre-wrap break-words"
            v-html="formatMessageContent(message.content)"
          />

          <!-- 系統訊息 -->
          <div
            v-else-if="isSystemMessage"
            class="text-sm"
          >
            {{ message.content }}
          </div>

          <!-- 附件 -->
          <div v-if="message.attachments?.length" class="space-y-2 mt-2">
            <div
              v-for="attachment in message.attachments"
              :key="attachment.id"
            >
              <!-- 圖片附件 -->
              <div
                v-if="isImageMessage"
                class="relative group/image cursor-pointer"
                @click="handleImagePreview(attachment)"
            >
              <img
                :src="attachment.url"
                :alt="attachment.name"
                  class="rounded-lg max-w-full h-auto shadow-sm hover:shadow-md transition-shadow"
                  loading="lazy"
              />
                <div class="absolute inset-0 bg-black/0 group-hover/image:bg-black/10 transition-colors rounded-lg flex items-center justify-center">
                  <Eye class="h-6 w-6 text-white opacity-0 group-hover/image:opacity-100 transition-opacity" />
            </div>
          </div>

              <!-- 檔案附件 -->
              <Card
                v-else-if="isFileMessage"
                :class="cn(
                  'p-3 cursor-pointer hover:bg-muted/50 transition-colors',
                  {
                    'bg-primary-foreground/10': isOwnMessage,
                    'bg-background': !isOwnMessage
                  }
                )"
                @click="handleFileDownload(attachment)"
              >
                <div class="flex items-center gap-3">
              <div class="flex-shrink-0">
                    <FileText class="h-8 w-8 text-muted-foreground" />
              </div>
              <div class="flex-1 min-w-0">
                <div class="font-medium text-sm truncate">
                  {{ attachment.name }}
                </div>
                <div class="text-xs text-muted-foreground">
                  {{ formatFileSize(attachment.size) }}
                </div>
              </div>
                  <Button variant="ghost" size="sm">
                    <Download class="h-4 w-4" />
              </Button>
                </div>
              </Card>

              <!-- 音頻附件 -->
              <Card
                v-else-if="isAudioMessage"
                :class="cn(
                  'p-3',
                  {
                    'bg-primary-foreground/10': isOwnMessage,
                    'bg-background': !isOwnMessage
                  }
                )"
              >
                <div class="flex items-center gap-3">
                  <div class="flex-shrink-0">
                    <Volume2 class="h-8 w-8 text-muted-foreground" />
            </div>
                  <div class="flex-1">
                    <div class="font-medium text-sm mb-2">
                      {{ attachment.name }}
            </div>
                    <audio
                      controls
                      class="w-full h-8"
              :src="attachment.url"
                      preload="metadata"
                    >
                      您的瀏覽器不支援音頻播放
                    </audio>
                    <div class="text-xs text-muted-foreground mt-1">
                      {{ formatDuration(attachment.duration) }} • {{ formatFileSize(attachment.size) }}
            </div>
          </div>
            </div>
              </Card>

              <!-- 視頻附件 -->
              <Card
                v-else-if="isVideoMessage"
                :class="cn(
                  'p-3',
                  {
                    'bg-primary-foreground/10': isOwnMessage,
                    'bg-background': !isOwnMessage
                  }
                )"
          >
                <div class="space-y-2">
                  <div class="font-medium text-sm">
                    {{ attachment.name }}
              </div>
                  <video
                    controls
                    class="w-full max-w-sm rounded"
                    :src="attachment.url"
                    :poster="attachment.thumbnailUrl"
                    preload="metadata"
              >
                    您的瀏覽器不支援視頻播放
                  </video>
                  <div class="text-xs text-muted-foreground">
                    {{ formatDuration(attachment.duration) }} • {{ formatFileSize(attachment.size) }}
                  </div>
              </div>
              </Card>
            </div>
          </div>
        </div>

        <!-- 自己訊息的時間和狀態 -->
        <div
          v-if="isOwnMessage"
          class="flex items-center gap-2 mt-1 px-1"
        >
          <Badge v-if="message.isEdited" variant="secondary" class="text-xs">
            已編輯
          </Badge>
          <span class="text-xs text-muted-foreground">
            {{ formatTime(message.createdAt) }}
          </span>
          <component
            :is="getStatusIcon(message.status)"
            :class="cn(
              'h-3 w-3',
              {
                'text-muted-foreground': message.status === 'sent',
                'text-blue-500': message.status === 'delivered',
                'text-blue-600': message.status === 'read',
                'text-destructive': message.status === 'failed'
              }
            )"
          />
        </div>

        <!-- 反應 -->
        <div v-if="groupedReactions.length" class="flex flex-wrap gap-1 mt-2">
          <Button
            v-for="reaction in groupedReactions"
            :key="reaction.emoji"
            variant="outline"
            size="sm"
            class="h-6 px-2 text-xs"
            :class="{
              'bg-primary/10 border-primary': reaction.hasUserReacted
            }"
            @click="handleReactionToggle(reaction.emoji)"
          >
            <span class="mr-1">{{ reaction.emoji }}</span>
            <span>{{ reaction.count }}</span>
          </Button>
    </div>

        <!-- 操作按鈕 (在泡泡下方) -->
    <div
          v-if="!isSystemMessage"
          class="flex items-center gap-1 mt-1 opacity-0 group-hover:opacity-100 transition-opacity"
        >
        <Button
          variant="ghost"
          size="sm"
            class="h-7 px-2 text-xs"
            @click="handleReply"
        >
            <Reply class="h-3 w-3 mr-1" />
            回覆
        </Button>
          
        <Button
          variant="ghost"
          size="sm"
            class="h-7 px-2 text-xs"
            @click="handleReact"
        >
            <Heart class="h-3 w-3 mr-1" />
            反應
        </Button>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" class="h-7 px-2 text-xs">
                <MoreHorizontal class="h-3 w-3" />
            </Button>
          </DropdownMenuTrigger>
            <DropdownMenuContent align="end" class="w-40">
              <DropdownMenuItem @click="handleCopy">
                <Copy class="mr-2 h-4 w-4" />
                複製
              </DropdownMenuItem>
              <DropdownMenuItem @click="handleForward">
                <Forward class="mr-2 h-4 w-4" />
              轉發
            </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                v-if="canEdit"
                @click="handleEdit"
              >
                <Edit class="mr-2 h-4 w-4" />
                編輯
              </DropdownMenuItem>
              <DropdownMenuItem @click="handleSelect">
                <Check class="mr-2 h-4 w-4" />
              選擇
            </DropdownMenuItem>
              <DropdownMenuSeparator />
            <DropdownMenuItem
                v-if="canDelete"
                @click="handleDelete"
                class="text-destructive focus:text-destructive"
            >
                <Trash2 class="mr-2 h-4 w-4" />
              刪除
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
        </div>
      </div>
    </div>

    <!-- 圖片預覽對話框 -->
    <Dialog v-model:open="showImagePreview">
      <DialogContent class="max-w-4xl">
        <DialogHeader>
          <DialogTitle>圖片預覽</DialogTitle>
        </DialogHeader>
        <div v-if="previewImage" class="flex justify-center">
          <img
            :src="previewImage.url"
            :alt="previewImage.name"
            class="max-w-full max-h-[70vh] object-contain"
          />
        </div>
        <DialogFooter>
          <Button variant="outline" @click="showImagePreview = false">
            關閉
          </Button>
          <Button @click="handleImageDownload">
            <Download class="mr-2 h-4 w-4" />
            下載
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>

    <!-- 刪除確認對話框 -->
    <AlertDialog v-model:open="showDeleteConfirm">
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>確認刪除</AlertDialogTitle>
          <AlertDialogDescription>
            您確定要刪除這則訊息嗎？此操作無法復原。
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>取消</AlertDialogCancel>
          <AlertDialogAction
            @click="confirmDelete"
            class="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            刪除
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { useAuth, useAuthStore } from '@horizai/auth'
import { useToast } from '@/components/ui/toast/use-toast'
import { cn } from '@/lib/utils'

// Shadcn-Vue 組件
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'

// 圖示
import {
  Check,
  CheckCheck,
  Clock,
  Copy,
  Download,
  Edit,
  Eye,
  FileText,
  Forward,
  Heart,
  MoreHorizontal,
  Reply,
  Trash2,
  Volume2,
  X,
} from 'lucide-vue-next'

// 類型
import type { ChatMessage, ChatAttachment, MessageReaction } from '@/types/models/chat.model'

// Props
interface Props {
  message: ChatMessage
  showSelection?: boolean
  isSelected?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showSelection: false,
  isSelected: false,
})

// Emits
interface Emits {
  (e: 'reply', messageId: string): void
  (e: 'edit', messageId: string): void
  (e: 'delete', messageId: string): void
  (e: 'react', messageId: string, emoji: string): void
  (e: 'copy', content: string): void
  (e: 'forward', messageId: string): void
  (e: 'select', messageId: string): void
}

const emit = defineEmits<Emits>()

// Composables
const { getUser } = useAuth()
const { toast } = useToast()
const authStore = useAuthStore()

// 響應式狀態
const showImagePreview = ref(false)
const showDeleteConfirm = ref(false)
const previewImage = ref<ChatAttachment | null>(null)

// 計算屬性
const isOwnMessage = computed(() => {
  return authStore.currentUser?.id === props.message.senderId
})

// 訊息類型計算屬性（處理大小寫不匹配）
const messageType = computed(() => {
  return props.message.type?.toLowerCase() || 'text'
})

const isTextMessage = computed(() => {
  return messageType.value === 'text'
})

const isSystemMessage = computed(() => {
  return messageType.value === 'system'
})

const isImageMessage = computed(() => {
  return messageType.value === 'image'
})

const isFileMessage = computed(() => {
  return messageType.value === 'file'
})

const isAudioMessage = computed(() => {
  return messageType.value === 'audio'
})

const isVideoMessage = computed(() => {
  return messageType.value === 'video'
})

const canEdit = computed(() => {
  return authStore.currentUser?.id === props.message.senderId && 
         isTextMessage.value &&
         props.message.status !== 'failed'
})

const canDelete = computed(() => {
  return authStore.currentUser?.id === props.message.senderId || 
         authStore.currentUser?.role === 'TENANT_ADMIN' ||
         authStore.currentUser?.role === 'SYSTEM_ADMIN'
})

const groupedReactions = computed(() => {
  if (!props.message.reactions?.length) return []
  
  const reactionMap = new Map<string, {
    emoji: string
    count: number
    hasUserReacted: boolean
    users: string[]
  }>()
  
  props.message.reactions.forEach(reaction => {
    const existing = reactionMap.get(reaction.emoji)
    if (existing) {
      existing.count++
      existing.users.push(reaction.userName)
        if (reaction.userId === authStore.currentUser?.id) {
          existing.hasUserReacted = true
      }
    } else {
        reactionMap.set(reaction.emoji, {
        emoji: reaction.emoji,
        count: 1,
          hasUserReacted: reaction.userId === authStore.currentUser?.id,
          users: [reaction.userName]
      })
    }
  })
  
  return Array.from(reactionMap.values())
})

// 方法
const getInitials = (name: string | undefined): string => {
  if (!name || typeof name !== 'string') {
    return 'U' // 預設為 'U' (Unknown)
  }
  
  return name
    .trim()
      .split(' ')
    .filter(word => word.length > 0)
    .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
    .slice(0, 2) || 'U'
}

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'sending':
      return Clock
    case 'sent':
      return Check
    case 'delivered':
    case 'read':
      return CheckCheck
    case 'failed':
      return X
    default:
      return Check
  }
}

const formatTime = (timestamp: string): string => {
  const date = new Date(timestamp)
  const now = new Date()
  const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60)
  
  if (diffInHours < 24) {
    return date.toLocaleTimeString('zh-TW', {
      hour: '2-digit',
      minute: '2-digit'
    })
  } else if (diffInHours < 24 * 7) {
    return date.toLocaleDateString('zh-TW', {
      weekday: 'short',
      hour: '2-digit',
      minute: '2-digit'
    })
  } else {
    return date.toLocaleDateString('zh-TW', {
      month: 'short',
      day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
  }
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

const formatDuration = (seconds?: number): string => {
  if (!seconds) return '0:00'
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

const formatMessageContent = (content: string): string => {
  // 簡單的 Markdown 支援
  return content
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    .replace(/`(.*?)`/g, '<code class="bg-muted px-1 py-0.5 rounded text-sm">$1</code>')
    .replace(/\n/g, '<br>')
}

const getReplyPreview = (message: ChatMessage): string => {
  const msgType = message.type?.toLowerCase() || 'text'
  
  if (msgType === 'text') {
    return message.content.length > 100 
      ? message.content.substring(0, 100) + '...'
      : message.content
  } else if (msgType === 'image') {
    return '📷 圖片'
  } else if (msgType === 'file') {
    return `📎 ${message.attachments?.[0]?.name || '檔案'}`
  } else if (msgType === 'audio') {
    return '🎵 音頻'
  } else if (msgType === 'video') {
    return '🎬 視頻'
  }
  return message.content
}

// 事件處理
const handleMessageClick = () => {
  if (props.showSelection) {
    emit('select', props.message.id)
  }
}

const handleReply = () => {
  emit('reply', props.message.id)
}

const handleEdit = () => {
  if (canEdit.value) {
    emit('edit', props.message.id)
  }
}

const handleDelete = () => {
  if (canDelete.value) {
    showDeleteConfirm.value = true
  }
}

const confirmDelete = () => {
  emit('delete', props.message.id)
  showDeleteConfirm.value = false
}

const handleReact = () => {
  // 顯示表情符號選擇器或使用預設表情
  emit('react', props.message.id, '👍')
}

const handleReactionToggle = (emoji: string) => {
  emit('react', props.message.id, emoji)
}

const handleCopy = async () => {
  try {
    await navigator.clipboard.writeText(props.message.content)
    toast({
      title: '已複製',
      description: '訊息內容已複製到剪貼板',
    })
    emit('copy', props.message.content)
  } catch (error) {
    toast({
      title: '複製失敗',
      description: '無法複製訊息內容',
      variant: 'destructive',
    })
  }
}

const handleForward = () => {
  emit('forward', props.message.id)
}

const handleSelect = () => {
  emit('select', props.message.id)
}

const handleImagePreview = (attachment: ChatAttachment) => {
  previewImage.value = attachment
  showImagePreview.value = true
}

const handleImageDownload = () => {
  if (previewImage.value) {
    handleFileDownload(previewImage.value)
  }
}

const handleFileDownload = (attachment: ChatAttachment) => {
  const link = document.createElement('a')
  link.href = attachment.url
  link.download = attachment.name
  link.target = '_blank'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}
</script> 