<template>
  <div class="space-y-3">
    <!-- 回覆預覽 -->
    <div
      v-if="replyTo"
      class="flex items-center justify-between p-3 bg-muted rounded-lg border-l-2 border-primary"
    >
      <div class="flex-1 min-w-0">
        <div class="text-xs text-muted-foreground mb-1">
          回覆 {{ replyTo.senderName }}
        </div>
        <div class="text-sm truncate">
          {{ getMessagePreview(replyTo) }}
        </div>
      </div>
      <Button
        variant="ghost"
        size="sm"
        @click="$emit('cancel-reply')"
        class="h-6 w-6 p-0 ml-2"
      >
        <XMarkIcon class="h-4 w-4" />
      </Button>
    </div>

    <!-- 輸入區域 -->
    <div class="flex items-end gap-2">
      <!-- 附件按鈕 -->
      <div class="flex flex-col gap-1">
        <Button
          variant="ghost"
          size="sm"
          @click="triggerFileUpload"
          class="h-9 w-9 p-0"
          title="上傳檔案"
        >
          <PaperClipIcon class="h-4 w-4" />
        </Button>
        
        <input
          ref="fileInput"
          type="file"
          multiple
          class="hidden"
          @change="handleFileSelect"
          accept="image/*,video/*,audio/*,.pdf,.doc,.docx,.txt"
        />
      </div>

      <!-- 主要輸入區域 -->
      <div class="flex-1 relative">
        <Textarea
          ref="textareaRef"
          v-model="localValue"
          :placeholder="placeholder"
          :disabled="isSending"
          class="min-h-[40px] max-h-[120px] resize-none pr-20"
          @keydown="handleKeyDown"
          @input="handleInput"
          @focus="handleFocus"
          @blur="handleBlur"
        />
        
        <!-- 右側按鈕組 -->
        <div class="absolute right-2 bottom-2 flex items-center gap-1">
          <!-- 表情符號按鈕 -->
          <Button
            variant="ghost"
            size="sm"
            @click="showEmojiPicker = !showEmojiPicker"
            class="h-7 w-7 p-0"
            title="表情符號"
          >
            <FaceSmileIcon class="h-4 w-4" />
          </Button>
          
          <!-- 發送按鈕 -->
          <Button
            :disabled="!canSend"
            @click="handleSend"
            size="sm"
            class="h-7 w-7 p-0"
            title="發送 (Ctrl+Enter)"
          >
            <PaperAirplaneIcon
              v-if="!isSending"
              class="h-4 w-4"
            />
            <div
              v-else
              class="h-3 w-3 border border-current border-t-transparent rounded-full animate-spin"
            />
          </Button>
        </div>

        <!-- 表情符號選擇器 -->
        <div
          v-if="showEmojiPicker"
          class="absolute bottom-full right-0 mb-2 z-10"
        >
          <EmojiPicker
            :show="showEmojiPicker"
            @select="insertEmoji"
            @close="showEmojiPicker = false"
          />
        </div>
      </div>
    </div>

    <!-- 檔案上傳進度 -->
    <div v-if="uploadingFiles.length > 0" class="space-y-2">
      <div
        v-for="file in uploadingFiles"
        :key="file.fileId"
        class="flex items-center gap-3 p-2 bg-muted rounded-lg"
      >
        <DocumentIcon class="h-5 w-5 text-muted-foreground flex-shrink-0" />
        <div class="flex-1 min-w-0">
          <div class="text-sm font-medium truncate">{{ file.fileName }}</div>
          <div class="flex items-center gap-2 mt-1">
            <div class="flex-1 bg-background rounded-full h-1.5">
              <div
                class="bg-primary h-1.5 rounded-full transition-all duration-300"
                :style="{ width: `${file.progress}%` }"
              />
            </div>
            <span class="text-xs text-muted-foreground">{{ file.progress }}%</span>
          </div>
          <div v-if="file.status === 'failed'" class="text-xs text-destructive mt-1">
            {{ file.error || '上傳失敗' }}
          </div>
        </div>
        <Button
          v-if="file.status === 'uploading'"
          variant="ghost"
          size="sm"
          @click="cancelUpload(file.fileId)"
          class="h-6 w-6 p-0"
        >
          <XMarkIcon class="h-3 w-3" />
        </Button>
      </div>
    </div>

    <!-- 輸入提示 -->
    <div class="flex items-center justify-between text-xs text-muted-foreground">
      <div class="flex items-center gap-4">
        <span>按 Ctrl+Enter 發送</span>
        <span v-if="characterCount > 0">{{ characterCount }}/{{ maxLength }}</span>
      </div>
      <div v-if="isTyping" class="flex items-center gap-1">
        <div class="flex gap-0.5">
          <div class="w-1 h-1 bg-current rounded-full animate-bounce" style="animation-delay: 0ms"></div>
          <div class="w-1 h-1 bg-current rounded-full animate-bounce" style="animation-delay: 150ms"></div>
          <div class="w-1 h-1 bg-current rounded-full animate-bounce" style="animation-delay: 300ms"></div>
        </div>
        <span>正在輸入...</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import {
  PaperClipIcon,
  FaceSmileIcon,
  PaperAirplaneIcon,
  XMarkIcon,
  DocumentIcon
} from '@heroicons/vue/24/outline'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import EmojiPicker from './EmojiPicker.vue'
import type { ChatMessage, FileUploadProgress } from '@/types/models/chat.model'

interface Props {
  modelValue: string
  conversationId: string
  replyTo?: ChatMessage | null
  isSending?: boolean
  uploadProgress?: Record<string, FileUploadProgress>
  placeholder?: string
  maxLength?: number
}

interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'send'): void
  (e: 'cancel-reply'): void
  (e: 'typing', isTyping: boolean): void
  (e: 'upload', files: File[]): void
  (e: 'cancel-upload', fileId: string): void
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '輸入訊息...',
  maxLength: 4000,
  isSending: false,
  uploadProgress: () => ({})
})

const emit = defineEmits<Emits>()

// 響應式數據
const textareaRef = ref<HTMLTextAreaElement>()
const fileInput = ref<HTMLInputElement>()
const showEmojiPicker = ref(false)
const isTyping = ref(false)
const typingTimer = ref<NodeJS.Timeout>()

// 本地值
const localValue = computed({
  get: () => props.modelValue,
  set: (value: string) => emit('update:modelValue', value)
})

// 計算屬性
const characterCount = computed(() => localValue.value.length)

const canSend = computed(() => {
  return localValue.value.trim().length > 0 && 
         localValue.value.length <= props.maxLength && 
         !props.isSending
})

const uploadingFiles = computed(() => {
  return Object.values(props.uploadProgress || {})
})

// 方法
const getMessagePreview = (message: ChatMessage): string => {
  switch (message.type) {
    case 'text':
      return message.content
    case 'image':
      return '📷 圖片'
    case 'file':
      return '📎 檔案'
    case 'audio':
      return '🎵 語音訊息'
    case 'video':
      return '🎥 影片'
    default:
      return '訊息'
  }
}

const handleKeyDown = (event: KeyboardEvent) => {
  if (event.key === 'Enter') {
    if (event.ctrlKey || event.metaKey) {
      event.preventDefault()
      handleSend()
    } else if (!event.shiftKey) {
      event.preventDefault()
      handleSend()
    }
  }
}

const handleInput = () => {
  // 自動調整高度
  if (textareaRef.value) {
    // 獲取實際的 DOM 元素
    const element = textareaRef.value.$el || textareaRef.value
    if (element && element.style && typeof element.scrollHeight === 'number') {
      element.style.height = 'auto'
      element.style.height = `${element.scrollHeight}px`
    }
  }

  // 處理輸入狀態
  if (!isTyping.value) {
    isTyping.value = true
    emit('typing', true)
  }

  // 清除之前的計時器
  if (typingTimer.value) {
    clearTimeout(typingTimer.value)
  }

  // 設置新的計時器
  typingTimer.value = setTimeout(() => {
    isTyping.value = false
    emit('typing', false)
  }, 1000)
}

const handleFocus = () => {
  // 聚焦時的處理
}

const handleBlur = () => {
  // 失焦時停止輸入狀態
  if (typingTimer.value) {
    clearTimeout(typingTimer.value)
  }
  if (isTyping.value) {
    isTyping.value = false
    emit('typing', false)
  }
}

const handleSend = () => {
  if (canSend.value) {
    emit('send')
    
    // 重置高度
    nextTick(() => {
      if (textareaRef.value) {
        // 獲取實際的 DOM 元素
        const element = textareaRef.value.$el || textareaRef.value
        if (element && element.style) {
          element.style.height = 'auto'
        }
      }
    })
  }
}

const triggerFileUpload = () => {
  fileInput.value?.click()
}

const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  const files = Array.from(target.files || [])
  
  if (files.length > 0) {
    emit('upload', files)
  }
  
  // 清除選擇，允許重複選擇同一檔案
  target.value = ''
}

const insertEmoji = (emoji: string) => {
  if (textareaRef.value) {
    // 獲取實際的 DOM 元素
    const textarea = textareaRef.value.$el || textareaRef.value
    if (textarea && typeof textarea.selectionStart === 'number') {
      const start = textarea.selectionStart
      const end = textarea.selectionEnd
      const newValue = localValue.value.slice(0, start) + emoji + localValue.value.slice(end)
      
      localValue.value = newValue
      
      // 恢復光標位置
      nextTick(() => {
        const newPosition = start + emoji.length
        if (textarea.setSelectionRange) {
          textarea.setSelectionRange(newPosition, newPosition)
        }
        if (textarea.focus) {
          textarea.focus()
        }
      })
    }
  }
  
  showEmojiPicker.value = false
}

const cancelUpload = (fileId: string) => {
  // 發送取消上傳事件給父組件
  emit('cancel-upload', fileId)
}

// 監聽點擊外部關閉表情符號選擇器
const handleClickOutside = (event: Event) => {
  const target = event.target as Element
  if (!target.closest('.emoji-picker-container')) {
    showEmojiPicker.value = false
  }
}

// 生命週期
watch(() => showEmojiPicker.value, (show) => {
  if (show) {
    document.addEventListener('click', handleClickOutside)
  } else {
    document.removeEventListener('click', handleClickOutside)
  }
})
</script>

<style scoped>
.emoji-picker-container {
  /* 用於點擊外部檢測 */
}
</style> 