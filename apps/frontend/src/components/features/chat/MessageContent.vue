<template>
  <div class="message-content">
    <span v-if="!enableMarkdown" v-html="processedContent"></span>
    <div v-else v-html="markdownContent"></div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  content: string
  mentions?: string[]
  enableMarkdown?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  mentions: () => [],
  enableMarkdown: true
})

// 處理提及
const processedContent = computed(() => {
  let content = props.content
  
  // 處理 @mentions
  if (props.mentions && props.mentions.length > 0) {
    props.mentions.forEach(userId => {
      const mentionRegex = new RegExp(`@${userId}`, 'g')
      content = content.replace(mentionRegex, `<span class="mention">@${userId}</span>`)
    })
  }
  
  // 處理 URL
  const urlRegex = /(https?:\/\/[^\s]+)/g
  content = content.replace(urlRegex, '<a href="$1" target="_blank" rel="noopener noreferrer" class="link">$1</a>')
  
  return content
})

// 簡單的 Markdown 處理
const markdownContent = computed(() => {
  let content = processedContent.value
  
  if (props.enableMarkdown) {
    // 粗體
    content = content.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    content = content.replace(/__(.*?)__/g, '<strong>$1</strong>')
    
    // 斜體
    content = content.replace(/\*(.*?)\*/g, '<em>$1</em>')
    content = content.replace(/_(.*?)_/g, '<em>$1</em>')
    
    // 刪除線
    content = content.replace(/~~(.*?)~~/g, '<del>$1</del>')
    
    // 行內代碼
    content = content.replace(/`(.*?)`/g, '<code class="inline-code">$1</code>')
    
    // 代碼塊
    content = content.replace(/```([\s\S]*?)```/g, '<pre class="code-block"><code>$1</code></pre>')
  }
  
  return content
})
</script>

<style scoped>
.message-content :deep(.mention) {
  @apply bg-primary/10 text-primary px-1 rounded;
}

.message-content :deep(.link) {
  @apply text-primary underline hover:no-underline;
}

.message-content :deep(.inline-code) {
  @apply bg-muted px-1 py-0.5 rounded text-sm font-mono;
}

.message-content :deep(.code-block) {
  @apply bg-muted p-2 rounded mt-2 text-sm font-mono overflow-x-auto;
}

.message-content :deep(strong) {
  @apply font-semibold;
}

.message-content :deep(em) {
  @apply italic;
}

.message-content :deep(del) {
  @apply line-through opacity-75;
}
</style> 