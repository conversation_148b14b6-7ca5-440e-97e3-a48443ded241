<template>
  <Dialog :open="open" @update:open="$emit('update:open', $event)">
    <DialogContent class="sm:max-w-2xl">
      <DialogHeader>
        <DialogTitle>搜尋對話和訊息</DialogTitle>
        <DialogDescription>
          搜尋對話名稱、成員或訊息內容
        </DialogDescription>
      </DialogHeader>

      <div class="space-y-4">
        <!-- 搜尋輸入 -->
        <div class="relative">
          <MagnifyingGlassIcon class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            ref="searchInput"
            v-model="searchQuery"
            placeholder="搜尋對話或訊息..."
            class="pl-9"
            @input="handleSearch"
          />
          <Button
            v-if="searchQuery"
            variant="ghost"
            size="sm"
            @click="clearSearch"
            class="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
          >
            <XMarkIcon class="h-4 w-4" />
          </Button>
        </div>

        <!-- 搜尋篩選 -->
        <div class="flex gap-2">
          <Button
            :variant="searchType === 'all' ? 'default' : 'outline'"
            size="sm"
            @click="searchType = 'all'"
          >
            全部
          </Button>
          <Button
            :variant="searchType === 'conversations' ? 'default' : 'outline'"
            size="sm"
            @click="searchType = 'conversations'"
          >
            對話
          </Button>
          <Button
            :variant="searchType === 'messages' ? 'default' : 'outline'"
            size="sm"
            @click="searchType = 'messages'"
          >
            訊息
          </Button>
        </div>

        <!-- 搜尋結果 -->
        <div class="max-h-96 overflow-y-auto">
          <div v-if="isSearching" class="flex items-center justify-center py-8">
            <div class="h-6 w-6 border border-current border-t-transparent rounded-full animate-spin" />
            <span class="ml-2 text-muted-foreground">搜尋中...</span>
          </div>

          <div v-else-if="searchQuery && searchResults.length === 0" class="text-center py-8">
            <MagnifyingGlassIcon class="h-8 w-8 text-muted-foreground mx-auto mb-2" />
            <p class="text-muted-foreground">找不到相關結果</p>
          </div>

          <div v-else-if="searchResults.length > 0" class="space-y-2">
            <!-- 對話結果 -->
            <div v-if="conversationResults.length > 0">
              <h4 class="text-sm font-medium text-muted-foreground mb-2">對話</h4>
              <div class="space-y-1">
                <div
                  v-for="conversation in conversationResults"
                  :key="conversation.id"
                  @click="selectConversation(conversation)"
                  class="flex items-center gap-3 p-3 rounded-lg hover:bg-accent cursor-pointer"
                >
                  <div class="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                    <UsersIcon v-if="conversation.type === 'group'" class="h-5 w-5 text-primary" />
                    <UserIcon v-else class="h-5 w-5 text-primary" />
                  </div>
                  <div class="flex-1 min-w-0">
                    <div class="text-sm font-medium truncate">{{ conversation.name }}</div>
                    <div class="text-xs text-muted-foreground">
                      {{ conversation.type === 'group' ? `${conversation.participants?.length || 0} 位成員` : '私人對話' }}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 訊息結果 -->
            <div v-if="messageResults.length > 0">
              <h4 class="text-sm font-medium text-muted-foreground mb-2 mt-4">訊息</h4>
              <div class="space-y-1">
                <div
                  v-for="message in messageResults"
                  :key="message.id"
                  @click="selectMessage(message)"
                  class="p-3 rounded-lg hover:bg-accent cursor-pointer"
                >
                  <div class="flex items-start gap-3">
                    <Avatar class="h-8 w-8">
                      <AvatarImage :src="message.senderAvatar || ''" />
                      <AvatarFallback>{{ message.senderName.charAt(0) }}</AvatarFallback>
                    </Avatar>
                    <div class="flex-1 min-w-0">
                      <div class="flex items-center gap-2 mb-1">
                        <span class="text-sm font-medium">{{ message.senderName }}</span>
                        <span class="text-xs text-muted-foreground">
                          {{ formatDate(message.createdAt) }}
                        </span>
                      </div>
                      <div class="text-sm text-muted-foreground mb-1">
                        在 {{ message.conversationName }}
                      </div>
                      <div class="text-sm" v-html="highlightSearchTerm(message.content)"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 空狀態 -->
          <div v-else-if="!searchQuery" class="text-center py-8">
            <MagnifyingGlassIcon class="h-8 w-8 text-muted-foreground mx-auto mb-2" />
            <p class="text-muted-foreground">輸入關鍵字開始搜尋</p>
          </div>
        </div>
      </div>

      <DialogFooter>
        <Button variant="outline" @click="$emit('update:open', false)">
          關閉
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { MagnifyingGlassIcon, XMarkIcon, UsersIcon, UserIcon } from '@heroicons/vue/24/outline'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar'
import type { Conversation, ChatMessage } from '@/types/models/chat.model'

interface SearchResult {
  type: 'conversation' | 'message'
  data: Conversation | (ChatMessage & { conversationName: string })
}

interface Props {
  open: boolean
  isSearching?: boolean
}

interface Emits {
  (e: 'update:open', value: boolean): void
  (e: 'search', query: string, type: string): void
  (e: 'select-conversation', conversation: Conversation): void
  (e: 'select-message', message: ChatMessage & { conversationName: string }): void
}

const props = withDefaults(defineProps<Props>(), {
  isSearching: false
})

const emit = defineEmits<Emits>()

// 響應式數據
const searchInput = ref<HTMLInputElement>()
const searchQuery = ref('')
const searchType = ref<'all' | 'conversations' | 'messages'>('all')
const searchResults = ref<SearchResult[]>([])

// 計算屬性
const conversationResults = computed(() => {
  return searchResults.value
    .filter(result => result.type === 'conversation')
    .map(result => result.data as Conversation)
})

const messageResults = computed(() => {
  return searchResults.value
    .filter(result => result.type === 'message')
    .map(result => result.data as ChatMessage & { conversationName: string })
})

// 方法
const handleSearch = () => {
  if (searchQuery.value.trim()) {
    emit('search', searchQuery.value.trim(), searchType.value)
  } else {
    searchResults.value = []
  }
}

const clearSearch = () => {
  searchQuery.value = ''
  searchResults.value = []
}

const selectConversation = (conversation: Conversation) => {
  emit('select-conversation', conversation)
  emit('update:open', false)
}

const selectMessage = (message: ChatMessage & { conversationName: string }) => {
  emit('select-message', message)
  emit('update:open', false)
}

const highlightSearchTerm = (content: string): string => {
  if (!searchQuery.value) return content
  
  const regex = new RegExp(`(${searchQuery.value})`, 'gi')
  return content.replace(regex, '<mark class="bg-yellow-200 dark:bg-yellow-800">$1</mark>')
}

const formatDate = (date: Date): string => {
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (days === 0) {
    return new Intl.DateTimeFormat('zh-TW', {
      hour: '2-digit',
      minute: '2-digit'
    }).format(date)
  } else if (days === 1) {
    return '昨天'
  } else if (days < 7) {
    return `${days} 天前`
  } else {
    return new Intl.DateTimeFormat('zh-TW', {
      month: 'short',
      day: 'numeric'
    }).format(date)
  }
}

// 監聽對話框開關
watch(() => props.open, (isOpen) => {
  if (isOpen) {
    nextTick(() => {
      searchInput.value?.focus()
    })
  } else {
    clearSearch()
  }
})

// 監聽搜尋類型變化
watch(searchType, () => {
  if (searchQuery.value.trim()) {
    handleSearch()
  }
})

// 暴露方法給父組件
const updateResults = (results: SearchResult[]) => {
  searchResults.value = results
}

defineExpose({
  updateResults
})
</script>

<style scoped>
:deep(mark) {
  @apply bg-yellow-200 dark:bg-yellow-800 px-0.5 rounded;
}
</style> 