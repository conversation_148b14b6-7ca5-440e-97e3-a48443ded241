<template>
  <Dialog v-model:open="isOpen">
    <DialogContent class="max-w-2xl">
      <DialogHeader>
        <DialogTitle>配置節點：{{ node?.data?.label || "未知" }}</DialogTitle>
        <DialogDescription>
          修改此 {{ node?.type || "節點" }} 的配置
        </DialogDescription>
      </DialogHeader>

      <div v-if="node" class="space-y-6 py-4">
        <!-- Basic Properties -->
        <div class="space-y-4">
          <h3 class="text-lg font-medium">基本屬性</h3>

          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">
                名稱
              </label>
              <input
                v-model="config.label"
                type="text"
                class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">
                類型
              </label>
              <div
                class="text-sm text-gray-600 bg-gray-50 px-3 py-2 rounded-md"
              >
                {{ node.type }}
              </div>
            </div>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">
              描述
            </label>
            <textarea
              v-model="config.description"
              rows="2"
              class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>

        <!-- Node Type Specific Configuration -->
        <div class="space-y-4">
          <h3 class="text-lg font-medium">節點配置</h3>

          <!-- AI Bot Configuration -->
          <template v-if="node.type === 'ai-bot'">
            <div class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  AI 機器人 ID
                </label>
                <input
                  v-model="config.botId"
                  type="text"
                  placeholder="輸入或選擇機器人 ID"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  自訂配置 (JSON)
                </label>
                <textarea
                  v-model="botConfigText"
                  rows="6"
                  placeholder='{"temperature": 0.7, "maxTokens": 1000}'
                  class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent font-mono"
                />
                <p v-if="configError" class="text-xs text-red-600 mt-1">
                  {{ configError }}
                </p>
              </div>
            </div>
          </template>

          <!-- Input Node Configuration -->
          <template v-if="node.type === 'input'">
            <div class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  輸入結構描述 (JSON Schema)
                </label>
                <textarea
                  v-model="schemaText"
                  rows="8"
                  placeholder='{
  "type": "object",
  "properties": {
    "message": {
      "type": "string",
      "description": "使用者輸入訊息"
    }
  },
  "required": ["message"]
}'
                  class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent font-mono"
                />
                <p v-if="schemaError" class="text-xs text-red-600 mt-1">
                  {{ schemaError }}
                </p>
              </div>
            </div>
          </template>

          <!-- Output Node Configuration -->
          <template v-if="node.type === 'output'">
            <div class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  輸出結構描述 (JSON Schema)
                </label>
                <textarea
                  v-model="schemaText"
                  rows="8"
                  placeholder='{
  "type": "object",
  "properties": {
    "result": {
      "type": "string",
      "description": "處理結果"
    },
    "status": {
      "type": "string",
      "enum": ["success", "error"]
    }
  },
  "required": ["result", "status"]
}'
                  class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent font-mono"
                />
                <p v-if="schemaError" class="text-xs text-red-600 mt-1">
                  {{ schemaError }}
                </p>
              </div>
            </div>
          </template>

          <!-- Condition Node Configuration -->
          <template v-if="node.type === 'condition'">
            <div class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  條件表達式
                </label>
                <input
                  v-model="config.expression"
                  type="text"
                  placeholder="例如：input.value > 0 || input.status === 'active'"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <p class="text-xs text-gray-500 mt-1">
                  使用類似 JavaScript 的表達式。可用變數：input, data
                </p>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  真值分支標籤
                </label>
                <input
                  v-model="config.trueBranchLabel"
                  type="text"
                  placeholder="例如：成功、有效、繼續"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  假值分支標籤
                </label>
                <input
                  v-model="config.falseBranchLabel"
                  type="text"
                  placeholder="例如：失敗、無效、停止"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
          </template>

          <!-- Generic Configuration for other node types -->
          <template
            v-if="
              !['ai-bot', 'input', 'output', 'condition'].includes(node.type)
            "
          >
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">
                節點配置 (JSON)
              </label>
              <textarea
                v-model="genericConfigText"
                rows="6"
                placeholder='{"key": "value"}'
                class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent font-mono"
              />
              <p v-if="genericConfigError" class="text-xs text-red-600 mt-1">
                {{ genericConfigError }}
              </p>
            </div>
          </template>
        </div>
      </div>

      <DialogFooter>
        <Button variant="outline" @click="cancel"> 取消 </Button>
        <Button @click="save" :disabled="hasErrors"> 儲存配置 </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, reactive } from "vue";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";

interface Node {
  id: string;
  type: string;
  position: { x: number; y: number };
  data: any;
}

// Props
const props = defineProps<{
  open: boolean;
  node?: Node | null;
}>();

// Emits
const emit = defineEmits<{
  "update:open": [value: boolean];
  save: [nodeId: string, config: any];
}>();

// Reactive state
const isOpen = computed({
  get: () => props.open,
  set: (value) => emit("update:open", value),
});

const config = reactive<any>({});
const schemaText = ref("");
const schemaError = ref("JSON 格式無效");
const botConfigText = ref("");
const configError = ref("JSON 格式無效");
const genericConfigText = ref("");
const genericConfigError = ref("JSON 格式無效");

// Computed
const hasErrors = computed(() => {
  return schemaError.value || configError.value || genericConfigError.value;
});

// Watchers
watch(
  () => props.node,
  (newNode) => {
    if (newNode) {
      // Initialize config from node data
      Object.assign(config, newNode.data || {});

      // Initialize type-specific configurations
      if (["input", "output"].includes(newNode.type) && newNode.data?.schema) {
        try {
          schemaText.value = JSON.stringify(newNode.data.schema, null, 2);
        } catch {
          schemaText.value = "";
        }
      }

      if (newNode.type === "ai-bot" && newNode.data?.config) {
        try {
          botConfigText.value = JSON.stringify(newNode.data.config, null, 2);
        } catch {
          botConfigText.value = "{}";
        }
      }

      if (
        !["ai-bot", "input", "output", "condition"].includes(newNode.type) &&
        newNode.data?.config
      ) {
        try {
          genericConfigText.value = JSON.stringify(
            newNode.data.config,
            null,
            2
          );
        } catch {
          genericConfigText.value = "{}";
        }
      }

      // Clear errors
      schemaError.value = "";
      configError.value = "";
      genericConfigError.value = "";
    }
  },
  { immediate: true }
);

// Watch for schema changes
watch(schemaText, () => {
  if (!schemaText.value.trim()) {
    schemaError.value = "";
    return;
  }

  try {
    const schema = JSON.parse(schemaText.value);
    config.schema = schema;
    schemaError.value = "";
  } catch (error) {
    schemaError.value = "JSON 格式無效";
  }
});

// Watch for bot config changes
watch(botConfigText, () => {
  if (!botConfigText.value.trim()) {
    configError.value = "";
    config.config = {};
    return;
  }

  try {
    const botConfig = JSON.parse(botConfigText.value);
    config.config = botConfig;
    configError.value = "";
  } catch (error) {
    configError.value = "JSON 格式無效";
  }
});

// Watch for generic config changes
watch(genericConfigText, () => {
  if (!genericConfigText.value.trim()) {
    genericConfigError.value = "";
    config.config = {};
    return;
  }

  try {
    const genericConfig = JSON.parse(genericConfigText.value);
    config.config = genericConfig;
    genericConfigError.value = "";
  } catch (error) {
    genericConfigError.value = "JSON 格式無效";
  }
});

// Methods
const save = () => {
  if (!props.node || hasErrors.value) return;

  emit("save", props.node.id, { ...config });
  isOpen.value = false;
};

const cancel = () => {
  isOpen.value = false;
};
</script>
