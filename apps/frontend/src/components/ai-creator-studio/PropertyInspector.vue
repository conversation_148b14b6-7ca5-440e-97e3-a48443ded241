<template>
  <div class="space-y-4">
    <!-- Node Properties -->
    <div v-if="selectedNode" class="space-y-4">
      <div class="space-y-3">
        <h3 class="text-sm font-medium text-gray-900">節點屬性</h3>

        <!-- Node Label -->
        <div>
          <label class="block text-xs font-medium text-gray-700 mb-1">
            標籤
          </label>
          <input
            v-model="nodeData.label"
            @input="updateNodeProperty('label', nodeData.label)"
            type="text"
            class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        <!-- Node Type Info -->
        <div>
          <label class="block text-xs font-medium text-gray-700 mb-1">
            類型
          </label>
          <div class="text-sm text-gray-600 bg-gray-50 px-3 py-2 rounded-md">
            {{ selectedNode.type }}
          </div>
        </div>

        <!-- Position -->
        <div class="grid grid-cols-2 gap-2">
          <div>
            <label class="block text-xs font-medium text-gray-700 mb-1">
              X 座標
            </label>
            <input
              v-model.number="selectedNode.position.x"
              @input="updateNodeProperty('position', selectedNode.position)"
              type="number"
              class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <div>
            <label class="block text-xs font-medium text-gray-700 mb-1">
              Y 座標
            </label>
            <input
              v-model.number="selectedNode.position.y"
              @input="updateNodeProperty('position', selectedNode.position)"
              type="number"
              class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>

        <!-- Node-specific Configuration -->
        <div v-if="nodeConfig" class="space-y-3">
          <h4 class="text-sm font-medium text-gray-900">配置設定</h4>

          <!-- AI Bot Configuration -->
          <template v-if="selectedNode.type === 'ai-bot'">
            <div>
              <label class="block text-xs font-medium text-gray-700 mb-1">
                機器人 ID
              </label>
              <input
                v-model="nodeData.botId"
                @input="updateNodeProperty('botId', nodeData.botId)"
                type="text"
                placeholder="選擇或輸入機器人 ID"
                class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </template>

          <!-- Input/Output Schema Configuration -->
          <template v-if="['input', 'output'].includes(selectedNode.type)">
            <div>
              <label class="block text-xs font-medium text-gray-700 mb-1">
                結構描述 (JSON)
              </label>
              <textarea
                v-model="schemaText"
                @input="updateSchema"
                rows="4"
                placeholder='{"type": "object", "properties": {...}}'
                class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent font-mono"
              />
              <p v-if="schemaError" class="text-xs text-red-600 mt-1">
                {{ schemaError }}
              </p>
            </div>
          </template>

          <!-- Condition Configuration -->
          <template v-if="selectedNode.type === 'condition'">
            <div>
              <label class="block text-xs font-medium text-gray-700 mb-1">
                條件表達式
              </label>
              <input
                v-model="nodeData.expression"
                @input="updateNodeProperty('expression', nodeData.expression)"
                type="text"
                placeholder="例如：input.value > 0"
                class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </template>
        </div>

        <!-- Delete Node Button -->
        <div class="pt-3 border-t border-gray-200">
          <button
            @click="deleteNode"
            class="w-full px-3 py-2 bg-red-600 text-white text-sm font-medium rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
          >
            刪除節點
          </button>
        </div>
      </div>
    </div>

    <!-- Edge Properties -->
    <div v-else-if="selectedEdge" class="space-y-4">
      <div class="space-y-3">
        <h3 class="text-sm font-medium text-gray-900">連線屬性</h3>

        <!-- Connection Info -->
        <div>
          <label class="block text-xs font-medium text-gray-700 mb-1">
            來源
          </label>
          <div class="text-sm text-gray-600 bg-gray-50 px-3 py-2 rounded-md">
            {{ selectedEdge.source }}
          </div>
        </div>

        <div>
          <label class="block text-xs font-medium text-gray-700 mb-1">
            目標
          </label>
          <div class="text-sm text-gray-600 bg-gray-50 px-3 py-2 rounded-md">
            {{ selectedEdge.target }}
          </div>
        </div>

        <!-- Edge Label -->
        <div>
          <label class="block text-xs font-medium text-gray-700 mb-1">
            標籤
          </label>
          <input
            v-model="edgeData.label"
            @input="updateEdgeProperty('label', edgeData.label)"
            type="text"
            class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        <!-- Condition for conditional edges -->
        <div v-if="selectedEdge.data?.type === 'conditional'">
          <label class="block text-xs font-medium text-gray-700 mb-1">
            條件
          </label>
          <input
            v-model="edgeData.condition"
            @input="updateEdgeProperty('condition', edgeData.condition)"
            type="text"
            placeholder="例如：result === 'success'"
            class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        <!-- Delete Edge Button -->
        <div class="pt-3 border-t border-gray-200">
          <button
            @click="deleteEdge"
            class="w-full px-3 py-2 bg-red-600 text-white text-sm font-medium rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
          >
            刪除連線
          </button>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div v-else class="text-center py-8 text-gray-500 text-sm">
      <div class="mb-2">
        <Settings class="h-8 w-8 mx-auto text-gray-400" />
      </div>
      <p>選擇節點或連線以查看屬性</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, reactive } from "vue";
import { Settings } from "lucide-vue-next";

interface Node {
  id: string;
  type: string;
  position: { x: number; y: number };
  data: any;
}

interface Edge {
  id: string;
  source: string;
  target: string;
  data?: any;
}

// Props
const props = defineProps<{
  selectedNode?: Node | null;
  selectedEdge?: Edge | null;
}>();

// Emits
const emit = defineEmits<{
  updateNode: [nodeId: string, data: any];
  updateEdge: [edgeId: string, data: any];
  deleteEdge: [edgeId: string];
  deleteNode: [nodeId: string];
}>();

// Reactive state
const nodeData = reactive<any>({});
const edgeData = reactive<any>({});
const schemaText = ref("");
const schemaError = ref("JSON 格式無效");

// Computed
const nodeConfig = computed(() => {
  return props.selectedNode?.data || {};
});

// Watchers
watch(
  () => props.selectedNode,
  (newNode) => {
    if (newNode) {
      Object.assign(nodeData, newNode.data || {});

      // Initialize schema text for input/output nodes
      if (["input", "output"].includes(newNode.type) && newNode.data?.schema) {
        try {
          schemaText.value = JSON.stringify(newNode.data.schema, null, 2);
        } catch {
          schemaText.value = "";
        }
      } else {
        schemaText.value = "";
      }
      schemaError.value = "";
    }
  },
  { immediate: true }
);

watch(
  () => props.selectedEdge,
  (newEdge) => {
    if (newEdge) {
      Object.assign(edgeData, newEdge.data || {});
    }
  },
  { immediate: true }
);

// Methods
const updateNodeProperty = (key: string, value: any) => {
  if (!props.selectedNode) return;

  nodeData[key] = value;
  emit("updateNode", props.selectedNode.id, { [key]: value });
};

const updateEdgeProperty = (key: string, value: any) => {
  if (!props.selectedEdge) return;

  edgeData[key] = value;
  emit("updateEdge", props.selectedEdge.id, { [key]: value });
};

const updateSchema = () => {
  if (
    !props.selectedNode ||
    !["input", "output"].includes(props.selectedNode.type)
  )
    return;

  try {
    const schema = JSON.parse(schemaText.value);
    schemaError.value = "";
    updateNodeProperty("schema", schema);
  } catch (error) {
    schemaError.value = "JSON 格式無效";
  }
};

const deleteEdge = () => {
  if (!props.selectedEdge) return;

  emit("deleteEdge", props.selectedEdge.id);
};

const deleteNode = () => {
  if (!props.selectedNode) return;

  emit("deleteNode", props.selectedNode.id);
};
</script>
