<template>
  <Dialog v-model:open="isOpen">
    <DialogContent class="max-w-4xl">
      <DialogHeader>
        <DialogTitle>工作流程管理器</DialogTitle>
        <DialogDescription>
          載入現有工作流程或建立新的工作流程
        </DialogDescription>
      </DialogHeader>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 py-4">
        <!-- Create New Workflow -->
        <div class="space-y-4">
          <h3 class="text-lg font-medium">建立新工作流程</h3>

          <div class="space-y-3">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">
                工作流程名稱
              </label>
              <input
                v-model="newWorkflow.name"
                type="text"
                placeholder="輸入工作流程名稱"
                class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">
                描述
              </label>
              <textarea
                v-model="newWorkflow.description"
                rows="3"
                placeholder="輸入工作流程描述"
                class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">
                可見性
              </label>
              <select
                v-model="newWorkflow.visibility"
                class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="PRIVATE">私人</option>
                <option value="WORKSPACE">工作區</option>
                <option value="PUBLIC">公開</option>
              </select>
            </div>

            <Button
              @click="createWorkflow"
              :disabled="!newWorkflow.name || creating"
              class="w-full"
            >
              <Plus class="h-4 w-4 mr-2" />
              {{ creating ? "建立中..." : "建立工作流程" }}
            </Button>
          </div>
        </div>

        <!-- Load Existing Workflow -->
        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-medium">載入現有工作流程</h3>
            <Button
              @click="refreshWorkflows"
              variant="outline"
              size="sm"
              :disabled="loading"
            >
              <RefreshCw :class="['h-4 w-4', loading && 'animate-spin']" />
            </Button>
          </div>

          <!-- Search -->
          <div class="relative">
            <input
              v-model="searchQuery"
              type="text"
              placeholder="搜尋工作流程..."
              class="w-full px-3 py-2 pl-9 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <Search class="h-4 w-4 absolute left-3 top-2.5 text-gray-400" />
          </div>

          <!-- Workflow List -->
          <div class="border rounded-md max-h-80 overflow-y-auto">
            <div v-if="loading" class="p-8 text-center">
              <div
                class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500 mx-auto"
              ></div>
              <p class="text-sm text-gray-500 mt-2">載入工作流程中...</p>
            </div>

            <div
              v-else-if="filteredWorkflows.length === 0"
              class="p-8 text-center text-gray-500 text-sm"
            >
              <FileText class="h-8 w-8 mx-auto mb-2 text-gray-400" />
              <p>找不到工作流程</p>
            </div>

            <div v-else class="divide-y">
              <div
                v-for="workflow in filteredWorkflows"
                :key="workflow.id"
                class="p-3 hover:bg-gray-50 cursor-pointer transition-colors"
                @click="selectWorkflow(workflow)"
              >
                <div class="flex items-start space-x-3">
                  <div class="flex-shrink-0 mt-1">
                    <div
                      :class="[
                        'w-2 h-2 rounded-full',
                        workflow.is_published ? 'bg-green-500' : 'bg-gray-400',
                      ]"
                    ></div>
                  </div>

                  <div class="flex-1 min-w-0">
                    <p class="text-sm font-medium text-gray-900">
                      {{ workflow.name }}
                    </p>
                    <p
                      v-if="workflow.description"
                      class="text-xs text-gray-500 mt-1"
                    >
                      {{ workflow.description }}
                    </p>
                    <div
                      class="flex items-center space-x-4 mt-2 text-xs text-gray-500"
                    >
                      <span>{{ workflow.status }}</span>
                      <span>{{ workflow.visibility }}</span>
                      <span>{{ formatDate(workflow.updated_at) }}</span>
                    </div>
                  </div>

                  <div class="flex-shrink-0">
                    <Button
                      @click.stop="loadWorkflow(workflow.id)"
                      size="sm"
                      variant="outline"
                    >
                      載入
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from "vue";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Plus, Search, RefreshCw, FileText } from "lucide-vue-next";
import {
  useAiWorkflowsApi,
  type AiWorkflow,
} from "@/composables/useAiWorkflowsApi";
import { useNotification } from "@/composables/shared/useNotification";

// Props
const props = defineProps<{
  open: boolean;
}>();

// Emits
const emit = defineEmits<{
  "update:open": [value: boolean];
  "load-workflow": [workflowId: string];
  "create-workflow": [workflow: AiWorkflow];
}>();

// Reactive state
const isOpen = computed({
  get: () => props.open,
  set: (value) => emit("update:open", value),
});

const newWorkflow = ref({
  name: "",
  description: "",
  visibility: "PRIVATE" as const,
});

const searchQuery = ref("");
const workflows = ref<AiWorkflow[]>([]);
const creating = ref(false);

// API and services
const {
  getWorkflows,
  createWorkflow: apiCreateWorkflow,
  loading,
} = useAiWorkflowsApi();
const notification = useNotification();

// Computed
const filteredWorkflows = computed(() => {
  // 確保 workflows.value 存在且是陣列
  if (!workflows.value || !Array.isArray(workflows.value)) return [];

  const query = searchQuery.value.toLowerCase();
  if (!query) return workflows.value;

  return workflows.value.filter(
    (workflow) =>
      workflow.name.toLowerCase().includes(query) ||
      (workflow.description &&
        workflow.description.toLowerCase().includes(query)) ||
      workflow.status.toLowerCase().includes(query)
  );
});

// Methods
const refreshWorkflows = async () => {
  try {
    workflows.value = await getWorkflows();
  } catch (error) {
    console.error("Failed to load workflows:", error);
    notification.toast.error("載入工作流程失敗");
  }
};

const createWorkflow = async () => {
  if (!newWorkflow.value.name) return;

  creating.value = true;
  try {
    const workflow = await apiCreateWorkflow({
      name: newWorkflow.value.name,
      description: newWorkflow.value.description,
      visibility: newWorkflow.value.visibility,
    });

    emit("create-workflow", workflow);
    notification.toast.success("工作流程建立成功");

    // Reset form
    newWorkflow.value = {
      name: "",
      description: "",
      visibility: "PRIVATE",
    };

    // Refresh workflow list
    await refreshWorkflows();
  } catch (error) {
    console.error("Failed to create workflow:", error);
    notification.toast.error("建立工作流程失敗");
  } finally {
    creating.value = false;
  }
};

const selectWorkflow = (workflow: AiWorkflow) => {
  // Optional: Could show workflow details or preview here
};

const loadWorkflow = (workflowId: string) => {
  emit("load-workflow", workflowId);
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString();
};

// Watchers
watch(
  () => props.open,
  (isOpen) => {
    if (isOpen) {
      refreshWorkflows();
    }
  }
);

// Lifecycle
onMounted(() => {
  if (props.open) {
    refreshWorkflows();
  }
});
</script>
