<template>
  <div
    class="ai-bot-node bg-white border-2 border-blue-200 rounded-lg shadow-sm hover:shadow-md transition-shadow p-4 w-auto max-w-xs"
  >
    <!-- Node Header -->
    <div class="flex items-center space-x-2 mb-3">
      <Bot class="h-5 w-5 text-blue-600" />
      <h3 class="text-sm font-medium text-gray-900">
        {{ data.label || "AI 機器人" }}
      </h3>
    </div>

    <!-- Bot Info -->
    <div class="text-xs text-gray-600 mb-3">
      <div v-if="data.botId">機器人 ID：{{ data.botId }}</div>
      <div v-else class="text-orange-600">尚未配置</div>
    </div>

    <!-- Input Handle -->
    <Handle
      type="target"
      :position="Position.Left"
      class="w-3 h-3 !bg-blue-500 !border-2 !border-white !opacity-100"
      :connectable="true"
    />

    <!-- Output Handle -->
    <Handle
      type="source"
      :position="Position.Right"
      class="w-3 h-3 !bg-blue-500 !border-2 !border-white !opacity-100"
      :connectable="true"
    />
  </div>
</template>

<script setup lang="ts">
import { Bot } from "lucide-vue-next";
import { Handle, Position } from "@vue-flow/core";

// Props
defineProps<{
  id: string;
  data: any;
}>();
</script>

<style scoped>
.ai-bot-node {
  position: relative;
}
</style>
