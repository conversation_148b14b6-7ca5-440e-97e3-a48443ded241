<template>
  <div
    class="input-node bg-white border-2 border-green-200 rounded-lg shadow-sm hover:shadow-md transition-shadow p-4 w-auto max-w-xs"
  >
    <!-- Node Header -->
    <div class="flex items-center space-x-2 mb-3">
      <ArrowRight class="h-5 w-5 text-green-600" />
      <h3 class="text-sm font-medium text-gray-900">
        {{ data.label || "輸入" }}
      </h3>
    </div>

    <!-- Schema Info -->
    <div class="text-xs text-gray-600 mb-3">
      <div v-if="data.schema">已定義結構描述</div>
      <div v-else class="text-orange-600">無結構描述</div>
    </div>

    <!-- Output Handle -->
    <Handle
      type="source"
      :position="Position.Right"
      class="w-3 h-3 !bg-green-500 !border-2 !border-white !opacity-100"
      :connectable="true"
    />
  </div>
</template>

<script setup lang="ts">
import { ArrowRight } from "lucide-vue-next";
import { Handle, Position } from "@vue-flow/core";

// Props
defineProps<{
  id: string;
  data: any;
}>();
</script>

<style scoped>
.input-node {
  position: relative;
}
</style>
