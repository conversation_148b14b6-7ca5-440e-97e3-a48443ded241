<template>
  <div
    class="condition-node bg-white border-2 border-yellow-200 rounded-lg shadow-sm hover:shadow-md transition-shadow p-4 w-auto max-w-xs"
  >
    <!-- Node Header -->
    <div class="flex items-center space-x-2 mb-3">
      <GitBranch class="h-5 w-5 text-yellow-600" />
      <h3 class="text-sm font-medium text-gray-900">
        {{ data.label || "條件" }}
      </h3>
    </div>

    <!-- Expression Info -->
    <div class="text-xs text-gray-600 mb-3">
      <div v-if="data.expression" class="font-mono">{{ data.expression }}</div>
      <div v-else class="text-orange-600">無表達式</div>
    </div>

    <!-- Input Handle -->
    <Handle
      type="target"
      :position="Position.Left"
      class="w-3 h-3 !bg-yellow-500 !border-2 !border-white !opacity-100"
      :connectable="true"
    />

    <!-- Output Handles -->
    <Handle
      id="true"
      type="source"
      :position="Position.Right"
      :style="{ top: '30%' }"
      class="w-3 h-3 !bg-green-500 !border-2 !border-white !opacity-100"
      :connectable="true"
    />
    <Handle
      id="false"
      type="source"
      :position="Position.Right"
      :style="{ top: '70%' }"
      class="w-3 h-3 !bg-red-500 !border-2 !border-white !opacity-100"
      :connectable="true"
    />
  </div>
</template>

<script setup lang="ts">
import { GitBranch } from "lucide-vue-next";
import { Handle, Position } from "@vue-flow/core";

// Props
defineProps<{
  id: string;
  data: any;
}>();
</script>

<style scoped>
.condition-node {
  position: relative;
}
</style>
