<template>
  <div>
    <div
      class="node-container p-4 bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200"
      :class="{ 'border-primary': selected }"
    >
      <div class="flex items-center justify-between mb-2">
        <div class="flex items-center">
          <div class="p-1.5 rounded-md bg-purple-100 mr-2">
            <i class="i-lucide-globe w-4 h-4 text-purple-600" />
          </div>
          <h3 class="font-medium text-sm text-gray-900">
            {{ data.label || "API 調用" }}
          </h3>
        </div>
        <div class="flex items-center space-x-1">
          <button
            @click="emit('nodeConfig', id)"
            class="p-1 text-gray-400 hover:text-gray-600 rounded hover:bg-gray-100"
          >
            <i class="i-lucide-settings w-3.5 h-3.5" />
          </button>
        </div>
      </div>

      <div class="text-xs text-gray-500 mt-1 mb-3">
        {{ data.description || "調用外部 API" }}
      </div>

      <div class="bg-gray-50 p-2 rounded-md text-xs">
        <div class="flex items-center mb-1">
          <span class="font-semibold mr-1">URL:</span>
          <span class="font-mono truncate text-blue-600">{{
            data.url || "..."
          }}</span>
        </div>
        <div class="flex items-center">
          <span class="font-semibold mr-1">Method:</span>
          <span class="font-mono">
            <span
              :class="{
                'text-green-600': data.method === 'GET',
                'text-blue-600': data.method === 'POST',
                'text-amber-600': data.method === 'PUT',
                'text-red-600': data.method === 'DELETE',
                'text-gray-600': !data.method,
              }"
            >
              {{ data.method || "GET" }}
            </span>
          </span>
        </div>
      </div>
    </div>

    <div class="nodrag">
      <Handle
        type="target"
        :position="Position.Left"
        :id="'input'"
        class="w-3 h-3 rounded-full border-2 border-white bg-gray-400 -ml-1.5 nodrag"
      />
      <Handle
        type="source"
        :position="Position.Right"
        :id="'output'"
        class="w-3 h-3 rounded-full border-2 border-white bg-gray-400 -mr-1.5 nodrag"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { Handle, Position } from "@vue-flow/core";

const props = defineProps<{
  id: string;
  data: any;
  selected: boolean;
}>();

const emit = defineEmits<{
  (e: "nodeConfig", id: string): void;
}>();
</script>
