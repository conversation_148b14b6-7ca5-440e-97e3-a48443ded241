<template>
  <div>
    <div
      class="node-container p-4 bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200"
      :class="{ 'border-primary': selected }"
    >
      <div class="flex items-center justify-between mb-2">
        <div class="flex items-center">
          <div class="p-1.5 rounded-md bg-blue-100 mr-2">
            <i class="i-lucide-settings-2 w-4 h-4 text-blue-600" />
          </div>
          <h3 class="font-medium text-sm text-gray-900">
            {{ data.label || "資料轉換" }}
          </h3>
        </div>
        <div class="flex items-center space-x-1">
          <button
            @click="emit('nodeConfig', id)"
            class="p-1 text-gray-400 hover:text-gray-600 rounded hover:bg-gray-100"
          >
            <i class="i-lucide-settings w-3.5 h-3.5" />
          </button>
        </div>
      </div>

      <div class="text-xs text-gray-500 mt-1 mb-3">
        {{ data.description || "轉換資料格式" }}
      </div>

      <div class="bg-gray-50 p-2 rounded-md text-xs font-mono">
        <div v-if="data.transformType === 'filter'" class="text-green-600">
          filter({{ data.filterCondition || "..." }})
        </div>
        <div v-else-if="data.transformType === 'map'" class="text-blue-600">
          map({{ data.mapExpression || "..." }})
        </div>
        <div v-else-if="data.transformType === 'reduce'" class="text-amber-600">
          reduce({{ data.reduceExpression || "..." }})
        </div>
        <div v-else class="text-gray-400">transform(...)</div>
      </div>
    </div>

    <div class="nodrag">
      <Handle
        type="target"
        :position="Position.Left"
        :id="'input'"
        class="w-3 h-3 rounded-full border-2 border-white bg-gray-400 -ml-1.5 nodrag"
      />
      <Handle
        type="source"
        :position="Position.Right"
        :id="'output'"
        class="w-3 h-3 rounded-full border-2 border-white bg-gray-400 -mr-1.5 nodrag"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { Handle, Position } from "@vue-flow/core";

const props = defineProps<{
  id: string;
  data: any;
  selected: boolean;
}>();

const emit = defineEmits<{
  (e: "nodeConfig", id: string): void;
}>();
</script>
