<template>
  <div class="space-y-4">
    <!-- Search -->
    <div class="relative">
      <input
        v-model="searchQuery"
        type="text"
        placeholder="搜尋節點..."
        class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
      />
      <Search class="h-4 w-4 absolute right-3 top-2.5 text-gray-400" />
    </div>

    <!-- Node Categories -->
    <div class="space-y-3">
      <div
        v-for="category in filteredCategories"
        :key="category.name"
        class="space-y-2"
      >
        <button
          @click="toggleCategory(category.name)"
          class="flex items-center justify-between w-full text-left text-sm font-medium text-gray-700 hover:text-gray-900"
        >
          <span>{{ category.name }}</span>
          <ChevronDown
            :class="[
              'h-4 w-4 transition-transform',
              expandedCategories.includes(category.name) ? 'rotate-180' : '',
            ]"
          />
        </button>

        <div
          v-if="expandedCategories.includes(category.name)"
          class="space-y-1 pl-2"
        >
          <div
            v-for="nodeType in category.nodes"
            :key="nodeType.type"
            :draggable="true"
            @dragstart="handleDragStart(nodeType.type, $event)"
            @dragend="handleDragEnd"
            class="p-3 bg-gray-50 border border-gray-200 rounded-lg cursor-grab hover:bg-gray-100 hover:border-gray-300 hover:shadow-sm transition-all duration-200 select-none"
            :class="{ 'cursor-grabbing': isDragging }"
          >
            <div class="flex items-start space-x-3">
              <div class="flex-shrink-0">
                <component
                  :is="getNodeIcon(nodeType.type)"
                  class="h-5 w-5 text-gray-600"
                />
              </div>
              <div class="flex-1 min-w-0">
                <p class="text-sm font-medium text-gray-900">
                  {{ nodeType.name }}
                </p>
                <p class="text-xs text-gray-500 mt-1">
                  {{ nodeType.description }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Loading state -->
    <div v-if="loading" class="flex items-center justify-center py-8">
      <div
        class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"
      ></div>
    </div>

    <!-- Empty state -->
    <div
      v-if="!loading && filteredCategories.length === 0"
      class="text-center py-8 text-gray-500 text-sm"
    >
      找不到符合 "{{ searchQuery }}" 的節點
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import {
  Search,
  ChevronDown,
  Bot,
  ArrowRight,
  Filter,
  Database,
  Mail,
  Webhook,
  FileText,
  RotateCw,
  GitBranch,
} from "lucide-vue-next";
import { useAiWorkflowsApi } from "@/composables/useAiWorkflowsApi";

interface NodeType {
  type: string;
  name: string;
  description: string;
  category: string;
}

interface NodeCategory {
  name: string;
  nodes: NodeType[];
}

// Emits
const emit = defineEmits<{
  "drag-start": [nodeType: string];
}>();

// Reactive state
const searchQuery = ref("");
const expandedCategories = ref<string[]>(["人工智慧", "輸入輸出"]); // Default expanded categories
const nodeTypes = ref<NodeType[]>([]);
const isDragging = ref(false);

// API
const { getAvailableNodeTypes, loading } = useAiWorkflowsApi();

// Computed
const filteredCategories = computed<NodeCategory[]>(() => {
  const query = searchQuery.value.toLowerCase();

  // Group nodes by category
  const categoryMap = new Map<string, NodeType[]>();

  // 確保 nodeTypes.value 是陣列
  const nodes = nodeTypes.value || [];

  nodes
    .filter(
      (node) =>
        !query ||
        node.name.toLowerCase().includes(query) ||
        node.description.toLowerCase().includes(query) ||
        node.type.toLowerCase().includes(query)
    )
    .forEach((node) => {
      if (!categoryMap.has(node.category)) {
        categoryMap.set(node.category, []);
      }
      categoryMap.get(node.category)!.push(node);
    });

  // Convert to array and sort
  return Array.from(categoryMap.entries())
    .map(([name, nodes]) => ({ name, nodes }))
    .sort((a, b) => {
      // Priority order for categories
      const order = [
        "輸入輸出",
        "人工智慧",
        "資料處理",
        "流程控制",
        "系統整合",
        "通訊",
      ];
      const aIndex = order.indexOf(a.name);
      const bIndex = order.indexOf(b.name);

      if (aIndex !== -1 && bIndex !== -1) {
        return aIndex - bIndex;
      } else if (aIndex !== -1) {
        return -1;
      } else if (bIndex !== -1) {
        return 1;
      } else {
        return a.name.localeCompare(b.name);
      }
    });
});

// Methods
const toggleCategory = (categoryName: string) => {
  const index = expandedCategories.value.indexOf(categoryName);
  if (index > -1) {
    expandedCategories.value.splice(index, 1);
  } else {
    expandedCategories.value.push(categoryName);
  }
};

const handleDragStart = (nodeType: string, event: DragEvent) => {
  console.log("NodePalette: Drag start for", nodeType); // Debug log

  if (!event.dataTransfer) {
    console.error("DataTransfer not available");
    return;
  }

  isDragging.value = true;

  event.dataTransfer.effectAllowed = "move";
  event.dataTransfer.setData("text/plain", nodeType);
  event.dataTransfer.setData("application/vueflow", nodeType);

  // 設置拖拽圖像
  const dragImage = event.target as HTMLElement;
  if (dragImage) {
    event.dataTransfer.setDragImage(dragImage, 50, 25);
  }

  emit("drag-start", nodeType);
  console.log("NodePalette: Drag start event emitted"); // Debug log
};

const handleDragEnd = () => {
  console.log("NodePalette: Drag end"); // Debug log
  isDragging.value = false;
};

const getNodeIcon = (nodeType: string) => {
  const iconMap: Record<string, any> = {
    INPUT: ArrowRight,
    OUTPUT: ArrowRight,
    AI_BOT: Bot,
    AI_ANALYSIS: Bot,
    PROMPT_TEMPLATE: FileText,
    DATA_TRANSFORM: Filter,
    DATA_FILTER: Filter,
    DATA_MERGE: GitBranch,
    CONDITION: GitBranch,
    LOOP: RotateCw,
    PARALLEL: GitBranch,
    API_CALL: Webhook,
    DATABASE: Database,
    FILE_OPERATION: FileText,
    NOTIFICATION: Mail,
    EMAIL: Mail,
    WEBHOOK: Webhook,
  };

  return iconMap[nodeType] || Bot;
};

// Lifecycle
onMounted(async () => {
  // 先設置預設的節點類型，確保界面有內容顯示
  nodeTypes.value = [
    {
      type: "input",
      name: "輸入節點",
      description: "接收外部輸入資料",
      category: "輸入輸出",
    },
    {
      type: "output",
      name: "輸出節點",
      description: "輸出處理結果",
      category: "輸入輸出",
    },
    {
      type: "ai-bot",
      name: "AI 機器人",
      description: "執行 AI 對話和處理",
      category: "人工智慧",
    },
    {
      type: "condition",
      name: "條件判斷",
      description: "根據條件分支執行",
      category: "流程控制",
    },
    {
      type: "data-transform",
      name: "資料轉換",
      description: "處理和轉換資料格式",
      category: "資料處理",
    },
    {
      type: "api-call",
      name: "API 調用",
      description: "調用外部 API 服務",
      category: "系統整合",
    },
  ];

  // 嘗試從 API 載入節點類型
  try {
    const apiNodeTypes = await getAvailableNodeTypes();
    // 只有當 API 返回有效資料時才更新
    if (Array.isArray(apiNodeTypes) && apiNodeTypes.length > 0) {
      nodeTypes.value = apiNodeTypes;
    }
  } catch (error) {
    console.error("Failed to load node types:", error);
    // 保持使用預設的節點類型
  }
});
</script>
