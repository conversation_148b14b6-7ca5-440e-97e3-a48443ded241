<template>
  <div class="space-y-4">
    <!-- Help Header with Search -->
    <div
      class="bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg p-4 text-white"
    >
      <div class="flex items-center justify-between mb-3">
        <h2 class="text-lg font-semibold">AI 工作流程助手</h2>
        <div class="flex items-center space-x-2">
          <button
            @click="showQuickTips = !showQuickTips"
            :class="[
              'text-xs px-2 py-1 rounded',
              showQuickTips
                ? 'bg-white text-blue-600'
                : 'bg-blue-400 text-white',
            ]"
          >
            快速提示
          </button>
        </div>
      </div>

      <!-- Search Box -->
      <div class="relative">
        <input
          v-model="searchQuery"
          type="text"
          placeholder="搜索說明內容..."
          class="w-full px-3 py-2 pl-10 text-gray-900 bg-white rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-300"
        />
        <svg
          class="absolute left-3 top-2.5 w-4 h-4 text-gray-400"
          fill="currentColor"
          viewBox="0 0 20 20"
        >
          <path
            fill-rule="evenodd"
            d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
            clip-rule="evenodd"
          />
        </svg>
      </div>
    </div>

    <!-- Quick Tips (Collapsible) -->
    <div
      v-if="showQuickTips"
      class="bg-blue-50 border border-blue-200 rounded-lg p-3"
    >
      <div class="text-sm text-blue-800">
        <div class="font-medium mb-2">💡 今日提示</div>
        <p>{{ getCurrentTip() }}</p>
      </div>
    </div>

    <div class="space-y-6">
      <!-- Contextual Help for Selected Items -->
      <div v-if="selectedNode || selectedEdge" class="space-y-4">
        <!-- Node Help -->
        <div
          v-if="selectedNode"
          class="bg-blue-50 border border-blue-200 rounded-lg p-4"
        >
          <div class="flex items-center space-x-2 mb-3">
            <div class="w-3 h-3 rounded-full bg-blue-500"></div>
            <h3 class="text-sm font-semibold text-blue-900">
              {{ getNodeTypeDisplayName(selectedNode.type) }} 節點說明
            </h3>
          </div>

          <div class="text-sm text-blue-800 space-y-2">
            <p>{{ getNodeDescription(selectedNode.type) }}</p>

            <div class="mt-3">
              <h4 class="font-medium mb-2">配置要點：</h4>
              <ul class="list-disc list-inside space-y-1 text-xs">
                <li v-for="tip in getNodeTips(selectedNode.type)" :key="tip">
                  {{ tip }}
                </li>
              </ul>
            </div>
          </div>
        </div>

        <!-- Edge Help -->
        <div
          v-if="selectedEdge"
          class="bg-green-50 border border-green-200 rounded-lg p-4"
        >
          <div class="flex items-center space-x-2 mb-3">
            <div class="w-3 h-3 rounded-full bg-green-500"></div>
            <h3 class="text-sm font-semibold text-green-900">連線說明</h3>
          </div>

          <div class="text-sm text-green-800 space-y-2">
            <p>
              此連線將
              <span class="font-medium">{{ selectedEdge.source }}</span>
              的輸出連接到
              <span class="font-medium">{{ selectedEdge.target }}</span>
              的輸入。
            </p>

            <div class="mt-3">
              <h4 class="font-medium mb-2">連線管理：</h4>
              <ul class="list-disc list-inside space-y-1 text-xs">
                <li>可以在屬性面板中設置連線標籤</li>
                <li>按 Delete 鍵或右鍵可刪除連線</li>
                <li>條件節點支援多個輸出連線</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <!-- General Help Sections -->
      <div class="space-y-4">
        <!-- Quick Start -->
        <div class="bg-gray-50 rounded-lg p-4">
          <h3
            class="text-sm font-semibold text-gray-900 mb-3 flex items-center"
          >
            <svg
              class="w-4 h-4 mr-2 text-gray-600"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fill-rule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                clip-rule="evenodd"
              />
            </svg>
            快速開始
          </h3>
          <div class="text-sm text-gray-700 space-y-2">
            <ol class="list-decimal list-inside space-y-1">
              <li>從左側節點調色盤拖拽節點到畫布</li>
              <li>點擊節點配置其屬性</li>
              <li>拖拽節點的連接點建立連線</li>
              <li>點擊工具列的「儲存」保存工作流程</li>
            </ol>
          </div>
        </div>

        <!-- Node Types -->
        <div class="bg-gray-50 rounded-lg p-4">
          <h3
            class="text-sm font-semibold text-gray-900 mb-3 flex items-center"
          >
            <svg
              class="w-4 h-4 mr-2 text-gray-600"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"
              />
            </svg>
            節點類型說明
          </h3>
          <div class="space-y-3 text-xs">
            <div
              v-for="nodeType in nodeTypes"
              :key="nodeType.type"
              class="flex items-start space-x-3 p-2 bg-white rounded border"
            >
              <div
                :class="['w-3 h-3 rounded-full mt-0.5', nodeType.color]"
              ></div>
              <div class="flex-1">
                <div class="font-medium text-gray-900">{{ nodeType.name }}</div>
                <div class="text-gray-600 mt-1">{{ nodeType.description }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- Keyboard Shortcuts -->
        <div class="bg-gray-50 rounded-lg p-4">
          <h3
            class="text-sm font-semibold text-gray-900 mb-3 flex items-center"
          >
            <svg
              class="w-4 h-4 mr-2 text-gray-600"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fill-rule="evenodd"
                d="M3 5a2 2 0 012-2h10a2 2 0 012 2v8a2 2 0 01-2 2h-2.22l.123.489.804.804A1 1 0 0113 18H7a1 1 0 01-.707-1.707l.804-.804L7.22 15H5a2 2 0 01-2-2V5zm5.771 7H5V5h10v7H8.771z"
                clip-rule="evenodd"
              />
            </svg>
            鍵盤快捷鍵
          </h3>
          <div class="space-y-2 text-xs">
            <div
              v-for="shortcut in shortcuts"
              :key="shortcut.key"
              class="flex justify-between items-center py-1"
            >
              <span class="text-gray-700">{{ shortcut.description }}</span>
              <kbd
                class="px-2 py-1 bg-white border border-gray-300 rounded text-gray-600 font-mono"
              >
                {{ shortcut.key }}
              </kbd>
            </div>
          </div>
        </div>

        <!-- Best Practices -->
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h3
            class="text-sm font-semibold text-yellow-900 mb-3 flex items-center"
          >
            <svg
              class="w-4 h-4 mr-2 text-yellow-600"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fill-rule="evenodd"
                d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                clip-rule="evenodd"
              />
            </svg>
            最佳實踐
          </h3>
          <div class="text-sm text-yellow-800 space-y-2">
            <ul class="list-disc list-inside space-y-1">
              <li>使用清晰的節點標籤名稱</li>
              <li>定期儲存工作流程</li>
              <li>合理安排節點布局，避免連線交叉</li>
              <li>測試工作流程的邏輯正確性</li>
              <li>為複雜條件添加註釋說明</li>
            </ul>
          </div>
        </div>

        <!-- Troubleshooting -->
        <div class="bg-red-50 border border-red-200 rounded-lg p-4">
          <h3 class="text-sm font-semibold text-red-900 mb-3 flex items-center">
            <svg
              class="w-4 h-4 mr-2 text-red-600"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fill-rule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                clip-rule="evenodd"
              />
            </svg>
            常見問題
          </h3>
          <div class="text-sm text-red-800 space-y-2">
            <div class="space-y-3">
              <div>
                <div class="font-medium">無法建立連線？</div>
                <div class="text-xs mt-1">
                  確保節點有可見的連接點，嘗試縮放畫布或檢查節點是否設為可連接。
                </div>
              </div>
              <div>
                <div class="font-medium">節點無法拖拽？</div>
                <div class="text-xs mt-1">
                  確保點擊的是節點本體而非連接點，避免在連接模式下拖拽。
                </div>
              </div>
              <div>
                <div class="font-medium">儲存失敗？</div>
                <div class="text-xs mt-1">
                  檢查網路連接，確保工作流程有有效的名稱和描述。
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from "vue";

interface Props {
  selectedNode?: any;
  selectedEdge?: any;
}

const props = defineProps<Props>();

// Reactive data
const searchQuery = ref("");
const showQuickTips = ref(true);

// Quick tips rotation
const quickTips = [
  "拖拽節點時，可以按住 Shift 鍵來複製節點",
  "使用 Ctrl+滾輪 可以快速縮放畫布",
  "雙擊空白區域可以快速適應畫布大小",
  "定期儲存工作流程可以避免意外丟失",
  "合理使用條件節點可以建立複雜的邏輯流程",
  "為節點設置清晰的標籤名稱有助於維護",
];

const getCurrentTip = (): string => {
  const today = new Date().getDate();
  return quickTips[today % quickTips.length];
};

// Computed properties for search filtering
const filteredContent = computed(() => {
  if (!searchQuery.value) return true;

  const query = searchQuery.value.toLowerCase();

  // Check if any content matches the search query
  const nodeTypeMatches = nodeTypes.some(
    (nt) =>
      nt.name.toLowerCase().includes(query) ||
      nt.description.toLowerCase().includes(query)
  );

  const shortcutMatches = shortcuts.some(
    (sc) =>
      sc.description.toLowerCase().includes(query) ||
      sc.key.toLowerCase().includes(query)
  );

  return (
    nodeTypeMatches ||
    shortcutMatches ||
    "快速開始".includes(query) ||
    "最佳實踐".includes(query) ||
    "常見問題".includes(query)
  );
});

const shouldShowSection = (sectionContent: string): boolean => {
  if (!searchQuery.value) return true;
  return sectionContent.toLowerCase().includes(searchQuery.value.toLowerCase());
};

// Node types information
const nodeTypes = [
  {
    type: "input",
    name: "輸入節點",
    description: "定義工作流程的輸入參數和資料結構",
    color: "bg-green-500",
  },
  {
    type: "ai-bot",
    name: "AI 機器人",
    description: "執行 AI 處理任務，如文本生成、分析等",
    color: "bg-blue-500",
  },
  {
    type: "condition",
    name: "條件節點",
    description: "根據條件判斷控制流程分支",
    color: "bg-yellow-500",
  },
  {
    type: "output",
    name: "輸出節點",
    description: "定義工作流程的輸出格式和結果",
    color: "bg-red-500",
  },
];

// Keyboard shortcuts
const shortcuts = [
  { key: "Delete", description: "刪除選中的節點或連線" },
  { key: "Backspace", description: "刪除選中的節點或連線" },
  { key: "Ctrl+S", description: "儲存工作流程" },
  { key: "滑鼠拖拽", description: "移動節點位置" },
  { key: "右鍵", description: "顯示連線刪除選項" },
];

// Helper functions
const getNodeTypeDisplayName = (type: string): string => {
  const nodeType = nodeTypes.find((nt) => nt.type === type);
  return nodeType ? nodeType.name : type;
};

const getNodeDescription = (type: string): string => {
  const descriptions: Record<string, string> = {
    input:
      "此節點用於定義工作流程的輸入參數。您可以設置資料結構和驗證規則，確保輸入資料的正確性。",
    "ai-bot":
      "此節點執行 AI 相關任務。請選擇適當的機器人 ID 並配置相關參數，確保 AI 服務能正確處理資料。",
    condition:
      "此節點用於流程控制。設置條件表達式來決定資料流的走向，支援複雜的邏輯判斷。",
    output:
      "此節點定義工作流程的最終輸出。配置輸出格式和資料結構，確保結果符合預期。",
  };
  return descriptions[type] || "此節點的詳細說明尚未提供。";
};

const getNodeTips = (type: string): string[] => {
  const tips: Record<string, string[]> = {
    input: [
      "設置清晰的標籤名稱",
      "定義完整的資料結構",
      "添加必要的驗證規則",
      "考慮預設值的設置",
    ],
    "ai-bot": [
      "選擇正確的機器人 ID",
      "配置適當的處理參數",
      "測試 AI 服務的可用性",
      "設置錯誤處理策略",
    ],
    condition: [
      "使用簡潔的條件表達式",
      "考慮所有可能的情況",
      "為每個分支添加標籤",
      "測試條件邏輯的正確性",
    ],
    output: [
      "定義清晰的輸出格式",
      "考慮資料的後續使用",
      "添加適當的元數據",
      "測試輸出結果的完整性",
    ],
  };
  return tips[type] || ["請查閱相關文檔以獲取更多信息"];
};
</script>
