<!-- AIKeyEditor.vue -->
<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Info, Key, Loader2, CheckCircle, X } from "lucide-vue-next";
import { AiKeysService } from "@/services/admin/ai/ai-keys.service";
import { useNotification } from "@/composables/shared/useNotification";

// 定義供應商類型
const KEY_PROVIDERS = [
  {
    key: "openai",
    name: "OpenAI",
    url: "https://platform.openai.com/api-keys",
  },
  {
    key: "anthropic",
    name: "Anthropic Claude",
    url: "https://console.anthropic.com/keys",
  },
  {
    key: "google-gemini",
    name: "Google Gemini",
    url: "https://aistudio.google.com/app/apikey",
  },
  { key: "openai-compatible", name: "OpenAI 相容性", url: "" },
] as const;

// 將常數曝露出去給父元件使用
defineExpose({
  KEY_PROVIDERS,
});

type ProviderKey = (typeof KEY_PROVIDERS)[number]["key"];

// 定義 props
const props = defineProps<{
  keyData: {
    id?: string;
    name?: string;
    provider?: string;
    api_key?: string;
    api_url?: string | null;
    description?: string | null;
    is_enabled?: boolean;
    last_test?: string | null;
  } | null;
  isEditing?: boolean;
}>();

// 定義事件
const emit = defineEmits<{
  (e: "save", data: any): void;
  (e: "cancel"): void;
  (e: "verify", result: { success: boolean }): void;
}>();

// 本地狀態
const localKeyData = ref({
  id: props.keyData?.id || "",
  name: props.keyData?.name || "",
  provider: props.keyData?.provider || "openai",
  api_key: props.keyData?.api_key || "",
  api_url: props.keyData?.api_url || "",
  description: props.keyData?.description || "",
  is_enabled:
    props.keyData?.is_enabled !== undefined ? props.keyData.is_enabled : true,
  last_test: props.keyData?.last_test || null,
});

// 確認對話框狀態
const showUnsavedAlert = ref(false);

// 跟踪初始數據以檢查是否有未保存的變更
const initialData = ref({});
watch(
  () => props.keyData,
  (newData) => {
    if (newData) {
      initialData.value = JSON.parse(JSON.stringify(newData));
    }
  },
  { immediate: true, deep: true }
);

// 檢查是否有未保存的變更
const hasUnsavedChanges = computed(() => {
  if (!props.keyData) return false;
  return (
    JSON.stringify(localKeyData.value) !== JSON.stringify(initialData.value)
  );
});

// 驗證相關狀態
const isVerifying = ref(false);
const verifyStatus = ref<"idle" | "verifying" | "success" | "error">("idle");
const apiKeyError = ref<string | null>(null);

// 計算當前供應商名稱
const providerName = computed(() => {
  return (
    KEY_PROVIDERS.find((p) => p.key === localKeyData.value.provider)?.name ||
    "供應商"
  );
});

// 驗證 API key 格式
const validateApiKey = (
  provider: string,
  key: string
): { valid: boolean; message?: string } => {
  if (!key.trim()) {
    return { valid: false, message: "金鑰不能為空" };
  }

  switch (provider) {
    case "openai":
      if (!key.startsWith("sk-"))
        return { valid: false, message: "OpenAI 金鑰必須以 sk- 開頭" };
      if (key.length < 30)
        return { valid: false, message: "OpenAI 金鑰長度不足" };
      break;
    case "anthropic":
      if (!key.startsWith("sk-ant-"))
        return {
          valid: false,
          message: "Anthropic (Claude) 金鑰必須以 sk-ant- 開頭",
        };
      if (key.length < 40)
        return { valid: false, message: "Anthropic (Claude) 金鑰長度不足" };
      break;
    case "google-gemini":
      if (key.length < 30)
        return { valid: false, message: "Google Gemini 金鑰長度不足" };
      if (!/^[a-zA-Z0-9_\\-]+$/.test(key))
        return { valid: false, message: "Google Gemini 金鑰包含無效字元" };
      break;
    case "openai-compatible":
      if (key.length < 8) return { valid: false, message: "金鑰長度不足" };
      break;
  }

  return { valid: true };
};

// 處理供應商變更 (用於 v-model on Select)
const handleProviderChange = (value: any) => {
  const providerValue = String(value ?? ''); // 確保是 string
  localKeyData.value.provider = providerValue;
  validateApiKeyFormat(); // 仍需驗證格式
  if (providerValue !== "openai-compatible") {
    localKeyData.value.api_url = "";
  }
};

// 驗證 API key 格式
const validateApiKeyFormat = () => {
  const provider = localKeyData.value.provider;
  const key = localKeyData.value.api_key;

  if (!key) {
    apiKeyError.value = props.isEditing ? null : "請輸入 API Key";
    return;
  }

  const validation = validateApiKey(provider, key);
  apiKeyError.value = validation.valid
    ? null
    : validation.message || "金鑰格式無效";
};

// 初始化通知
const notification = useNotification();

// 模擬驗證金鑰
const handleVerify = async () => {
  if (apiKeyError.value) return;
  
  // 檢查是否為測試金鑰
  if (localKeyData.value.api_key.includes('sk-test-')) {
    notification.toast.error("不能使用測試金鑰 (sk-test-...)，請使用有效的 API 金鑰");
    return;
  }

  isVerifying.value = true;
  verifyStatus.value = "verifying";

  try {
    // 實際調用後端 API 驗證
    const result = await AiKeysService.testKeyPlaintext({
      provider: localKeyData.value.provider,
      apiKey: localKeyData.value.api_key,
      apiUrl: localKeyData.value.api_url || undefined
    });
    
    const success = result.success;

    verifyStatus.value = success ? "success" : "error";
    
    if (success) {
      // 更新驗證時間
      localKeyData.value.last_test = new Date().toISOString();
    } else {
      notification.toast.error(`API 金鑰驗證失敗: ${(result as any)?.message || '未知錯誤'}`);
    }
    
    emit("verify", { success });
  } catch (error) {
    console.error('驗證金鑰時發生錯誤:', error);
    verifyStatus.value = "error";
    notification.toast.error(`驗證過程中發生錯誤: ${(error as any)?.message || '網路錯誤'}`);
    emit("verify", { success: false });
  } finally {
    isVerifying.value = false;
  }
};

// 保存變更
const handleSave = () => {
  if (!localKeyData.value.name.trim()) {
    apiKeyError.value = "請輸入金鑰名稱";
    return;
  }

  if (!props.isEditing && !localKeyData.value.api_key) {
    apiKeyError.value = "請輸入 API Key";
    return;
  }

  if (apiKeyError.value) return;

  // 發送保存事件 - 修正欄位名稱對應
  const saveData = {
    id: localKeyData.value.id,
    name: localKeyData.value.name,
    provider: localKeyData.value.provider,
    api_key: localKeyData.value.api_key,  // 確保使用 api_key 
    api_url: localKeyData.value.api_url,
    description: localKeyData.value.description,
    is_enabled: localKeyData.value.is_enabled
  };
  
  emit("save", saveData);
};

// 取消編輯
const handleCancel = () => {
  if (hasUnsavedChanges.value) {
    showUnsavedAlert.value = true;
  } else {
    emit("cancel");
  }
};

// 確認放棄變更
const confirmCancel = () => {
  showUnsavedAlert.value = false;
  emit("cancel");
};

// 格式化驗證時間
const formattedLastTestTime = computed(() => {
  if (!localKeyData.value.last_test) return "尚未驗證";

  return new Date(localKeyData.value.last_test).toLocaleString("zh-TW", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
  });
});

// 當 props.keyData?.name 變動時，若 localKeyData.name 尚未被手動編輯則自動同步
watch(
  () => props.keyData?.name,
  (newName, oldName) => {
    // 僅當 localKeyData.name 尚未被手動編輯時才同步
    if (localKeyData.value.name === oldName) {
      localKeyData.value.name = newName || "";
    }
  }
);
</script>

<template>
  <div class="space-y-6">
    <div class="grid grid-cols-2 gap-6">
      <!-- 供應商選擇 -->
      <div class="space-y-2">
        <div class="flex items-center gap-1.5">
          <Label class="text-sm font-medium">供應商</Label>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  class="h-5 w-5 rounded-full p-0"
                >
                  <Info class="h-3.5 w-3.5 text-muted-foreground" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="right">
                <p class="max-w-xs">
                  選擇 AI 供應商。每個供應商需要不同的 API 金鑰格式
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
        <Select
          v-model="localKeyData.provider"
          :disabled="isEditing || isVerifying"
          @update:modelValue="handleProviderChange"
        >
          <SelectTrigger class="w-full">
            <SelectValue placeholder="選擇供應商" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem
              v-for="p in KEY_PROVIDERS"
              :key="p.key"
              :value="p.key"
              >{{ p.name }}</SelectItem
            >
          </SelectContent>
        </Select>
      </div>

      <!-- 金鑰名稱 -->
      <div class="space-y-2">
        <div class="flex items-center gap-1.5">
          <Label class="text-sm font-medium">名稱</Label>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  class="h-5 w-5 rounded-full p-0"
                >
                  <Info class="h-3.5 w-3.5 text-muted-foreground" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="right">
                <p class="max-w-xs">
                  這個金鑰的名稱，例如「產品描述生成」、「客服對話模型」等
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
        <Input
          v-model="localKeyData.name"
          placeholder="例如：產品描述生成、客服對話模型等"
          :disabled="isVerifying"
        />
      </div>

      <!-- 詳細說明 -->
      <div class="space-y-2 col-span-2">
        <div class="flex items-center gap-1.5">
          <Label class="text-sm font-medium">詳細說明（選填）</Label>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  class="h-5 w-5 rounded-full p-0"
                >
                  <Info class="h-3.5 w-3.5 text-muted-foreground" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="right">
                <p class="max-w-xs">
                  可選的詳細說明，用於記錄這個金鑰的詳細用途或注意事項
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
        <div class="relative">
          <Textarea
            v-model="localKeyData.description"
            placeholder="詳細描述此金鑰的用途、使用場景或其他注意事項"
            rows="3"
            class="pr-10"
            :disabled="isVerifying"
          />
          <button
            v-if="localKeyData.description"
            type="button"
            class="absolute right-2 top-2 text-muted-foreground hover:text-destructive"
            @click="localKeyData.description = ''"
            tabindex="-1"
            aria-label="清除說明"
            :disabled="isVerifying"
          >
            <X class="h-4 w-4" />
          </button>
        </div>
      </div>
    </div>

    <!-- API Key -->
    <div class="space-y-2">
      <div class="flex items-center gap-1.5">
        <Label class="text-sm font-medium">API Key</Label>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                class="h-5 w-5 rounded-full p-0"
              >
                <Info class="h-3.5 w-3.5 text-muted-foreground" />
              </Button>
            </TooltipTrigger>
            <TooltipContent side="right">
              <p class="max-w-xs">
                請輸入從供應商處獲取的 API 金鑰。OpenAI 金鑰格式為
                sk-...，Claude 金鑰格式為 sk-ant-...
              </p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
      <div class="relative">
        <Input
          v-model="localKeyData.api_key"
          placeholder="sk-..."
          type="password"
          class="font-mono pr-20"
          :class="{ 'border-destructive': apiKeyError }"
          :disabled="isVerifying"
          @update:modelValue="validateApiKeyFormat"
        />
        <button
          v-if="localKeyData.api_key"
          type="button"
          class="absolute right-10 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-destructive"
          @click="() => { localKeyData.api_key = ''; validateApiKeyFormat(); }"
          tabindex="-1"
          aria-label="清除金鑰"
          :disabled="isVerifying"
        >
          <X class="h-4 w-4" />
        </button>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                size="icon"
                variant="ghost"
                class="absolute right-2 top-1/2 -translate-y-1/2 h-7 w-7"
                :disabled="
                  isVerifying || !localKeyData.api_key || !!apiKeyError
                "
                @click="handleVerify"
                aria-label="驗證金鑰"
              >
                <template v-if="isVerifying">
                  <Loader2 class="h-4 w-4 animate-spin text-primary" />
                </template>
                <template v-else-if="verifyStatus === 'success'">
                  <CheckCircle class="h-4 w-4 text-green-500" />
                </template>
                <template v-else-if="verifyStatus === 'error'">
                  <X class="h-4 w-4 text-destructive" />
                </template>
                <template v-else>
                  <Key class="h-4 w-4" />
                </template>
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <span>驗證金鑰</span>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
      <p v-if="apiKeyError" class="text-xs text-destructive mt-1">
        {{ apiKeyError }}
      </p>
      <p class="text-xs text-muted-foreground">
        請從 {{ providerName }} 控制台獲取 API 金鑰
      </p>
      <!-- 顯示最後驗證時間 -->
      <div v-if="localKeyData.last_test" class="flex justify-end">
        <span class="text-xs text-muted-foreground">
          最後驗證時間： {{ formattedLastTestTime }}
        </span>
      </div>
    </div>

    <!-- API URL (只有 OpenAI 相容時顯示) -->
    <div v-if="localKeyData.provider === 'openai-compatible'" class="space-y-2">
      <div class="flex items-center gap-1.5">
        <Label class="text-sm font-medium">API URL</Label>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                class="h-5 w-5 rounded-full p-0"
              >
                <Info class="h-3.5 w-3.5 text-muted-foreground" />
              </Button>
            </TooltipTrigger>
            <TooltipContent side="right">
              <p class="max-w-xs">
                第三方 OpenAI 相容服務的 API 端點網址，例如:
                https://api.third-party.com/v1
              </p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
      <Input
        v-model="localKeyData.api_url"
        placeholder="https://api.openai.com/v1"
        class="font-mono"
        :disabled="isVerifying"
      />
      <p class="text-xs text-muted-foreground">
        請輸入相容 OpenAI 的 API 端點 URL
      </p>
    </div>

    <!-- 啟用狀態 -->
    <div class="flex items-center space-x-2 pt-2">
      <input
        id="is-enabled"
        type="checkbox"
        v-model="localKeyData.is_enabled"
        class="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
      />
      <Label for="is-enabled">啟用此金鑰</Label>
    </div>

    <!-- 按鈕 -->
    <div class="flex justify-end gap-3 pt-4">
      <Button variant="outline" @click="handleCancel" :disabled="isVerifying">
        取消
      </Button>
      <Button
        variant="default"
        @click="handleSave"
        :disabled="
          isVerifying ||
          !!apiKeyError ||
          !localKeyData.name ||
          (!props.isEditing && !localKeyData.api_key)
        "
      >
        {{ isEditing ? "儲存變更" : "新增金鑰" }}
      </Button>
    </div>
  </div>

  <!-- 未儲存變更提示 -->
  <AlertDialog :open="showUnsavedAlert">
    <AlertDialogContent>
      <AlertDialogHeader>
        <AlertDialogTitle>確認放棄變更</AlertDialogTitle>
        <AlertDialogDescription>
          您有未儲存的變更。確定要放棄這些變更嗎？
        </AlertDialogDescription>
      </AlertDialogHeader>
      <AlertDialogFooter>
        <AlertDialogCancel @click="showUnsavedAlert = false"
          >取消</AlertDialogCancel
        >
        <AlertDialogAction @click="confirmCancel">確定放棄</AlertDialogAction>
      </AlertDialogFooter>
    </AlertDialogContent>
  </AlertDialog>
</template>
