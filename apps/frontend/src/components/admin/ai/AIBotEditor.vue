<script setup lang="ts">
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import { Textarea } from "@/components/ui/textarea";
import { Copy, Trash2, <PERSON><PERSON><PERSON>, Link } from "lucide-vue-next";
import type { AIBot } from "@/types/models/ai-bot.model";
import { computed, watch } from "vue";
import SystemPromptInput from "@/components/admin/ai/SystemPromptInput.vue";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { RouterLink } from "vue-router";
import { useAuthStore } from "@horizai/auth";
import type { AiBotProviderType } from "@/types/models/ai.model";
import { BotResponseFormat as EditorResponseFormat } from "@/types/models/ai-bot.model";
import { useNotification } from "@/composables/shared/useNotification";
import { useUtils } from "@/composables/shared/useUtils";
import { useAIBotEditor } from "@/composables/admin/ai/bots/useAIBotEditor";
import { useAIValidation } from "@/composables/admin/ai/shared/useAIValidation";
import KeyRequiredGuide from "@/components/admin/ai/shared/KeyRequiredGuide.vue";

const props = defineProps<{
  bot: AIBot | null;
  editData: AIBot;
  isEditing: boolean;
  availableModels: { id: string; name: string; is_enabled?: boolean }[];
  botScenes: { value: string; label: string; description?: string }[];
  canEditCoreProps?: boolean;
  canDelete?: boolean;
  scopeDescription: string;
  isSystemBot: boolean;
  systemFeatureInfo: { name: string } | null;
  systemFeatures: { key: string; name: string }[];
  updateSystemFeature: (val: string) => void;
  updateEditData: (key: string, value: any) => void;
  responseFormats: { value: string; label: string }[];
  availableKeys?: {
    id: string;
    name: string;
    api_key?: string;
    provider: string;
    api_url?: string | null;
    is_enabled: boolean;
    created_at?: string;
    updated_at?: string;
    last_test?: string | null;
  }[];
}>();

const emit = defineEmits<{
  "confirm-delete": [];
  "update:editData": [data: AIBot];
  "duplicate-bot": [];
  "update:isDirty": [value: boolean];
}>();

const authStore = useAuthStore();
const notification = useNotification();
const { copyToClipboard } = useUtils();
const {
  nameInput,
  getActiveModels,
  getLastUpdatedAt,
  getSelectedKeyDetails,
  createFieldUpdateHandler,
  createSystemFeatureSelectHandler,
  focusNameInput,
} = useAIBotEditor();

// AI 金鑰驗證邏輯
const availableKeysRef = computed(() => props.availableKeys || []);
const selectedKeyIdRef = computed(() => props.editData.key_id);

const {
  hasAnyValidKey,
  hasValidSelectedKey,
  validKeyCount,
  availableProviders,
  validateBotSavePrerequisites,
  showKeySetupNotification,
  getOperationReadiness
} = useAIValidation(availableKeysRef, selectedKeyIdRef);

// 檢查金鑰配置狀態
const keyConfigStatus = computed(() => getOperationReadiness('save', props.editData));
const canSaveBot = computed(() => keyConfigStatus.value.isReady);

// 過濾激活模型列表
const activeModels = computed(() => getActiveModels(props.availableModels));

// 最後更新時間
const lastUpdatedAt = getLastUpdatedAt(props.bot);

// 選中金鑰詳細資訊
const selectedKeyDetails = getSelectedKeyDetails(
  props.editData,
  props.availableKeys
);

// 處理欄位更新
const handleFieldUpdate = createFieldUpdateHandler<AIBot>(props.updateEditData);

// 處理場景變更，自動填入描述
const handleSceneChange = (newScene: string) => {
  // 更新場景
  handleFieldUpdate('scene', newScene);
  
  // 如果當前描述為空，自動填入場景對應的描述
  if (!props.editData.description?.trim()) {
    const sceneInfo = props.botScenes.find(s => s.value === newScene);
    if (sceneInfo && 'description' in sceneInfo && sceneInfo.description) {
      handleFieldUpdate('description', sceneInfo.description);
    }
  }
};

// 處理系統功能選擇
const handleSystemFeatureSelect = createSystemFeatureSelectHandler(
  props.systemFeatures,
  props.updateSystemFeature,
  props.updateEditData
);

// 暴露 focus 方法給父組件
defineExpose({
  focus: focusNameInput,
});
</script>

<template>
  <div class="flex-1 overflow-auto w-full">
    <div v-if="bot" class="space-y-6 max-w-4xl mx-auto p-6 pb-20">
      <!-- 基本資訊 -->
      <div
        v-if="bot.scope !== 'WORKSPACE'"
        class="mb-4 p-4 bg-card border border-border rounded-lg"
      >
        <div class="flex flex-col gap-1.5">
          <div class="flex items-center flex-wrap gap-2">
            <Badge variant="default" class="text-xs px-2 py-0.5"
              >系統助手</Badge
            >
            <Badge variant="secondary" class="text-xs px-2 py-0.5"
              >系統功能專用</Badge
            >
            <span
              class="text-sm text-muted-foreground font-normal truncate"
              :title="scopeDescription"
            >
              {{ scopeDescription }}
            </span>
          </div>
          <div
            v-if="isSystemBot && systemFeatureInfo"
            class="flex items-center gap-1.5 text-xs text-muted-foreground"
          >
            <Link class="h-3.5 w-3.5 text-primary shrink-0" />
            <p class="leading-snug">
              <span>綁定系統功能：</span>
              <RouterLink
                :to="{
                  path: '/admin/ai-settings',
                  query: { highlight_feature: editData.scene },
                }"
                class="text-primary hover:underline font-medium"
                :title="`前往 ${systemFeatureInfo.name} 功能設定`"
              >
                {{ systemFeatureInfo.name }}
              </RouterLink>
            </p>
          </div>
        </div>
      </div>

      <div class="space-y-4">
        <!-- 系統功能選擇 (僅系統 Bot 顯示) -->
        <div v-if="isSystemBot" class="space-y-2">
          <Label>系統功能</Label>
          <Select
            :model-value="editData.scene"
            @update:model-value="
              (val) => handleSystemFeatureSelect(val as string | undefined)
            "
            :disabled="true"
          >
            <SelectTrigger class="w-full">
              <SelectValue placeholder="選擇系統功能">
                {{
                  systemFeatures.find((f) => f.key === editData.scene)?.name ||
                  editData.name ||
                  ""
                }}
              </SelectValue>
            </SelectTrigger>
            <SelectContent>
              <ScrollArea class="max-h-[300px]">
                <SelectItem
                  v-for="feature in systemFeatures"
                  :key="feature.key"
                  :value="feature.key"
                >
                  {{ feature.name }}
                </SelectItem>
              </ScrollArea>
            </SelectContent>
          </Select>
          <p class="text-xs text-muted-foreground">
            系統助理的功能綁定由系統自動設定，無法修改
          </p>
        </div>

        <!-- 名稱欄位 -->
        <div v-if="!isSystemBot" class="space-y-2">
          <Label for="bot-editor-name">名稱</Label>
          <div class="relative">
            <Input
              id="bot-editor-name"
              ref="nameInput"
              :model-value="editData.name"
              @update:model-value="
                (val) => handleFieldUpdate('name', String(val ?? ''))
              "
              placeholder="輸入助手名稱"
              class="pr-8"
            />
            <div
              v-if="editData.name"
              class="absolute right-2 top-1/2 -translate-y-1/2"
            >
              <Button
                variant="ghost"
                size="icon"
                class="h-6 w-6"
                @click="copyToClipboard(editData.name)"
                title="複製名稱"
              >
                <Copy class="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        <div class="space-y-2">
          <Label for="bot-editor-description">描述</Label>
          <Input
            id="bot-editor-description"
            :model-value="editData.description || ''"
            @update:model-value="
              (val) => handleFieldUpdate('description', String(val ?? ''))
            "
            placeholder="選填，描述此 AI 助手的用途和功能"
          />
        </div>

        <div class="space-y-2">
          <Label>系統提示詞</Label>
          <div class="relative">
            <SystemPromptInput
              :model-value="editData.system_prompt || ''"
              @update:modelValue="
                (val) => handleFieldUpdate('system_prompt', val as string)
              "
              :disabled="false"
              :botId="editData.id || ''"
              placeholder="定義 AI 助手的角色、行為和指令..."
              :rows="6"
              :notification-service="notification"
              :scene="editData.scene || ''"
              :provider-type="selectedKeyDetails.providerType"
              :api-key="selectedKeyDetails.apiKey"
              :api-url="selectedKeyDetails.apiUrl"
            />
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="space-y-2">
            <Label>金鑰</Label>
            
            <!-- 金鑰選擇器 -->
            <div v-if="hasAnyValidKey">
              <Select
                :model-value="editData.key_id"
                @update:model-value="
                  (val) => handleFieldUpdate('key_id', (val as string) || '')
                "
              >
                <SelectTrigger class="w-full">
                  <SelectValue placeholder="選擇金鑰">
                    {{
                      props.availableKeys?.find((k) => k.id === editData.key_id)
                        ?.name || "未指定金鑰"
                    }}
                  </SelectValue>
                </SelectTrigger>
                <SelectContent>
                  <ScrollArea
                    v-if="props.availableKeys && props.availableKeys.length > 0"
                    class="max-h-[300px]"
                  >
                    <SelectItem
                      v-for="key in props.availableKeys"
                      :key="key.id"
                      :value="key.id"
                      :disabled="!key.is_enabled"
                    >
                      <div class="flex items-center justify-between w-full">
                        <span class="mr-2">{{ key.name }}</span>
                        <div class="flex gap-1">
                          <badge
                            v-if="!key.is_enabled"
                            variant="secondary"
                            class="text-xs"
                            >已停用</badge
                          >
                          <badge
                            v-if="key.provider"
                            variant="outline"
                            class="text-xs"
                            >{{ key.provider }}</badge
                          >
                        </div>
                      </div>
                    </SelectItem>
                  </ScrollArea>
                </SelectContent>
              </Select>
              
              <!-- 金鑰資訊顯示 -->
              <p
                v-if="
                  editData.key_id &&
                  props.availableKeys?.find((k) => k.id === editData.key_id)
                "
                class="text-xs text-muted-foreground mt-1"
              >
                供應商:
                {{
                  props.availableKeys?.find((k) => k.id === editData.key_id)
                    ?.provider
                }}
                <span
                  v-if="
                    props.availableKeys?.find((k) => k.id === editData.key_id)
                      ?.api_url
                  "
                  class="ml-2"
                >
                  API URL:
                  {{
                    props.availableKeys?.find((k) => k.id === editData.key_id)
                      ?.api_url
                  }}
                </span>
              </p>
              
              <!-- 金鑰配置狀態提示 -->
              <div 
                v-if="!canSaveBot" 
                class="mt-2 text-xs text-amber-600 bg-amber-50 dark:bg-amber-900/20 p-2 rounded border border-amber-200 dark:border-amber-800"
              >
                ⚠️ {{ keyConfigStatus.message }}
              </div>
            </div>
            
            <!-- 金鑰設定引導 -->
            <KeyRequiredGuide
              v-else
              variant="inline"
              title="需要設定 API 金鑰"
              description="請先設定有效的 API 金鑰才能創建或編輯 AI 助理"
              :available-key-count="validKeyCount"
              :available-providers="availableProviders"
              :show-status="false"
              :show-help="false"
              redirect-tab="keys"
            />
          </div>

          <div class="space-y-2">
            <Label>模型</Label>
            <Select
              :model-value="editData.model_id"
              @update:model-value="
                (val) => handleFieldUpdate('model_id', (val as string) || '')
              "
            >
              <SelectTrigger class="w-full">
                <SelectValue placeholder="選擇模型">
                  {{
                    activeModels.find((m) => m.id === editData.model_id)
                      ?.name || ""
                  }}
                </SelectValue>
              </SelectTrigger>
              <SelectContent>
                <ScrollArea class="max-h-[300px]">
                  <SelectItem
                    v-for="model in activeModels"
                    :key="model.id"
                    :value="model.id"
                  >
                    {{ model.name }}
                  </SelectItem>
                </ScrollArea>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div v-if="!isSystemBot" class="space-y-2">
            <Label>場景</Label>
            <Select
              :model-value="editData.scene || ''"
              @update:model-value="
                (val) => handleSceneChange(val as string)
              "
              :disabled="!canEditCoreProps"
            >
              <SelectTrigger class="w-full">
                <SelectValue placeholder="選擇場景" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem
                  v-for="scene in botScenes"
                  :key="scene.value"
                  :value="scene.value"
                >
                  {{ scene.label }}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div class="space-y-2">
            <Label>回覆格式</Label>
            <Select
              :model-value="
                editData.response_format || EditorResponseFormat.TEXT
              "
              @update:model-value="
                (val) =>
                  handleFieldUpdate(
                    'response_format',
                    (val as EditorResponseFormat) || EditorResponseFormat.TEXT
                  )
              "
            >
              <SelectTrigger class="w-full">
                <SelectValue placeholder="選擇回覆格式">
                  {{
                    (Array.isArray(responseFormats) &&
                      responseFormats.find(
                        (f) => f.value === (editData.response_format || "")
                      )?.label) ||
                    ""
                  }}
                </SelectValue>
              </SelectTrigger>
              <SelectContent>
                <SelectItem
                  v-for="format in responseFormats"
                  :key="format.value"
                  :value="format.value"
                >
                  {{ format.label }}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div class="space-y-2 pt-2">
          <div class="flex justify-between items-center">
            <Label>溫度</Label>
            <span
              class="text-sm font-medium bg-muted text-foreground px-2 py-0.5 rounded"
              >{{ (editData.temperature ?? 0.7).toFixed(1) }}</span
            >
          </div>
          <Slider
            :model-value="[editData.temperature ?? 0.7]"
            @update:model-value="
              (val: number[] | undefined) =>
                handleFieldUpdate('temperature', val?.[0] ?? 0.7)
            "
            :min="0"
            :max="1"
            :step="0.1"
          />
        </div>
      </div>

      <!-- Operation Buttons re-added here -->
      <div
        class="flex items-center justify-start gap-2 mt-8 p-6 border-t border-transparent sticky bottom-0 bg-background z-10"
      >
        <Button
          variant="outline"
          size="sm"
          @click="emit('duplicate-bot')"
          :disabled="!isEditing || isSystemBot"
        >
          <Copy class="h-4 w-4 mr-2" />
          複製
        </Button>
        <Button
          variant="outline"
          size="sm"
          @click="emit('confirm-delete')"
          :disabled="!isEditing || !canDelete || isSystemBot"
          class="text-destructive hover:text-destructive border-destructive hover:bg-destructive/5"
        >
          <Trash2 class="h-4 w-4 mr-2" />
          刪除
        </Button>
      </div>
    </div>
    <div v-else class="flex items-center justify-center h-full">
      <p class="text-muted-foreground">沒有可編輯的 AI 助理資料。</p>
    </div>
  </div>
</template>

<style scoped>
.transition-colors {
  transition: all 0.2s ease;
}
</style>
