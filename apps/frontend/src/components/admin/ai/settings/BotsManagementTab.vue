<script setup lang="ts">
import { computed } from "vue";
import { Card } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  TableCaption,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Plus,
  Edit,
  Trash2,
  Sparkles,
  Search,
  X,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
} from "lucide-vue-next";

// Import types from central files
import {
  AiBotScope,
  type AiModel as CoreAiModel,
  type <PERSON><PERSON><PERSON> as CoreAiKey,
  type AiBotWithRelations,
} from "@/types/models/ai.model";
import { useBotsManagementTab } from "@/composables/admin/ai/bots/useBotsManagementTab";

const props = defineProps<{
  aiBots: AiBotWithRelations[] | undefined;
  aiModels: CoreAiModel[] | undefined;
  aiKeys: CoreAiKey[] | undefined;
}>();

const emit = defineEmits<{
  (e: "edit-bot", bot: AiBotWithRelations): void;
  (e: "delete-bot", botId: string): void;
  (e: "create-bot"): void;
  (e: "test-bot", botId: string): void;
}>();

// 使用 composable
const {
  searchTerm,
  scopeFilter,
  statusFilter,
  sortField,
  sortDirection,
  createFilteredAndSortedBots,
  toggleSort,
  resetFilters,
  getBotStats,
  formatBotForDisplay,
} = useBotsManagementTab();

// 將 props 轉換為 computed refs 以確保響應性
const aiBots = computed(() => props.aiBots);
const aiModels = computed(() => props.aiModels);
const aiKeys = computed(() => props.aiKeys);

// 篩選和排序後的機器人列表
const filteredAndSortedBots = createFilteredAndSortedBots(aiBots, aiModels, aiKeys);

// 格式化後的機器人列表，包含顯示資訊
const displayBots = computed(() => {
  return filteredAndSortedBots.value.map((bot) =>
    formatBotForDisplay(bot, aiModels.value, aiKeys.value)
  );
});

// 機器人統計
const botStats = getBotStats(aiBots);

// 獲取排序圖標
const getSortIcon = (field: keyof AiBotWithRelations) => {
  if (sortField.value !== field) return null;
  return sortDirection.value === "asc" ? "asc" : "desc";
};

// 清空搜尋
const clearSearch = () => {
  searchTerm.value = "";
};

// Make AiBotScope available in the template for the :disabled condition
const BotScopeEnum = AiBotScope;
</script>

<template>
  <div>
    <div
      class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6"
    >
      <div>
        <h3 class="text-lg font-medium">AI 助理管理</h3>
        <p class="text-sm text-muted-foreground">
          創建和管理不同範疇的 AI 助理 (Bots)。
        </p>
      </div>
      <Button @click="emit('create-bot')" class="sm:shrink-0">
        <Plus class="h-4 w-4 mr-2" />新增 AI 助理
      </Button>
    </div>

    <!-- 篩選控制區 -->
    <div class="bg-muted/10 rounded-lg p-4 border shadow-sm mb-6">
      <div class="space-y-4">
        <!-- 搜尋 (全寬) -->
        <div>
          <Label class="text-sm mb-2 block">搜尋助理</Label>
          <div class="relative">
            <Search
              class="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"
            />
            <Input
              v-model="searchTerm"
              placeholder="搜尋名稱、描述、場景..."
              class="pl-8"
            />
            <button
              v-if="searchTerm"
              @click="clearSearch"
              class="absolute right-2 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
            >
              <X class="h-4 w-4" />
            </button>
          </div>
        </div>

        <!-- 篩選選項 -->
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          <!-- 範疇篩選 -->
          <div>
            <Label class="text-sm mb-2 block">範疇篩選</Label>
            <Select v-model="scopeFilter">
              <SelectTrigger>
                <SelectValue placeholder="選擇範疇" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部範疇</SelectItem>
                <SelectItem value="SYSTEM">系統</SelectItem>
                <SelectItem value="TENANT_TEMPLATE">租戶範本</SelectItem>
                <SelectItem value="WORKSPACE">工作空間</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <!-- 狀態篩選 -->
          <div>
            <Label class="text-sm mb-2 block">狀態篩選</Label>
            <Select v-model="statusFilter">
              <SelectTrigger>
                <SelectValue placeholder="選擇狀態" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部狀態</SelectItem>
                <SelectItem value="enabled">已啟用</SelectItem>
                <SelectItem value="disabled">已停用</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <!-- 重置按鈕 -->
          <div class="flex items-end">
            <Button variant="outline" @click="resetFilters" class="w-full">
              重置篩選
            </Button>
          </div>
        </div>
      </div>
    </div>

    <!-- 空狀態提示 -->
    <div
      v-if="Array.isArray(props.aiBots) && props.aiBots.length === 0"
      class="text-center py-8 text-muted-foreground"
    >
      目前沒有 AI 助理。
    </div>
    <div
      v-else-if="!props.aiBots"
      class="text-center py-8 text-muted-foreground"
    >
      正在載入 AI 助理...
    </div>
    <div
      v-else-if="displayBots.length === 0"
      class="text-center py-8 text-muted-foreground"
    >
      沒有符合篩選條件的 AI 助理。
    </div>

    <!-- 桌面版表格 (md 以上螢幕) -->
    <div class="hidden md:block" v-if="props.aiBots && displayBots.length > 0">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>
              <div
                class="flex items-center cursor-pointer"
                @click="toggleSort('name')"
              >
                名稱
                <ArrowUp
                  v-if="getSortIcon('name') === 'asc'"
                  class="ml-1 h-3 w-3"
                />
                <ArrowDown
                  v-if="getSortIcon('name') === 'desc'"
                  class="ml-1 h-3 w-3"
                />
                <ArrowUpDown
                  v-if="!getSortIcon('name')"
                  class="ml-1 h-3 w-3 text-muted-foreground"
                />
              </div>
            </TableHead>
            <TableHead>
              <div
                class="flex items-center cursor-pointer"
                @click="toggleSort('scope')"
              >
                範疇 (Scope)
                <ArrowUp
                  v-if="getSortIcon('scope') === 'asc'"
                  class="ml-1 h-3 w-3"
                />
                <ArrowDown
                  v-if="getSortIcon('scope') === 'desc'"
                  class="ml-1 h-3 w-3"
                />
                <ArrowUpDown
                  v-if="!getSortIcon('scope')"
                  class="ml-1 h-3 w-3 text-muted-foreground"
                />
              </div>
            </TableHead>
            <TableHead>模型</TableHead>
            <TableHead>金鑰</TableHead>
            <TableHead>
              <div
                class="flex items-center cursor-pointer"
                @click="toggleSort('scene')"
              >
                場景 (Scene)
                <ArrowUp
                  v-if="getSortIcon('scene') === 'asc'"
                  class="ml-1 h-3 w-3"
                />
                <ArrowDown
                  v-if="getSortIcon('scene') === 'desc'"
                  class="ml-1 h-3 w-3"
                />
                <ArrowUpDown
                  v-if="!getSortIcon('scene')"
                  class="ml-1 h-3 w-3 text-muted-foreground"
                />
              </div>
            </TableHead>
            <TableHead>
              <div
                class="flex items-center cursor-pointer"
                @click="toggleSort('is_enabled')"
              >
                啟用
                <ArrowUp
                  v-if="getSortIcon('is_enabled') === 'asc'"
                  class="ml-1 h-3 w-3"
                />
                <ArrowDown
                  v-if="getSortIcon('is_enabled') === 'desc'"
                  class="ml-1 h-3 w-3"
                />
                <ArrowUpDown
                  v-if="!getSortIcon('is_enabled')"
                  class="ml-1 h-3 w-3 text-muted-foreground"
                />
              </div>
            </TableHead>
            <TableHead class="text-right">操作</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow v-for="bot in displayBots" :key="bot.id">
            <TableCell class="font-medium">
              <div>
                <div>{{ bot.name }}</div>
                <div
                  v-if="bot.description"
                  class="text-xs text-muted-foreground"
                >
                  {{ bot.description }}
                </div>
              </div>
            </TableCell>
            <TableCell>
              <Badge variant="outline">{{ bot.scopeDisplayName }}</Badge>
            </TableCell>
            <TableCell>{{ bot.modelName }}</TableCell>
            <TableCell>{{ bot.keyName }}</TableCell>
            <TableCell>{{ bot.scene || "-" }}</TableCell>
            <TableCell>
              <Badge :variant="bot.statusVariant">
                {{ bot.is_enabled ? "已啟用" : "已停用" }}
              </Badge>
            </TableCell>
            <TableCell class="text-right space-x-1">
              <Button
                variant="ghost"
                size="icon"
                @click="emit('edit-bot', bot)"
                title="編輯 AI 助理"
                :disabled="!bot.canEdit"
              >
                <Edit class="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                @click="emit('test-bot', bot.id)"
                title="測試 AI 助理"
              >
                <Sparkles class="h-4 w-4" />
              </Button>
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    :disabled="!bot.canDelete"
                    title="刪除 AI 助理"
                  >
                    <Trash2 class="h-4 w-4" />
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle
                      >確定要刪除此 AI 助理嗎？</AlertDialogTitle
                    >
                    <AlertDialogDescription>
                      此操作無法復原。AI 助理 "{{ bot.name }}" 將會被永久刪除。
                      如果此 AI 助理正被任何 AI
                      功能設定使用，那些功能可能會失效或需要重新指派 AI 助理。
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>取消</AlertDialogCancel>
                    <AlertDialogAction
                      class="bg-destructive hover:bg-destructive/90"
                      @click="emit('delete-bot', bot.id)"
                      >確定刪除</AlertDialogAction
                    >
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </div>

    <!-- 手機版卡片列表 (md 以下螢幕) -->
    <div
      class="md:hidden space-y-4"
      v-if="props.aiBots && displayBots.length > 0"
    >
      <Card v-for="bot in displayBots" :key="bot.id" class="p-4">
        <div class="space-y-3">
          <!-- 標題行 -->
          <div class="flex items-start justify-between">
            <div class="flex-1 min-w-0">
              <h4 class="font-medium truncate">{{ bot.name }}</h4>
              <p
                v-if="bot.description"
                class="text-sm text-muted-foreground mt-1 line-clamp-2"
              >
                {{ bot.description }}
              </p>
            </div>
            <Badge :variant="bot.statusVariant" class="ml-2 shrink-0">
              {{ bot.is_enabled ? "已啟用" : "已停用" }}
            </Badge>
          </div>

          <!-- 詳細資訊 -->
          <div class="grid grid-cols-2 gap-3 text-sm">
            <div>
              <span class="text-muted-foreground">範疇：</span>
              <Badge variant="outline" class="ml-1">{{
                bot.scopeDisplayName
              }}</Badge>
            </div>
            <div>
              <span class="text-muted-foreground">場景：</span>
              <span class="ml-1">{{ bot.scene || "-" }}</span>
            </div>
            <div class="col-span-2">
              <span class="text-muted-foreground">模型：</span>
              <span class="ml-1">{{ bot.modelName }}</span>
            </div>
            <div class="col-span-2">
              <span class="text-muted-foreground">金鑰：</span>
              <span class="ml-1">{{ bot.keyName }}</span>
            </div>
          </div>

          <!-- 操作按鈕 -->
          <div class="flex items-center justify-end space-x-2 pt-2 border-t">
            <Button
              variant="outline"
              size="sm"
              @click="emit('edit-bot', bot)"
              :disabled="!bot.canEdit"
            >
              <Edit class="h-4 w-4 mr-1" />
              編輯
            </Button>
            <Button
              variant="outline"
              size="sm"
              @click="emit('test-bot', bot.id)"
            >
              <Sparkles class="h-4 w-4 mr-1" />
              測試
            </Button>
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  :disabled="!bot.canDelete"
                  class="text-destructive hover:text-destructive"
                >
                  <Trash2 class="h-4 w-4 mr-1" />
                  刪除
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>確定要刪除此 AI 助理嗎？</AlertDialogTitle>
                  <AlertDialogDescription>
                    此操作無法復原。AI 助理 "{{ bot.name }}" 將會被永久刪除。
                    如果此 AI 助理正被任何 AI
                    功能設定使用，那些功能可能會失效或需要重新指派 AI 助理。
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>取消</AlertDialogCancel>
                  <AlertDialogAction
                    class="bg-destructive hover:bg-destructive/90"
                    @click="emit('delete-bot', bot.id)"
                    >確定刪除</AlertDialogAction
                  >
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </div>
      </Card>
    </div>
  </div>
</template>
