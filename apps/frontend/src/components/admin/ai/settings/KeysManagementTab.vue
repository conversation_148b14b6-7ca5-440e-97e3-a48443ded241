<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  TableCaption,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Plus, Edit, Trash2 } from "lucide-vue-next";
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetDescription } from "@/components/ui/sheet";
import AiKeyEditor from "@/components/admin/ai/AiKeyEditor.vue";
import { useAIKeyManager } from "@/composables/admin/ai/keys/useAIKeyManager";

// Import the CoreAiKey type from the central models file
import type { AiKey as CoreAiKey } from "@/types/models/ai.model";

// 不再從父層接收 aiKeys，改由此組件自己管理和渲染
// const props = defineProps<{ aiKeys: CoreAiKey[] | undefined; }>();
// const emit = defineEmits<...>();

// 使用 Composable 管理金鑰列表與編輯狀態
const {
  keyList,
  editingKey,
  isNew,
  isLoading,
  initialize,
  handleNew,
  handleSelect,
  handleSave,
  handleDelete,
  handleClose,
} = useAIKeyManager((event) => {
  if (event === 'close') {
    isKeySheetOpen.value = false;
  }
});

const isKeySheetOpen = ref(false);
const currentKeyForEditor = computed(() => editingKey.value);
const isEditingKey = computed(() => !isNew.value && !!editingKey.value);

// 操作方法
const openNewKeySheet = () => {
  handleNew();
  isKeySheetOpen.value = true;
};
const openEditKeySheet = (key: CoreAiKey) => {
  handleSelect(key.id);
  isKeySheetOpen.value = true;
};
const saveKeyHandler = async (keyData: Partial<CoreAiKey>) => {
  await handleSave(keyData);
  isKeySheetOpen.value = false;
  await initialize();
};
const deleteKeyHandler = async (keyId: string) => {
  await handleDelete(keyId);
  await initialize();
};

onMounted(() => {
  initialize();
});
</script>

<template>
  <div>
    <div class="flex flex-row items-center justify-between mb-6">
      <div>
        <h3 class="text-lg font-medium">API 金鑰管理</h3>
        <p class="text-sm text-muted-foreground">
          管理用於連接各種 AI 供應商的 API 金鑰。
        </p>
      </div>
      <Button @click="openNewKeySheet">
        <Plus class="h-4 w-4 mr-2" />新增 API 金鑰
      </Button>
    </div>

    <Table>
      <TableCaption
        v-if="Array.isArray(keyList) && keyList.length === 0"
        >目前沒有 API 金鑰。</TableCaption
      >
      <TableCaption v-else-if="isLoading"
        >正在載入 API 金鑰...</TableCaption
      >
      <TableHeader>
        <TableRow>
          <TableHead>名稱</TableHead>
          <TableHead>供應商</TableHead>
          <TableHead>API 金鑰 (部分顯示)</TableHead>
          <TableHead>啟用</TableHead>
          <TableHead class="text-right">操作</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody v-if="!isLoading">
        <TableRow v-for="keyItem in keyList" :key="keyItem.id">
          <TableCell class="font-medium">{{ keyItem.name }}</TableCell>
          <TableCell>{{ keyItem.provider }}</TableCell>
          <TableCell>
            <span class="text-sm text-muted-foreground">********</span>
          </TableCell>
          <TableCell>
            <Badge :variant="keyItem.is_enabled ? 'default' : 'secondary'">
              {{ keyItem.is_enabled ? "已啟用" : "已停用" }}
            </Badge>
          </TableCell>
          <TableCell class="text-right space-x-2">
            <Button
              variant="ghost"
              size="icon"
              @click="openEditKeySheet(keyItem)"
            >
              <Edit class="h-4 w-4" />
            </Button>
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="ghost" size="icon">
                  <Trash2 class="h-4 w-4" />
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>確定要刪除此 API 金鑰嗎？</AlertDialogTitle>
                  <AlertDialogDescription>
                    此操作無法復原。金鑰 "{{ keyItem.name }}" 將會被永久刪除。
                    如果此金鑰正被任何 AI
                    助理使用，那些助理將無法正常運作，直到被指派新的金鑰。
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>取消</AlertDialogCancel>
                  <AlertDialogAction @click="deleteKeyHandler(keyItem.id)"
                    >確認刪除</AlertDialogAction
                  >
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </TableCell>
        </TableRow>
      </TableBody>
    </Table>

    <Sheet v-model:open="isKeySheetOpen">
      <SheetContent class="w-full sm:max-w-lg md:max-w-xl lg:max-w-2xl xl:max-w-3xl overflow-y-auto">
        <SheetHeader>
          <SheetTitle>{{ isEditingKey ? '編輯 API 金鑰' : '新增 API 金鑰' }}</SheetTitle>
          <SheetDescription>
            {{ isEditingKey ? '修改您的 API 金鑰詳細資訊。' : '新增一個 API 金鑰以供 AI Bot 使用。' }}
          </SheetDescription>
        </SheetHeader>
        <AiKeyEditor
          :key-data="currentKeyForEditor"
          :is-editing="isEditingKey"
          @save="saveKeyHandler"
          @cancel="handleClose"
        />
      </SheetContent>
    </Sheet>
  </div>
</template>
