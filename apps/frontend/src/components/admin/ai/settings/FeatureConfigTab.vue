<script setup lang="ts">
import { ref, watch, computed } from "vue";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Sparkles, RefreshCw } from "lucide-vue-next";

// 修改：直接使用 useAiFeatureConfig 中整合的 useFeatureConfigTabLogic
import { useFeatureConfigTabLogic } from "@/composables/admin/ai/features/useAiFeatureConfig"; 
import { AI_FEATURES } from "@/constants/ai-features.constants";
import { useNotification } from "@/composables/shared/useNotification";

// Import types from central files
import type {
  AiFeatureConfigUpdateDto,
  AiBotUpdateDto,
} from "@/types/dto/ai.dto";
import {
  AiBotScope,
  type AiBot as CoreAiBot,
  type AiFeatureConfig as CoreAiFeatureConfig,
} from "@/types/models/ai.model";

// Define prop types using imported or local types
export interface Props {
  systemFeatureDefinitions: any[] | null | undefined; // Keep any for now as it comes from parent
  aiBots: any[] | undefined; // Keep any for now as it comes from parent
  updateFeatureConfig: (
    featureKey: string,
    data: AiFeatureConfigUpdateDto
  ) => Promise<CoreAiFeatureConfig | null>;
  fetchSystemFeatureDefinitions: () => Promise<void>;
  getBotById: (botId: string) => any | undefined;
  getEffectiveBotForFeatureProp: (featureDefinition: any) => any | undefined;
  updateAiBot: (
    botId: string,
    data: AiBotUpdateDto
  ) => Promise<CoreAiBot | null>;
  fetchAiBots: () => Promise<void>;
  activeTab: string;
  syncFeatureDefinitions: (features: Array<{
    key: string;
    name: string;
    description?: string;
    systemBot?: boolean;
  }>) => Promise<{
    created: number;
    updated: number;
    skipped: number;
    errors: string[];
  }>;
}

const props = defineProps<Props>();

// 監控 props.aiBots 的變化
watch(() => props.aiBots, (newBots, oldBots) => {
  // Optional: Log for debugging if needed in development
}, { deep: true });

const emit = defineEmits<{
  (
    e: "request-new-bot",
    payload: { scope: AiBotScope; scene: string; name: string }
  ): void;
  (e: "update:activeTab", value: string): void;
}>();

// 修改：直接使用從 useAiFeatureConfig 導入的 useFeatureConfigTabLogic
const { 
  handleFeatureToggle, 
  onRequestNewBot 
} = useFeatureConfigTabLogic({
  aiBots: computed(() => props.aiBots),
  aiModels: [], // Assuming aiModels and aiKeys are not directly needed by this specific logic now
  aiKeys: [],   // or should be passed if they are
  systemFeatureDefinitions: computed(() => props.systemFeatureDefinitions || []),
  fetchAiBots: props.fetchAiBots,
  fetchSystemFeatureDefinitions: props.fetchSystemFeatureDefinitions,
  updateAiBot: props.updateAiBot,
  updateFeatureConfig: props.updateFeatureConfig, // Pass the prop from AISettings
});

// 移除重複的初始化調用，因為父組件 AISettings.vue 已經在 initialLoad() 中
// 調用了 fetchSystemFeatureDefinitions()，數據會通過 props 傳遞下來

// directToggleFeature is now effectively handleFeatureToggle from the composable
// No need for a separate directToggleFeature function if handleFeatureToggle does the same

// 檢查功能是否啟用
const isFeatureEnabled = (featureDef: any): boolean => {
  if (!featureDef) return false;
  const isEnabled = !!featureDef?.config?.is_enabled;
  return isEnabled;
}

// 同步功能定義的狀態管理
const notification = useNotification();
const isSyncing = ref(false);

// 同步功能定義到資料庫
const handleSyncFeatureDefinitions = async () => {
  if (isSyncing.value) return;
  
  isSyncing.value = true;
  try {
    const result = await props.syncFeatureDefinitions(AI_FEATURES);
    
    notification.toast.success("同步完成", `創建: ${result.created} 個，更新: ${result.updated} 個${result.errors.length > 0 ? `，錯誤: ${result.errors.length} 個` : ''}`);

    // 如果有錯誤，在控制台顯示詳細信息
    if (result.errors.length > 0) {
      console.error("同步功能定義時發生錯誤:", result.errors);
      notification.toast.warning("同步完成但有錯誤", `請檢查控制台了解詳細錯誤信息`);
    }
  } catch (error) {
    console.error("同步功能定義失敗:", error);
    notification.toast.error("同步失敗", "無法同步功能定義到資料庫，請檢查網路連線或聯絡管理員");
  } finally {
    isSyncing.value = false;
  }
};

</script>

<template>
  <div>
    <!-- 標題區域 -->
    <div class="flex flex-row items-center justify-between mb-6">
      <div>
        <h3 class="text-lg font-medium">AI 系統功能配置</h3>
        <p class="text-sm text-muted-foreground">
          配置各 AI 系統功能的啟用狀態。系統會自動將功能與對應的 AI 助理關聯，
          無需手動選擇。系統會根據 feature_key=scene 自動匹配助理。
        </p>
      </div>
      
      <!-- 右上角按鈕區域 -->
      <div class="flex items-center gap-2">
        <Button
          variant="outline"
          size="sm"
          @click="handleSyncFeatureDefinitions"
          :disabled="isSyncing"
          class="flex items-center gap-2"
        >
          <RefreshCw :class="{ 'animate-spin': isSyncing }" class="h-4 w-4" />
          {{ isSyncing ? '同步中...' : '同步功能定義' }}
        </Button>
      </div>
    </div>

    <!-- System Features -->
    <div class="space-y-4">
      <div class="space-y-2">
        <div
          v-for="featureDef in props.systemFeatureDefinitions"
          :key="featureDef.id"
          class="border rounded-lg shadow-sm p-4"
        >
          <div class="flex items-center justify-between">
            <div>
              <div class="flex items-center gap-2">
                <h4 class="text-base font-semibold">{{ featureDef.name }}</h4>
                <Badge>{{ featureDef.key }}</Badge>
              </div>
              <p class="text-sm text-muted-foreground">
                {{ featureDef.description || "無描述" }}
              </p>
            </div>
            <div class="flex items-center gap-4">
              <div class="flex items-center space-x-2">
                <Switch
                  :id="`feature-${featureDef.id}`"
                  :model-value="isFeatureEnabled(featureDef)"
                  @update:model-value="
                    (val: boolean) => handleFeatureToggle(featureDef, val) 
                  "
                />
                <Label :for="`feature-${featureDef.id}`" class="min-w-[5ch]">{{
                  isFeatureEnabled(featureDef) ? "已啟用" : "已停用"
                }}</Label>
              </div>
            </div>
          </div>

          <!-- Bot Configuration -->
          <div v-if="isFeatureEnabled(featureDef)" class="mt-2">
            <div
              class="flex items-center gap-2 text-sm font-medium text-muted-foreground"
            >
              <Sparkles class="h-4 w-4" />
              <span>AI 助理:</span>
              
              <!-- 如果有專用助理，顯示助理名稱 -->
              <Badge 
                v-if="props.getEffectiveBotForFeatureProp?.(featureDef)" 
                variant="outline" 
                class="ml-2"
              >
                {{ props.getEffectiveBotForFeatureProp?.(featureDef)?.name }}
              </Badge>
              
              <!-- 如果沒有專用助理，提供新增助理按鈕 -->
              <div v-else class="flex items-center gap-2 ml-2">
                <Badge variant="secondary">未配置專用助理</Badge>
                <Button
                  size="sm"
                  variant="outline"
                  @click="() => emit('request-new-bot', {
                    scope: AiBotScope.SYSTEM,
                    scene: featureDef.key,
                    name: `${featureDef.name}助理`
                  })"
                  class="h-6 px-2 text-xs"
                >
                  新增助理
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
