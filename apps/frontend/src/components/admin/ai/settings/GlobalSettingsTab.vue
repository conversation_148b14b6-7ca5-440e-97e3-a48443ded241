<script setup lang="ts">
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";
import { Settings2, Info, Save } from "lucide-vue-next";
import { onMounted, watch, ref, computed } from "vue";
import { useAiGlobalSettings } from "@/composables/admin/ai/settings/useAiGlobalSettings";

// 使用 composable
const {
  aiGlobalSettings,
  loading: isLoading,
  isUpdating,
  error,
  fetch: fetchGlobalSettings,
  changeGlobalEnabled,
  changeQuotaTokens,
  changeQuotaCalls,
  saveQuotaSettings,
} = useAiGlobalSettings();

// 使用 ref 來處理雙向綁定
const tokensInputValue = ref('');
const callsInputValue = ref('');

// 監聽數據變化，同步到 input
watch(
  () => aiGlobalSettings.value?.global_monthly_quota_tokens,
  (newValue) => {
    tokensInputValue.value = newValue != null ? newValue.toString() : '';
  },
  { immediate: true }
);

watch(
  () => aiGlobalSettings.value?.global_monthly_quota_calls,
  (newValue) => {
    callsInputValue.value = newValue != null ? newValue.toString() : '';
  },
  { immediate: true }
);

// 處理輸入變化
const handleTokensChange = (value: string | number) => {
  const stringValue = String(value);
  tokensInputValue.value = stringValue;
  changeQuotaTokens(stringValue);
};

const handleCallsChange = (value: string | number) => {
  const stringValue = String(value);
  callsInputValue.value = stringValue;
  changeQuotaCalls(stringValue);
};

// 組件掛載時載入設定
onMounted(async () => {
  await fetchGlobalSettings();
});

// 監聽 aiGlobalSettings 的變化
watch(
  () => aiGlobalSettings.value,
  (newSettings, oldSettings) => {
    // Optional: Log for debugging if needed in development
  },
  { deep: true, immediate: true }
);

// 監聽載入狀態
watch(
  () => isLoading.value,
  (loading) => {
    // Optional: Log for debugging if needed in development
  },
  { immediate: true }
);

// 監聽 isUpdating 狀態
watch(
  () => isUpdating.value,
  (newUpdating, oldUpdating) => {
    // Optional: Log for debugging if needed in development
  }
);

// 處理開關變化
const handleSwitchChange = async (value: boolean) => {
  if (isUpdating.value) {
    return;
  }

  try {
    await changeGlobalEnabled(value);
  } catch (error) {
    console.error("更新全域 AI 設定失敗:", error);
  }
};

// 處理配額設定保存
const handleSaveQuotaSettings = async () => {
  try {
    await saveQuotaSettings();
  } catch (error) {
    console.error("保存配額設定失敗:", error);
  }
};
</script>

<template>
  <div>
    <div class="flex flex-row items-center justify-between mb-6">
      <div>
        <h3 class="text-lg font-medium">全域 AI 設定</h3>
        <p class="text-sm text-muted-foreground">
          控制平台 AI 功能的總體啟用狀態及用量限制。
        </p>
      </div>
    </div>

    <div
      class="space-y-6"
      v-if="
        aiGlobalSettings &&
        typeof aiGlobalSettings.is_ai_globally_enabled === 'boolean'
      "
    >
      <div class="rounded-lg border p-4">
        <div class="flex items-start justify-between">
          <div>
            <Label for="global-ai-switch" class="text-base font-semibold"
              >全域啟用 AI 功能</Label
            >
            <p class="text-sm text-muted-foreground">
              關閉此選項將停用所有 AI 功能，包括所有機器人和 API 存取。
            </p>
          </div>
          <div class="flex flex-col items-end gap-1">
            <Switch
              id="global-ai-switch"
              class="ml-auto"
              :model-value="aiGlobalSettings.is_ai_globally_enabled"
              @update:model-value="handleSwitchChange"
              :disabled="isUpdating"
            />
            <span class="text-xs text-muted-foreground">
              {{
                aiGlobalSettings.is_ai_globally_enabled ? "已啟用" : "已停用"
              }}
              <span v-if="isUpdating" class="italic text-yellow-500"
                >(更新中...)</span
              >
            </span>
          </div>
        </div>
      </div>

      <div class="rounded-lg border p-4 space-y-4">
        <div>
          <Label for="global-monthly-tokens" class="text-base font-semibold"
            >全域每月 Token 配額 (選填)</Label
          >
          <Input
            id="global-monthly-tokens"
            type="text"
            v-model="tokensInputValue"
            @update:model-value="handleTokensChange"
            placeholder="例如：1000000 (留空表示不限制)"
            :disabled="isUpdating"
          />
          <p class="text-xs text-muted-foreground mt-1">
            整個系統每月可使用的總 Token 數量上限。
          </p>
        </div>

        <div>
          <Label for="global-monthly-calls" class="text-base font-semibold"
            >全域每月 API 呼叫配額 (選填)</Label
          >
          <Input
            id="global-monthly-calls"
            type="text"
            v-model="callsInputValue"
            @update:model-value="handleCallsChange"
            placeholder="例如：10000 (留空表示不限制)"
            :disabled="isUpdating"
          />
          <p class="text-xs text-muted-foreground mt-1">
            整個系統每月可進行的總 API 呼叫次數上限。
          </p>
        </div>

        <div class="flex justify-end">
          <Button
            @click="handleSaveQuotaSettings"
            variant="default"
            size="sm"
            :disabled="isUpdating"
          >
            <Save class="h-4 w-4 mr-2" />儲存配額設定
          </Button>
        </div>
      </div>
    </div>
    <div v-else class="text-center text-muted-foreground py-8">
      <Info class="mx-auto h-12 w-12 text-gray-400" />
      <p class="mt-2" v-if="isLoading">正在載入全域設定...</p>
      <p class="mt-2" v-else-if="error">載入設定時發生錯誤: {{ error }}</p>
      <p class="mt-2" v-else>無法載入全域設定，或設定為空。</p>
    </div>
  </div>
</template>
