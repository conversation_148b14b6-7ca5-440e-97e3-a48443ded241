<template>
  <div class="key-required-guide relative" :class="variantClasses">
    <!-- 關閉按鈕 -->
    <Button
      v-if="showClose"
      @click="$emit('close')"
      variant="ghost"
      size="sm"
      class="absolute top-2 right-2 h-6 w-6 p-0 hover:bg-muted"
    >
      <X class="h-4 w-4" />
    </Button>

    <!-- 圖示區域 -->
    <div class="flex justify-center mb-4" v-if="showIcon">
      <div class="w-16 h-16 rounded-full flex items-center justify-center" :class="iconContainerClass">
        <component :is="iconComponent" class="w-8 h-8" :class="iconClass" />
      </div>
    </div>

    <!-- 標題 -->
    <h3 class="text-lg font-semibold text-center mb-2" v-if="title">
      {{ title }}
    </h3>

    <!-- 描述 -->
    <p class="text-muted-foreground text-center mb-6" v-if="description">
      {{ description }}
    </p>

    <!-- API 金鑰錯誤詳情 -->
    <div v-if="errorType === 'invalid-key'" class="bg-destructive/10 border border-destructive/20 rounded-lg p-4 mb-6">
      <div class="space-y-3">
        <h4 class="font-medium text-destructive">API 金鑰驗證失敗</h4>
        
        <div class="space-y-2 text-sm text-muted-foreground">
          <div class="space-y-1">
            <p class="font-medium">常見問題：</p>
            <ul class="list-disc list-inside space-y-1 ml-2">
              <li>API 金鑰格式不正確或已過期</li>
              <li>OpenAI 測試金鑰 (sk-test-...) 無法使用</li>
              <li>Anthropic 金鑰未正確設定 x-api-key</li>
              <li>API 配額已用完或帳戶被停用</li>
            </ul>
          </div>
          
          <div class="space-y-1">
            <p class="font-medium">解決方案：</p>
            <ul class="list-disc list-inside space-y-1 ml-2">
              <li>檢查 API 金鑰是否正確複製</li>
              <li>確認金鑰格式：OpenAI (sk-...), Anthropic (sk-ant-...)</li>
              <li>驗證帳戶狀態和餘額</li>
              <li>重新生成新的 API 金鑰</li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- 狀態資訊 -->
    <div class="bg-muted/50 rounded-lg p-4 mb-6" v-if="showStatus">
      <div class="space-y-2">
        <div class="flex items-center justify-between text-sm">
          <span>可用金鑰數量：</span>
          <Badge variant="outline" :class="statusBadgeClass">
            {{ availableKeyCount }}
          </Badge>
        </div>
        
        <div class="flex items-center justify-between text-sm" v-if="availableProviders.length > 0">
          <span>支援的供應商：</span>
          <div class="flex gap-1">
            <Badge v-for="provider in availableProviders" :key="provider" variant="secondary" class="text-xs">
              {{ provider }}
            </Badge>
          </div>
        </div>
      </div>
    </div>

    <!-- 操作按鈕 -->
    <div class="flex flex-col gap-3">
      <!-- 主要按鈕 -->
      <Button 
        @click="navigateToSettings" 
        class="w-full"
        :variant="primaryButtonVariant"
      >
        <component :is="primaryButtonIcon" class="w-4 h-4 mr-2" />
        {{ primaryButtonText }}
      </Button>

      <!-- 次要按鈕 (如果提供) -->
      <Button 
        v-if="secondaryButtonText"
        @click="toggleGuide"
        variant="outline" 
        class="w-full"
      >
        <ChevronDown class="w-4 h-4 mr-2 transition-transform duration-200" :class="{ 'rotate-180': showGuide }" />
        {{ secondaryButtonText }}
      </Button>

      <!-- 設定指南展開區塊 -->
      <div 
        v-if="secondaryButtonText && showGuide" 
        class="transition-all duration-300 ease-in-out overflow-hidden"
      >
        <div class="bg-muted/30 rounded-lg p-4 mt-2 border-l-4 border-primary/30">
          <h4 class="font-medium text-sm mb-3 flex items-center">
            <Settings class="w-4 h-4 mr-2" />
            API 金鑰設定步驟
          </h4>
          
          <ol class="space-y-2 text-sm text-muted-foreground">
            <li class="flex items-start">
              <span class="inline-flex items-center justify-center w-5 h-5 rounded-full bg-primary/10 text-primary text-xs font-medium mr-3 mt-0.5 flex-shrink-0">1</span>
              <span>前往「API 金鑰」頁面</span>
            </li>
            <li class="flex items-start">
              <span class="inline-flex items-center justify-center w-5 h-5 rounded-full bg-primary/10 text-primary text-xs font-medium mr-3 mt-0.5 flex-shrink-0">2</span>
              <span>點擊「新增金鑰」按鈕</span>
            </li>
            <li class="flex items-start">
              <span class="inline-flex items-center justify-center w-5 h-5 rounded-full bg-primary/10 text-primary text-xs font-medium mr-3 mt-0.5 flex-shrink-0">3</span>
              <span>選擇 AI 供應商（OpenAI、Anthropic、Google Gemini 等）</span>
            </li>
            <li class="flex items-start">
              <span class="inline-flex items-center justify-center w-5 h-5 rounded-full bg-primary/10 text-primary text-xs font-medium mr-3 mt-0.5 flex-shrink-0">4</span>
              <span>輸入您的 API 金鑰並點擊「驗證」</span>
            </li>
            <li class="flex items-start">
              <span class="inline-flex items-center justify-center w-5 h-5 rounded-full bg-primary/10 text-primary text-xs font-medium mr-3 mt-0.5 flex-shrink-0">5</span>
              <span>確認驗證成功後儲存設定</span>
            </li>
          </ol>
          
          <div class="mt-4 p-3 bg-blue-50 dark:bg-blue-950/20 rounded-md">
            <p class="text-xs text-blue-700 dark:text-blue-300 flex items-start">
              <AlertTriangle class="w-4 h-4 mr-2 mt-0.5 flex-shrink-0" />
              <span>設定完成後，請返回此頁面刷新以使用 AI 功能。</span>
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- 額外資訊 -->
    <div class="mt-6 text-xs text-muted-foreground text-center" v-if="showHelp">
      <p>需要幫助？查看我們的 
        <Button variant="link" class="p-0 h-auto text-xs" @click="$emit('show-help')">
          設定指南
        </Button>
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Key, Settings, AlertTriangle, RefreshCw, X, ChevronDown } from 'lucide-vue-next';
import { useRouter } from 'vue-router';

interface Props {
  // 外觀配置
  variant?: 'card' | 'inline' | 'compact';
  showIcon?: boolean;
  showStatus?: boolean;
  showHelp?: boolean;
  showClose?: boolean;
  
  // 錯誤類型
  errorType?: 'no-key' | 'invalid-key' | 'quota-exceeded';
  
  // 內容配置
  title?: string;
  description?: string;
  primaryButtonText?: string;
  secondaryButtonText?: string;
  
  // 狀態資料
  availableKeyCount?: number;
  availableProviders?: string[];
  
  // 導航配置
  redirectTab?: string;
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'card',
  showIcon: true,
  showStatus: true,
  showHelp: true,
  showClose: false,
  errorType: 'no-key',
  title: '需要設定 API 金鑰',
  description: '此功能需要有效的 AI API 金鑰才能使用。請先前往設定頁面配置您的 API 金鑰。',
  primaryButtonText: '前往設定',
  availableKeyCount: 0,
  availableProviders: () => [],
  redirectTab: 'keys'
});

const emit = defineEmits<{
  'show-help': [];
  'close': [];
}>();

const router = useRouter();

// 根據錯誤類型動態調整內容
const iconComponent = computed(() => {
  switch (props.errorType) {
    case 'invalid-key':
      return AlertTriangle;
    case 'quota-exceeded':
      return RefreshCw;
    default:
      return Key;
  }
});

const iconContainerClass = computed(() => {
  switch (props.errorType) {
    case 'invalid-key':
      return 'bg-destructive/10 dark:bg-destructive/20';
    case 'quota-exceeded':
      return 'bg-orange-100 dark:bg-orange-900/20';
    default:
      return 'bg-amber-100 dark:bg-amber-900/20';
  }
});

const iconClass = computed(() => {
  switch (props.errorType) {
    case 'invalid-key':
      return 'text-destructive';
    case 'quota-exceeded':
      return 'text-orange-600 dark:text-orange-400';
    default:
      return 'text-amber-600 dark:text-amber-400';
  }
});

const primaryButtonIcon = computed(() => {
  switch (props.errorType) {
    case 'invalid-key':
      return Settings;
    case 'quota-exceeded':
      return RefreshCw;
    default:
      return Settings;
  }
});

// 動態標題和描述
const dynamicTitle = computed(() => {
  if (props.title !== '需要設定 API 金鑰') return props.title;
  
  switch (props.errorType) {
    case 'invalid-key':
      return 'API 金鑰驗證失敗';
    case 'quota-exceeded':
      return 'API 配額已用完';
    default:
      return '需要設定 API 金鑰';
  }
});

const dynamicDescription = computed(() => {
  if (props.description !== '此功能需要有效的 AI API 金鑰才能使用。請先前往設定頁面配置您的 API 金鑰。') {
    return props.description;
  }
  
  switch (props.errorType) {
    case 'invalid-key':
      return '您的 API 金鑰無法通過驗證。請檢查金鑰是否正確，或重新設定新的 API 金鑰。';
    case 'quota-exceeded':
      return '當前 API 配額已達上限。請檢查您的帳戶餘額或升級方案。';
    default:
      return '此功能需要有效的 AI API 金鑰才能使用。請先前往設定頁面配置您的 API 金鑰。';
  }
});

const dynamicPrimaryButtonText = computed(() => {
  if (props.primaryButtonText !== '前往設定') return props.primaryButtonText;
  
  switch (props.errorType) {
    case 'invalid-key':
      return '檢查金鑰設定';
    case 'quota-exceeded':
      return '檢查帳戶配額';
    default:
      return '前往設定';
  }
});

// 使用動態值覆蓋原有值
const title = computed(() => dynamicTitle.value);
const description = computed(() => dynamicDescription.value);
const primaryButtonText = computed(() => dynamicPrimaryButtonText.value);

// 其他計算屬性保持不變
const variantClasses = computed(() => {
  switch (props.variant) {
    case 'inline':
      return 'p-4 border border-dashed border-muted-foreground/30 rounded-lg';
    case 'compact':
      return 'p-3 bg-muted/30 rounded-md';
    default:
      return 'p-6 border border-border rounded-lg bg-card';
  }
});

const statusBadgeClass = computed(() => {
  return props.availableKeyCount > 0 
    ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
    : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
});

const primaryButtonVariant = computed(() => {
  return props.errorType === 'invalid-key' ? 'destructive' : 'default';
});

const navigateToSettings = () => {
  router.push({
    name: 'admin-ai-settings',
    query: { tab: props.redirectTab }
  });
};

const showGuide = ref(false);

const toggleGuide = () => {
  showGuide.value = !showGuide.value;
};
</script>

<style scoped>
.key-required-guide {
  @apply text-center;
}
</style>
