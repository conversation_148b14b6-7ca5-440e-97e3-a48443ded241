<script setup lang="ts">
import { computed, watch, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Save } from 'lucide-vue-next'
import GeneralSettings from './tabs/GeneralSettings.vue'
import SecuritySettings from './tabs/SecuritySettings.vue'
import UserSettings from './tabs/UserSettings.vue'
import EmailSettings from './tabs/EmailSettings.vue'
import BillingSettings from './tabs/BillingSettings.vue'
import LegalSettings from './tabs/LegalSettings.vue'
import { useSystemSettings } from '@/composables/admin/useSystemSettings'
import type { Component } from 'vue'

const route = useRoute()
const router = useRouter()
const systemSettings = useSystemSettings()
const { isLoading, saveSettings, loadGeneralSettings, loadSecuritySettings, loadUserSettings, loadEmailSettings, loadBillingSettings, loadLegalSettings } = systemSettings

// 完全簡化的點擊處理函數 - 直接呼叫儲存函數，不做任何額外的日誌記錄或操作
const handleSaveClick = () => {
  saveSettings()
}

const currentTab = computed(() => route.query.tab?.toString() || 'general')

// 設定標籤頁元件對應
const tabComponents: Record<string, Component> = {
  general: GeneralSettings,
  security: SecuritySettings,
  user: UserSettings,
  email: EmailSettings,
  billing: BillingSettings,
  legal: LegalSettings
}

const loadMap: Record<string, () => Promise<any>> = {
  general: loadGeneralSettings,
  security: loadSecuritySettings,
  user: loadUserSettings,
  email: loadEmailSettings,
  billing: loadBillingSettings,
  legal: loadLegalSettings
}

// 載入當前標籤所需的資料
const loadCurrentTab = async () => {
  if (!route.query.tab) {
    return // 如果還沒準備好，不要載入資料
  }
  
  // 檢查當前標籤，判斷是否需要載入資料
  const tabName = currentTab.value
  const loader = loadMap[tabName]
  
  if (loader) {
    // 檢查是否已經載入資料，避免重複 API 呼叫
    // general 標籤由 App.vue 和 GeneralSettings.vue 處理，這裡可以跳過
    // if (tabName === 'general' && generalSettings?.siteName) {
    //   console.log(`${tabName} 標籤資料已載入，跳過重複載入`)
    //   return
    // }
    
    console.log(`準備載入 ${tabName} 標籤資料`)
    await loader()
  }
}

// 監聽路由準備就緒後載入資料
router.isReady().then(() => {
  loadCurrentTab()
})

// 監聽標籤變更時重新載入資料
watch(currentTab, loadCurrentTab, { immediate: false })
</script>

<template>
  <div class="h-full w-full">
    <!-- 儲存按鈕 -->
    <Card class="mb-3 w-full shadow-sm">
      <CardContent class="p-2.5 sm:p-3">
        <div class="flex items-center justify-between">
          <p class="text-sm text-muted-foreground">變更設定後請記得儲存</p>
          <Button @click="handleSaveClick" :disabled="isLoading" size="sm">
            <Save class="h-3.5 w-3.5 mr-1.5" />
            儲存設定
          </Button>
        </div>
      </CardContent>
    </Card>

    <!-- 設定內容 -->
    <div class="w-full">
      <component :is="tabComponents[currentTab]" />
    </div>
  </div>
</template>

<style scoped>
.overflow-y-auto::-webkit-scrollbar {
  width: 4px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: transparent;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.3);
  border-radius: 2px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.5);
}
</style> 