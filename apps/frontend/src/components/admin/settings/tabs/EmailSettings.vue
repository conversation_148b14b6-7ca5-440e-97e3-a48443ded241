<script setup lang="ts">
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Separator } from '@/components/ui/separator'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Button } from '@/components/ui/button'
import { useSystemSettings } from '@/composables/admin/useSystemSettings'
import { onMounted, ref, watch, computed } from 'vue'
import { useNotification } from '@/composables/shared/useNotification'
import { Textarea } from '@/components/ui/textarea'
import { adminSettingsService } from '@/services/admin/settings.service'

const { emailSettings, loadEmailSettings, updateEmailSettings, testEmailSettings } = useSystemSettings()
const notification = useNotification()
const dynamicTemplateDataString = ref('{}')
const sendGridTemplateId = ref('')
const recipientEmail = ref('')
const testingEmail = ref(false)
const savingSettings = ref(false)
// 前端郵箱格式驗證
const isFromEmailValid = computed(() => {
  const email = emailSettings.value.fromEmailAddress || ''
  return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)
})

const EMAIL_PROVIDERS = [
  { value: 'smtp', label: 'SMTP 伺服器' },
  { value: 'sendgrid', label: 'SendGrid' }
]

onMounted(async () => {
  try {
    await loadEmailSettings()
  } catch (error) {
    console.error('載入郵件設定失敗:', error)
    notification.toast.error('無法載入郵件設定')
  }
})

// 初始化 dynamicTemplateDataString
watch(
  () => emailSettings.value?.selectedTemplateId,
  () => {
    if (!emailSettings.value) return
    
    dynamicTemplateDataString.value =
      emailSettings.value.dynamicTemplateData && Object.keys(emailSettings.value.dynamicTemplateData).length
        ? JSON.stringify(emailSettings.value.dynamicTemplateData, null, 2)
        : ''
  },
  { immediate: true }
)


const handleSave = async () => {
  if (!isFromEmailValid.value) {
    notification.toast.error('請輸入正確的寄件人郵箱')
    return
  }
  try {
    savingSettings.value = true
    await updateEmailSettings(emailSettings.value)
    notification.toast.success('郵件設定已儲存')
  } catch (error: any) {
    // 後端驗證錯誤處理
    if (error?.response?.data?.errors) {
      // 只顯示一次錯誤
      const msg = error.response.data.errors.map((e: any) => `${e.field}: ${e.message}`).join('\n')
      notification.toast.error(msg)
    } else if (typeof error?.message === 'string') {
      notification.toast.error(error.message)
    } else {
      notification.toast.error('儲存郵件設定失敗')
    }
    console.error('儲存郵件設定失敗:', error)
  } finally {
    savingSettings.value = false
  }
}

const handleTest = async () => {
  try {
    testingEmail.value = true
    await testEmailSettings()
    notification.toast.success('測試郵件發送成功')
  } catch (error) {
    notification.toast.error('發送測試郵件失敗')
    console.error('測試郵件發送失敗:', error)
  } finally {
    testingEmail.value = false
  }
}

const handleDynamicDataChange = (event: Event) => {
  if (!emailSettings.value) return
  
  const target = event.target as HTMLTextAreaElement
  if (!target) return

  try {
    const parsed = JSON.parse(target.value)
    emailSettings.value = {
      ...emailSettings.value,
      dynamicTemplateData: parsed
    }
  } catch (e) {
    // Invalid JSON, ignore
  }
}

// SendGrid 模板處理
const addTemplateId = () => {
  if (!sendGridTemplateId.value) return
  
  if (!emailSettings.value) return
  
  // 初始化 sendGridTemplates 陣列（如果不存在）
  if (!emailSettings.value.sendGridTemplates) {
    emailSettings.value.sendGridTemplates = []
  }
  
  // 檢查是否已存在相同的模板 ID
  const templateExists = emailSettings.value.sendGridTemplates.some(
    (t) => t.id === sendGridTemplateId.value
  )
  
  if (templateExists) {
    notification.toast.warning('此模板 ID 已經新增過')
    return
  }
  
  // 新增新模板
  emailSettings.value.sendGridTemplates.push({
    id: sendGridTemplateId.value,
    name: sendGridTemplateId.value
  })
  
  sendGridTemplateId.value = ''
  notification.toast.success('模板 ID 新增成功')
}

const removeTemplateId = (index: number) => {
  if (!emailSettings.value || !emailSettings.value.sendGridTemplates) return
  
  // 檢查是否要移除當前選中的模板
  const templateToRemove = emailSettings.value.sendGridTemplates[index]
  if (templateToRemove && templateToRemove.id === emailSettings.value.selectedTemplateId) {
    emailSettings.value.selectedTemplateId = ''
    dynamicTemplateDataString.value = ''
    emailSettings.value.dynamicTemplateData = {}
  }
  
  emailSettings.value.sendGridTemplates.splice(index, 1)
}

const selectTemplateId = (templateId: string) => {
  if (!emailSettings.value) return
  
  emailSettings.value.selectedTemplateId = templateId
  
  // 提供一個默認的動態數據結構作為範例
  if (!emailSettings.value.dynamicTemplateData || Object.keys(emailSettings.value.dynamicTemplateData).length === 0) {
    const defaultData = {
      subject: '郵件標題',
      name: '收件人名稱',
      company: '公司名稱',
      date: new Date().toLocaleDateString(),
      content: '這是郵件內容示例',
      cta: {
        text: '點擊此處',
        url: 'https://example.com'
      }
    }
    emailSettings.value.dynamicTemplateData = defaultData
    dynamicTemplateDataString.value = JSON.stringify(defaultData, null, 2)
  } else {
    dynamicTemplateDataString.value = JSON.stringify(emailSettings.value.dynamicTemplateData, null, 2)
  }
  
  notification.toast.success('已選擇模板')
}
</script>

<template>
  <div>
    <Card>
      <CardHeader>
        <div class="flex items-center justify-between">
          <div>
            <CardTitle>郵件設定</CardTitle>
            <CardDescription>設定系統郵件伺服器與寄件人資訊</CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent v-if="emailSettings" class="space-y-6">
        <!-- 郵件服務提供者選擇 -->
        <div class="space-y-2">
          <Label>郵件服務提供者</Label>
          <Select v-model="emailSettings.provider">
            <SelectTrigger>
              <SelectValue placeholder="選擇郵件服務提供者" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem
                v-for="provider in EMAIL_PROVIDERS"
                :key="provider.value"
                :value="provider.value"
              >
                {{ provider.label }}
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        <!-- SMTP 設定 -->
        <template v-if="emailSettings.provider === 'smtp'">
          <div class="space-y-2">
            <Label>SMTP 主機</Label>
            <Input
              v-model="emailSettings.smtpHost"
              placeholder="例如：smtp.gmail.com"
            />
          </div>
          <div class="space-y-2">
            <Label>SMTP 連接埠</Label>
            <Input
              v-model="emailSettings.smtpPort"
              type="number"
              placeholder="例如：587"
            />
          </div>
          <div class="space-y-2">
            <Label>SMTP 帳號</Label>
            <Input
              v-model="emailSettings.smtpUser"
              placeholder="輸入 SMTP 帳號"
            />
          </div>
          <div class="space-y-2">
            <Label>SMTP 密碼</Label>
            <Input
              v-model="emailSettings.smtpPassword"
              type="password"
              placeholder="輸入 SMTP 密碼"
            />
          </div>
          <div class="flex items-center space-x-2">
            <Switch v-model="emailSettings.smtpSecure" />
            <Label>使用 SSL/TLS</Label>
          </div>
        </template>

        <!-- SendGrid 設定 -->
        <template v-if="emailSettings.provider === 'sendgrid'">
          <div class="space-y-2">
            <Label>SendGrid API Key</Label>
            <div class="space-y-1">
              <Input
                v-model="emailSettings.sendGridApiKey"
                type="password"
                placeholder="輸入 SendGrid API Key"
              />
              <p class="text-xs text-muted-foreground">
                從 SendGrid 控制台讀取 API Key。訪問 
                <a 
                  href="https://app.sendgrid.com/settings/api_keys" 
                  target="_blank"
                  class="text-primary hover:underline"
                >
                  SendGrid API Keys
                </a>
                 頁面建立或管理您的 API Key。
              </p>
            </div>
          </div>
          
          <div class="space-y-2">
            <Label>SendGrid 模板選擇 (選填)</Label>
            <div class="grid grid-cols-1 gap-4">
              <p class="text-sm text-muted-foreground">
                您可以使用 SendGrid 的動態郵件模板來發送自定義郵件。這些模板允許您使用變數並保持一致的品牌形象。
              </p>
              
              <div class="flex items-center gap-2">
                <Input
                  v-model="sendGridTemplateId"
                  placeholder="輸入 SendGrid 模板 ID"
                />
                <Button 
                  variant="outline" 
                  size="sm"
                  @click="addTemplateId" 
                  :disabled="!sendGridTemplateId"
                >
                  新增
                </Button>
              </div>
              
              <div v-if="emailSettings.sendGridTemplates && emailSettings.sendGridTemplates.length > 0" class="space-y-2">
                <p class="font-medium text-sm">已新增的模板:</p>
                <div v-for="(template, index) in emailSettings.sendGridTemplates" :key="index" 
                  class="flex items-center justify-between p-2 border rounded-md">
                  <span class="text-sm truncate max-w-[400px]">{{ template.id }}</span>
                  <div class="flex items-center gap-2">
                    <Button 
                      variant="outline" 
                      size="sm"
                      @click="selectTemplateId(template.id)"
                    >
                      使用此模板
                    </Button>
                    <Button 
                      variant="ghost" 
                      size="sm"
                      @click="removeTemplateId(index)"
                    >
                      <span class="sr-only">移除</span>
                      <span>✕</span>
                    </Button>
                  </div>
                </div>
              </div>
              
              <!-- 當前選定的模板 -->
              <div v-if="emailSettings.selectedTemplateId" class="p-3 border rounded-md bg-muted/20">
                <div class="flex items-center justify-between mb-2">
                  <p class="font-medium">當前使用的模板:</p>
                  <Button 
                    variant="ghost" 
                    size="sm"
                    @click="emailSettings.selectedTemplateId = ''"
                  >
                    取消選擇
                  </Button>
                </div>
                <p class="text-sm mb-2">{{ emailSettings.selectedTemplateId }}</p>
                
                <!-- 動態數據編輯器 -->
                <div class="mt-3">
                  <Label>動態模板數據 (JSON 格式)</Label>
                  <Textarea 
                    v-model="dynamicTemplateDataString"
                    rows="5"
                    class="font-mono text-xs"
                    placeholder="{}"
                    @change="handleDynamicDataChange"
                  />
                  <p class="text-xs text-muted-foreground mt-1">
                    輸入 JSON 格式的動態數據，這些數據將用於替換模板中的變數。
                  </p>
                </div>
              </div>
              
              <div class="mt-1">
                <p class="text-xs text-muted-foreground">
                  您可以在 
                  <a 
                    href="https://mc.sendgrid.com/dynamic-templates" 
                    target="_blank"
                    class="text-primary hover:underline"
                  >
                    SendGrid Dynamic Templates
                  </a>
                   頁面建立和管理您的郵件模板。
                </p>
              </div>
            </div>
          </div>
        </template>

        <Separator />
        <!-- 寄件人設定 -->
        <div class="space-y-4">
          <div class="flex flex-col space-y-1.5">
            <Label>寄件人郵箱</Label>
            <Input v-model="emailSettings.fromEmailAddress" placeholder="例如: <EMAIL>" />
          </div>
          <div class="flex flex-col space-y-1.5">
            <Label>寄件人名稱</Label>
            <Input v-model="emailSettings.fromName" placeholder="例如: Your Company Name" />
          </div>
        </div>
        <!-- 測試收件人設定 -->
        <div class="space-y-2">
          <Label>收件人</Label>
          <Input v-model="recipientEmail" placeholder="例如: <EMAIL>" />
          <p class="text-xs text-muted-foreground">
            測試郵件將發送至此地址。如留空，將發送至寄件人信箱。
          </p>
        </div>
        <div class="flex justify-end space-x-2 pt-4">
          <Button v-model:disabled="testingEmail" @click="handleTest" variant="outline">
            <span v-if="testingEmail">發送中...</span>
            <span v-else>測試郵件</span>
          </Button>
          <Button v-model:disabled="savingSettings" @click="handleSave">
            <span v-if="savingSettings">儲存中...</span>
            <span v-else>儲存設定</span>
          </Button>
        </div>
      </CardContent>
    </Card>
  </div>
</template> 