<template>
  <div class="space-y-6">
    <!-- 權限系統狀態總覽 -->
    <Card>
      <CardHeader>
        <div class="flex items-center justify-between">
          <div>
            <CardTitle class="flex items-center gap-2">
              <Shield class="h-5 w-5" />
              權限系統狀態
            </CardTitle>
            <CardDescription>
              程式碼與資料庫權限定義同步狀態
            </CardDescription>
          </div>
          <Button @click="refreshStatus" :disabled="isLoading" variant="outline" size="sm">
            <RefreshCw class="h-4 w-4 mr-2" :class="{ 'animate-spin': isLoading }" />
            重新整理
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div v-if="isLoading" class="flex items-center justify-center py-8">
          <Loader2 class="h-6 w-6 animate-spin" />
          <span class="ml-2">載入狀態中...</span>
        </div>
        
        <div v-else-if="syncStatus" class="space-y-6">
          <!-- 同步狀態指示器 -->
          <div class="flex items-center gap-4 p-4 rounded-lg border" 
               :class="needsSync ? 'bg-orange-50 border-orange-200 dark:bg-orange-950/20 dark:border-orange-800' : 'bg-green-50 border-green-200 dark:bg-green-950/20 dark:border-green-800'">
            <div class="flex-shrink-0">
              <div v-if="needsSync" class="flex items-center justify-center w-10 h-10 bg-orange-100 rounded-full dark:bg-orange-900/50">
                <AlertTriangle class="h-5 w-5 text-orange-600 dark:text-orange-400" />
              </div>
              <div v-else class="flex items-center justify-center w-10 h-10 bg-green-100 rounded-full dark:bg-green-900/50">
                <CheckCircle class="h-5 w-5 text-green-600 dark:text-green-400" />
              </div>
            </div>
            <div class="flex-1">
              <h3 class="text-lg font-semibold mb-1" :class="needsSync ? 'text-orange-900 dark:text-orange-100' : 'text-green-900 dark:text-green-100'">
                {{ needsSync ? '發現權限變更' : '權限已同步' }}
              </h3>
              <p class="text-sm" :class="needsSync ? 'text-orange-700 dark:text-orange-300' : 'text-green-700 dark:text-green-300'">
                {{ needsSync 
                  ? `程式碼中有 ${pendingChanges} 個權限變更需要同步到資料庫` 
                  : '程式碼與資料庫的權限定義完全一致，無需同步' 
                }}
              </p>
              <div v-if="lastSyncTime" class="text-xs mt-1" :class="needsSync ? 'text-orange-600 dark:text-orange-400' : 'text-green-600 dark:text-green-400'">
                最後同步: {{ formatDateTime(lastSyncTime) }}
              </div>
            </div>
          </div>

          <!-- 權限統計 -->
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="text-center p-4 bg-blue-50 rounded-lg border border-blue-200 dark:bg-blue-950/20 dark:border-blue-800">
              <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">{{ syncStatus.codePermissions }}</div>
              <div class="text-sm text-blue-700 dark:text-blue-300">程式碼定義</div>
              <div class="text-xs text-blue-600 dark:text-blue-400 mt-1">來源：@horizai/permissions</div>
            </div>
            <div class="text-center p-4 bg-purple-50 rounded-lg border border-purple-200 dark:bg-purple-950/20 dark:border-purple-800">
              <div class="text-2xl font-bold text-purple-600 dark:text-purple-400">{{ syncStatus.dbPermissions }}</div>
              <div class="text-sm text-purple-700 dark:text-purple-300">資料庫儲存</div>
              <div class="text-xs text-purple-600 dark:text-purple-400 mt-1">Permission 表</div>
            </div>
            <div class="text-center p-4 bg-gray-50 rounded-lg border border-gray-200 dark:bg-gray-950/20 dark:border-gray-800">
              <div class="text-2xl font-bold text-gray-600 dark:text-gray-400">{{ syncStatus.totalPermissions }}</div>
              <div class="text-sm text-gray-700 dark:text-gray-300">系統總計</div>
              <div class="text-xs text-gray-600 dark:text-gray-400 mt-1">可用權限數</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 同步操作控制與結果展示 -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- 左欄: 同步操作控制 -->
      <div class="lg:col-span-2">
        <Card>
          <CardHeader>
            <CardTitle class="flex items-center gap-2">
              <Workflow class="h-5 w-5" />
              權限同步操作
            </CardTitle>
            <CardDescription>
              依照下列步驟掃描程式碼權限定義並同步到資料庫
            </CardDescription>
          </CardHeader>
          <CardContent class="space-y-6">
            <!-- 步驟 1: 掃描程式碼權限 -->
            <div>
              <h3 class="text-md font-semibold mb-2 flex items-center">
                <Search class="h-4 w-4 mr-2 text-primary-600 dark:text-primary-400" />
                1. 掃描程式碼權限
              </h3>
              <p class="text-sm text-muted-foreground mb-3">
                分析程式碼中的權限定義，檢查與資料庫的差異。
              </p>
              <div class="flex flex-wrap items-center gap-x-6 gap-y-3">
                <Button 
                  @click="scanPermissions" 
                  :disabled="isOperating" 
                  variant="outline"
                  class="min-w-[130px]"
                >
                  <RefreshCw class="h-4 w-4 mr-2" :class="{ 'animate-spin': isScanning && !isLoading && !isSyncing }" />
                  {{ (isScanning && !isLoading && !isSyncing) ? '掃描中...' : '開始掃描' }}
                </Button>
                <div class="flex items-center space-x-2">
                  <Checkbox 
                    id="no-cache" 
                    v-model:checked="syncOptions.noCache"
                    :disabled="isOperating"
                  />
                  <label for="no-cache" class="text-sm font-medium text-foreground flex items-center">
                    忽略快取
                    <TooltipProvider :delay-duration="100">
                      <Tooltip>
                        <TooltipTrigger as-child>
                          <Info class="h-3.5 w-3.5 ml-1.5 text-muted-foreground cursor-help hover:text-foreground transition-colors" />
                        </TooltipTrigger>
                        <TooltipContent class="max-w-xs">
                          <p>勾選此項將強制重新分析所有程式碼檔案，而非使用先前掃描的快取結果。掃描時間可能較長，適用於快取可能過時的情況。</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </label>
                </div>
              </div>
            </div>

            <Separator />

            <!-- 步驟 2: 預覽變更 (條件顯示) -->
            <div v-if="syncStatus && pendingChanges > 0 && !isLoading">
              <h3 class="text-md font-semibold mb-2 flex items-center">
                <Eye class="h-4 w-4 mr-2 text-primary-600 dark:text-primary-400" />
                2. 預覽變更
              </h3>
              <p class="text-sm text-muted-foreground mb-3">
                查看將同步到資料庫的具體權限變更。此操作不會實際修改資料庫。
              </p>
              <Button 
                @click="previewSync" 
                :disabled="isOperating" 
                variant="outline"
                class="min-w-[130px]"
              >
                <FileSearch class="h-4 w-4 mr-2" />
                檢視預覽
              </Button>
            </div>
            <div v-else-if="syncStatus && pendingChanges === 0 && !isLoading && !isOperating">
               <p class="text-sm text-muted-foreground flex items-center">
                <CheckCircle class="h-4 w-4 mr-2 text-green-500" />
                程式碼與資料庫權限定義目前一致，無需預覽或同步。
              </p>
            </div>


            <Separator v-if="syncStatus && pendingChanges > 0 && !isLoading" />

            <!-- 步驟 3: 執行同步 (條件顯示) -->
            <div v-if="needsSync && !isLoading">
              <h3 class="text-md font-semibold mb-2 flex items-center">
                <RotateCw class="h-4 w-4 mr-2 text-primary-600 dark:text-primary-400" />
                3. 執行同步
              </h3>
              <p class="text-sm text-muted-foreground mb-3">
                將掃描到的程式碼權限定義變更實際應用到資料庫中。
              </p>
              <div class="flex flex-wrap items-center gap-x-6 gap-y-3">
                <Button 
                  @click="executeSync" 
                  :disabled="isOperating || !needsSync"
                  :variant="needsSync ? 'default' : 'secondary'"
                  class="min-w-[130px]"
                >
                  <Zap class="h-4 w-4 mr-2" :class="{ 'animate-spin': isSyncing && !isLoading && !isScanning }" />
                  {{ (isSyncing && !isLoading && !isScanning) ? '同步中...' : '執行同步' }}
                </Button>
                <div class="flex items-center space-x-2">
                  <Checkbox 
                    id="force" 
                    v-model:checked="syncOptions.force"
                    :disabled="isOperating"
                  />
                  <label for="force" class="text-sm font-medium text-foreground flex items-center">
                    強制覆蓋屬性
                    <TooltipProvider :delay-duration="100">
                      <Tooltip>
                        <TooltipTrigger as-child>
                          <AlertTriangle class="h-3.5 w-3.5 ml-1.5 text-orange-500 cursor-help hover:text-orange-600 transition-colors" />
                        </TooltipTrigger>
                        <TooltipContent class="max-w-xs border-orange-500 bg-orange-50 text-orange-700 dark:bg-orange-900/80 dark:text-orange-300 dark:border-orange-700">
                          <p class="font-semibold">警告：重要操作</p>
                          <p>勾選此項將使用程式碼中的定義覆蓋資料庫中權限的現有描述、分類等屬性。除非您確定要這麼做，否則請勿勾選。</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </label>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- 右欄: 最近操作結果 -->
      <div class="space-y-6 lg:col-span-1">
        <!-- 掃描結果 -->
        <Card v-if="lastScanResult">
          <CardHeader>
            <CardTitle class="flex items-center gap-2">
              <FileSearch class="h-5 w-5" />
              最近掃描結果
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div class="space-y-3">
              <div class="grid grid-cols-3 gap-3">
                <div class="text-center p-3 bg-blue-50 rounded-lg dark:bg-blue-950/20">
                  <div class="text-lg font-bold text-blue-600 dark:text-blue-400">{{ lastScanResult.filesScanned }}</div>
                  <div class="text-xs text-blue-700 dark:text-blue-300">掃描檔案</div>
                </div>
                <div class="text-center p-3 bg-green-50 rounded-lg dark:bg-green-950/20">
                  <div class="text-lg font-bold text-green-600 dark:text-green-400">{{ lastScanResult.permissionsFound }}</div>
                  <div class="text-xs text-green-700 dark:text-green-300">發現權限</div>
                </div>
                <div class="text-center p-3 bg-orange-50 rounded-lg dark:bg-orange-950/20">
                  <div class="text-lg font-bold text-orange-600 dark:text-orange-400">{{ lastScanResult.hardcodedWarnings }}</div>
                  <div class="text-xs text-orange-700 dark:text-orange-300">硬編碼警告</div>
                </div>
              </div>
              <div class="text-xs text-muted-foreground text-center">
                {{ formatDateTime(lastScanResult.timestamp) }}
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- 同步結果 -->
        <Card v-if="lastSyncResult">
          <CardHeader>
            <CardTitle class="flex items-center gap-2">
              <CheckCircle class="h-5 w-5" />
              最近同步結果
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div class="space-y-3">
              <div class="grid grid-cols-2 gap-3">
                <div class="text-center p-3 bg-green-50 rounded-lg dark:bg-green-950/20">
                  <div class="text-lg font-bold text-green-600 dark:text-green-400">{{ lastSyncResult.created }}</div>
                  <div class="text-xs text-green-700 dark:text-green-300">新增</div>
                </div>
                <div class="text-center p-3 bg-blue-50 rounded-lg dark:bg-blue-950/20">
                  <div class="text-lg font-bold text-blue-600 dark:text-blue-400">{{ lastSyncResult.updated }}</div>
                  <div class="text-xs text-blue-700 dark:text-blue-300">更新</div>
                </div>
                <div class="text-center p-3 bg-orange-50 rounded-lg dark:bg-orange-950/20">
                  <div class="text-lg font-bold text-orange-600 dark:text-orange-400">{{ lastSyncResult.deprecated }}</div>
                  <div class="text-xs text-orange-700 dark:text-orange-300">廢棄</div>
                </div>
                <div class="text-center p-3 bg-red-50 rounded-lg dark:bg-red-950/20">
                  <div class="text-lg font-bold text-red-600 dark:text-red-400">{{ lastSyncResult.errors }}</div>
                  <div class="text-xs text-red-700 dark:text-red-300">錯誤</div>
                </div>
              </div>
              
              <div class="flex items-center justify-between">
                <div class="text-xs text-muted-foreground">
                  {{ formatDateTime(lastSyncResult.timestamp) }}
                </div>
                <Button 
                  v-if="lastSyncResult.changes && lastSyncResult.changes.length > 0"
                  @click="showSyncDetails = true" 
                  variant="outline" 
                  size="sm"
                >
                  <FileText class="h-4 w-4 mr-2" />
                  查看詳情
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- 若無結果的提示卡片 -->
        <Card v-if="!lastScanResult && !lastSyncResult && !isLoading && !isOperating">
          <CardHeader>
            <CardTitle class="flex items-center gap-2">
              <Info class="h-5 w-5 text-muted-foreground" />
              操作結果提示
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p class="text-sm text-muted-foreground">
              執行掃描或同步操作後，相關結果將顯示於此處。
            </p>
          </CardContent>
        </Card>
      </div>
    </div>

    <!-- 詳細變更對話框 -->
    <Dialog v-model:open="showSyncDetails">
      <DialogContent class="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>同步變更詳情</DialogTitle>
          <DialogDescription>
            查看本次同步的詳細變更記錄
          </DialogDescription>
        </DialogHeader>
        
        <div v-if="lastSyncResult?.changes" class="space-y-4">
          <div v-for="(change, index) in lastSyncResult.changes" :key="index" 
               class="p-4 border rounded-lg">
            <div class="flex items-center gap-2 mb-2">
              <Badge :variant="getChangeVariant(change.type)">
                {{ getChangeLabel(change.type) }}
              </Badge>
              <span class="font-medium">{{ change.action }}:{{ change.subject }}</span>
            </div>
            <div v-if="change.description" class="text-sm text-muted-foreground">
              {{ change.description }}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { usePermissionsSync } from '@/composables/admin/usePermissionsSync';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Separator } from '@/components/ui/separator';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { 
  RefreshCw, 
  Download, 
  Search, 
  Eye, 
  CheckCircle, 
  Loader2,
  Workflow,
  FileSearch,
  FileText,
  Shield,
  AlertTriangle,
  Zap,
  RotateCw,
  Info
} from 'lucide-vue-next';

const {
  isLoading,
  isSyncing,
  isScanning,
  syncStatus,
  lastSyncResult,
  lastScanResult,
  needsSync,
  pendingChanges,
  lastSyncTime,
  loadSyncStatus,
  scanPermissions: performScan,
  previewSync: performPreview,
  syncPermissions: performSync,
} = usePermissionsSync();

// 計算屬性
const isOperating = computed(() => isLoading.value || isSyncing.value || isScanning.value);

// 本地狀態
const showSyncDetails = ref(false);
const syncOptions = ref({
  dryRun: false,
  force: false,
  noCache: false,
});

// 方法
const refreshStatus = async () => {
  await loadSyncStatus();
};

const scanPermissions = async () => {
  await performScan(syncOptions.value.noCache);
};

const previewSync = async () => {
  await performPreview();
};

const executeSync = async () => {
  await performSync(syncOptions.value.force);
  // 等待一小段時間確保後端狀態更新完成
  await new Promise(resolve => setTimeout(resolve, 1000));
  // 同步完成後重新載入狀態
  await loadSyncStatus();
};

const formatDateTime = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-TW');
};

const getChangeVariant = (type: string) => {
  switch (type) {
    case 'created':
    case 'will_create':
      return 'default';
    case 'updated':
    case 'will_update':
      return 'secondary';
    case 'deprecated':
    case 'will_deprecate':
      return 'destructive';
    default:
      return 'outline';
  }
};

const getChangeLabel = (type: string) => {
  switch (type) {
    case 'created':
    case 'will_create':
      return '新增';
    case 'updated':
    case 'will_update':
      return '更新';
    case 'deprecated':
    case 'will_deprecate':
      return '廢棄';
    default:
      return type;
  }
};

// 初始化
onMounted(async () => {
  await loadSyncStatus();
});
</script> 