<template>
  <!-- 角色選擇卡片（可選擇性顯示） -->
  <Card v-if="props.showRoleSelect">
    <CardContent class="p-3">
      <div
        class="flex flex-col space-y-3 sm:flex-row sm:space-y-0 sm:space-x-4 sm:items-center"
      >
        <div class="w-full max-w-xs">
          <Select
            :key="`select-${roles?.length || 0}`"
            :value="modelValue"
            @update:value="(value: string) => emit('update:modelValue', value)"
          >
            <SelectTrigger>
              <SelectValue placeholder="選擇角色" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem v-for="role in roles" :key="role.id" :value="role.id">
                {{ role.displayName }}
                <span class="ml-2 text-xs opacity-70">
                  {{ getScopeLabel(role.scope) }}
                </span>
              </SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div class="text-xs text-muted-foreground">
          設定角色的權限，變更會自動儲存
        </div>
      </div>
    </CardContent>
  </Card>

  <!-- 權限分群母項目 Accordion -->
  <TooltipProvider>
    <Accordion type="single" collapsible :default-value="[]">
      <AccordionItem
        v-for="group in permissionGroups"
        :key="group.id"
        :value="group.id"
      >
        <AccordionTrigger>
          <div class="flex items-center gap-2">
            <CardTitle class="flex items-center gap-2 m-0">
              <component
                :is="iconMap[group.icon ?? 'Settings']"
                class="h-4 w-4"
              />
              {{ group.name }}
            </CardTitle>
            <CardDescription
              v-if="group.description"
              class="text-xs ml-2"
              style="margin-top: 0"
            >
              {{ group.description }}
            </CardDescription>
          </div>
        </AccordionTrigger>
        <AccordionContent>
          <div>
            <div v-for="sub in group.children" :key="sub.id" class="mb-2">
              <div class="font-semibold text-xs mb-1">{{ sub.name }}</div>
              <div class="flex justify-end mb-1">
                <button
                  class="text-xs text-primary underline"
                  @click="toggleAll(sub.permissions, true)"
                >
                  全選
                </button>
                <span class="mx-1">/</span>
                <button
                  class="text-xs text-primary underline"
                  @click="toggleAll(sub.permissions, false)"
                >
                  全不選
                </button>
              </div>
              <div class="space-y-2">
                <FormField
                  v-for="perm in sub.permissions"
                  :key="perm.id"
                  :name="perm.id"
                  v-slot="{ value, handleChange }"
                >
                  <FormItem
                    class="flex flex-row items-center justify-between rounded-lg border p-4"
                  >
                    <div class="space-y-0.5">
                      <FormLabel
                        :class="[
                          'text-xs',
                          perm.danger ? 'text-destructive' : '',
                        ]"
                      >
                        {{ extractSubjectFromPermissionName(perm.name) }}
                        <span
                          v-if="perm.danger"
                          class="ml-1 text-[10px] text-destructive"
                          >(高風險)</span
                        >
                      </FormLabel>
                      <FormDescription v-if="perm.description">
                        {{ perm.description }}
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        :id="perm.id"
                        :model-value="
                          permissionCheckedMap.get(perm.id) ?? false
                        "
                        :disabled="isLoading"
                        @update:model-value="
                          (checked) => togglePermission(perm.id, checked)
                        "
                      />
                    </FormControl>
                  </FormItem>
                </FormField>
              </div>
            </div>
          </div>
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  </TooltipProvider>
</template>

<script setup lang="ts">
import type { Role } from "@/types/models/role.model";
import { RoleScope } from "@/types/models/role.model";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import {
  Accordion,
  AccordionItem,
  AccordionTrigger,
  AccordionContent,
} from "@/components/ui/accordion";
import Tooltip from "@/components/ui/tooltip/Tooltip.vue";
import TooltipProvider from "@/components/ui/tooltip/TooltipProvider.vue";
import {
  Settings,
  Users,
  Briefcase,
  Folders,
  CreditCard,
  BarChart,
  Zap,
} from "lucide-vue-next";
import type {
  PermissionGroup,
  PermissionItem,
} from "@/composables/admin/usePermissionMatrix";
import { computed } from "vue";
import {
  FormField,
  FormItem,
  FormLabel,
  FormDescription,
  FormControl,
} from "@/components/ui/form";
import { extractSubjectFromPermissionName } from "@/utils/i18n.utils";

const props = defineProps<{
  modelValue?: string;
  roles?: Role[];
  permissionGroups: PermissionGroup[];
  rolePermissions: string[];
  isLoading: boolean;
  showRoleSelect?: boolean;
}>();

const emit = defineEmits<{
  (e: "update:modelValue", value: string): void;
  (e: "toggle-permission", permissionId: string, checked: boolean): void;
  (
    e: "toggle-permissions-batch",
    permissionIds: string[],
    checked: boolean
  ): void;
}>();

const iconMap: Record<string, any> = {
  // 支援資料庫中的字串圖示名稱
  Settings: Settings,
  Users: Users,
  Briefcase: Briefcase,
  Folders: Folders,
  CreditCard: CreditCard,
  BarChart: BarChart,
  Zap: Zap,
};

const hasPermission = (permissionId: string): boolean => {
  // Create a plain array copy to avoid potential proxy issues
  const currentPerms = Array.from(props.rolePermissions || []);
  return currentPerms.includes(permissionId);
};

const togglePermission = (permissionId: string, checked: boolean) => {
  emit("toggle-permission", permissionId, checked);
};

const toggleAll = (permissions: PermissionItem[], checked: boolean) => {
  const ids = permissions.map((p) => p.id);
  emit("toggle-permissions-batch", ids, checked);
};

// 獲取範疇標籤的函數
const getScopeLabel = (scope: string): string => {
  return scope === RoleScope.SYSTEM
    ? "(系統管理)"
    : scope === RoleScope.TENANT
      ? "(租戶管理)"
      : "(工作區域)";
};

// Computed property to create a map of permission ID -> checked state
const permissionCheckedMap = computed(() => {
  const map = new Map<string, boolean>();
  const currentPerms = Array.from(props.rolePermissions || []);
  // Need to iterate through all *possible* permissions shown in the matrix
  for (const group of props.permissionGroups) {
    for (const sub of group.children) {
      for (const perm of sub.permissions) {
        map.set(perm.id, currentPerms.includes(perm.id));
      }
    }
  }
  return map;
});
</script>

<style scoped>
/* Accordion 展開/收摺動畫 */
.accordion-fade-enter-active,
.accordion-fade-leave-active {
  transition:
    max-height 0.3s cubic-bezier(0.4, 0, 0.2, 1),
    opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}
.accordion-fade-enter-from,
.accordion-fade-leave-to {
  max-height: 0;
  opacity: 0;
}
.accordion-fade-enter-to,
.accordion-fade-leave-from {
  max-height: 500px;
  opacity: 1;
}
</style>
