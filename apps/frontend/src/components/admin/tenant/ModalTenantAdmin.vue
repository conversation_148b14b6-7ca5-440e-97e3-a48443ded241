<script setup lang="ts">
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useTenantAdmin } from "@/composables/admin/useTenantAdmin";
import { useTenantPermissions } from "@/composables/admin/useTenantPermissions";
import {
  UserPlus,
  Mail,
  Trash2,
  Check,
  Ban,
  AlertTriangle,
  MailPlus,
} from "lucide-vue-next";
import { watch } from "vue";
import { useAbility } from "@horizai/auth";
import { Actions, Subjects } from "@horizai/permissions";

defineOptions({ name: "ModalTenantAdmin" });

const props = defineProps<{
  show: boolean;
  tenantId: string | null;
  tenantName: string;
}>();

const emit = defineEmits<{
  "update:show": [value: boolean];
}>();

// 權限檢查
const { tenantPermissions } = useTenantPermissions();
const { can } = useAbility();

// 使用 composable
const {
  // 狀態
  activeTab,
  tenantAdmins,
  isLoadingTenantAdmins,
  inviteEmail,
  isSendingInvite,
  invitations,
  isLoadingInvitations,

  // 方法
  fetchTenantAdmins,
  removeAdminFromTenant,
  toggleAdminStatus,
  sendAdminInvite,
  fetchInvitations,
  resendInvitation,
  cancelInvitation,
  formatDate,
  formatExpiryDate,
  resetState,
} = useTenantAdmin(props);

// 監聽 activeTab 變化
watch(activeTab, (newTab) => {
  console.log("[ModalTenantAdmin] activeTab 變更為:", newTab);
  if (newTab === "invitations" && props.tenantId) {
    console.log("[ModalTenantAdmin] 切換到邀請列表頁籤，開始讀取邀請列表");
    fetchInvitations();
  }
});

// 監聽 show 變化
watch(
  () => props.show,
  (newShow) => {
    console.log("[ModalTenantAdmin] show 變更為:", newShow);
    if (newShow && props.tenantId) {
      console.log("[ModalTenantAdmin] 模態框顯示，開始讀取數據");
      fetchTenantAdmins();
      if (activeTab.value === "invitations") {
        fetchInvitations();
      }
    } else {
      console.log("[ModalTenantAdmin] 模態框隱藏，重置狀態");
      resetState();
    }
  }
);

// 處理重新發送邀請
const handleResendInvitation = async (invitationId: string) => {
  console.log("[ModalTenantAdmin] 嘗試重新發送邀請:", invitationId);
  if (!can(Actions.UPDATE, Subjects.TENANT_INVITATION)) {
    console.error("[ModalTenantAdmin] 沒有權限重新發送邀請");
    return;
  }
  await resendInvitation(invitationId);
};

// 處理取消邀請
const handleCancelInvitation = async (invitationId: string) => {
  console.log("[ModalTenantAdmin] 嘗試取消邀請:", invitationId);
  if (!can(Actions.DELETE, Subjects.TENANT_INVITATION)) {
    console.error("[ModalTenantAdmin] 沒有權限取消邀請");
    return;
  }
  await cancelInvitation(invitationId);
};

// 關閉對話框
const closeDialog = () => {
  emit("update:show", false);
  resetState();
};
</script>

<template>
  <Dialog :open="show" @update:open="emit('update:show', $event)">
    <DialogContent class="sm:max-w-[550px] max-h-[80vh] overflow-hidden">
      <DialogHeader>
        <DialogTitle>租戶管理員設定</DialogTitle>
        <DialogDescription>
          管理「{{ tenantName }}」租戶的管理員，或邀請新管理員加入
        </DialogDescription>
      </DialogHeader>

      <Tabs v-model="activeTab" class="w-full mt-4">
        <TabsList class="grid w-full grid-cols-3">
          <TabsTrigger value="current-admins">目前管理員</TabsTrigger>
          <TabsTrigger value="send-invite">邀請新管理員</TabsTrigger>
          <TabsTrigger value="invitations">邀請列表</TabsTrigger>
        </TabsList>

        <div
          class="mt-4 overflow-y-auto"
          style="max-height: calc(80vh - 220px)"
        >
          <!-- 目前管理員列表 -->
          <TabsContent value="current-admins" class="mt-0 h-full">
            <div
              v-if="isLoadingTenantAdmins"
              class="flex items-center justify-center h-[300px]"
            >
              <div class="text-center space-y-2">
                <div
                  class="animate-spin rounded-full h-8 w-8 border-2 border-primary border-t-transparent mx-auto"
                ></div>
                <p class="text-muted-foreground">載入管理員中...</p>
              </div>
            </div>

            <div
              v-else-if="tenantAdmins.length === 0"
              class="text-center py-10 border rounded-md"
            >
              <div class="space-y-3">
                <UserPlus class="w-12 h-12 text-muted-foreground mx-auto" />
                <h3 class="text-lg font-medium">此租戶尚無管理員</h3>
                <p class="text-muted-foreground">
                  使用「邀請新管理員」頁籤來邀請管理員加入此租戶
                </p>
              </div>
            </div>

            <div v-else>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>管理員</TableHead>
                    <TableHead>狀態</TableHead>
                    <TableHead>上次登入</TableHead>
                    <TableHead class="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow v-for="admin in tenantAdmins" :key="admin.id">
                    <TableCell>
                      <div class="flex items-center gap-2">
                        <div
                          class="w-8 h-8 rounded-full bg-muted flex items-center justify-center text-xs font-semibold overflow-hidden"
                        >
                          <img
                            v-if="admin.avatar"
                            :src="admin.avatar"
                            alt=""
                            class="w-full h-full object-cover"
                          />
                          <span v-else>{{
                            admin.name?.charAt(0) ?? admin.email.charAt(0)
                          }}</span>
                        </div>
                        <div>
                          <div class="font-medium">
                            {{ admin.name || "(未設定名稱)" }}
                          </div>
                          <div class="text-xs text-muted-foreground">
                            {{ admin.email }}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge
                        :variant="
                          admin.status === 'ACTIVE' ? 'default' : 'secondary'
                        "
                      >
                        {{ admin.status === "ACTIVE" ? "啟用" : "停用" }}
                      </Badge>
                    </TableCell>
                    <TableCell>{{ formatDate(admin.lastLoginAt) }}</TableCell>
                    <TableCell class="text-right">
                      <div class="flex items-center justify-end gap-2">
                        <TooltipProvider>
                          <template v-if="tenantPermissions.canManageUsers">
                            <Tooltip v-if="admin.status === 'ACTIVE'">
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  class="h-8 w-8"
                                  @click="
                                    toggleAdminStatus(admin.id, admin.status)
                                  "
                                >
                                  <Ban class="h-4 w-4" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>停用管理員</p>
                              </TooltipContent>
                            </Tooltip>

                            <Tooltip v-else>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  class="h-8 w-8"
                                  @click="
                                    toggleAdminStatus(admin.id, admin.status)
                                  "
                                >
                                  <Check class="h-4 w-4" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>啟用管理員</p>
                              </TooltipContent>
                            </Tooltip>

                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  class="h-8 w-8 text-destructive hover:text-destructive"
                                  @click="removeAdminFromTenant(admin.id)"
                                >
                                  <Trash2 class="h-4 w-4" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>移除管理員</p>
                              </TooltipContent>
                            </Tooltip>
                          </template>
                        </TooltipProvider>
                      </div>
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </div>
          </TabsContent>

          <!-- 發送管理員邀請頁籤 -->
          <TabsContent value="send-invite" class="mt-0 h-full">
            <Card>
              <CardHeader>
                <CardTitle>邀請新管理員加入租戶</CardTitle>
                <CardDescription>
                  發送邀請郵件給尚未加入系統的使用者，指定其作為租戶管理員
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form @submit.prevent="sendAdminInvite" class="space-y-4">
                  <div class="space-y-2">
                    <label for="invite-email" class="text-sm font-medium"
                      >管理員電子郵件</label
                    >
                    <div class="flex items-center space-x-2">
                      <div class="relative flex-1">
                        <Mail
                          class="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground pointer-events-none"
                        />
                        <Input
                          id="invite-email"
                          v-model="inviteEmail"
                          placeholder="輸入新管理員的電子郵件地址"
                          type="email"
                          required
                          class="pl-8"
                        />
                      </div>
                    </div>
                  </div>

                  <div
                    class="bg-amber-50 border-l-4 border-amber-400 p-4 rounded-md"
                  >
                    <div class="flex">
                      <div class="flex-shrink-0">
                        <AlertTriangle class="h-5 w-5 text-amber-400" />
                      </div>
                      <div class="ml-3">
                        <p class="text-sm text-amber-700">
                          注意：管理員邀請將以郵件形式發送到上述地址。<br />
                          管理員將擁有租戶管理權限，能夠管理租戶內的使用者、設定等。<br />
                          如果是已有的系統使用者，將直接新增為租戶管理員。<br />
                          如果是新使用者，他們需要先完成註冊，然後才能管理此租戶。
                        </p>
                      </div>
                    </div>
                  </div>

                  <Button
                    type="submit"
                    class="w-full"
                    :disabled="
                      isSendingInvite ||
                      !inviteEmail ||
                      !tenantPermissions.canSendInvitations
                    "
                    v-if="tenantPermissions.canSendInvitations"
                  >
                    <Mail class="h-4 w-4 mr-2" />
                    {{ isSendingInvite ? "發送中..." : "發送管理員邀請" }}
                  </Button>

                  <div
                    v-else
                    class="bg-destructive/10 border-l-4 border-destructive p-4 rounded-md"
                  >
                    <p class="text-sm text-destructive">
                      您沒有權限發送管理員邀請。請聯絡系統管理員讀取權限。
                    </p>
                  </div>
                </form>
              </CardContent>
            </Card>
          </TabsContent>

          <!-- 管理員邀請列表頁籤 -->
          <TabsContent value="invitations" class="mt-0 h-full">
            <div
              v-if="isLoadingInvitations"
              class="flex items-center justify-center h-[300px]"
            >
              <div class="text-center space-y-2">
                <div
                  class="animate-spin rounded-full h-8 w-8 border-2 border-primary border-t-transparent mx-auto"
                ></div>
                <p class="text-muted-foreground">載入管理員邀請中...</p>
              </div>
            </div>

            <div
              v-else-if="invitations.length === 0"
              class="text-center py-10 border rounded-md"
            >
              <div class="space-y-3">
                <Mail class="w-12 h-12 text-muted-foreground mx-auto" />
                <h3 class="text-lg font-medium">目前沒有管理員邀請</h3>
                <p class="text-muted-foreground">
                  使用「邀請新管理員」頁籤來邀請租戶管理員
                </p>
                <p class="text-xs text-muted-foreground">
                  租戶ID: {{ tenantId }}
                </p>
              </div>
            </div>

            <div v-else>
              <div class="mb-2 text-sm text-muted-foreground">
                找到 {{ invitations.length }} 個邀請
              </div>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>電子郵件</TableHead>
                    <TableHead>狀態</TableHead>
                    <TableHead>過期時間</TableHead>
                    <TableHead class="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow
                    v-for="invitation in invitations"
                    :key="invitation.id"
                  >
                    <TableCell>{{ invitation.email }}</TableCell>
                    <TableCell>
                      <Badge
                        :variant="
                          invitation.status === 'pending'
                            ? 'default'
                            : 'secondary'
                        "
                      >
                        {{
                          invitation.status === "pending" ? "待處理" : "已取消"
                        }}
                      </Badge>
                    </TableCell>
                    <TableCell>{{
                      formatExpiryDate(invitation.expiresAt)
                    }}</TableCell>
                    <TableCell class="text-right">
                      <div class="flex items-center justify-end gap-2">
                        <TooltipProvider>
                          <template
                            v-if="
                              can(Actions.UPDATE, Subjects.TENANT_INVITATION)
                            "
                          >
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  class="h-8 w-8"
                                  @click.stop="
                                    handleResendInvitation(invitation.id)
                                  "
                                  :disabled="invitation.status !== 'pending'"
                                >
                                  <MailPlus class="h-4 w-4" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>重新發送邀請</p>
                              </TooltipContent>
                            </Tooltip>
                          </template>
                        </TooltipProvider>
                        <TooltipProvider>
                          <template
                            v-if="
                              can(Actions.DELETE, Subjects.TENANT_INVITATION)
                            "
                          >
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  class="h-8 w-8 text-destructive hover:text-destructive"
                                  @click.stop="
                                    handleCancelInvitation(invitation.id)
                                  "
                                  :disabled="invitation.status !== 'pending'"
                                >
                                  <Trash2 class="h-4 w-4" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>取消邀請</p>
                              </TooltipContent>
                            </Tooltip>
                          </template>
                        </TooltipProvider>
                      </div>
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </div>
          </TabsContent>
        </div>
      </Tabs>

      <div class="flex justify-end mt-4 pt-2 border-t">
        <Button variant="outline" @click="closeDialog">關閉</Button>
      </div>
    </DialogContent>
  </Dialog>
</template>
