<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import { orderService } from '@/services/admin/order.service'
import type { Order, OrderStatus } from '@/types/models/order.model'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectTrigger, SelectContent, SelectItem, SelectValue } from '@/components/ui/select'
import { format, startOfMonth, endOfMonth, subMonths } from 'date-fns'

interface Props {
  show: boolean
  tenantName: string
}

const { show, tenantName } = defineProps<Props>()
const emit = defineEmits(['close'])

const orders = ref<Order[]>([])
const isLoading = ref(false)
const errorMsg = ref('')
const status = ref<OrderStatus | 'all'>('all')
const today = new Date()
const startDate = ref(format(startOfMonth(today), 'yyyy-MM-dd'))
const endDate = ref(format(endOfMonth(today), 'yyyy-MM-dd'))
const customRange = ref(false)

const fetchOrders = async () => {
  if (!tenantName) return
  isLoading.value = true
  errorMsg.value = ''
  try {
    orders.value = await orderService.getOrders({
      search: tenantName,
      status: status.value === 'all' ? undefined : status.value,
      startDate: startDate.value || undefined,
      endDate: endDate.value || undefined
    })
  } catch (err: any) {
    errorMsg.value = err?.message || '載入訂單失敗'
  } finally {
    isLoading.value = false
  }
}

watch(() => [show, tenantName], ([s, name]) => {
  if (s && name) fetchOrders()
})

const handleClose = () => {
  emit('close')
}

const handleFilter = () => {
  fetchOrders()
}

const setThisMonth = () => {
  const now = new Date()
  startDate.value = format(startOfMonth(now), 'yyyy-MM-dd')
  endDate.value = format(endOfMonth(now), 'yyyy-MM-dd')
  customRange.value = false
  fetchOrders()
}

const setThreeMonths = () => {
  const now = new Date()
  startDate.value = format(startOfMonth(subMonths(now, 2)), 'yyyy-MM-dd')
  endDate.value = format(endOfMonth(now), 'yyyy-MM-dd')
  customRange.value = false
  fetchOrders()
}

const setCustomRange = () => {
  customRange.value = true
}
</script>

<template>
  <Dialog :open="show" @update:open="val => !val && handleClose()">
    <DialogContent class="max-w-5xl">
      <DialogHeader>
        <DialogTitle>訂單列表</DialogTitle>
        <DialogDescription>
          查看租戶 <span class="font-semibold">{{ tenantName }}</span> 的所有訂單
        </DialogDescription>
      </DialogHeader>
      <div class="flex flex-wrap gap-2 items-center mb-4">
        <div class="flex gap-1 items-center">
          <Select v-model="status">
            <SelectTrigger class="w-32">
              <SelectValue placeholder="全部狀態" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部狀態</SelectItem>
              <SelectItem value="PENDING">待處理</SelectItem>
              <SelectItem value="COMPLETED">已完成</SelectItem>
              <SelectItem value="CANCELLED">已取消</SelectItem>
            </SelectContent>
          </Select>
          <Button size="sm" variant="secondary" @click="setThisMonth">本月</Button>
          <Button size="sm" variant="secondary" @click="setThreeMonths">三個月</Button>
          <Button size="sm" variant="secondary" @click="setCustomRange">自訂區間</Button>
        </div>
        <template v-if="customRange">
          <Input type="date" v-model="startDate" class="w-36" placeholder="起始日" />
          <Input type="date" v-model="endDate" class="w-36" placeholder="結束日" />
        </template>
        <Button @click="handleFilter" :disabled="isLoading">查詢</Button>
        <Button variant="outline" @click="status='all';setThisMonth()" :disabled="isLoading">清除</Button>
      </div>
      <div v-if="isLoading" class="text-center py-8 text-muted-foreground">載入中...</div>
      <div v-else-if="errorMsg" class="text-center py-8 text-destructive">{{ errorMsg }}</div>
      <div v-else-if="orders.length === 0" class="text-center py-8 text-muted-foreground">無訂單資料</div>
      <div v-else class="overflow-x-auto">
        <table class="min-w-full text-sm border">
          <thead>
            <tr class="bg-muted">
              <th class="px-3 py-2 text-left">訂單編號</th>
              <th class="px-3 py-2 text-left">方案名稱</th>
              <th class="px-3 py-2 text-right">金額</th>
              <th class="px-3 py-2 text-center">狀態</th>
              <th class="px-3 py-2 text-center">建立日期</th>
              <th class="px-3 py-2 text-center">起訖日</th>
              <th class="px-3 py-2 text-left">備註</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="order in orders" :key="order.id" class="border-b">
              <td class="px-3 py-2">{{ order.id }}</td>
              <td class="px-3 py-2">{{ order.planName }}</td>
              <td class="px-3 py-2 text-right">{{ order.amount.toLocaleString() }}</td>
              <td class="px-3 py-2 text-center">
                <span v-if="order.status==='PENDING'" class="text-yellow-600">待處理</span>
                <span v-else-if="order.status==='COMPLETED'" class="text-green-600">已完成</span>
                <span v-else-if="order.status==='CANCELLED'" class="text-gray-400">已取消</span>
              </td>
              <td class="px-3 py-2 text-center">{{ order.createdAt ? format(new Date(order.createdAt), 'yyyy-MM-dd') : '-' }}</td>
              <td class="px-3 py-2 text-center">{{ order.startDate }} ~ {{ order.endDate || '-' }}</td>
              <td class="px-3 py-2">{{ order.remarks || '-' }}</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="flex justify-end mt-4">
        <Button variant="outline" @click="handleClose">關閉</Button>
      </div>
    </DialogContent>
  </Dialog>
</template> 