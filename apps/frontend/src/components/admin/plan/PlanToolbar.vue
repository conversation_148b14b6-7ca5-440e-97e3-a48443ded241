<!-- 方案工具列元件 -->
<script setup lang="ts">
import { Search, Download, Plus } from 'lucide-vue-next'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent } from '@/components/ui/card'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'

interface Props {
  searchQuery: string
  selectedPriceRange: { label: string; min: number; max: number } | null
  selectedSort: string
  priceRanges: { label: string; min: number; max: number }[]
  sortOptions: { label: string; value: string }[]
}

interface Emits {
  (e: 'update:searchQuery', value: string): void
  (e: 'update:selectedPriceRange', value: { label: string; min: number; max: number } | null): void
  (e: 'update:selectedSort', value: string): void
  (e: 'export'): void
  (e: 'create'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()
</script>

<template>
  <Card>
    <CardContent class="p-4">
      <div class="flex flex-col sm:flex-row items-center justify-between gap-4">
        <div class="flex flex-wrap items-center gap-2 flex-1">
          <div class="relative w-full sm:w-auto sm:min-w-[300px]">
            <Search class="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              :value="searchQuery"
              @input="(e: Event) => emit('update:searchQuery', (e.target as HTMLInputElement).value)"
              placeholder="搜尋方案..."
              class="pl-8 w-full"
            />
          </div>

          <Select
            :model-value="selectedPriceRange"
            @update:model-value="(value: any) => emit('update:selectedPriceRange', value)"
          >
            <SelectTrigger class="w-full sm:w-[130px]">
              <SelectValue placeholder="價格區間" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem
                v-for="range in priceRanges"
                :key="range.label"
                :value="range"
              >
                {{ range.label }}
              </SelectItem>
            </SelectContent>
          </Select>

          <Select
            :model-value="selectedSort"
            @update:model-value="(value: any) => emit('update:selectedSort', value)"
          >
            <SelectTrigger class="w-full sm:w-[130px]">
              <SelectValue placeholder="排序方式" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem
                v-for="option in sortOptions"
                :key="option.value"
                :value="option.value"
              >
                {{ option.label }}
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div class="flex items-center space-x-2 w-full sm:w-auto">
          <Button variant="outline" class="w-full sm:w-auto" @click="emit('export')">
            <Download class="h-4 w-4 mr-2" />
            導出
          </Button>
          <Button class="w-full sm:w-auto" @click="emit('create')">
            <Plus class="h-4 w-4 mr-2" />
            新增方案
          </Button>
        </div>
      </div>
    </CardContent>
  </Card>
</template> 