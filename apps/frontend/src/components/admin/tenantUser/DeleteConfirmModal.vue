<template>
  <Dialog :open="isOpen" @update:open="$emit('update:isOpen', $event)">
    <DialogContent>
      <DialogHeader>
        <DialogTitle>確認刪除</DialogTitle>
        <DialogDescription>
          您確定要刪除此使用者帳號嗎？此操作無法復原。
        </DialogDescription>
      </DialogHeader>
      
      <div class="space-y-4">
        <div class="p-4 border rounded-lg bg-muted">
          <div class="space-y-2">
            <div class="flex items-center space-x-2">
              <User class="h-4 w-4 text-muted-foreground" />
              <span class="font-medium">{{ user?.name }}</span>
            </div>
            <div class="flex items-center space-x-2">
              <Mail class="h-4 w-4 text-muted-foreground" />
              <span class="text-sm text-muted-foreground">{{ user?.email }}</span>
            </div>
            <div class="flex items-center space-x-2">
              <Building2 class="h-4 w-4 text-muted-foreground" />
              <span class="text-sm text-muted-foreground">{{ user?.tenant?.name || "—" }}</span>
            </div>
          </div>
        </div>
      </div>

      <DialogFooter>
        <Button
          type="button"
          variant="outline"
          :disabled="isDeleting"
          @click="$emit('update:isOpen', false)"
        >
          取消
        </Button>
        <Button
          type="button"
          variant="destructive"
          :disabled="isDeleting"
          @click="handleConfirm"
        >
          <Loader2 v-if="isDeleting" class="mr-2 h-4 w-4 animate-spin" />
          {{ isDeleting ? '刪除中...' : '確認刪除' }}
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { User, Mail, Building2, Loader2 } from 'lucide-vue-next'
import type { ITenantUser } from '@/types/models/tenant-user.model'

interface Props {
  isOpen: boolean
  user: ITenantUser | null
}

interface Emits {
  (e: 'update:isOpen', value: boolean): void
  (e: 'confirm'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const isDeleting = ref(false)

const handleConfirm = async () => {
  try {
    isDeleting.value = true
    emit('confirm')
  } finally {
    isDeleting.value = false
  }
}
</script> 