<template>
  <Dialog v-model:open="dialogData.isOpen">
    <DialogContent class="sm:max-w-md flex flex-col py-4">
      <DialogHeader>
        <DialogTitle>{{ dialogData.mode === 'create' ? '建立新角色' : '編輯角色' }}</DialogTitle>
        <DialogDescription>
          {{ dialogData.mode === 'create' ? '定義一個新的系統角色' : '修改角色基本資訊' }}
        </DialogDescription>
      </DialogHeader>
      <div class="flex-1 space-y-5 px-1 py-2 overflow-y-auto">
        <!-- 左側：基本資料 -->
        <div class="space-y-4">
          <div class="space-y-2">
            <label for="roleName" class="text-sm font-medium">系統識別碼</label>
            <Input 
              id="roleName" 
              v-model="localRole.name" 
              placeholder="如：PROJECT_MANAGER" 
              :disabled="dialogData.mode === 'edit' && isSystemRole"
              class="w-full"
            />
          </div>
          <div class="space-y-2">
            <label for="roleDisplayName" class="text-sm font-medium">顯示名稱</label>
            <Input id="roleDisplayName" v-model="localRole.displayName" placeholder="如：專案經理" class="w-full" />
          </div>
          <div class="space-y-2">
            <label for="roleDescription" class="text-sm font-medium">角色描述</label>
            <Input id="roleDescription" v-model="localRole.description" placeholder="描述此角色的主要職責" class="w-full" />
          </div>
          <div class="space-y-2">
            <label for="roleScope" class="text-sm font-medium">角色區域</label>
            <Select v-model="localRole.scope">
              <SelectTrigger id="roleScope">
                <SelectValue placeholder="選擇角色區域" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem :value="RoleScope.SYSTEM">系統管理</SelectItem>
                <SelectItem :value="RoleScope.TENANT">租戶管理</SelectItem>
                <SelectItem :value="RoleScope.WORKSPACE">工作區域</SelectItem>
              </SelectContent>
            </Select>
            <p class="text-xs text-muted-foreground">設定此角色適用的系統區域</p>
          </div>
        </div>
      </div>
      <DialogFooter class="bg-white dark:bg-zinc-900 z-10 mt-auto pt-4 border-t">
        <Button variant="outline" @click="close" :disabled="isLoading">取消</Button>
        <Button :disabled="isLoading" @click="save">
          <span v-if="isLoading" class="mr-2">
            <svg class="animate-spin h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          </span>
          {{ dialogData.mode === 'create' ? '建立角色' : '儲存變更' }}
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { computed, toRef, watch, ref, reactive } from 'vue'
import type { RoleDialogData, RoleBasicInfo } from '@/types/models/role.model'
import { RoleScope } from '@/types/models/role.model'

// UI 元件
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { 
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter 
} from '@/components/ui/dialog'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue 
} from '@/components/ui/select'

const props = defineProps<{
  dialogData: RoleDialogData
  isLoading: boolean
  isSystemRole?: boolean
}>()

const emit = defineEmits<{
  (e: 'update:dialogData', value: RoleDialogData): void
  (e: 'save', value: RoleBasicInfo): void
}>()

const localRole = reactive<RoleBasicInfo>({ 
  id: props.dialogData.role.id || '',
  name: props.dialogData.role.name || '',
  displayName: props.dialogData.role.displayName || '',
  description: props.dialogData.role.description || '',
  scope: props.dialogData.role.scope || RoleScope.WORKSPACE,
  isSystem: (props.dialogData.role as any)?.isSystem || false,
})

watch(
  () => props.dialogData.role,
  (val) => {
    localRole.id = val.id || '';
    localRole.name = val.name || '';
    localRole.displayName = val.displayName || '';
    localRole.description = val.description || '';
    localRole.scope = val.scope || RoleScope.WORKSPACE;
    localRole.isSystem = (val as any)?.isSystem || false;
  },
  { immediate: true, deep: true }
)

const close = () => {
  emit('update:dialogData', {
    ...props.dialogData,
    isOpen: false
  })
}

const save = () => {
  const { ...roleBasicData } = localRole;
  emit('save', roleBasicData)
}
</script>