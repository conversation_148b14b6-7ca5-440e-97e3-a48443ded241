<template>
  <div class="rounded-md border">
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>角色名稱</TableHead>
          <TableHead>描述</TableHead>
          <TableHead>區域</TableHead>
          <TableHead>使用者數</TableHead>
          <TableHead>建立時間</TableHead>
          <TableHead class="text-right">操作</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        <TableRow v-for="role in roles" :key="role.id">
          <TableCell>
            <div class="font-medium">{{ role.displayName }}</div>
            <div class="text-xs text-muted-foreground">{{ role.name }}</div>
          </TableCell>
          <TableCell>{{ role.description }}</TableCell>
          <TableCell>
                    <Badge :variant="role.scope === RoleScope.SYSTEM ? 'default' : 'secondary'">
          {{ role.scope === RoleScope.SYSTEM ? '系統管理' : role.scope === RoleScope.TENANT ? '租戶管理' : '工作區域' }}
            </Badge>
          </TableCell>
          <TableCell>
            <div class="flex items-center gap-2">
              <Users class="h-4 w-4 text-muted-foreground" />
              {{ role.userCount }}
            </div>
          </TableCell>
          <TableCell>{{ new Date(role.createdAt).toLocaleDateString() }}</TableCell>
          <TableCell class="text-right">
            <div class="flex items-center justify-end gap-1">
              <Badge v-if="role.isSystem" variant="outline">系統</Badge>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger as-child>
                    <Button variant="ghost" size="icon" @click="$emit('set-permissions', role.id)">
                      <Settings class="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>設定權限</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger as-child>
                    <Button variant="ghost" size="icon" @click="$emit('edit', role)">
                      <Edit class="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>編輯角色</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              <TooltipProvider v-if="!role.isSystem">
                <Tooltip>
                  <TooltipTrigger as-child>
                    <Button 
                      variant="ghost" 
                      size="icon" 
                      class="text-destructive hover:text-destructive" 
                      @click="$emit('delete', role.id)"
                    >
                      <Trash class="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>刪除角色</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </TableCell>
        </TableRow>
        <TableRow v-if="roles.length === 0">
          <TableCell colspan="6" class="h-24 text-center">
            <div class="flex flex-col items-center justify-center text-muted-foreground">
              <Key class="h-8 w-8 mb-2" />
              <p>無符合條件的角色</p>
            </div>
          </TableCell>
        </TableRow>
      </TableBody>
    </Table>
  </div>
</template>

<script setup lang="ts">
import type { Role } from '@/types/models/role.model'
import { RoleScope } from '@/types/models/role.model'

// UI 組件
import { Table } from '@/components/ui/table'
import { TableBody } from '@/components/ui/table'
import { TableCell } from '@/components/ui/table'
import { TableHead } from '@/components/ui/table'
import { TableHeader } from '@/components/ui/table'
import { TableRow } from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'

// Lucide 圖標
import { Edit, Trash, Users, Key, Settings } from 'lucide-vue-next'

defineProps<{
  roles: Role[]
}>()

defineEmits<{
  (e: 'edit', role: Role): void
  (e: 'delete', id: string): void
  (e: 'set-permissions', roleId: string): void
}>()
</script>