/* 自定義工具類 */
@layer utilities {
  /* 文字省略 */
  .text-ellipsis-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .text-ellipsis-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* 漸變背景 */
  .bg-gradient-primary {
    background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary) / 0.8) 100%);
  }

  .bg-gradient-secondary {
    background: linear-gradient(135deg, hsl(var(--secondary)) 0%, hsl(var(--muted)) 100%);
  }

  /* 玻璃效果 */
  .glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .dark .glass {
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  /* 陰影變體 */
  .shadow-soft {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04), 0 1px 2px rgba(0, 0, 0, 0.06);
  }

  .shadow-medium {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08), 0 2px 4px rgba(0, 0, 0, 0.06);
  }

  .shadow-strong {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12), 0 4px 8px rgba(0, 0, 0, 0.08);
  }

  /* 深色模式陰影 */
  .dark .shadow-soft {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3), 0 1px 2px rgba(0, 0, 0, 0.2);
  }

  .dark .shadow-medium {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4), 0 2px 4px rgba(0, 0, 0, 0.3);
  }

  .dark .shadow-strong {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5), 0 4px 8px rgba(0, 0, 0, 0.4);
  }

  /* 動畫工具類 */
  .animate-fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-slide-down {
    animation: slideDown 0.3s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.2s ease-out;
  }

  /* 響應式容器 */
  .container-fluid {
    width: 100%;
    padding-left: 1rem;
    padding-right: 1rem;
    margin-left: auto;
    margin-right: auto;
  }

  @media (min-width: 640px) {
    .container-fluid {
      padding-left: 1.5rem;
      padding-right: 1.5rem;
    }
  }

  @media (min-width: 1024px) {
    .container-fluid {
      padding-left: 2rem;
      padding-right: 2rem;
    }
  }

  /* 狀態指示器 */
  .status-dot {
    @apply inline-block w-2 h-2 rounded-full;
  }

  .status-dot-success {
    @apply bg-green-500;
  }

  .status-dot-warning {
    @apply bg-yellow-500;
  }

  .status-dot-error {
    @apply bg-red-500;
  }

  .status-dot-info {
    @apply bg-blue-500;
  }

  .status-dot-pending {
    @apply bg-orange-500;
  }

  .status-dot-inactive {
    @apply bg-gray-400;
  }

  /* 互動效果 */
  .interactive {
    @apply transition-all duration-200 ease-in-out;
  }

  .interactive:hover {
    @apply scale-[1.02] shadow-medium;
  }

  .interactive:active {
    @apply scale-[0.98];
  }

  /* 卡片樣式 */
  .card-elevated {
    @apply bg-card border border-border rounded-lg shadow-soft;
  }

  .card-elevated:hover {
    @apply shadow-medium;
  }

  /* 表單樣式 */
  .form-section {
    @apply space-y-4 p-6 bg-card border border-border rounded-lg;
  }

  .form-group {
    @apply space-y-2;
  }

  .form-label {
    @apply text-sm font-medium text-foreground;
  }

  .form-description {
    @apply text-sm text-muted-foreground;
  }

  .form-error {
    @apply text-sm text-destructive;
  }
}

/* 關鍵幀動畫 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
} 