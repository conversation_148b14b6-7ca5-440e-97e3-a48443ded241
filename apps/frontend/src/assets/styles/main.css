@import './variables.css';
@import './fullcalendar.css';
@import './utilities.css';
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* 主要顏色 - Emerald */
    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;
    --primary-content: 0 0% 98%;

    /* 次要顏色 - Zinc */
    --secondary: 240 4.8% 95.9%;
    --secondary-focus: 240 4% 87%;
    --secondary-content: 240 5% 20%;

    /* 強調顏色 - Sky */
    --accent: 240 4.8% 95.9%;
    --accent-focus: 220 20% 55%;
    --accent-content: 220 1% 98%;

    /* 中性色調 - Zinc */
    --neutral: 240 0.6% 27%;
    --neutral-focus: 240 0.6% 21%;
    --neutral-content: 240 1% 98%;

    /* 基礎色調 */
    --base-100: 0 0% 100%;
    --base-200: 240 2% 98%;
    --base-300: 240 2% 96%;
    --base-content: 240 0.6% 27%;

    /* 功能色調 */
    --info: 220 20% 65%;
    --info-content: 220 1% 98%;
    --success: 150 25% 65%;
    --success-content: 150 1% 98%;
    --warning: 80 25% 80%;
    --warning-content: 80 5% 20%;
    --error: 30 30% 65%;
    --error-content: 30 1% 98%;

    /* 背景與前景 */
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;

    /* 卡片 */
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;

    /* 彈出層 */
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;

    /* 靜音色 */
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;

    /* 破壞性操作 */
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    /* 邊框與輸入 */
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 10% 3.9%;

    /* 圓角 */
    --radius: 0.5rem;

    /* 圖表顏色 */
    --chart-1: 12 76% 61%; /* Emerald */
    --chart-2: 173 58% 39%; /* Sky */
    --chart-3: 197 37% 24%; /* Warning */
    --chart-4: 43 74% 66%; /* Success */
    --chart-5: 27 87% 67%; /* Error */ --sidebar-background: 0 0% 98%; --sidebar-foreground: 240 5.3% 26.1%; --sidebar-primary: 240 5.9% 10%; --sidebar-primary-foreground: 0 0% 98%; --sidebar-accent: 240 4.8% 95.9%; --sidebar-accent-foreground: 240 5.9% 10%; --sidebar-border: 220 13% 91%; --sidebar-ring: 217.2 91.2% 59.8%; --secondary-foreground: 240 5.9% 10%; --accent-foreground: 240 5.9% 10%;
  }

  .dark {
    /* 主要顏色 - Emerald */
    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;
    --primary-content: 0 0% 98%;

    /* 次要顏色 - Zinc */
    --secondary: 240 3.7% 15.9%;
    --secondary-focus: 240 4% 15.9%;
    --secondary-content: 240 5% 98%;

    /* 強調顏色 - Sky */
    --accent: 240 3.7% 15.9%;
    --accent-focus: 220 20% 55%;
    --accent-content: 220 1% 98%;

    /* 中性色調 - Zinc */
    --neutral: 240 0.6% 27%;
    --neutral-focus: 240 0.6% 21%;
    --neutral-content: 240 1% 98%;

    /* 基礎色調 */
    --base-100: 240 10% 3.9%;
    --base-200: 240 3% 15.9%;
    --base-300: 240 3% 15.9%;
    --base-content: 240 1% 98%;

    /* 功能色調 */
    --info: 220 20% 65%;
    --info-content: 220 1% 98%;
    --success: 150 25% 65%;
    --success-content: 150 1% 98%;
    --warning: 80 25% 80%;
    --warning-content: 80 5% 20%;
    --error: 30 30% 65%;
    --error-content: 30 1% 98%;

    /* 背景與前景 */
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;

    /* 卡片 */
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;

    /* 彈出層 */
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;

    /* 靜音色 */
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;

    /* 破壞性操作 */
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    /* 邊框與輸入 */
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;

    /* 圖表顏色 */
    --chart-1: 220 70% 50%; /* Emerald */
    --chart-2: 160 60% 45%; /* Sky */
    --chart-3: 30 80% 55%; /* Warning */
    --chart-4: 280 65% 60%; /* Success */
    --chart-5: 340 75% 55%; /* Error */ --sidebar-background: 240 5.9% 10%; --sidebar-foreground: 240 4.8% 95.9%; --sidebar-primary: 224.3 76.3% 48%; --sidebar-primary-foreground: 0 0% 100%; --sidebar-accent: 240 3.7% 15.9%; --sidebar-accent-foreground: 240 4.8% 95.9%; --sidebar-border: 240 3.7% 15.9%; --sidebar-ring: 217.2 91.2% 59.8%; --secondary-foreground: 0 0% 98%; --accent-foreground: 0 0% 98%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }

  /* macOS 風格捲軸 */
  ::-webkit-scrollbar {
    @apply w-[8px] h-[8px];
  }

  /* 隱藏捲軸軌道 */
  ::-webkit-scrollbar-track {
    @apply bg-transparent;
  }

  /* 捲軸滑塊樣式 */
  ::-webkit-scrollbar-thumb {
    @apply bg-zinc-400/40 hover:bg-zinc-400/60 active:bg-zinc-400/80
           dark:bg-zinc-500/40 dark:hover:bg-zinc-500/60 dark:active:bg-zinc-500/80
           rounded-full transition-colors;
  }

  /* 初始隱藏捲軸 */
  ::-webkit-scrollbar-thumb {
    visibility: hidden;
  }

  /* 滾動時顯示捲軸 */
  *:hover::-webkit-scrollbar-thumb,
  *:focus::-webkit-scrollbar-thumb,
  *:focus-within::-webkit-scrollbar-thumb {
    visibility: visible;
  }

  /* Firefox 捲軸樣式 */
  * {
    scrollbar-width: thin;
    scrollbar-color: transparent transparent;
  }

  *:hover,
  *:focus,
  *:focus-within {
    scrollbar-color: rgba(161, 161, 170, 0.4) transparent;
  }

  .dark *:hover,
  .dark *:focus,
  .dark *:focus-within {
    scrollbar-color: rgba(113, 113, 122, 0.4) transparent;
  }
}