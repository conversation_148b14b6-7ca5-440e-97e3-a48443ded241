import { computed, ref, type Ref } from 'vue';
import type { <PERSON><PERSON><PERSON> } from '@/types/models/ai.model';
import { useNotification } from '@/composables/shared/useNotification';
import { useRouter } from 'vue-router';

/**
 * AI 功能驗證狀態檢查 Composable
 * 
 * 提供統一的 AI 金鑰有效性檢查、錯誤處理和用戶引導機制
 * 用於確保所有 AI 相關功能在有效金鑰存在時才能使用
 */
export function useAIValidation(
  availableKeys?: Ref<AiKey[]>,
  selectedKeyId?: Ref<string | undefined>
) {
  const notification = useNotification();
  const router = useRouter();

  /**
   * 檢查是否有任何有效的 API 金鑰
   */
  const hasAnyValidKey = computed(() => {
    if (!availableKeys?.value) return false;
    return availableKeys.value.some(key => 
      key.is_enabled && 
      key.api_key && 
      key.api_key.trim() !== '' &&
      !key.api_key.includes('sk-test-') // 排除測試金鑰
      // 注意：'********' 表示有效的遮罩金鑰，應該被認為是有效的
    );
  });

  /**
   * 檢查當前選中的金鑰是否有效
   */
  const hasValidSelectedKey = computed(() => {
    if (!availableKeys?.value || !selectedKeyId?.value) return false;
    const selectedKey = availableKeys.value.find(key => key.id === selectedKeyId.value);
    return !!(selectedKey?.is_enabled && 
              selectedKey?.api_key && 
              selectedKey.api_key.trim() !== '' &&
              !selectedKey.api_key.includes('sk-test-')); // 排除測試金鑰
  });

  /**
   * 獲取有效金鑰數量
   */
  const validKeyCount = computed(() => {
    if (!availableKeys?.value) return 0;
    return availableKeys.value.filter(key => 
      key.is_enabled && 
      key.api_key && 
      key.api_key.trim() !== '' &&
      !key.api_key.includes('sk-test-') // 排除測試金鑰
    ).length;
  });

  /**
   * 獲取可用的供應商類型列表
   */
  const availableProviders = computed(() => {
    if (!availableKeys?.value) return [];
    const providers = new Set(
      availableKeys.value
        .filter(key => key.is_enabled && 
                      key.api_key && 
                      key.api_key.trim() !== '' &&
                      !key.api_key.includes('sk-test-')) // 排除測試金鑰
        .map(key => key.provider)
    );
    return Array.from(providers);
  });

  /**
   * 顯示金鑰設定提醒通知
   */
  const showKeySetupNotification = (customMessage?: string) => {
    const message = customMessage || '請先設定有效的 AI API 金鑰才能使用此功能';
    
    notification.toast.warning(message, {
      action: {
        label: '前往設定',
        onClick: () => navigateToKeySettings()
      },
      duration: 8000, // 延長顯示時間
    });
  };

  /**
   * 顯示金鑰錯誤通知
   */
  const showKeyErrorNotification = (errorMessage?: string) => {
    const message = errorMessage || 'API 金鑰無效或已過期，請檢查設定';
    
    notification.toast.error(message, {
      action: {
        label: '檢查金鑰',
        onClick: () => navigateToKeySettings()
      },
      duration: 10000, // 錯誤訊息顯示更久
    });
  };

  /**
   * 導航到金鑰設定頁面
   */
  const navigateToKeySettings = () => {
    router.push({
      name: 'admin-ai-settings',
      query: { tab: 'keys' }
    });
  };

  /**
   * 處理 API 錯誤並顯示相應的用戶引導
   */
  const handleAPIError = (error: any, context?: { operation?: string; provider?: string }) => {
    const status = error?.response?.status;
    const message = error?.message || '';
    const responseData = error?.response?.data;
    
    // API 認證錯誤 (401)
    if (status === 401 || message.includes('API key') || message.includes('authentication')) {
      let errorDetail = '';
      
      // 檢查具體的錯誤類型
      if (message.includes('sk-test-') || responseData?.error?.message?.includes('sk-test-')) {
        errorDetail = 'OpenAI 測試金鑰無法使用，請使用正式的 API 金鑰';
      } else if (message.includes('x-api-key') || responseData?.error?.type === 'authentication_error') {
        errorDetail = 'Anthropic API 金鑰驗證失敗，請檢查金鑰格式是否正確';
      } else if (responseData?.error?.code === 'invalid_api_key') {
        errorDetail = 'API 金鑰無效或已過期，請重新設定';
      } else {
        errorDetail = 'API 金鑰驗證失敗，請檢查金鑰設定';
      }
      
      // 顯示詳細的錯誤通知
      notification.toast.error(`${errorDetail}`, {
        action: {
          label: '前往設定',
          onClick: () => navigateToKeySettings()
        },
        duration: 8000 // 延長顯示時間以便用戶閱讀
      });
      
      return { handled: true, errorType: 'invalid-key', errorDetail, userMessage: errorDetail };
    }
    
    // 配額/限制錯誤 (429)
    if (status === 429 || message.includes('quota') || message.includes('rate limit')) {
      const quotaMessage = responseData?.error?.message || '使用額度已達上限';
      
      notification.toast.error(`API ${quotaMessage}`, {
        action: {
          label: '檢查帳戶',
          onClick: () => {
            // 可以導向到供應商的帳戶頁面或我們的配額管理頁面
            window.open(context?.provider === 'openai' 
              ? 'https://platform.openai.com/usage' 
              : 'https://console.anthropic.com/settings/usage', '_blank');
          }
        },
        duration: 10000
      });
      
      return { handled: true, errorType: 'quota-exceeded', errorDetail: quotaMessage, userMessage: `API ${quotaMessage}` };
    }
    
    // 網路或伺服器錯誤
    if (status >= 500 || message.includes('network') || message.includes('timeout')) {
      const networkErrorMessage = '伺服器連線錯誤，請稍後再試';
      notification.toast.error(networkErrorMessage, {
        duration: 5000
      });
      
      return { handled: true, errorType: 'network-error', errorDetail: networkErrorMessage, userMessage: networkErrorMessage };
    }
    
    // 其他 4xx 錯誤
    if (status >= 400 && status < 500) {
      // 優先使用後端返回的 message，再嘗試其他可能的錯誤訊息欄位
      const clientErrorMessage = responseData?.message || 
                                responseData?.error?.message || 
                                `請求錯誤 (${status})`;
      
      notification.toast.error(clientErrorMessage, {
        duration: 6000
      });
      
      return { handled: true, errorType: 'client-error', errorDetail: clientErrorMessage, userMessage: clientErrorMessage };
    }

    return { handled: false, errorType: 'unknown', errorDetail: message, userMessage: message }; // 表示未處理此錯誤，由調用方處理
  };

  /**
   * 驗證 AI 功能前置條件
   * @param requireSelectedKey 是否需要特定的選中金鑰
   * @param showNotificationOnFail 驗證失敗時是否顯示通知
   * @returns 驗證是否通過
   */
  const validateAIPrerequisites = (
    requireSelectedKey = false,
    showNotificationOnFail = true
  ): boolean => {
    // 檢查是否有任何可用金鑰
    if (!hasAnyValidKey.value) {
      if (showNotificationOnFail) {
        showKeySetupNotification('目前沒有設定任何有效的 AI API 金鑰');
      }
      return false;
    }

    // 如果需要特定選中的金鑰
    if (requireSelectedKey && !hasValidSelectedKey.value) {
      if (showNotificationOnFail) {
        showKeySetupNotification('請選擇一個有效的 AI API 金鑰');
      }
      return false;
    }

    return true;
  };

  /**
   * 檢查特定供應商是否有可用金鑰
   */
  const hasValidKeyForProvider = (provider: string): boolean => {
    if (!availableKeys?.value) return false;
    return availableKeys.value.some(
      key => key.provider === provider && key.is_enabled && key.api_key
    );
  };

  /**
   * 獲取 AI 功能可用性狀態摘要
   */
  const getAIAvailabilityStatus = () => {
    return {
      hasAnyKey: hasAnyValidKey.value,
      hasSelectedKey: hasValidSelectedKey.value,
      keyCount: validKeyCount.value,
      providers: availableProviders.value,
      isReady: hasAnyValidKey.value
    };
  };

  /**
   * 檢查機器人金鑰是否有效（不顯示通知的版本）
   */
  const isBotKeyValid = (botData: any): boolean => {
    if (!botData.key_id || !availableKeys?.value) return false;
    
    const selectedKey = availableKeys.value.find(key => key.id === botData.key_id);
    if (!selectedKey) return false;

    return selectedKey.is_enabled && 
           selectedKey.api_key && 
           selectedKey.api_key.trim() !== '' &&
           !selectedKey.api_key.includes('sk-test-');
  };

  /**
   * 驗證機器人保存前置條件 (新增)
   * 確保機器人有選擇有效的金鑰才能保存
   */
  const validateBotSavePrerequisites = (botData: any): boolean => {
    if (!botData.key_id) {
      showKeySetupNotification('請為此機器人選擇一個有效的 API 金鑰');
      return false;
    }

    // 檢查選中的金鑰是否在可用金鑰列表中且有效
    if (!availableKeys?.value) {
      showKeyErrorNotification('無法載入 API 金鑰列表');
      return false;
    }

    const selectedKey = availableKeys.value.find(key => key.id === botData.key_id);
    if (!selectedKey) {
      showKeyErrorNotification('選擇的 API 金鑰不存在，請重新選擇');
      return false;
    }

    // 檢查金鑰是否有效（排除測試金鑰）
    const isKeyValid = selectedKey.is_enabled && 
                      selectedKey.api_key && 
                      selectedKey.api_key.trim() !== '' &&
                      !selectedKey.api_key.includes('sk-test-');

    if (!isKeyValid) {
      showKeyErrorNotification('選擇的 API 金鑰無效，請重新選擇');
      return false;
    }

    return true;
  };

  /**
   * 驗證對話測試前置條件 (新增)
   * 確保機器人有有效金鑰才能進行對話測試
   */
  const validateChatTestPrerequisites = (botData: any): boolean => {
    if (!botData.key_id) {
      showKeySetupNotification('此機器人尚未配置 API 金鑰，無法進行對話測試');
      return false;
    }

    // 檢查選中的金鑰是否在可用金鑰列表中且有效
    if (!availableKeys?.value) {
      showKeyErrorNotification('無法載入 API 金鑰列表');
      return false;
    }

    const selectedKey = availableKeys.value.find(key => key.id === botData.key_id);
    if (!selectedKey) {
      showKeyErrorNotification('機器人配置的 API 金鑰不存在，請重新配置');
      return false;
    }

    // 檢查金鑰是否有效（排除測試金鑰）
    const isKeyValid = selectedKey.is_enabled && 
                      selectedKey.api_key && 
                      selectedKey.api_key.trim() !== '' &&
                      !selectedKey.api_key.includes('sk-test-');

    if (!isKeyValid) {
      showKeyErrorNotification('機器人配置的 API 金鑰無效，無法進行對話測試');
      return false;
    }

    return true;
  };

  /**
   * 驗證提示詞優化前置條件 (新增)
   * 確保有有效金鑰才能使用提示詞優化功能
   */
  const validatePromptOptimizationPrerequisites = (providerType?: string): boolean => {
    if (!validateAIPrerequisites(false, false)) {
      showKeySetupNotification('請先設定 API 金鑰才能使用提示詞優化功能');
      return false;
    }

    if (providerType && !hasValidKeyForProvider(providerType)) {
      showKeySetupNotification(`需要 ${providerType} 供應商的 API 金鑰才能使用提示詞優化功能`);
      return false;
    }

    return true;
  };

  /**
   * 獲取針對特定操作的金鑰狀態檢查結果 (新增)
   */
  const getOperationReadiness = (operation: 'save' | 'test' | 'optimize', context?: any) => {
    switch (operation) {
      case 'save':
        const isValid = isBotKeyValid(context);
        return {
          isReady: isValid,
          message: isValid ? '金鑰配置完成' : (!context?.key_id ? '請選擇 API 金鑰' : '請為此機器人選擇一個有效的 API 金鑰')
        };
      case 'test':
        return {
          isReady: validateChatTestPrerequisites(context),
          message: hasAnyValidKey.value ? '可以開始測試' : '需要配置 API 金鑰'
        };
      case 'optimize':
        return {
          isReady: validatePromptOptimizationPrerequisites(context?.providerType),
          message: hasAnyValidKey.value ? '可以使用優化功能' : '需要配置 API 金鑰'
        };
      default:
        return {
          isReady: hasAnyValidKey.value,
          message: hasAnyValidKey.value ? '準備就緒' : '需要配置 API 金鑰'
        };
    }
  };

  return {
    // 狀態檢查
    hasAnyValidKey,
    hasValidSelectedKey,
    validKeyCount,
    availableProviders,
    
    // 功能檢查
    validateAIPrerequisites,
    hasValidKeyForProvider,
    getAIAvailabilityStatus,
    
    // 用戶引導
    showKeySetupNotification,
    showKeyErrorNotification,
    navigateToKeySettings,
    handleAPIError,

    // 特定操作驗證 (新增)
    validateBotSavePrerequisites,
    validateChatTestPrerequisites,
    validatePromptOptimizationPrerequisites,
    getOperationReadiness,
    isBotKeyValid,
  };
}

/**
 * 創建一個帶有預設金鑰列表的 AI 驗證實例
 * 適用於需要獨立驗證但沒有現成金鑰列表的場景
 */
export function useStandaloneAIValidation() {
  const availableKeys = ref<AiKey[]>([]);
  
  return {
    availableKeys,
    ...useAIValidation(availableKeys),
  };
}
