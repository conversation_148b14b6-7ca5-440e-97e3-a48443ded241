import { USER_ROLES } from '@/types/models/user.model';
import type { RouteLocationRaw } from 'vue-router';
import type { User } from '@horizai/auth';

/**
 * 根據使用者角色讀取預設重定向路徑
 */
export function getDefaultRedirectPath(role: string): RouteLocationRaw {
  switch (role) {
    case USER_ROLES.SUPER_ADMIN:
    case USER_ROLES.SYSTEM_ADMIN:
      return { name: 'admin-dashboard' };
    case USER_ROLES.TENANT_ADMIN:
      return { name: 'tenant-dashboard' };
    default:
      return { name: 'workspace-select' };
  }
}

/**
 * 根據使用者資訊和當前路徑決定重定向目標
 */
export function getRedirectTarget(
  user: User | null,
  currentPath: string,
  query?: { redirect?: string },
): RouteLocationRaw {
  // 如果有指定的重定向路徑，優先使用
  if (query?.redirect) {
    return { path: query.redirect };
  }

  // 如果使用者未登入，重定向到登入頁
  if (!user) {
    return {
      name: 'login',
      query: { redirect: currentPath },
    };
  }

  // 根據使用者角色決定重定向路徑
  return getDefaultRedirectPath(user.role);
}

/**
 * 處理未授權訪問的重定向
 */
export function handleUnauthorizedAccess(currentPath: string): RouteLocationRaw {
  return {
    name: 'login',
    query: {
      redirect: currentPath,
      reason: 'unauthorized',
    },
  };
}

/**
 * 處理權限不足的重定向
 */
export function handleForbiddenAccess(message?: string): RouteLocationRaw {
  return {
    name: 'forbidden',
    query: message ? { reason: message } : undefined,
  };
}

/**
 * 處理登出後的重定向
 */
export function handleLogoutRedirect(): RouteLocationRaw {
  // 清除所有可能的認證相關資料
  if (typeof window !== 'undefined') {
    try {
      // 清除 localStorage 和 sessionStorage
      const keysToRemove = [
        'auth.session.exists',
        'auth.user',
        'auth.token',
        'user',
        'auth_token',
        'access_token',
        'refresh_token',
        'remember_me',
        'auth.remember_me',
        'currentWorkspaceId',
        'selectedTenantId',
      ];

      keysToRemove.forEach((key) => {
        localStorage.removeItem(key);
        sessionStorage.removeItem(key);
      });

      // 清除所有可能的認證相關 cookies（前端能清除的部分）
      const cookiesToClear = ['auth_token', 'auth.token', 'access_token', 'refresh_token'];
      const paths = ['/', '/auth', '/api'];
      const domains = [window.location.hostname, `.${window.location.hostname}`];

      cookiesToClear.forEach(cookieName => {
        paths.forEach(path => {
          document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=${path}`;
          domains.forEach(domain => {
            document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=${path}; domain=${domain}`;
          });
        });
      });

      // 清除 sessionStorage
      sessionStorage.clear();

      // 嘗試清除所有可能的 Cookie（雖然 HTTP-only Cookie 無法清除）
      const cookiesToClear = ['auth_token', 'auth.token', 'access_token', 'refresh_token'];
      cookiesToClear.forEach((cookieName) => {
        document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/`;
        document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/; domain=${window.location.hostname}`;
      });
    } catch (error) {
      console.warn('清除認證資料時發生錯誤:', error);
    }
  }

  return { name: 'login' };
}

/**
 * 處理會話過期的重定向
 */
export function handleSessionExpired(currentPath: string): RouteLocationRaw {
  return {
    name: 'login',
    query: {
      redirect: currentPath,
      reason: 'session_expired',
    },
  };
}
