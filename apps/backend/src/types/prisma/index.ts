export * from '@prisma/client'

export interface User {
  id: string;
  email: string;
  password: string;
  name: string | null;
  role: 'SUPER_ADMIN' | 'SYSTEM_ADMIN' | 'TENANT_ADMIN' | 'TENANT_USER';
  status: string;
  tenantId: string;
  avatar: string | null;
  phone: string | null;
  title: string | null;
  company: string | null;
  department: string | null;
  location: string | null;
  bio: string | null;
  socialLinks?: any;
  preferences?: any;
  lineUserId: string | null;
  lineConnected: boolean;
  lastLogoutAt: Date | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface Tenant {
  id: string;
  name: string;
  domain?: string;
  adminName?: string;
  adminEmail?: string;
  companySize?: string;
  industry?: string;
  planId?: string;
  maxUsers?: number;
  maxProjects?: number;
  maxStorage?: number;
  departments: string[];
  status: string;
  billingCycle?: string;
  nextBillingDate?: Date;
  paymentStatus?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Plan {
  id: string;
  name: string;
  description: string;
  price: number;
  billingCycle: string;
  features: any;
  maxUsers: number;
  maxProjects: number;
  maxStorage: number;
  isPopular: boolean;
  createdAt: Date;
  updatedAt: Date;
  monthlyAiCreditsLimit?: any; // Prisma schema: Decimal?，TS 端可用 any 或 string/number/Decimal
}

// Prisma 查詢輸入型別
export namespace Prisma {
  export interface usersWhereInput {
    AND?: usersWhereInput[];
    OR?: usersWhereInput[];
    NOT?: usersWhereInput[];
    id?: string | StringFilter;
    email?: string | StringFilter;
    tenantId?: string | StringFilter;
    role?: any | EnumFilter;
    status?: string | StringFilter;
    name?: string | StringNullableFilter;
    [key: string]: any;
  }

  export interface usersWhereUniqueInput {
    id?: string;
    email?: string;
    lineUserId?: string;
    [key: string]: any;
  }

  export interface usersOrderByWithRelationInput {
    id?: SortOrder;
    email?: SortOrder;
    name?: SortOrder;
    createdAt?: SortOrder;
    updatedAt?: SortOrder;
    [key: string]: any;
  }

  export interface usersCreateInput {
    id?: string;
    email: string;
    password: string;
    name?: string;
    role?: any;
    status?: string;
    tenantId: string;
    [key: string]: any;
  }

  export interface StringFilter {
    equals?: string;
    in?: string[];
    notIn?: string[];
    lt?: string;
    lte?: string;
    gt?: string;
    gte?: string;
    contains?: string;
    startsWith?: string;
    endsWith?: string;
    [key: string]: any;
  }

  export interface StringNullableFilter {
    equals?: string | null;
    in?: string[] | null;
    notIn?: string[] | null;
    lt?: string;
    lte?: string;
    gt?: string;
    gte?: string;
    contains?: string;
    startsWith?: string;
    endsWith?: string;
    [key: string]: any;
  }

  export interface EnumFilter {
    equals?: any;
    in?: any[];
    notIn?: any[];
    [key: string]: any;
  }

  export enum SortOrder {
    asc = 'asc',
    desc = 'desc',
  }
}
