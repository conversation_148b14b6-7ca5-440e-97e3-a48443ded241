import { PrismaClient, SystemUserRole } from "@prisma/client";

export interface ISystemUser {
  id: string;
  email: string;
  password: string;
  name: string | null;
  role: SystemUserRole;
  status: string;
  avatar: string | null;
  phone: string | null;
  mfaEnabled: boolean;
  mfaSecret: string | null;
  lastLoginAt: Date | null;
  lastLoginIp: string | null;
  passwordLastChangedAt: Date | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface ICreateSystemUser {
  email: string;
  password: string;
  name?: string;
  role?: SystemUserRole;
  status?: string;
  avatar?: string;
  phone?: string;
}

export interface IUpdateSystemUser {
  email?: string;
  name?: string;
  role?: SystemUserRole;
  status?: string;
  avatar?: string;
  phone?: string;
  mfaEnabled?: boolean;
}

export interface ISystemUserProfile {
  id: string;
  email: string;
  name: string | null;
  role: SystemUserRole;
  status: string;
  avatar: string | null;
  phone: string | null;
  mfaEnabled: boolean;
  lastLoginAt: Date | null;
  createdAt: Date;
  updatedAt: Date;
}
