import { PrismaC<PERSON>, Prisma, Tenant<PERSON>ser<PERSON><PERSON>, TenantUserStatus } from "@prisma/client";

// TenantUser - extends base User model with tenant-specific fields
export interface ITenantUser {
  id: string;
  email: string;
  // password is not directly exposed, handled by auth
  name: string | null;
  role: TenantUserRole;
  status: TenantUserStatus;
  avatar: string | null;
  phone: string | null;
  title: string | null;
  department: string | null;
  tenantId: string;
  invitedBy: string | null; // User ID of the inviter
  mfaEnabled: boolean;
  mfaSecret: string | null;
  lastLoginAt: Date | null;
  lastLoginIp: string | null;
  passwordLastChangedAt: Date | null;
  createdAt: Date;
  updatedAt: Date;
  leftCompanyAt: Date | null;
  leftCompanyReason: string | null;
  dataTransferStatus: string | null;
  dataTransferNote: string | null;
  // Relations (optional, depending on query includes)
  // tenant?: Tenant;
  // invitedByUser?: User;
  // settings?: Setting[];
}

export interface ICreateTenantUser {
  email: string;
  password: string; // password will be set if it's a new user creation flow through tenant invitation acceptance
  name?: string;
  role?: TenantUserRole;
  status?: TenantUserStatus;
  avatar?: string;
  phone?: string;
  title?: string;
  department?: string;
  tenantId: string;
  invitedBy?: string;
}

export interface IUpdateTenantUser {
  email?: string;
  name?: string;
  role?: TenantUserRole;
  status?: TenantUserStatus;
  avatar?: string;
  phone?: string;
  title?: string;
  department?: string;
  mfaEnabled?: boolean;
  leftCompanyAt?: Date | null;
  leftCompanyReason?: string | null;
  dataTransferStatus?: string | null;
  dataTransferNote?: string | null;
}

// Simplified profile for tenant user, excluding sensitive or internal fields
export interface ITenantUserProfile {
  id: string;
  email: string;
  name: string | null;
  role: TenantUserRole;
  status: TenantUserStatus;
  avatar: string | null;
  phone: string | null;
  title: string | null;
  department: string | null;
  tenantId: string;
  mfaEnabled: boolean;
  lastLoginAt: Date | null;
  createdAt: Date;
  updatedAt: Date;
}

// For tenant user invitation
export interface ITenantUserInvitation {
  email: string;
  name?: string;
  role?: TenantUserRole;
  tenantId: string;
  invitedBy: string;
}

// For searching/filtering tenant users
export interface ITenantUserFilters {
  status?: TenantUserStatus;
  role?: TenantUserRole;
  department?: string;
  mfaEnabled?: boolean;
  search?: string; // General search term for name, email, etc.
}

// For paginated responses
export interface PaginatedTenantUsers {
  users: ITenantUserProfile[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// For bulk operations
export interface BulkTenantUserOperationDto {
  userIds: string[];
  status?: TenantUserStatus; // Example: bulk activate/deactivate
  // Add other fields relevant for bulk updates if needed
}

export interface BulkTenantUserOperationResult {
  success: boolean;
  affectedCount: number;
  errors?: Array<{ userId: string; message: string }>;
}
