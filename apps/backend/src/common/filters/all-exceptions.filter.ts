import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  BadRequestException,
  Injectable,
} from "@nestjs/common";
import { Request, Response } from "express";
import { SystemLogService } from "../services/system-log.service";

function structuredLog({
  level,
  context,
  action,
  message,
  user,
  meta,
}: {
  level: string;
  context: string;
  action: string;
  message: string;
  user?: any;
  meta?: any;
}) {
  const log = {
    timestamp: new Date().toISOString(),
    level,
    context,
    action,
    message,
    ...(user ? { user } : {}),
    ...(meta ? { meta } : {}),
  };
  console.log(JSON.stringify(log));
}

@Injectable()
@Catch()
export class AllExceptionsFilter implements ExceptionFilter {
  constructor(private readonly systemLogService: SystemLogService) { }

  async catch(exception: any, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    let status = 500;
    let message = "Internal server error";
    let errors: any[] = [];
    let action = "error";
    let user = (request as any).user
      ? { id: (request as any).user.id, email: (request as any).user.email }
      : undefined;
    // 取得優先為 IPv4 的 IP
    let ip =
      (request.headers["x-forwarded-for"] as string)
        ?.split(",")
        .find((ip) => ip && ip.includes("."))
        ?.trim() ||
      (typeof request.ip === "string" && request.ip.includes(".")
        ? request.ip
        : undefined) ||
      request.ip;
    let meta = {
      ip,
      path: request.url,
      method: request.method,
      stack: exception.stack,
    };

    if (exception instanceof HttpException) {
      status = exception.getStatus();
      const res = exception.getResponse();

      if (typeof res === "string") {
        message = res;
      } else if (typeof res === "object") {
        // class-validator 錯誤格式
        if ((res as any).message && Array.isArray((res as any).message)) {
          message = "驗證失敗";
          errors = (res as any).message.map((msg: string) => {
            const match = msg.match(/^(.+) (must|should|is|cannot|has|does)/);
            return {
              field: match ? match[1] : undefined,
              message: msg,
            };
          });
          action = "validation";
        } else {
          message = (res as any).message || message;
          errors = (res as any).errors || [];
        }
      }
    } else if (exception instanceof Error) {
      message = exception.message;
    }

    // 結構化 log 輸出
    structuredLog({
      level: status >= 500 ? "error" : "warn",
      context: "AllExceptionsFilter",
      action,
      message,
      user,
      meta,
    });

    // 寫入 DB
    try {
      await this.systemLogService.logError({
        level: status >= 500 ? "error" : "warn",
        message: message,
        stack: exception.stack,
        path: request.url,
        method: request.method,
        userId: (request as any).user?.id,
        ip: request.ip,
      });
    } catch (e) {
      structuredLog({
        level: "error",
        context: "AllExceptionsFilter",
        action: "logError",
        message: `Failed to log error: ${e.message}`,
        meta: { originalError: message },
      });
    }

    const errorResponse = {
      statusCode: status,
      message,
      errors,
      timestamp: new Date().toISOString(),
      path: request.url,
    };

    response.status(status).json(errorResponse);
  }
}
