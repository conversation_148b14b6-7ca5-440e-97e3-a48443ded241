// 系統日誌服務，負責寫入 system_logs 資料表
import { Injectable } from "@nestjs/common";
import { PrismaService } from "../../modules/core/prisma/prisma.service";
import * as crypto from "crypto";

@Injectable()
export class SystemLogService {
  constructor(private readonly prisma: PrismaService) {}

  async logError(data: {
    level: string;
    message: string;
    stack?: string;
    path?: string;
    method?: string;
    userId?: string;
    ip?: string;
  }) {
    await this.prisma.system_logs.create({ 
      data: {
        id: crypto.randomUUID(),
        ...data
      }
    });
  }

  async logAudit(data: {
    message: string;
    userId?: string;
    ip?: string;
    tenantId?: string;
    path?: string;
    method?: string;
    targetResource?: string;
    targetResourceId?: string;
    action?: string;
    status?: string;
    errorMessage?: string;
    details?: any;
  }) {
    await this.prisma.system_logs.create({
      data: {
        id: crypto.randomUUID(),
        level: "AUDIT",
        ...data,
      },
    });
  }

  async findLogs(filters: {
    level?: string;
    userId?: string;
    action?: string;
    startDate?: string;
    endDate?: string;
    status?: string;
    searchQuery?: string;
    limit?: number;
    offset?: number;
  }) {
    const where: any = {};

    // 建構查詢條件
    if (filters.level && filters.level.trim()) {
      where.level = filters.level.trim();
    }

    if (filters.userId && filters.userId.trim()) {
      where.userId = { 
        contains: filters.userId.trim(),
        mode: 'insensitive' // 不區分大小寫
      };
    }

    if (filters.action && filters.action.trim()) {
      where.action = { 
        contains: filters.action.trim(),
        mode: 'insensitive' // 不區分大小寫
      };
    }

    if (filters.status && filters.status.trim()) {
      where.status = filters.status.trim();
    }

    if (filters.searchQuery && filters.searchQuery.trim()) {
      where.message = { 
        contains: filters.searchQuery.trim(),
        mode: 'insensitive' // 不區分大小寫
      };
    }

    // 處理日期範圍
    if (filters.startDate && filters.startDate.trim()) {
      const startDate = new Date(filters.startDate.trim());
      startDate.setHours(0, 0, 0, 0); // 設為當天開始
      
      where.createdAt = {
        ...where.createdAt,
        gte: startDate,
      };
    }

    if (filters.endDate && filters.endDate.trim()) {
      const endDate = new Date(filters.endDate.trim());
      endDate.setHours(23, 59, 59, 999); // 設為當天結束
      
      where.createdAt = {
        ...where.createdAt,
        lte: endDate,
      };
    }

    console.log('SystemLogService findLogs where條件:', JSON.stringify(where, null, 2));

    const logs = await this.prisma.system_logs.findMany({
      where,
      orderBy: { createdAt: 'desc' },
      take: filters.limit,
      skip: filters.offset,
    });

    return logs;
  }

  async countLogs(filters: {
    level?: string;
    userId?: string;
    action?: string;
    startDate?: string;
    endDate?: string;
    status?: string;
    searchQuery?: string;
  }) {
    const where: any = {};

    // 建構查詢條件（與 findLogs 相同邏輯）
    if (filters.level && filters.level.trim()) {
      where.level = filters.level.trim();
    }

    if (filters.userId && filters.userId.trim()) {
      where.userId = { 
        contains: filters.userId.trim(),
        mode: 'insensitive'
      };
    }

    if (filters.action && filters.action.trim()) {
      where.action = { 
        contains: filters.action.trim(),
        mode: 'insensitive'
      };
    }

    if (filters.status && filters.status.trim()) {
      where.status = filters.status.trim();
    }

    if (filters.searchQuery && filters.searchQuery.trim()) {
      where.message = { 
        contains: filters.searchQuery.trim(),
        mode: 'insensitive'
      };
    }

    // 處理日期範圍（與 findLogs 相同邏輯）
    if (filters.startDate && filters.startDate.trim()) {
      const startDate = new Date(filters.startDate.trim());
      startDate.setHours(0, 0, 0, 0);
      
      where.createdAt = {
        ...where.createdAt,
        gte: startDate,
      };
    }

    if (filters.endDate && filters.endDate.trim()) {
      const endDate = new Date(filters.endDate.trim());
      endDate.setHours(23, 59, 59, 999);
      
      where.createdAt = {
        ...where.createdAt,
        lte: endDate,
      };
    }

    return await this.prisma.system_logs.count({ where });
  }
}
