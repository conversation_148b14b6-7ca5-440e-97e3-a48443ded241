import {
    Injectable,
    NestInterceptor,
    Execution<PERSON>ontex<PERSON>,
    CallHandler,
    ForbiddenException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Observable } from 'rxjs';
import { TENANT_SCOPED_KEY } from '../decorators/tenant-scoped.decorator';

@Injectable()
export class TenantScopeInterceptor implements NestInterceptor {
    constructor(private reflector: Reflector) { }

    intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
        const tenantScopeOptions = this.reflector.get(
            TENANT_SCOPED_KEY,
            context.getHandler()
        );

        if (!tenantScopeOptions) {
            return next.handle();
        }

        const request = context.switchToHttp().getRequest();
        const user = request.user;
        const tenant = request.tenant;

        // 檢查是否為系統管理員（可以跨租戶操作）
        const isSystemAdmin = user?.userType === 'system' &&
            ['SUPER_ADMIN', 'SYSTEM_ADMIN'].includes(user?.role);

        if (!isSystemAdmin) {
            // 非系統管理員必須有租戶上下文
            if (tenantScopeOptions.required && !tenant?.id) {
                throw new ForbiddenException('此操作需要租戶上下文');
            }

            // 將租戶 ID 注入到請求參數中
            if (tenant?.id) {
                const field = tenantScopeOptions.field || 'tenantId';

                // 注入到 query 參數
                if (!request.query[field]) {
                    request.query[field] = tenant.id;
                }

                // 注入到 body 參數（如果是 POST/PUT 請求）
                if (request.body && typeof request.body === 'object') {
                    if (!request.body[field]) {
                        request.body[field] = tenant.id;
                    }
                }
            }
        }

        return next.handle();
    }
} 