import { Injectable, CanActivate, ExecutionContext } from "@nestjs/common";
import { Reflector } from "@nestjs/core";
import { CaslAbilityFactory } from "../casl-ability.factory";
import { AppAbility } from "../../types/models/casl.model";
import { CHECK_POLICIES_KEY } from "../decorators/check-policies.decorator";
import { PolicyHandler } from "../interfaces/policy-handler.interface";

// Extend Express Request object to include 'ability' for CASL
declare global {
  namespace Express {
    interface Request {
      ability?: AppAbility;
    }
  }
}

@Injectable()
export class PoliciesGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private caslAbilityFactory: CaslAbilityFactory
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const policyHandlers = this.reflector.get<PolicyHandler[]>(
      CHECK_POLICIES_KEY,
      context.getHandler()
    );

    // If no policies are defined, allow access by default (or deny if preferred)
    if (!policyHandlers || policyHandlers.length === 0) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const { user } = request;

    if (!user || !user.id) {
      // Or throw an UnauthorizedException if user is absolutely required for policy checks
      return false;
    }

    // CaslAbilityFactory.createForUser now expects { id: string, tenantId?: string | null }
    const ability = await this.caslAbilityFactory.createForUser({
      id: user.id,
      // role: user.role, // REMOVED: CaslAbilityFactory no longer uses this
      tenantId: user.tenantId, // Pass tenantId if available on user object from JWT/request
    });

    // Attach ability to request for use in controller if needed for instance-based checks
    request.ability = ability;

    // Execute all policy handlers (e.g, functions passed to @CheckPolicies decorator)
    return policyHandlers.every(
      (handler) => this.execPolicyHandler(handler, ability, context) // Pass context if handlers need it
    );
  }

  private execPolicyHandler(
    handler: PolicyHandler,
    ability: AppAbility,
    context: ExecutionContext
  ) {
    if (typeof handler === "function") {
      // If handler signature is (ability, context) for more complex scenarios
      // return handler(ability, context);
      return handler(ability); // Current handler signature is (ability)
    }
    // If handler is an object with a handle method
    // return handler.handle(ability, context);
    return handler.handle(ability);
  }
}
