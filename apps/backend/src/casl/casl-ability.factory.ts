import { Injectable, Logger } from "@nestjs/common";
import { PrismaService } from "../modules/core/prisma/prisma.service";
import {
  AbilityBuilder,
  createMongoAbility,
  Subject,
  MongoQuery,
} from "@casl/ability";
import { Action as CaslAppAction } from "../common/enums/action.enum";
import { AppAbility } from "../types/models/casl.model";
import { Prisma } from "@prisma/client";

// Define constants for special role names, ensure these match the 'name' field in the 'roles' table for system roles
const SUPER_ADMIN_ROLE_NAME = "SUPER_ADMIN"; // 修正為與資料庫中一致的名稱

/**
 * 實際從資料庫中獲取的 Permission 的結構
 */
interface DbPermission {
  id: string;
  action: string;
  subject: string; // This will be a string like 'User', 'Role', 'Project', etc.
  conditions: Prisma.JsonValue | null;
  fields?: string[];
  description: string | null;
  deprecated: boolean;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * 系統用戶角色關聯查詢結果類型
 */
interface SystemUserRoleWithRole {
  id: string;
  system_user_id: string;
  role_id: string;
  created_at: Date;
  updated_at: Date;
  role: {
    id: string;
    name: string;
    display_name: string;
    description: string | null;
    scope: string;
    is_system: boolean;
    tenant_id: string | null;
    created_at: Date;
    updated_at: Date;
  };
}

/**
 * 租戶用戶角色關聯查詢結果類型
 */
interface TenantUserRoleWithRole {
  id: string;
  tenant_user_id: string;
  role_id: string;
  created_at: Date;
  updated_at: Date;
  role: {
    id: string;
    name: string;
    display_name: string;
    description: string | null;
    scope: string;
    is_system: boolean;
    tenant_id: string | null;
    created_at: Date;
    updated_at: Date;
  };
}

/**
 * 使用者類型枚舉
 */
enum UserType {
  SYSTEM = "system",
  TENANT = "tenant",
}

/**
 * 處理權限條件中的動態參數
 * 將條件中的特殊變數 (如 ${user.tenantId}) 替換為實際值
 */
function processConditions(
  conditions: Prisma.JsonValue | null | undefined,
  userContext: { id: string; tenantId?: string | null; [key: string]: any }
): MongoQuery | undefined {
  if (
    !conditions ||
    typeof conditions !== "object" ||
    Array.isArray(conditions)
  ) {
    return undefined;
  }

  const processed: MongoQuery = {};
  for (const key in conditions) {
    if (Object.prototype.hasOwnProperty.call(conditions, key)) {
      let value = (conditions as Prisma.JsonObject)[key];

      if (typeof value === "string") {
        if (value === "${user.tenantId}") {
          value = userContext.tenantId ?? null;
        } else if (value === "${user.id}") {
          value = userContext.id;
        }
        // Add more replacements if needed, e.g, for user roles or other context
      }
      processed[key] = value;
    }
  }
  return Object.keys(processed).length > 0 ? processed : undefined;
}

@Injectable()
export class CaslAbilityFactory {
  private readonly logger = new Logger(CaslAbilityFactory.name);

  constructor(private prisma: PrismaService) {}

  /**
   * 安全地訪問 Prisma 模型，使用動態屬性訪問和運行時檢查
   * 方案一：使用 (this.prisma as any)[modelName] 進行動態模型訪問
   */
  private getModelDelegate(modelName: string) {
    const delegate = (this.prisma as any)[modelName];
    if (!delegate) {
      throw new Error(`Model ${modelName} not found on PrismaClient`);
    }
    return delegate;
  }

  /**
   * 根據用戶 ID 和類型確定用戶身份
   */
  private async determineUserType(
    userId: string
  ): Promise<{ type: UserType; tenantId?: string } | null> {
    try {
      // 首先嘗試查找系統用戶 - 使用動態模型訪問
      const systemUsersDelegate = this.getModelDelegate("system_users");
      const systemUser = await systemUsersDelegate.findUnique({
        where: { id: userId },
      });

      if (systemUser) {
        return { type: UserType.SYSTEM };
      }

      // 然後嘗試查找租戶用戶 - 使用動態模型訪問
      const tenantUsersDelegate = this.getModelDelegate("tenant_users");
      const tenantUser = await tenantUsersDelegate.findUnique({
        where: { id: userId },
        select: { id: true, tenantId: true }, // 使用正確的欄位名稱 tenantId (camelCase)
      });

      if (tenantUser) {
        return { type: UserType.TENANT, tenantId: tenantUser.tenantId };
      }

      return null;
    } catch (error: any) {
      this.logger.error(
        `Error determining user type for user ID ${userId}: ${error.message}`,
        error.stack
      );
      return null;
    }
  }

  /**
   * 為使用者建立 CASL Ability 物件
   * JWT payload should provide userId. Roles and tenantId (if primary) are fetched or contextually determined.
   */
  async createForUser(userPayloadFromJwt: {
    id: string; // userId from JWT
    tenantId?: string | null; // Primary tenantId from JWT, if available and relevant for global context
    // Any other relevant user context from JWT can be added here
  }): Promise<AppAbility> {
    // 使用 createMongoAbility 工廠並以 constructor.name 作為 detectSubjectType
    const subjectTypeDetector = (subject: any) =>
      (subject && ((subject as any).__typename || subject.constructor.name)) ||
      undefined;
    const builder = new AbilityBuilder<AppAbility>(createMongoAbility);
    const { can, build } = builder;

    if (!userPayloadFromJwt || !userPayloadFromJwt.id) {
      this.logger.warn(
        `User payload or ID is missing. Building an empty ability.`
      );
      return build({ detectSubjectType: subjectTypeDetector });
    }

    try {
      // 確定用戶類型和租戶 ID
      const userIdentity = await this.determineUserType(userPayloadFromJwt.id);

      if (!userIdentity) {
        this.logger.warn(
          `User with ID ${userPayloadFromJwt.id} not found in system_users or tenant_users`
        );
        return build({ detectSubjectType: subjectTypeDetector });
      }

      // User context for variable replacement in conditions
      const userContextForConditions = {
        id: userPayloadFromJwt.id,
        tenantId: userIdentity.tenantId || userPayloadFromJwt.tenantId,
      };

      // 根據用戶類型獲取角色
      let userRoleNames: string[] = [];

      if (userIdentity.type === UserType.SYSTEM) {
        // 獲取系統用戶的角色 - 使用動態模型訪問
        const systemUserRolesDelegate =
          this.getModelDelegate("system_user_roles");
        const systemUserRoles: SystemUserRoleWithRole[] =
          await systemUserRolesDelegate.findMany({
            where: { system_user_id: userPayloadFromJwt.id },
            include: {
              role: true,
            },
          });

        userRoleNames = systemUserRoles.map((mapping) => mapping.role.name);

        this.logger.debug(
          `Found ${systemUserRoles.length} system user roles for user ${userPayloadFromJwt.id}: ${userRoleNames.join(", ")}`
        );
      } else if (userIdentity.type === UserType.TENANT) {
        // 獲取租戶用戶的角色 - 使用動態模型訪問
        const tenantUserRolesDelegate =
          this.getModelDelegate("tenant_user_roles");
        const tenantUserRoles: TenantUserRoleWithRole[] =
          await tenantUserRolesDelegate.findMany({
            where: { tenant_user_id: userPayloadFromJwt.id },
            include: {
              role: true,
            },
          });

        userRoleNames = tenantUserRoles.map((mapping) => mapping.role.name);

        this.logger.debug(
          `Found ${tenantUserRoles.length} tenant user roles for user ${userPayloadFromJwt.id}: ${userRoleNames.join(", ")}`
        );
      }

      if (userRoleNames.length === 0) {
        this.logger.warn(
          `No roles found for user ID: ${userPayloadFromJwt.id} (type: ${userIdentity.type}). User might have no permissions.`
        );
        // return build(); // Decide if user with no roles gets an empty ability or some default guest ability
      }

      // 獲取這些角色對應的所有權限 - 使用動態模型訪問
      const permissionsDelegate = this.getModelDelegate("permissions");
      const permissions: DbPermission[] = (await permissionsDelegate.findMany({
        where: {
          role_permissions: {
            some: {
              roles: {
                name: {
                  in: userRoleNames,
                },
              },
            },
          },
          deprecated: false, // Skip deprecated permissions
        },
        // Ensure all necessary fields for DbPermission are selected if not default
      })) as DbPermission[]; // Cast if PrismaPermission doesn't exactly match DbPermission

      if (permissions.length === 0 && userRoleNames.length > 0) {
        this.logger.warn(
          `No specific permissions found in DB for roles: ${userRoleNames.join(", ")}. User ID: ${userPayloadFromJwt.id}`
        );
      } else {
        this.logger.debug(
          `Found ${permissions.length} permissions for user ${userPayloadFromJwt.id} with roles: ${userRoleNames.join(", ")}`
        );
      }

      for (const perm of permissions) {
        const action = perm.action as CaslAppAction;
        // The subject string from DB (e.g, 'User', 'Role', 'Project') is used directly by CASL.
        // When checking ability.can('action', SomePrismaModelInstance),
        // detectSubjectType will map the instance to its string name (e.g. 'User').
        const subject = perm.subject as Subject;
        const processedCond = processConditions(
          perm.conditions,
          userContextForConditions
        );

        if (perm.fields && perm.fields.length > 0) {
          can(action, subject, perm.fields, processedCond);
        } else {
          can(action, subject, processedCond);
        }
      }

      // 為 SUPER_ADMIN_ROLE_NAME (e.g, 'SUPER_ADMIN') 角色賦予 'manage' 'all' 權限
      if (userRoleNames.includes(SUPER_ADMIN_ROLE_NAME)) {
        can(CaslAppAction.MANAGE, "all");
        this.logger.debug(
          `Granted MANAGE ALL permissions to SUPER_ADMIN user ${userPayloadFromJwt.id}`
        );
      }

      // Add any default permissions for all logged-in users if necessary
      // For example: can(CaslAppAction.READ, 'UserProfile', { id: userPayloadFromJwt.id });

      return build({ detectSubjectType: subjectTypeDetector });
    } catch (error: any) {
      this.logger.error(
        `Error fetching or processing permissions for user ID ${userPayloadFromJwt.id}: ${error.message}`,
        error.stack
      );
      return build({ detectSubjectType: subjectTypeDetector }); // Return empty ability on error
    }
  }
}
