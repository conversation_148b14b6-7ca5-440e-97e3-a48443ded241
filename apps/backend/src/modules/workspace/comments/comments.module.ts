import { Modu<PERSON> } from '@nestjs/common';
import { CommentsController } from './comments.controller';
import { CommentsService } from './comments.service';
import { PrismaModule } from '../../core/prisma/prisma.module';
import { WebSocketModule } from '../websocket/websocket.module';
import { CaslModule } from '../../../casl/casl.module';

@Module({
    imports: [PrismaModule, WebSocketModule, CaslModule],
    controllers: [CommentsController],
    providers: [CommentsService],
    exports: [CommentsService],
})
export class CommentsModule { } 