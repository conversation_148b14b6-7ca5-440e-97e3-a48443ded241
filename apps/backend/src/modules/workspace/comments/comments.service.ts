import { Injectable, NotFoundException, ForbiddenException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../../core/prisma/prisma.service';
import { CreateCommentDto, UpdateCommentDto, AddCommentReactionDto } from './dto';
import { CommentEntityType, CommentReactionType } from '@prisma/client';
import { RealtimeEventsService } from '../websocket/realtime-events.service';

@Injectable()
export class CommentsService {
    constructor(
        private prisma: PrismaService,
        private realtimeEvents: RealtimeEventsService,
    ) { }

    async createComment(
        createCommentDto: CreateCommentDto,
        authorId: string,
        authorType: 'system' | 'tenant',
        tenantId: string,
        workspaceId?: string,
    ) {
        const { content, contentType, entityType, entityId, parentId, mentions } = createCommentDto;

        // 驗證實體是否存在
        await this.validateEntity(entityType, entityId, tenantId, workspaceId);

        // 如果是回覆，驗證父評論是否存在
        if (parentId) {
            const parentComment = await this.prisma.comments.findFirst({
                where: {
                    id: parentId,
                    tenantId,
                    ...(workspaceId && { workspaceId }),
                },
            });

            if (!parentComment) {
                throw new NotFoundException('Parent comment not found');
            }
        }

        // 創建評論
        const comment = await this.prisma.comments.create({
            data: {
                content,
                contentType,
                entityType,
                entityId,
                parentId,
                threadId: parentId ? undefined : undefined, // 將在創建後設置
                authorId,
                authorType,
                tenantId,
                workspaceId,
            },
            include: {
                reactions: true,
                mentions: true,
                replies: {
                    include: {
                        reactions: true,
                        mentions: true,
                    },
                },
            },
        });

        // 如果是頂級評論，設置 threadId 為自己的 id
        if (!parentId) {
            await this.prisma.comments.update({
                where: { id: comment.id },
                data: { threadId: comment.id },
            });
        } else {
            // 如果是回覆，設置 threadId 為父評論的 threadId
            const parentComment = await this.prisma.comments.findUnique({
                where: { id: parentId },
                select: { threadId: true },
            });

            await this.prisma.comments.update({
                where: { id: comment.id },
                data: { threadId: parentComment?.threadId || parentId },
            });
        }

        // 處理提及
        if (mentions && mentions.length > 0) {
            await this.createMentions(comment.id, mentions, tenantId, workspaceId);
        }

        // 發送即時通知
        if (workspaceId) {
            await this.realtimeEvents.notifyCommentCreated({
                commentId: comment.id,
                content: comment.content,
                entityType: comment.entityType,
                entityId: comment.entityId,
                authorId: comment.authorId,
                authorName: 'User', // 這裡可以從用戶資料中獲取真實姓名
                workspaceId,
                parentCommentId: comment.parentId || undefined,
            });
        }

        return this.getCommentById(comment.id, tenantId, workspaceId);
    }

    async getCommentsByEntity(
        entityType: CommentEntityType,
        entityId: string,
        tenantId: string,
        workspaceId?: string,
        page = 1,
        limit = 20,
    ) {
        const skip = (page - 1) * limit;

        const [comments, total] = await Promise.all([
            this.prisma.comments.findMany({
                where: {
                    entityType,
                    entityId,
                    tenantId,
                    ...(workspaceId && { workspaceId }),
                    parentId: null, // 只獲取頂級評論
                    isDeleted: false,
                },
                include: {
                    reactions: true,
                    mentions: true,
                    replies: {
                        where: { isDeleted: false },
                        include: {
                            reactions: true,
                            mentions: true,
                        },
                        orderBy: { createdAt: 'asc' },
                    },
                },
                orderBy: { createdAt: 'desc' },
                skip,
                take: limit,
            }),
            this.prisma.comments.count({
                where: {
                    entityType,
                    entityId,
                    tenantId,
                    ...(workspaceId && { workspaceId }),
                    parentId: null,
                    isDeleted: false,
                },
            }),
        ]);

        return {
            comments,
            pagination: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit),
            },
        };
    }

    async getCommentById(commentId: string, tenantId: string, workspaceId?: string) {
        const comment = await this.prisma.comments.findFirst({
            where: {
                id: commentId,
                tenantId,
                ...(workspaceId && { workspaceId }),
                isDeleted: false,
            },
            include: {
                reactions: true,
                mentions: true,
                replies: {
                    where: { isDeleted: false },
                    include: {
                        reactions: true,
                        mentions: true,
                    },
                    orderBy: { createdAt: 'asc' },
                },
            },
        });

        if (!comment) {
            throw new NotFoundException('Comment not found');
        }

        return comment;
    }

    async updateComment(
        commentId: string,
        updateCommentDto: UpdateCommentDto,
        authorId: string,
        authorType: 'system' | 'tenant',
        tenantId: string,
        workspaceId?: string,
    ) {
        const comment = await this.prisma.comments.findFirst({
            where: {
                id: commentId,
                tenantId,
                ...(workspaceId && { workspaceId }),
                isDeleted: false,
            },
        });

        if (!comment) {
            throw new NotFoundException('Comment not found');
        }

        // 檢查是否為評論作者
        if (comment.authorId !== authorId || comment.authorType !== authorType) {
            throw new ForbiddenException('You can only edit your own comments');
        }

        const { content, contentType, mentions } = updateCommentDto;

        // 更新評論
        const updatedComment = await this.prisma.comments.update({
            where: { id: commentId },
            data: {
                ...(content && { content }),
                ...(contentType && { contentType }),
                isEdited: true,
            },
            include: {
                reactions: true,
                mentions: true,
                replies: {
                    include: {
                        reactions: true,
                        mentions: true,
                    },
                },
            },
        });

        // 更新提及
        if (mentions !== undefined) {
            // 刪除舊的提及
            await this.prisma.comment_mentions.deleteMany({
                where: { commentId },
            });

            // 創建新的提及
            if (mentions.length > 0) {
                await this.createMentions(commentId, mentions, tenantId, workspaceId);
            }
        }

        return this.getCommentById(commentId, tenantId, workspaceId);
    }

    async deleteComment(
        commentId: string,
        authorId: string,
        authorType: 'system' | 'tenant',
        tenantId: string,
        workspaceId?: string,
    ) {
        const comment = await this.prisma.comments.findFirst({
            where: {
                id: commentId,
                tenantId,
                ...(workspaceId && { workspaceId }),
                isDeleted: false,
            },
        });

        if (!comment) {
            throw new NotFoundException('Comment not found');
        }

        // 檢查是否為評論作者
        if (comment.authorId !== authorId || comment.authorType !== authorType) {
            throw new ForbiddenException('You can only delete your own comments');
        }

        // 軟刪除評論
        await this.prisma.comments.update({
            where: { id: commentId },
            data: {
                isDeleted: true,
                deletedAt: new Date(),
            },
        });

        return { message: 'Comment deleted successfully' };
    }

    async addReaction(
        commentId: string,
        addReactionDto: AddCommentReactionDto,
        userId: string,
        userType: 'system' | 'tenant',
        tenantId: string,
        workspaceId?: string,
    ) {
        const comment = await this.prisma.comments.findFirst({
            where: {
                id: commentId,
                tenantId,
                ...(workspaceId && { workspaceId }),
                isDeleted: false,
            },
        });

        if (!comment) {
            throw new NotFoundException('Comment not found');
        }

        const { reaction } = addReactionDto;

        // 檢查是否已經有相同的反應
        const existingReaction = await this.prisma.comment_reactions.findFirst({
            where: {
                commentId,
                userId,
                userType,
            },
        });

        if (existingReaction) {
            if (existingReaction.reaction === reaction) {
                // 如果是相同反應，則移除
                await this.prisma.comment_reactions.delete({
                    where: { id: existingReaction.id },
                });
                return { message: 'Reaction removed' };
            } else {
                // 如果是不同反應，則更新
                await this.prisma.comment_reactions.update({
                    where: { id: existingReaction.id },
                    data: { reaction },
                });
                return { message: 'Reaction updated' };
            }
        } else {
            // 創建新反應
            await this.prisma.comment_reactions.create({
                data: {
                    commentId,
                    userId,
                    userType,
                    reaction,
                    tenantId,
                    workspaceId,
                },
            });
            return { message: 'Reaction added' };
        }
    }

    async removeReaction(
        commentId: string,
        userId: string,
        userType: 'system' | 'tenant',
        tenantId: string,
        workspaceId?: string,
    ) {
        const reaction = await this.prisma.comment_reactions.findFirst({
            where: {
                commentId,
                userId,
                userType,
                tenantId,
                ...(workspaceId && { workspaceId }),
            },
        });

        if (!reaction) {
            throw new NotFoundException('Reaction not found');
        }

        await this.prisma.comment_reactions.delete({
            where: { id: reaction.id },
        });

        return { message: 'Reaction removed' };
    }

    private async validateEntity(
        entityType: CommentEntityType,
        entityId: string,
        tenantId: string,
        workspaceId?: string,
    ) {
        let entity;

        switch (entityType) {
            case CommentEntityType.PROJECT:
                entity = await this.prisma.projects.findFirst({
                    where: {
                        id: entityId,
                        tenantId,
                        ...(workspaceId && { workspaceId }),
                    },
                });
                break;
            case CommentEntityType.TASK:
                entity = await this.prisma.tasks.findFirst({
                    where: {
                        id: entityId,
                        project: {
                            tenantId,
                            ...(workspaceId && { workspaceId }),
                        },
                    },
                });
                break;
            case CommentEntityType.PROGRESS_ENTRY:
                entity = await this.prisma.progress_entries.findFirst({
                    where: {
                        id: entityId,
                        project: {
                            tenantId,
                            ...(workspaceId && { workspaceId }),
                        },
                    },
                });
                break;
            case CommentEntityType.MILESTONE:
                entity = await this.prisma.project_milestones.findFirst({
                    where: {
                        id: entityId,
                        project: {
                            tenantId,
                            ...(workspaceId && { workspaceId }),
                        },
                    },
                });
                break;
            case CommentEntityType.PHOTO:
                entity = await this.prisma.photos.findFirst({
                    where: {
                        id: entityId,
                        tenantId,
                        ...(workspaceId && { workspaceId }),
                    },
                });
                break;
            default:
                throw new BadRequestException('Unsupported entity type');
        }

        if (!entity) {
            throw new NotFoundException(`${entityType} not found`);
        }
    }

    private async createMentions(
        commentId: string,
        mentions: string[],
        tenantId: string,
        workspaceId?: string,
    ) {
        const mentionData = mentions.map(userId => ({
            commentId,
            userId,
            userType: 'tenant' as const, // 假設都是租戶用戶
            tenantId,
            workspaceId,
        }));

        await this.prisma.comment_mentions.createMany({
            data: mentionData,
            skipDuplicates: true,
        });
    }
} 