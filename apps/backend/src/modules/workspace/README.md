# 即時聊天與訊息中心系統

這是一個完整的即時聊天與訊息中心後端系統，基於 NestJS + Socket.IO + Prisma 構建，支援多租戶架構。

## 功能特性

### 🚀 即時聊天功能

- ✅ 即時訊息發送與接收
- ✅ 對話管理（創建、更新、刪除）
- ✅ 參與者管理（添加、移除）
- ✅ 訊息類型支援（文字、圖片、檔案、音訊、影片）
- ✅ 訊息回覆功能
- ✅ 訊息反應（emoji）
- ✅ 輸入狀態指示
- ✅ 已讀狀態追蹤
- ✅ 訊息搜索功能
- ✅ 用戶在線狀態

### 📨 訊息中心功能

- ✅ 系統通知管理
- ✅ 即時通知推送
- ✅ 通知分類與優先級
- ✅ 批量操作（標記已讀、歸檔）
- ✅ 通知過期機制
- ✅ 未讀計數統計

### 🔧 技術特性

- ✅ WebSocket 即時通訊
- ✅ JWT 身份驗證
- ✅ 多租戶支援
- ✅ 房間管理系統
- ✅ 權限控制
- ✅ 錯誤處理
- ✅ 日誌記錄
- ✅ API 文檔（Swagger）

## 架構概覽

```
workspace/
├── chat/                    # 聊天模組
│   ├── controllers/         # REST API 控制器
│   ├── services/           # 業務邏輯服務
│   ├── gateways/           # WebSocket 閘道器
│   └── dto/                # 數據傳輸對象
├── message-center/         # 訊息中心模組
│   ├── controllers/        # REST API 控制器
│   ├── services/          # 業務邏輯服務
│   └── dto/               # 數據傳輸對象
└── websocket/             # WebSocket 基礎設施
    ├── websocket.gateway.ts    # 主要 WebSocket 閘道器
    ├── websocket.service.ts    # WebSocket 服務
    └── realtime-events.service.ts # 即時事件服務
```

## 快速開始

### 1. 環境配置

確保以下環境變數已設置：

```env
# JWT 配置
JWT_SECRET=your-jwt-secret

# 前端 URL（用於 CORS）
FRONTEND_URL=http://localhost:5173

# 資料庫連接
DATABASE_URL=postgresql://username:password@localhost:5432/database
```

### 2. 資料庫設置

確保以下資料表已創建：

- `conversations` - 聊天對話
- `messages` - 聊天訊息
- `conversation_participants` - 對話參與者
- `message_reactions` - 訊息反應
- `message_conversations` - 訊息中心對話
- `message_center_messages` - 訊息中心訊息
- `message_center_notifications` - 系統通知

### 3. 啟動服務

```bash
# 安裝依賴
npm install

# 啟動開發服務器
npm run start:dev
```

## API 使用指南

### 聊天 API

#### 對話管理

```typescript
// 創建對話
POST /workspace/chat/conversations
{
  "type": "DIRECT" | "GROUP",
  "name": "對話名稱",
  "participantIds": ["user1", "user2"]
}

// 獲取對話列表
GET /workspace/chat/conversations?workspaceId=xxx&page=1&limit=20

// 獲取對話詳情
GET /workspace/chat/conversations/:id

// 更新對話
PATCH /workspace/chat/conversations/:id
{
  "name": "新名稱"
}

// 刪除對話
DELETE /workspace/chat/conversations/:id
```

#### 訊息管理

```typescript
// 發送訊息
POST /workspace/chat/messages
{
  "conversationId": "conv-id",
  "content": "訊息內容",
  "type": "TEXT",
  "replyToId": "message-id" // 可選
}

// 獲取對話訊息
GET /workspace/chat/conversations/:id/messages?page=1&limit=50

// 編輯訊息
PATCH /workspace/chat/messages/:id
{
  "content": "新內容"
}

// 刪除訊息
DELETE /workspace/chat/messages/:id

// 添加反應
POST /workspace/chat/messages/:id/reactions
{
  "emoji": "👍"
}
```

### 訊息中心 API

#### 通知管理

```typescript
// 創建通知
POST /workspace/message-center/notifications
{
  "title": "通知標題",
  "message": "通知內容",
  "type": "INFO",
  "recipientId": "user-id",
  "recipientType": "tenant"
}

// 獲取通知列表
GET /workspace/message-center/notifications?limit=50&offset=0

// 標記通知已讀
PATCH /workspace/message-center/notifications/:id/read

// 批量標記已讀
PATCH /workspace/message-center/notifications/read-all
```

## WebSocket 事件

### 連接與認證

```typescript
// 客戶端連接
const socket = io("ws://localhost:3000", {
  auth: {
    token: "your-jwt-token",
  },
});
```

### 聊天事件

```typescript
// 加入對話房間
socket.emit("chat:join-conversation", {
  conversationId: "conv-id",
});

// 發送訊息
socket.emit("chat:send-message", {
  conversationId: "conv-id",
  content: "訊息內容",
  type: "TEXT",
});

// 開始輸入
socket.emit("chat:typing-start", {
  conversationId: "conv-id",
});

// 停止輸入
socket.emit("chat:typing-stop", {
  conversationId: "conv-id",
});

// 監聽新訊息
socket.on("chat:message-received", (data) => {
  console.log("收到新訊息:", data.message);
});

// 監聽輸入狀態
socket.on("chat:typing-start", (data) => {
  console.log(`${data.username} 正在輸入...`);
});
```

### 訊息中心事件

```typescript
// 加入訊息中心對話
socket.emit("message-center:join-conversation", {
  conversationId: "conv-id",
});

// 發送訊息中心訊息
socket.emit("message-center:send-message", {
  conversationId: "conv-id",
  content: "訊息內容",
  contentType: "TEXT",
});

// 監聽新通知
socket.on("message-center:new-notification", (data) => {
  console.log("收到新通知:", data.notification);
});

// 監聽未讀計數更新
socket.on("message-center:unread-count-update", (data) => {
  console.log("未讀計數:", data.unreadCount);
});
```

### 通用事件

```typescript
// 監聽用戶上線
socket.on("user:online", (data) => {
  console.log(`${data.username} 上線了`);
});

// 監聽用戶離線
socket.on("user:offline", (data) => {
  console.log(`${data.username} 離線了`);
});

// 監聽錯誤
socket.on("error", (error) => {
  console.error("WebSocket 錯誤:", error);
});
```

## 權限控制

### 聊天權限

- 只有對話參與者可以查看和發送訊息
- 對話創建者和管理員可以添加/移除參與者
- 用戶只能編輯和刪除自己的訊息

### 訊息中心權限

- 基於租戶隔離
- 用戶只能查看發送給自己的通知
- 支援工作區級別的權限控制

## 最佳實踐

### 1. 錯誤處理

```typescript
// 客戶端錯誤處理
socket.on("error", (error) => {
  switch (error.message) {
    case "Unauthorized":
      // 重新認證
      break;
    case "Access denied to room":
      // 權限不足
      break;
    default:
      console.error("未知錯誤:", error);
  }
});
```

### 2. 連接管理

```typescript
// 連接狀態管理
socket.on("connect", () => {
  console.log("WebSocket 已連接");
  // 重新加入之前的房間
});

socket.on("disconnect", () => {
  console.log("WebSocket 已斷開");
  // 清理本地狀態
});
```

### 3. 性能優化

- 使用分頁載入歷史訊息
- 實施訊息快取策略
- 合理設置 WebSocket 心跳間隔
- 避免頻繁的房間加入/離開操作

## 故障排除

### 常見問題

1. **WebSocket 連接失敗**

   - 檢查 JWT token 是否有效
   - 確認 CORS 設置正確
   - 檢查防火牆設置

2. **訊息發送失敗**

   - 確認用戶已加入對話房間
   - 檢查用戶權限
   - 驗證訊息格式

3. **通知未收到**
   - 確認用戶在線狀態
   - 檢查通知權限設置
   - 驗證 recipientId 正確性

### 日誌查看

```bash
# 查看 WebSocket 連接日誌
grep "WebSocket" logs/application.log

# 查看聊天相關日誌
grep "Chat" logs/application.log

# 查看訊息中心日誌
grep "MessageCenter" logs/application.log
```

## 擴展開發

### 添加新的訊息類型

1. 更新 `MessageType` 枚舉
2. 修改前端處理邏輯
3. 添加相應的驗證規則

### 自定義通知類型

1. 擴展 `NotificationType` 枚舉
2. 實現對應的處理邏輯
3. 更新前端顯示組件

### 添加新的 WebSocket 事件

1. 在 `websocket.gateway.ts` 中添加事件處理器
2. 更新 DTO 定義
3. 實現相應的業務邏輯

## 貢獻指南

1. Fork 專案
2. 創建功能分支
3. 提交變更
4. 發起 Pull Request

## 授權

MIT License
