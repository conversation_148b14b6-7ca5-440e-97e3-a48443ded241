import { IsString, IsEnum, IsOptional, IsDateString, <PERSON><PERSON><PERSON>y } from 'class-validator';
import { FilePermission } from '@prisma/client';

export class GrantFilePermissionDto {
    @IsString()
    userId: string;

    @IsString()
    @IsOptional()
    userType?: string = 'tenant';

    @IsEnum(FilePermission)
    permission: FilePermission;

    @IsDateString()
    @IsOptional()
    expiresAt?: string;
}

export class BulkGrantPermissionDto {
    @IsArray()
    userIds: string[];

    @IsString()
    @IsOptional()
    userType?: string = 'tenant';

    @IsEnum(FilePermission)
    permission: FilePermission;

    @IsDateString()
    @IsOptional()
    expiresAt?: string;
} 