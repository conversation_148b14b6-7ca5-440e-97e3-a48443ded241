import { IsString, IsOptional, IsEnum, IsBoolean, IsDateString, IsUUID } from 'class-validator';
import { FileCategory, FileEntityType, FileVisibility } from '@prisma/client';

export class UploadFileDto {
    @IsString()
    @IsOptional()
    name?: string; // 如果不提供，使用原始檔名

    @IsString()
    @IsOptional()
    description?: string;

    @IsEnum(FileCategory)
    @IsOptional()
    category?: FileCategory;

    @IsEnum(FileEntityType)
    @IsOptional()
    entityType?: FileEntityType;

    @IsUUID()
    @IsOptional()
    entityId?: string;

    @IsEnum(FileVisibility)
    @IsOptional()
    visibility?: FileVisibility = FileVisibility.PRIVATE;

    @IsBoolean()
    @IsOptional()
    allowDownload?: boolean = true;

    @IsBoolean()
    @IsOptional()
    allowComment?: boolean = true;

    @IsDateString()
    @IsOptional()
    expiresAt?: string;
} 