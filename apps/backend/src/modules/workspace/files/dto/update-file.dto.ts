import { IsString, IsOptional, IsEnum, IsBoolean, IsDateString } from 'class-validator';
import { FileCategory, FileVisibility } from '@prisma/client';

export class UpdateFileDto {
    @IsString()
    @IsOptional()
    name?: string;

    @IsString()
    @IsOptional()
    description?: string;

    @IsEnum(FileCategory)
    @IsOptional()
    category?: FileCategory;

    @IsEnum(FileVisibility)
    @IsOptional()
    visibility?: FileVisibility;

    @IsBoolean()
    @IsOptional()
    allowDownload?: boolean;

    @IsBoolean()
    @IsOptional()
    allowComment?: boolean;

    @IsDateString()
    @IsOptional()
    expiresAt?: string;
} 