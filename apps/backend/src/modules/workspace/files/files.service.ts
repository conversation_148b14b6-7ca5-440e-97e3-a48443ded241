import { Injectable, NotFoundException, ForbiddenException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../../core/prisma/prisma.service';
import { StorageService } from '../../core/storage/storage.service';
import { SettingsService } from '../../admin/settings/settings.service';
import {
    UploadFileDto,
    UpdateFileDto,
    CreateShareDto,
    GrantFilePermissionDto,
    BulkGrantPermissionDto
} from './dto';
import {
    FileCategory,
    FileEntityType,
    FileVisibility,
    FileStatus,
    FilePermission,
    AccessType,
    ShareType
} from '@prisma/client';
import * as path from 'path';
import * as crypto from 'crypto';
// import sharp from 'sharp'; // 改為動態導入
import { createReadStream } from 'fs';

@Injectable()
export class FilesService {
    constructor(
        private prisma: PrismaService,
        private storageService: StorageService,
        private settingsService: SettingsService,
    ) { }

    async uploadFile(
        workspaceId: string,
        userId: string,
        file: Express.Multer.File,
        uploadFileDto: UploadFileDto,
        ipAddress?: string,
    ) {
        // 獲取工作區信息以確定租戶
        const workspace = await this.prisma.workspaces.findUnique({
            where: { id: workspaceId },
            select: { tenantId: true }
        });

        if (!workspace) {
            throw new NotFoundException('Workspace not found');
        }

        const tenantId = workspace.tenantId;
        const uploaderType = 'tenant'; // 假設都是租戶用戶

        // 獲取系統設定
        const storageSettings = await this.settingsService.getStorageSettings();

        // 驗證檔案
        await this.validateFile(file, storageSettings);

        // 確定檔案分類
        const category = uploadFileDto.category || this.determineFileCategory(file.mimetype);

        // 生成檔案名稱
        const fileName = uploadFileDto.name || file.originalname;
        const fileExtension = path.extname(file.originalname);
        const uniqueFileName = `${crypto.randomUUID()}${fileExtension}`;

        // 上傳檔案
        const filePath = await this.storageService.uploadFile({
            ...file,
            originalname: uniqueFileName,
        });

        // 生成縮圖和預覽（如果是圖片）
        let thumbnailPath: string | undefined;
        let previewPath: string | undefined;
        let metadata: any = {};

        if (category === FileCategory.IMAGE) {
            const result = await this.generateImagePreviews(file, uniqueFileName);
            thumbnailPath = result.thumbnailPath;
            previewPath = result.previewPath;
            metadata = result.metadata;
        }

        // 驗證實體關聯（如果有）
        if (uploadFileDto.entityType && uploadFileDto.entityId) {
            await this.validateEntityAccess(
                uploadFileDto.entityType,
                uploadFileDto.entityId,
                tenantId,
                workspaceId,
            );
        }

        // 創建檔案記錄
        const sharedFile = await this.prisma.shared_files.create({
            data: {
                name: fileName,
                originalName: file.originalname,
                description: uploadFileDto.description,
                fileType: file.mimetype,
                fileExtension,
                fileSize: file.size,
                filePath,
                category,
                entityType: uploadFileDto.entityType,
                entityId: uploadFileDto.entityId,
                thumbnailPath,
                previewPath,
                metadata,
                visibility: uploadFileDto.visibility || FileVisibility.PRIVATE,
                allowDownload: uploadFileDto.allowDownload ?? true,
                allowComment: uploadFileDto.allowComment ?? true,
                expiresAt: uploadFileDto.expiresAt ? new Date(uploadFileDto.expiresAt) : null,
                uploaderId: userId,
                uploaderType,
                tenantId,
                workspaceId,
            },
            include: {
                permissions: true,
                shares: true,
            },
        });

        // 記錄存取日誌
        await this.logFileAccess(
            sharedFile.id,
            AccessType.VIEW,
            userId,
            uploaderType,
            tenantId,
            ipAddress,
        );

        return sharedFile;
    }

    async getFiles(
        workspaceId: string,
        userId: string,
        filters?: {
            entityType?: string;
            entityId?: string;
            category?: string;
            search?: string;
            page?: number;
            limit?: number;
        },
    ) {
        // 獲取工作區信息
        const workspace = await this.prisma.workspaces.findUnique({
            where: { id: workspaceId },
            select: { tenantId: true }
        });

        if (!workspace) {
            throw new NotFoundException('Workspace not found');
        }

        const tenantId = workspace.tenantId;
        const userType = 'tenant';

        return this.getFilesInternal(
            tenantId,
            workspaceId,
            userId,
            userType,
            {
                category: filters?.category as FileCategory,
                entityType: filters?.entityType as FileEntityType,
                entityId: filters?.entityId,
                search: filters?.search,
            },
            {
                page: filters?.page,
                limit: filters?.limit,
            }
        );
    }

    async getFilesInternal(
        tenantId: string,
        workspaceId?: string,
        userId?: string,
        userType?: string,
        filters?: {
            category?: FileCategory;
            entityType?: FileEntityType;
            entityId?: string;
            visibility?: FileVisibility;
            search?: string;
        },
        pagination?: {
            page?: number;
            limit?: number;
        },
    ) {
        const page = pagination?.page || 1;
        const limit = pagination?.limit || 20;
        const skip = (page - 1) * limit;

        const where: any = {
            tenantId,
            ...(workspaceId && { workspaceId }),
            isDeleted: false,
            status: FileStatus.ACTIVE,
        };

        // 應用過濾器
        if (filters?.category) where.category = filters.category;
        if (filters?.entityType) where.entityType = filters.entityType;
        if (filters?.entityId) where.entityId = filters.entityId;
        if (filters?.visibility) where.visibility = filters.visibility;

        if (filters?.search) {
            where.OR = [
                { name: { contains: filters.search, mode: 'insensitive' } },
                { description: { contains: filters.search, mode: 'insensitive' } },
                { originalName: { contains: filters.search, mode: 'insensitive' } },
            ];
        }

        // 權限過濾：只顯示用戶有權限存取的檔案
        if (userId && userType) {
            where.OR = [
                // 用戶上傳的檔案
                { uploaderId: userId, uploaderType: userType },
                // 公開檔案
                { visibility: FileVisibility.PUBLIC },
                // 租戶內可見檔案
                { visibility: FileVisibility.TENANT },
                // 工作區可見檔案（如果在工作區內）
                ...(workspaceId ? [{ visibility: FileVisibility.WORKSPACE }] : []),
                // 有明確權限的檔案
                {
                    permissions: {
                        some: {
                            userId,
                            userType,
                            OR: [
                                { expiresAt: null },
                                { expiresAt: { gt: new Date() } },
                            ],
                        },
                    },
                },
            ];
        }

        const [files, total] = await Promise.all([
            this.prisma.shared_files.findMany({
                where,
                include: {
                    permissions: {
                        include: {
                            // user: true, // 需要根據實際關聯調整
                        },
                    },
                    shares: true,
                    // uploader: true, // 需要根據實際關聯調整
                },
                orderBy: { createdAt: 'desc' },
                skip,
                take: limit,
            }),
            this.prisma.shared_files.count({ where }),
        ]);

        return {
            files,
            pagination: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit),
            },
        };
    }

    async getFile(workspaceId: string, fileId: string, userId: string) {
        // 獲取工作區信息
        const workspace = await this.prisma.workspaces.findUnique({
            where: { id: workspaceId },
            select: { tenantId: true }
        });

        if (!workspace) {
            throw new NotFoundException('Workspace not found');
        }

        const tenantId = workspace.tenantId;
        const userType = 'tenant';

        return this.getFileById(fileId, tenantId, workspaceId, userId, userType);
    }

    async updateFile(
        workspaceId: string,
        fileId: string,
        userId: string,
        updateFileDto: UpdateFileDto,
    ) {
        // 獲取工作區信息
        const workspace = await this.prisma.workspaces.findUnique({
            where: { id: workspaceId },
            select: { tenantId: true }
        });

        if (!workspace) {
            throw new NotFoundException('Workspace not found');
        }

        const tenantId = workspace.tenantId;
        const userType = 'tenant';

        return this.updateFileInternal(fileId, updateFileDto, userId, userType, tenantId, workspaceId);
    }

    async updateFileInternal(
        fileId: string,
        updateFileDto: UpdateFileDto,
        userId: string,
        userType: 'system' | 'tenant',
        tenantId: string,
        workspaceId?: string,
    ) {
        // 檢查檔案是否存在和權限
        const file = await this.getFileById(fileId, tenantId, workspaceId, userId, userType);

        if (!file) {
            throw new NotFoundException('File not found');
        }

        // 檢查是否有編輯權限
        const hasPermission = await this.checkFileAccess(file, userId, userType, FilePermission.EDIT);
        if (!hasPermission) {
            throw new ForbiddenException('Insufficient permissions to edit this file');
        }

        // 更新檔案
        const updatedFile = await this.prisma.shared_files.update({
            where: { id: fileId },
            data: {
                name: updateFileDto.name,
                description: updateFileDto.description,
                visibility: updateFileDto.visibility,
                allowDownload: updateFileDto.allowDownload,
                allowComment: updateFileDto.allowComment,
                expiresAt: updateFileDto.expiresAt ? new Date(updateFileDto.expiresAt) : null,
                updatedAt: new Date(),
            },
            include: {
                permissions: true,
                shares: true,
            },
        });

        return updatedFile;
    }

    async deleteFile(workspaceId: string, fileId: string, userId: string) {
        // 獲取工作區信息
        const workspace = await this.prisma.workspaces.findUnique({
            where: { id: workspaceId },
            select: { tenantId: true }
        });

        if (!workspace) {
            throw new NotFoundException('Workspace not found');
        }

        const tenantId = workspace.tenantId;
        const userType = 'tenant';

        return this.deleteFileInternal(fileId, userId, userType, tenantId, workspaceId);
    }

    async deleteFileInternal(
        fileId: string,
        userId: string,
        userType: 'system' | 'tenant',
        tenantId: string,
        workspaceId?: string,
    ) {
        // 檢查檔案是否存在和權限
        const file = await this.getFileById(fileId, tenantId, workspaceId, userId, userType);

        if (!file) {
            throw new NotFoundException('File not found');
        }

        // 檢查是否有刪除權限
        const hasPermission = await this.checkFileAccess(file, userId, userType, FilePermission.DELETE);
        if (!hasPermission) {
            throw new ForbiddenException('Insufficient permissions to delete this file');
        }

        // 軟刪除檔案
        await this.prisma.shared_files.update({
            where: { id: fileId },
            data: {
                isDeleted: true,
                deletedAt: new Date(),
            },
        });

        // 記錄存取日誌
        await this.logFileAccess(
            fileId,
            AccessType.DOWNLOAD,
            userId,
            userType,
            tenantId,
        );

        return { message: 'File deleted successfully' };
    }

    async downloadFile(
        workspaceId: string,
        fileId: string,
        userId: string,
        version?: number,
    ) {
        // 獲取工作區信息
        const workspace = await this.prisma.workspaces.findUnique({
            where: { id: workspaceId },
            select: { tenantId: true }
        });

        if (!workspace) {
            throw new NotFoundException('Workspace not found');
        }

        const tenantId = workspace.tenantId;
        const userType = 'tenant';

        // 獲取檔案信息
        const file = await this.getFileById(fileId, tenantId, workspaceId, userId, userType);

        if (!file) {
            throw new NotFoundException('File not found');
        }

        // 檢查下載權限
        if (!file.allowDownload) {
            throw new ForbiddenException('File download is not allowed');
        }

        const hasPermission = await this.checkFileAccess(file, userId, userType, FilePermission.DOWNLOAD);
        if (!hasPermission) {
            throw new ForbiddenException('Insufficient permissions to download this file');
        }

        // 記錄存取日誌
        await this.logFileAccess(
            fileId,
            AccessType.DOWNLOAD,
            userId,
            userType,
            tenantId,
        );

        // 創建檔案流
        const stream = createReadStream(file.filePath);

        return {
            file: {
                mimeType: file.fileType,
                originalName: file.originalName,
                fileSize: file.fileSize,
            },
            stream,
        };
    }

    async getFilePreview(
        workspaceId: string,
        fileId: string,
        userId: string,
        size: 'small' | 'medium' | 'large' = 'medium',
    ) {
        // 獲取工作區信息
        const workspace = await this.prisma.workspaces.findUnique({
            where: { id: workspaceId },
            select: { tenantId: true }
        });

        if (!workspace) {
            throw new NotFoundException('Workspace not found');
        }

        const tenantId = workspace.tenantId;
        const userType = 'tenant';

        // 獲取檔案信息
        const file = await this.getFileById(fileId, tenantId, workspaceId, userId, userType);

        if (!file) {
            throw new NotFoundException('File not found');
        }

        // 檢查查看權限
        const hasPermission = await this.checkFileAccess(file, userId, userType, FilePermission.VIEW);
        if (!hasPermission) {
            throw new ForbiddenException('Insufficient permissions to view this file');
        }

        // 記錄存取日誌
        await this.logFileAccess(
            fileId,
            AccessType.VIEW,
            userId,
            userType,
            tenantId,
        );

        // 根據檔案類型和大小返回適當的預覽
        let previewPath = file.filePath;
        if (file.category === FileCategory.IMAGE) {
            switch (size) {
                case 'small':
                    previewPath = file.thumbnailPath || file.filePath;
                    break;
                case 'medium':
                    previewPath = file.previewPath || file.filePath;
                    break;
                case 'large':
                default:
                    previewPath = file.filePath;
                    break;
            }
        }

        const stream = createReadStream(previewPath);

        return {
            file: {
                mimeType: file.fileType,
                originalName: file.originalName,
                fileSize: file.fileSize,
            },
            stream,
        };
    }

    async getFileVersions(workspaceId: string, fileId: string, userId: string) {
        // 獲取工作區信息
        const workspace = await this.prisma.workspaces.findUnique({
            where: { id: workspaceId },
            select: { tenantId: true }
        });

        if (!workspace) {
            throw new NotFoundException('Workspace not found');
        }

        const tenantId = workspace.tenantId;
        const userType = 'tenant';

        // 獲取檔案信息
        const file = await this.getFileById(fileId, tenantId, workspaceId, userId, userType);

        if (!file) {
            throw new NotFoundException('File not found');
        }

        // 檢查查看權限
        const hasPermission = await this.checkFileAccess(file, userId, userType, FilePermission.VIEW);
        if (!hasPermission) {
            throw new ForbiddenException('Insufficient permissions to view file versions');
        }

        // 獲取檔案版本（假設有版本表）
        // 這裡需要根據實際的版本表結構來實現
        return {
            fileId,
            versions: [], // 暫時返回空陣列
        };
    }

    async uploadNewVersion(
        workspaceId: string,
        fileId: string,
        userId: string,
        file: Express.Multer.File,
        description?: string,
    ) {
        // 獲取工作區信息
        const workspace = await this.prisma.workspaces.findUnique({
            where: { id: workspaceId },
            select: { tenantId: true }
        });

        if (!workspace) {
            throw new NotFoundException('Workspace not found');
        }

        const tenantId = workspace.tenantId;
        const userType = 'tenant';

        return this.createFileVersion(fileId, file, userId, userType, tenantId, workspaceId);
    }

    async bulkGrantPermissions(
        workspaceId: string,
        fileId: string,
        userId: string,
        bulkGrantDto: BulkGrantPermissionDto,
    ) {
        // 獲取工作區信息
        const workspace = await this.prisma.workspaces.findUnique({
            where: { id: workspaceId },
            select: { tenantId: true }
        });

        if (!workspace) {
            throw new NotFoundException('Workspace not found');
        }

        const tenantId = workspace.tenantId;

        // 實現批量授權邏輯
        const results: any[] = [];

        for (const targetUserId of bulkGrantDto.userIds) {
            try {
                const grantPermissionDto: GrantFilePermissionDto = {
                    userId: targetUserId,
                    userType: bulkGrantDto.userType,
                    permission: bulkGrantDto.permission,
                    expiresAt: bulkGrantDto.expiresAt,
                };

                const result = await this.grantPermission(
                    fileId,
                    grantPermissionDto,
                    userId,
                    tenantId,
                    workspaceId,
                );
                results.push({ success: true, userId: targetUserId, result });
            } catch (error) {
                results.push({ success: false, userId: targetUserId, error: error.message });
            }
        }
        return { results };
    }

    async getFilePermissions(workspaceId: string, fileId: string, userId: string) {
        // 獲取工作區信息
        const workspace = await this.prisma.workspaces.findUnique({
            where: { id: workspaceId },
            select: { tenantId: true }
        });

        if (!workspace) {
            throw new NotFoundException('Workspace not found');
        }

        const tenantId = workspace.tenantId;
        const userType = 'tenant';

        // 獲取檔案權限
        const file = await this.getFileById(fileId, tenantId, workspaceId, userId, userType);
        if (!file) {
            throw new NotFoundException('File not found');
        }

        return file.permissions;
    }

    async revokePermission(
        workspaceId: string,
        fileId: string,
        permissionId: string,
        userId: string,
    ) {
        // 實現撤銷權限邏輯
        await this.prisma.file_permissions.delete({
            where: { id: permissionId },
        });

        return { message: 'Permission revoked successfully' };
    }

    async createFileShare(
        workspaceId: string,
        fileId: string,
        userId: string,
        createShareDto: CreateShareDto,
    ) {
        // 獲取工作區信息
        const workspace = await this.prisma.workspaces.findUnique({
            where: { id: workspaceId },
            select: { tenantId: true }
        });

        if (!workspace) {
            throw new NotFoundException('Workspace not found');
        }

        const tenantId = workspace.tenantId;
        const userType = 'tenant';

        return this.createShare(fileId, createShareDto, userId, userType, tenantId, workspaceId);
    }

    async getFileShares(workspaceId: string, fileId: string, userId: string) {
        // 獲取工作區信息
        const workspace = await this.prisma.workspaces.findUnique({
            where: { id: workspaceId },
            select: { tenantId: true }
        });

        if (!workspace) {
            throw new NotFoundException('Workspace not found');
        }

        const tenantId = workspace.tenantId;

        // 獲取檔案分享
        const shares = await this.prisma.file_shares.findMany({
            where: {
                fileId,
                // 可以添加更多過濾條件
            },
            include: {
                // 根據需要包含相關數據
            },
        });

        return shares;
    }

    async deleteFileShare(
        workspaceId: string,
        fileId: string,
        shareId: string,
        userId: string,
    ) {
        // 刪除檔案分享
        await this.prisma.file_shares.delete({
            where: { id: shareId },
        });

        return { message: 'Share deleted successfully' };
    }

    async getAccessLogs(
        workspaceId: string,
        fileId: string,
        userId: string,
        pagination: { page: number; limit: number },
    ) {
        // 獲取工作區信息
        const workspace = await this.prisma.workspaces.findUnique({
            where: { id: workspaceId },
            select: { tenantId: true }
        });

        if (!workspace) {
            throw new NotFoundException('Workspace not found');
        }

        const tenantId = workspace.tenantId;

        return this.getFileAccessLogs(fileId, tenantId, workspaceId, pagination);
    }

    async getSharedFile(shareToken: string) {
        const share = await this.prisma.file_shares.findUnique({
            where: { shareToken },
            include: {
                file: true,
            },
        });

        if (!share) {
            throw new NotFoundException('Share not found');
        }

        // 檢查分享是否過期
        if (share.expiresAt && share.expiresAt < new Date()) {
            throw new ForbiddenException('Share has expired');
        }

        return share;
    }

    async downloadSharedFile(shareToken: string, password?: string) {
        const share = await this.getSharedFile(shareToken);

        // 檢查密碼（如果需要）
        if (share.password && share.password !== password) {
            throw new ForbiddenException('Invalid password');
        }

        // 記錄存取
        await this.logFileAccess(
            share.fileId,
            AccessType.DOWNLOAD,
            undefined,
            undefined,
            undefined,
            undefined,
            share.id,
        );

        const stream = createReadStream(share.file.filePath);

        return {
            file: {
                mimeType: share.file.fileType,
                originalName: share.file.originalName,
                fileSize: share.file.fileSize,
            },
            stream,
        };
    }

    async previewSharedFile(
        shareToken: string,
        password?: string,
        size: 'small' | 'medium' | 'large' = 'medium',
    ) {
        const share = await this.getSharedFile(shareToken);

        // 檢查密碼（如果需要）
        if (share.password && share.password !== password) {
            throw new ForbiddenException('Invalid password');
        }

        // 記錄存取
        await this.logFileAccess(
            share.fileId,
            AccessType.VIEW,
            undefined,
            undefined,
            undefined,
            undefined,
            share.id,
        );

        // 根據檔案類型和大小返回適當的預覽
        let previewPath = share.file.filePath;
        if (share.file.category === FileCategory.IMAGE) {
            switch (size) {
                case 'small':
                    previewPath = share.file.thumbnailPath || share.file.filePath;
                    break;
                case 'medium':
                    previewPath = share.file.previewPath || share.file.filePath;
                    break;
                case 'large':
                default:
                    previewPath = share.file.filePath;
                    break;
            }
        }

        const stream = createReadStream(previewPath);

        return {
            file: {
                mimeType: share.file.fileType,
                originalName: share.file.originalName,
                fileSize: share.file.fileSize,
            },
            stream,
        };
    }

    async getFileById(
        fileId: string,
        tenantId: string,
        workspaceId?: string,
        userId?: string,
        userType?: string,
        ipAddress?: string,
    ) {
        const file = await this.prisma.shared_files.findFirst({
            where: {
                id: fileId,
                tenantId,
                ...(workspaceId && { workspaceId }),
                isDeleted: false,
            },
            include: {
                permissions: true,
                shares: {
                    where: { isActive: true },
                },
                versions: {
                    where: { isDeleted: false },
                    orderBy: { version: 'desc' },
                    take: 10,
                },
                parentFile: true,
                _count: {
                    select: {
                        versions: true,
                        access_logs: true,
                    },
                },
            },
        });

        if (!file) {
            throw new NotFoundException('File not found');
        }

        // 檢查存取權限
        if (userId && userType) {
            const hasAccess = await this.checkFileAccess(
                file,
                userId,
                userType,
                FilePermission.VIEW,
            );

            if (!hasAccess) {
                throw new ForbiddenException('Access denied');
            }

            // 記錄存取日誌
            await this.logFileAccess(
                fileId,
                AccessType.VIEW,
                userId,
                userType,
                tenantId,
                ipAddress,
            );
        }

        return file;
    }

    async createFileVersion(
        fileId: string,
        newFile: Express.Multer.File,
        userId: string,
        userType: 'system' | 'tenant',
        tenantId: string,
        workspaceId?: string,
    ) {
        const originalFile = await this.getFileById(fileId, tenantId, workspaceId, userId, userType);

        // 檢查編輯權限
        const hasEditAccess = await this.checkFileAccess(
            originalFile,
            userId,
            userType,
            FilePermission.EDIT,
        );

        if (!hasEditAccess) {
            throw new ForbiddenException('Edit access denied');
        }

        // 驗證檔案類型一致性
        if (newFile.mimetype !== originalFile.fileType) {
            throw new BadRequestException('File type must match the original file');
        }

        // 獲取下一個版本號
        const latestVersion = await this.prisma.shared_files.findFirst({
            where: {
                OR: [
                    { id: fileId },
                    { parentFileId: originalFile.parentFileId || fileId },
                ],
            },
            orderBy: { version: 'desc' },
        });

        const nextVersion = (latestVersion?.version || 0) + 1;

        // 上傳新版本檔案
        const fileExtension = path.extname(newFile.originalname);
        const uniqueFileName = `${crypto.randomUUID()}${fileExtension}`;
        const filePath = await this.storageService.uploadFile({
            ...newFile,
            originalname: uniqueFileName,
        });

        // 生成預覽（如果需要）
        let thumbnailPath: string | undefined;
        let previewPath: string | undefined;
        let metadata: any = {};

        if (originalFile.category === FileCategory.IMAGE) {
            const result = await this.generateImagePreviews(newFile, uniqueFileName);
            thumbnailPath = result.thumbnailPath;
            previewPath = result.previewPath;
            metadata = result.metadata;
        }

        // 將舊版本標記為非最新
        await this.prisma.shared_files.updateMany({
            where: {
                OR: [
                    { id: fileId },
                    { parentFileId: originalFile.parentFileId || fileId },
                ],
            },
            data: { isLatestVersion: false },
        });

        // 創建新版本
        const newVersion = await this.prisma.shared_files.create({
            data: {
                name: originalFile.name,
                originalName: newFile.originalname,
                description: originalFile.description,
                fileType: newFile.mimetype,
                fileExtension,
                fileSize: newFile.size,
                filePath,
                category: originalFile.category,
                entityType: originalFile.entityType,
                entityId: originalFile.entityId,
                version: nextVersion,
                parentFileId: originalFile.parentFileId || fileId,
                isLatestVersion: true,
                thumbnailPath,
                previewPath,
                metadata,
                visibility: originalFile.visibility,
                allowDownload: originalFile.allowDownload,
                allowComment: originalFile.allowComment,
                expiresAt: originalFile.expiresAt,
                uploaderId: userId,
                uploaderType: userType,
                tenantId,
                workspaceId,
            },
            include: {
                permissions: true,
                shares: true,
                parentFile: true,
            },
        });

        return newVersion;
    }

    async createShare(
        fileId: string,
        createShareDto: CreateShareDto,
        userId: string,
        userType: 'system' | 'tenant',
        tenantId: string,
        workspaceId?: string,
    ) {
        const file = await this.getFileById(fileId, tenantId, workspaceId, userId, userType);

        // 檢查分享權限
        const hasShareAccess = await this.checkFileAccess(
            file,
            userId,
            userType,
            FilePermission.SHARE,
        );

        if (!hasShareAccess) {
            throw new ForbiddenException('Share access denied');
        }

        // 生成分享令牌
        const shareToken = crypto.randomBytes(32).toString('hex');

        const share = await this.prisma.file_shares.create({
            data: {
                fileId,
                shareToken,
                shareType: createShareDto.shareType || ShareType.LINK,
                allowDownload: createShareDto.allowDownload ?? true,
                allowComment: createShareDto.allowComment ?? false,
                requireAuth: createShareDto.requireAuth ?? false,
                password: createShareDto.password,
                maxDownloads: createShareDto.maxDownloads,
                expiresAt: createShareDto.expiresAt ? new Date(createShareDto.expiresAt) : null,
                sharedBy: userId,
                sharedByType: userType,
                tenantId,
            },
            include: {
                file: true,
            },
        });

        return share;
    }

    async grantPermission(
        fileId: string,
        grantPermissionDto: GrantFilePermissionDto,
        grantedBy: string,
        tenantId: string,
        workspaceId?: string,
    ) {
        const file = await this.getFileById(fileId, tenantId, workspaceId);

        // 檢查是否有管理權限
        const hasManageAccess = await this.checkFileAccess(
            file,
            grantedBy,
            'tenant', // 假設都是租戶用戶
            FilePermission.MANAGE,
        );

        if (!hasManageAccess) {
            throw new ForbiddenException('Manage access denied');
        }

        // 檢查權限是否已存在
        const existingPermission = await this.prisma.file_permissions.findFirst({
            where: {
                fileId,
                userId: grantPermissionDto.userId,
                userType: grantPermissionDto.userType || 'tenant',
            },
        });

        if (existingPermission) {
            // 更新現有權限
            return this.prisma.file_permissions.update({
                where: { id: existingPermission.id },
                data: {
                    permission: grantPermissionDto.permission,
                    expiresAt: grantPermissionDto.expiresAt ?
                        new Date(grantPermissionDto.expiresAt) : null,
                    grantedBy,
                    grantedAt: new Date(),
                },
            });
        } else {
            // 創建新權限
            return this.prisma.file_permissions.create({
                data: {
                    fileId,
                    userId: grantPermissionDto.userId,
                    userType: grantPermissionDto.userType || 'tenant',
                    permission: grantPermissionDto.permission,
                    grantedBy,
                    expiresAt: grantPermissionDto.expiresAt ?
                        new Date(grantPermissionDto.expiresAt) : null,
                    tenantId,
                },
            });
        }
    }

    async getFileAccessLogs(
        fileId: string,
        tenantId: string,
        workspaceId?: string,
        pagination?: { page?: number; limit?: number },
    ) {
        const page = pagination?.page || 1;
        const limit = pagination?.limit || 50;
        const skip = (page - 1) * limit;

        const [logs, total] = await Promise.all([
            this.prisma.file_access_logs.findMany({
                where: {
                    fileId,
                    tenantId,
                },
                orderBy: { accessedAt: 'desc' },
                skip,
                take: limit,
            }),
            this.prisma.file_access_logs.count({
                where: {
                    fileId,
                    tenantId,
                },
            }),
        ]);

        return {
            logs,
            pagination: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit),
            },
        };
    }

    private async validateFile(file: Express.Multer.File, storageSettings: any) {
        // 檢查檔案大小
        const maxFileSize = (storageSettings.maxFileSize || 100) * 1024 * 1024; // MB to bytes
        if (file.size > maxFileSize) {
            throw new BadRequestException(
                `File size exceeds limit of ${storageSettings.maxFileSize}MB`
            );
        }

        // 檢查檔案類型
        if (storageSettings.allowedFileTypes && storageSettings.allowedFileTypes.length > 0) {
            const fileExtension = path.extname(file.originalname).toLowerCase();
            if (!storageSettings.allowedFileTypes.includes(fileExtension)) {
                throw new BadRequestException(
                    `File type ${fileExtension} is not allowed`
                );
            }
        }
    }

    private determineFileCategory(mimeType: string): FileCategory {
        if (mimeType.startsWith('image/')) return FileCategory.IMAGE;
        if (mimeType.startsWith('video/')) return FileCategory.VIDEO;
        if (mimeType.startsWith('audio/')) return FileCategory.AUDIO;
        if (mimeType.includes('pdf')) return FileCategory.DOCUMENT;
        if (mimeType.includes('spreadsheet') || mimeType.includes('excel')) return FileCategory.SPREADSHEET;
        if (mimeType.includes('presentation') || mimeType.includes('powerpoint')) return FileCategory.PRESENTATION;
        if (mimeType.includes('zip') || mimeType.includes('rar') || mimeType.includes('tar')) return FileCategory.ARCHIVE;
        return FileCategory.DOCUMENT;
    }

    private async generateImagePreviews(file: Express.Multer.File, fileName: string) {
        try {
            // 動態導入 Sharp
            const sharp = await import('sharp').then(m => m.default);

            const baseName = path.parse(fileName).name;

            // 生成縮圖 (200x200)
            const thumbnailBuffer = await sharp(file.buffer)
                .resize(200, 200, { fit: 'cover' })
                .jpeg({ quality: 80 })
                .toBuffer();

            const thumbnailFileName = `${baseName}_thumb.jpg`;
            const thumbnailPath = await this.storageService.uploadFile({
                ...file,
                buffer: thumbnailBuffer,
                originalname: thumbnailFileName,
            });

            // 生成預覽 (800x600)
            const previewBuffer = await sharp(file.buffer)
                .resize(800, 600, { fit: 'inside', withoutEnlargement: true })
                .jpeg({ quality: 85 })
                .toBuffer();

            const previewFileName = `${baseName}_preview.jpg`;
            const previewPath = await this.storageService.uploadFile({
                ...file,
                buffer: previewBuffer,
                originalname: previewFileName,
            });

            // 獲取圖片元數據
            const metadata = await sharp(file.buffer).metadata();

            return {
                thumbnailPath,
                previewPath,
                metadata: {
                    width: metadata.width,
                    height: metadata.height,
                    format: metadata.format,
                    size: file.size,
                },
            };
        } catch (error) {
            console.error('Failed to generate image previews:', error);
            console.warn('Sharp module not available, skipping image preview generation');
            return {
                thumbnailPath: undefined,
                previewPath: undefined,
                metadata: {},
            };
        }
    }

    private async validateEntityAccess(
        entityType: FileEntityType,
        entityId: string,
        tenantId: string,
        workspaceId?: string,
    ) {
        // 根據實體類型驗證存取權限
        switch (entityType) {
            case FileEntityType.PROJECT:
                const project = await this.prisma.projects.findFirst({
                    where: {
                        id: entityId,
                        tenantId,
                        ...(workspaceId && { workspaceId }),
                    },
                });
                if (!project) {
                    throw new NotFoundException('Project not found');
                }
                break;

            case FileEntityType.TASK:
                const task = await this.prisma.tasks.findFirst({
                    where: {
                        id: entityId,
                        project: {
                            tenantId,
                            ...(workspaceId && { workspaceId }),
                        },
                    },
                });
                if (!task) {
                    throw new NotFoundException('Task not found');
                }
                break;

            // 其他實體類型的驗證...
        }
    }

    private async checkFileAccess(
        file: any,
        userId: string,
        userType: string,
        requiredPermission: FilePermission,
    ): Promise<boolean> {
        // 檔案上傳者總是有完整權限
        if (file.uploaderId === userId && file.uploaderType === userType) {
            return true;
        }

        // 檢查可見性設定
        switch (file.visibility) {
            case FileVisibility.PUBLIC:
                return true;
            case FileVisibility.TENANT:
                // 租戶內用戶可存取
                return true;
            case FileVisibility.WORKSPACE:
                // 工作區成員可存取（需要額外檢查）
                return true;
            case FileVisibility.PRIVATE:
                // 需要明確權限
                break;
        }

        // 檢查明確權限
        const permission = await this.prisma.file_permissions.findFirst({
            where: {
                fileId: file.id,
                userId,
                userType,
                OR: [
                    { expiresAt: null },
                    { expiresAt: { gt: new Date() } },
                ],
            },
        });

        if (!permission) {
            return false;
        }

        // 檢查權限層級
        const permissionHierarchy = {
            [FilePermission.VIEW]: 1,
            [FilePermission.DOWNLOAD]: 2,
            [FilePermission.COMMENT]: 3,
            [FilePermission.EDIT]: 4,
            [FilePermission.SHARE]: 5,
            [FilePermission.DELETE]: 6,
            [FilePermission.MANAGE]: 7,
        };

        return permissionHierarchy[permission.permission] >= permissionHierarchy[requiredPermission];
    }

    private async logFileAccess(
        fileId: string,
        accessType: AccessType,
        userId?: string,
        userType?: string,
        tenantId?: string,
        ipAddress?: string,
        shareId?: string,
        userAgent?: string,
    ) {
        try {
            await this.prisma.file_access_logs.create({
                data: {
                    fileId,
                    shareId,
                    userId,
                    userType,
                    ipAddress: ipAddress || 'unknown',
                    userAgent,
                    accessType,
                    tenantId: tenantId || 'unknown',
                },
            });
        } catch (error) {
            console.error('Failed to log file access:', error);
        }
    }
} 