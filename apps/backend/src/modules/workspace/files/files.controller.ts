import {
    Controller,
    Post,
    Get,
    Put,
    Delete,
    Param,
    Body,
    Query,
    UploadedFile,
    UseInterceptors,
    UseGuards,
    Request,
    Response,
    StreamableFile,
    ParseIntPipe,
    BadRequestException,
    NotFoundException,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { JwtAuthGuard } from '../../core/auth/guards/auth.guard';
import { WorkspaceGuard } from '../../admin/workspaces/guards/workspace.guard';
import { PoliciesGuard } from '../../../casl/guards/permission.guard';
import { CheckPolicies } from '../../../casl/decorators/check-policies.decorator';
import { AppAbility } from '../../../types/models/casl.model';
import { Actions, Subjects } from '@horizai/permissions';
import { FilesService } from './files.service';
import {
    UploadFileDto,
    UpdateFileDto,
    CreateShareDto,
    GrantFilePermissionDto,
    BulkGrantPermissionDto,
} from './dto';
import { FilePermission, ShareType } from '@prisma/client';
import { createReadStream } from 'fs';
import { join } from 'path';

@Controller('workspace/:workspaceId/files')
@UseGuards(JwtAuthGuard, WorkspaceGuard, PoliciesGuard)
export class FilesController {
    constructor(private readonly filesService: FilesService) { }

    @Post('upload')
    @UseInterceptors(FileInterceptor('file'))
    @CheckPolicies((ability: AppAbility) => ability.can(Actions.CREATE, Subjects.SHARED_FILE))
    async uploadFile(
        @Param('workspaceId') workspaceId: string,
        @UploadedFile() file: Express.Multer.File,
        @Body() uploadFileDto: UploadFileDto,
        @Request() req: any,
    ) {
        if (!file) {
            throw new BadRequestException('No file provided');
        }

        return this.filesService.uploadFile(
            workspaceId,
            req.user.id,
            file,
            uploadFileDto,
        );
    }

    @Get()
    @CheckPolicies((ability: AppAbility) => ability.can(Actions.READ, Subjects.SHARED_FILE))
    async getFiles(
        @Param('workspaceId') workspaceId: string,
        @Request() req: any,
        @Query('entityType') entityType?: string,
        @Query('entityId') entityId?: string,
        @Query('category') category?: string,
        @Query('search') search?: string,
        @Query('page') page: number = 1,
        @Query('limit') limit: number = 20,
    ) {
        return this.filesService.getFiles(workspaceId, req.user.id, {
            entityType,
            entityId,
            category,
            search,
            page: Number(page),
            limit: Number(limit),
        });
    }

    @Get(':fileId')
    @CheckPolicies((ability: AppAbility) => ability.can(Actions.READ, Subjects.SHARED_FILE))
    async getFile(
        @Param('workspaceId') workspaceId: string,
        @Param('fileId') fileId: string,
        @Request() req: any,
    ) {
        return this.filesService.getFile(workspaceId, fileId, req.user.id);
    }

    @Put(':fileId')
    @CheckPolicies((ability: AppAbility) => ability.can(Actions.UPDATE, Subjects.SHARED_FILE))
    async updateFile(
        @Param('workspaceId') workspaceId: string,
        @Param('fileId') fileId: string,
        @Body() updateFileDto: UpdateFileDto,
        @Request() req: any,
    ) {
        return this.filesService.updateFile(
            workspaceId,
            fileId,
            req.user.id,
            updateFileDto,
        );
    }

    @Delete(':fileId')
    @CheckPolicies((ability: AppAbility) => ability.can(Actions.DELETE, Subjects.SHARED_FILE))
    async deleteFile(
        @Param('workspaceId') workspaceId: string,
        @Param('fileId') fileId: string,
        @Request() req: any,
    ) {
        await this.filesService.deleteFile(workspaceId, fileId, req.user.id);
        return { message: 'File deleted successfully' };
    }

    @Get(':fileId/download')
    async downloadFile(
        @Param('workspaceId') workspaceId: string,
        @Param('fileId') fileId: string,
        @Request() req: any,
        @Response({ passthrough: true }) res: any,
        @Query('version') version?: string,
    ) {
        const { file, stream } = await this.filesService.downloadFile(
            workspaceId,
            fileId,
            req.user.id,
            version ? Number(version) : undefined,
        );

        res.set({
            'Content-Type': file.mimeType,
            'Content-Disposition': `attachment; filename="${file.originalName}"`,
            'Content-Length': file.fileSize,
        });

        return new StreamableFile(stream);
    }

    @Get(':fileId/preview')
    async previewFile(
        @Param('workspaceId') workspaceId: string,
        @Param('fileId') fileId: string,
        @Query('size') size: string = 'medium',
        @Request() req: any,
        @Response({ passthrough: true }) res: any,
    ) {
        const { file, stream } = await this.filesService.getFilePreview(
            workspaceId,
            fileId,
            req.user.id,
            size as 'small' | 'medium' | 'large',
        );

        res.set({
            'Content-Type': file.mimeType,
            'Cache-Control': 'public, max-age=3600',
        });

        return new StreamableFile(stream);
    }

    @Get(':fileId/versions')
    async getFileVersions(
        @Param('workspaceId') workspaceId: string,
        @Param('fileId') fileId: string,
        @Request() req: any,
    ) {
        return this.filesService.getFileVersions(workspaceId, fileId, req.user.id);
    }

    @Post(':fileId/versions')
    @UseInterceptors(FileInterceptor('file'))
    async uploadNewVersion(
        @Param('workspaceId') workspaceId: string,
        @Param('fileId') fileId: string,
        @UploadedFile() file: Express.Multer.File,
        @Request() req: any,
        @Body('description') description?: string,
    ) {
        if (!file) {
            throw new BadRequestException('No file provided');
        }

        return this.filesService.uploadNewVersion(
            workspaceId,
            fileId,
            req.user.id,
            file,
            description,
        );
    }

    @Post(':fileId/permissions')
    @CheckPolicies((ability: AppAbility) => ability.can(Actions.CREATE, Subjects.FILE_PERMISSION))
    async grantPermission(
        @Param('workspaceId') workspaceId: string,
        @Param('fileId') fileId: string,
        @Body() grantPermissionDto: GrantFilePermissionDto,
        @Request() req: any,
    ) {
        return this.filesService.grantPermission(
            fileId,
            grantPermissionDto,
            req.user.id,
            workspaceId,
        );
    }

    @Post(':fileId/permissions/bulk')
    async bulkGrantPermissions(
        @Param('workspaceId') workspaceId: string,
        @Param('fileId') fileId: string,
        @Body() bulkGrantDto: BulkGrantPermissionDto,
        @Request() req: any,
    ) {
        return this.filesService.bulkGrantPermissions(
            workspaceId,
            fileId,
            req.user.id,
            bulkGrantDto,
        );
    }

    @Get(':fileId/permissions')
    async getFilePermissions(
        @Param('workspaceId') workspaceId: string,
        @Param('fileId') fileId: string,
        @Request() req: any,
    ) {
        return this.filesService.getFilePermissions(workspaceId, fileId, req.user.id);
    }

    @Delete(':fileId/permissions/:permissionId')
    async revokePermission(
        @Param('workspaceId') workspaceId: string,
        @Param('fileId') fileId: string,
        @Param('permissionId') permissionId: string,
        @Request() req: any,
    ) {
        await this.filesService.revokePermission(
            workspaceId,
            fileId,
            permissionId,
            req.user.id,
        );
        return { message: 'Permission revoked successfully' };
    }

    @Post(':fileId/shares')
    @CheckPolicies((ability: AppAbility) => ability.can(Actions.CREATE, Subjects.FILE_SHARE))
    async createShare(
        @Param('workspaceId') workspaceId: string,
        @Param('fileId') fileId: string,
        @Body() createShareDto: CreateShareDto,
        @Request() req: any,
    ) {
        return this.filesService.createFileShare(
            workspaceId,
            fileId,
            req.user.id,
            createShareDto,
        );
    }

    @Get(':fileId/shares')
    @CheckPolicies((ability: AppAbility) => ability.can(Actions.READ, Subjects.FILE_SHARE))
    async getFileShares(
        @Param('workspaceId') workspaceId: string,
        @Param('fileId') fileId: string,
        @Request() req: any,
    ) {
        return this.filesService.getFileShares(workspaceId, fileId, req.user.id);
    }

    @Delete(':fileId/shares/:shareId')
    @CheckPolicies((ability: AppAbility) => ability.can(Actions.DELETE, Subjects.FILE_SHARE))
    async deleteShare(
        @Param('workspaceId') workspaceId: string,
        @Param('fileId') fileId: string,
        @Param('shareId') shareId: string,
        @Request() req: any,
    ) {
        await this.filesService.deleteFileShare(workspaceId, fileId, shareId, req.user.id);
        return { message: 'Share deleted successfully' };
    }

    @Get(':fileId/access-logs')
    async getAccessLogs(
        @Param('workspaceId') workspaceId: string,
        @Param('fileId') fileId: string,
        @Query('page') page: number = 1,
        @Query('limit') limit: number = 50,
        @Request() req: any,
    ) {
        return this.filesService.getAccessLogs(workspaceId, fileId, req.user.id, {
            page: Number(page),
            limit: Number(limit),
        });
    }
}

// 公開分享連結控制器 (不需要認證)
@Controller('shared')
export class SharedFilesController {
    constructor(private readonly filesService: FilesService) { }

    @Get(':shareToken')
    async getSharedFile(@Param('shareToken') shareToken: string) {
        return this.filesService.getSharedFile(shareToken);
    }

    @Get(':shareToken/download')
    async downloadSharedFile(
        @Param('shareToken') shareToken: string,
        @Response({ passthrough: true }) res: any,
        @Query('password') password?: string,
    ) {
        const { file, stream } = await this.filesService.downloadSharedFile(
            shareToken,
            password,
        );

        res.set({
            'Content-Type': file.mimeType,
            'Content-Disposition': `attachment; filename="${file.originalName}"`,
            'Content-Length': file.fileSize,
        });

        return new StreamableFile(stream);
    }

    @Get(':shareToken/preview')
    async previewSharedFile(
        @Param('shareToken') shareToken: string,
        @Response({ passthrough: true }) res: any,
        @Query('password') password?: string,
        @Query('size') size: string = 'medium',
    ) {
        const { file, stream } = await this.filesService.previewSharedFile(
            shareToken,
            password,
            size as 'small' | 'medium' | 'large',
        );

        res.set({
            'Content-Type': file.mimeType,
            'Cache-Control': 'public, max-age=3600',
        });

        return new StreamableFile(stream);
    }
} 