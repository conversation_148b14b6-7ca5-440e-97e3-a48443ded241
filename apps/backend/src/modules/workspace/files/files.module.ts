import { Modu<PERSON> } from '@nestjs/common';
import { MulterModule } from '@nestjs/platform-express';
import { FilesController, SharedFilesController } from './files.controller';
import { FilesService } from './files.service';
import { PrismaModule } from '../../core/prisma/prisma.module';
import { StorageModule } from '../../core/storage/storage.module';
import { SettingsModule } from '../../admin/settings/settings.module';
import { WorkspacesModule } from '../../admin/workspaces/workspaces.module';
import { CaslModule } from '../../../casl/casl.module';
import { multerConfig } from '../../../common/config/multer.config';

@Module({
    imports: [
        PrismaModule,
        StorageModule,
        SettingsModule,
        WorkspacesModule,
        CaslModule,
        MulterModule.register(multerConfig),
    ],
    controllers: [FilesController, SharedFilesController],
    providers: [FilesService],
    exports: [FilesService],
})
export class FilesModule { } 