import { IsString, IsOptional, <PERSON>NotEmpty, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>UUID } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateProjectDto {
    @ApiProperty({
        description: '專案名稱',
        example: '新產品開發專案',
        minLength: 1,
        maxLength: 100
    })
    @IsString()
    @IsNotEmpty()
    @MinLength(1)
    @MaxLength(100)
    name: string;

    @ApiPropertyOptional({
        description: '專案描述',
        example: '開發新的 SaaS 產品，包含前後端功能',
        maxLength: 500
    })
    @IsOptional()
    @IsString()
    @MaxLength(500)
    description?: string;

    @ApiPropertyOptional({
        description: '專案狀態',
        example: 'planning',
        enum: ['planning', 'active', 'on-hold', 'completed', 'cancelled']
    })
    @IsOptional()
    @IsString()
    status?: 'planning' | 'active' | 'on-hold' | 'completed' | 'cancelled';

    @ApiPropertyOptional({
        description: '專案開始日期',
        example: '2024-01-01T00:00:00.000Z'
    })
    @IsOptional()
    startDate?: Date;

    @ApiPropertyOptional({
        description: '專案結束日期',
        example: '2024-12-31T23:59:59.999Z'
    })
    @IsOptional()
    endDate?: Date;

    @ApiPropertyOptional({
        description: '專案預算',
        example: 100000
    })
    @IsOptional()
    budget?: number;

    @ApiPropertyOptional({
        description: '專案優先級',
        example: 'high',
        enum: ['low', 'medium', 'high', 'urgent']
    })
    @IsOptional()
    @IsString()
    priority?: 'low' | 'medium' | 'high' | 'urgent';

    @ApiPropertyOptional({
        description: '父專案 ID（用於建立子專案）',
        example: 'clxxxxxxxxxxxxx'
    })
    @IsOptional()
    @IsUUID()
    parentProjectId?: string;
} 