import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Req,
  UnauthorizedException,
  Query,
  ValidationPipe,
  ParseIntPipe,
  DefaultValuePipe
} from '@nestjs/common';
import { ProjectsService } from './projects.service';
import { CreateProjectDto, UpdateProjectDto, ProjectResponseDto, MoveProjectDto } from './dto';
import { Request } from 'express';
import { ApiTags, ApiBearerAuth, ApiOperation, ApiResponse, ApiQuery } from '@nestjs/swagger';

// 擴展 Request 介面
interface RequestWithUser extends Request {
  user?: {
    id: string;
    tenantId: string;
    [key: string]: any;
  };
}

@ApiTags('workspace/projects')
@ApiBearerAuth()
@Controller('workspace/projects')
export class ProjectsController {
  constructor(private readonly projectsService: ProjectsService) { }

  @Post()
  @ApiOperation({ summary: '創建新專案' })
  @ApiResponse({
    status: 201,
    description: '專案創建成功',
    type: ProjectResponseDto
  })
  @ApiResponse({ status: 400, description: '請求參數錯誤' })
  @ApiResponse({ status: 401, description: '未授權' })
  async create(
    @Body(ValidationPipe) createProjectDto: CreateProjectDto,
    @Req() req: RequestWithUser
  ): Promise<ProjectResponseDto> {
    const userId = req.user?.id;
    const tenantId = req.user?.tenantId;

    if (!userId || !tenantId) {
      throw new UnauthorizedException('使用者未登入或缺少租戶資訊');
    }

    return this.projectsService.create(createProjectDto, userId, tenantId);
  }

  @Get()
  @ApiOperation({ summary: '獲取專案列表' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: '頁碼' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: '每頁數量' })
  @ApiQuery({ name: 'search', required: false, type: String, description: '搜尋關鍵字' })
  @ApiQuery({ name: 'status', required: false, type: String, description: '專案狀態' })
  @ApiQuery({ name: 'priority', required: false, type: String, description: '專案優先級' })
  @ApiResponse({
    status: 200,
    description: '專案列表獲取成功',
    schema: {
      type: 'object',
      properties: {
        projects: { type: 'array', items: { $ref: '#/components/schemas/ProjectResponseDto' } },
        total: { type: 'number' },
        page: { type: 'number' },
        limit: { type: 'number' },
        totalPages: { type: 'number' }
      }
    }
  })
  async findAll(
    @Req() req: RequestWithUser,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('search') search?: string,
    @Query('status') status?: string,
    @Query('priority') priority?: string
  ) {
    const tenantId = req.user?.tenantId;

    if (!tenantId) {
      throw new UnauthorizedException('缺少租戶資訊');
    }

    return this.projectsService.findAll(tenantId, page, limit, search, status, priority);
  }

  @Get('hierarchy')
  @ApiOperation({ summary: '獲取專案階層樹狀結構' })
  @ApiResponse({
    status: 200,
    description: '專案階層結構獲取成功',
    type: [ProjectResponseDto]
  })
  async getHierarchy(@Req() req: RequestWithUser): Promise<ProjectResponseDto[]> {
    const tenantId = req.user?.tenantId;

    if (!tenantId) {
      throw new UnauthorizedException('缺少租戶資訊');
    }

    return this.projectsService.getProjectHierarchy(tenantId);
  }

  @Get('stats')
  @ApiOperation({ summary: '獲取專案統計資料' })
  @ApiResponse({
    status: 200,
    description: '專案統計資料獲取成功',
    schema: {
      type: 'object',
      properties: {
        total: { type: 'number' },
        byStatus: { type: 'object' },
        byPriority: { type: 'object' }
      }
    }
  })
  async getStats(@Req() req: RequestWithUser) {
    const tenantId = req.user?.tenantId;

    if (!tenantId) {
      throw new UnauthorizedException('缺少租戶資訊');
    }

    return this.projectsService.getProjectStats(tenantId);
  }

  @Get(':id/detailed-stats')
  @ApiOperation({ summary: '獲取專案詳細統計資料' })
  @ApiResponse({
    status: 200,
    description: '專案詳細統計資料獲取成功',
    schema: {
      type: 'object',
      properties: {
        project: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            name: { type: 'string' },
            status: { type: 'string' },
            priority: { type: 'string' }
          }
        },
        tasks: {
          type: 'object',
          properties: {
            total: { type: 'number' },
            completed: { type: 'number' },
            inProgress: { type: 'number' },
            pending: { type: 'number' },
            overdue: { type: 'number' },
            completionRate: { type: 'number' }
          }
        },
        progress: {
          type: 'object',
          properties: {
            totalEntries: { type: 'number' },
            latestProgress: { type: 'number', nullable: true },
            averageProgress: { type: 'number', nullable: true }
          }
        },
        milestones: {
          type: 'object',
          properties: {
            total: { type: 'number' },
            completed: { type: 'number' },
            pending: { type: 'number' },
            overdue: { type: 'number' }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 404, description: '專案不存在' })
  async getDetailedStats(
    @Param('id') id: string,
    @Req() req: RequestWithUser
  ) {
    const tenantId = req.user?.tenantId;

    if (!tenantId) {
      throw new UnauthorizedException('缺少租戶資訊');
    }

    return this.projectsService.getProjectDetailedStats(id, tenantId);
  }

  @Get(':id')
  @ApiOperation({ summary: '獲取單一專案詳情' })
  @ApiResponse({
    status: 200,
    description: '專案詳情獲取成功',
    type: ProjectResponseDto
  })
  @ApiResponse({ status: 404, description: '專案不存在' })
  async findOne(
    @Param('id') id: string,
    @Req() req: RequestWithUser
  ): Promise<ProjectResponseDto> {
    const tenantId = req.user?.tenantId;

    if (!tenantId) {
      throw new UnauthorizedException('缺少租戶資訊');
    }

    return this.projectsService.findOne(id, tenantId);
  }

  @Patch(':id')
  @ApiOperation({ summary: '更新專案' })
  @ApiResponse({
    status: 200,
    description: '專案更新成功',
    type: ProjectResponseDto
  })
  @ApiResponse({ status: 400, description: '請求參數錯誤' })
  @ApiResponse({ status: 404, description: '專案不存在' })
  async update(
    @Param('id') id: string,
    @Body(ValidationPipe) updateProjectDto: UpdateProjectDto,
    @Req() req: RequestWithUser
  ): Promise<ProjectResponseDto> {
    const userId = req.user?.id;
    const tenantId = req.user?.tenantId;

    if (!userId || !tenantId) {
      throw new UnauthorizedException('使用者未登入或缺少租戶資訊');
    }

    return this.projectsService.update(id, updateProjectDto, userId, tenantId);
  }

  @Get(':id/sub-projects')
  @ApiOperation({ summary: '獲取專案的所有子專案' })
  @ApiResponse({
    status: 200,
    description: '子專案列表獲取成功',
    type: [ProjectResponseDto]
  })
  @ApiResponse({ status: 404, description: '專案不存在' })
  async getSubProjects(
    @Param('id') id: string,
    @Req() req: RequestWithUser
  ): Promise<ProjectResponseDto[]> {
    const tenantId = req.user?.tenantId;

    if (!tenantId) {
      throw new UnauthorizedException('缺少租戶資訊');
    }

    return this.projectsService.getSubProjects(id, tenantId);
  }

  @Patch(':id/move')
  @ApiOperation({ summary: '移動專案到新的父專案下' })
  @ApiResponse({
    status: 200,
    description: '專案移動成功',
    type: ProjectResponseDto
  })
  @ApiResponse({ status: 400, description: '請求參數錯誤或會造成循環引用' })
  @ApiResponse({ status: 404, description: '專案不存在' })
  async moveProject(
    @Param('id') id: string,
    @Body(ValidationPipe) moveProjectDto: MoveProjectDto,
    @Req() req: RequestWithUser
  ): Promise<ProjectResponseDto> {
    const tenantId = req.user?.tenantId;

    if (!tenantId) {
      throw new UnauthorizedException('缺少租戶資訊');
    }

    return this.projectsService.moveProject(id, moveProjectDto.newParentProjectId || null, tenantId);
  }

  @Delete(':id')
  @ApiOperation({ summary: '刪除專案' })
  @ApiResponse({ status: 200, description: '專案刪除成功' })
  @ApiResponse({ status: 404, description: '專案不存在' })
  async remove(
    @Param('id') id: string,
    @Req() req: RequestWithUser
  ): Promise<{ message: string }> {
    const tenantId = req.user?.tenantId;

    if (!tenantId) {
      throw new UnauthorizedException('缺少租戶資訊');
    }

    await this.projectsService.remove(id, tenantId);
    return { message: '專案刪除成功' };
  }
} 