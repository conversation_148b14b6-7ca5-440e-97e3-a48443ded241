import { Injectable, Logger, NotFoundException } from "@nestjs/common";
import { PrismaService } from "@/modules/core/prisma/prisma.service";
import { AiBotsService } from "@/modules/admin/ai/bots/ai-bots.service";

@Injectable()
export class AiExecutionService {
  private readonly logger = new Logger(AiExecutionService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly botExecutionService: AiBotsService
  ) {}

  async executeService(
    definitionId: string,
    inputData: Record<string, any>,
    userId: string,
    workspaceId?: string
  ) {
    this.logger.log(
      `Starting execution for service definition: ${definitionId} by user: ${userId}`
    );

    try {
      // 簡化的執行邏輯
      const result = {
        executionId: `exec_${Date.now()}`,
        result: inputData,
        status: "completed",
      };

      this.logger.log(
        `Successfully completed execution: ${result.executionId}`
      );
      return result;
    } catch (error) {
      this.logger.error(
        `Failed execution for definition ${definitionId}: ${error.message}`,
        error.stack
      );
      throw error;
    }
  }
}
