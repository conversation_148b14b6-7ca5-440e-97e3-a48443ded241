import {
  Controller,
  Post,
  Body,
  UseGuards,
  Req,
  Get,
  Param,
  Put,
  Delete,
  Query,
} from "@nestjs/common";
import {
  ApiTags,
  ApiOperation,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from "@nestjs/swagger";
import { WorkspaceAiBotsService } from "./workspace-ai-bots.service";
import { WorkspaceChatDto } from "./dto/workspace-ai-bots-chat.dto";
import {
  CreateWorkspaceAiBotDto,
  UpdateWorkspaceAiBotDto,
} from "./dto/workspace-ai-bot.dto";
import { JwtAuthGuard } from "@/modules/core/auth/guards/auth.guard";
import { AuthenticatedRequest } from "@/types/express";

@ApiTags("workspace/ai")
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller("tenants/:tenantId/workspace/ai")
export class WorkspaceAiBotsController {
  constructor(
    private readonly workspaceAiBotsService: WorkspaceAiBotsService
  ) {}

  @Get("bots")
  @ApiOperation({ summary: "取得租戶的 AI Bot 列表" })
  @ApiParam({ name: "tenantId", description: "租戶 ID" })
  @ApiQuery({ name: "page", required: false, description: "頁碼" })
  @ApiQuery({ name: "limit", required: false, description: "每頁數量" })
  async getBots(
    @Param("tenantId") tenantId: string,
    @Req() req: AuthenticatedRequest,
    @Query("page") page?: number,
    @Query("limit") limit?: number
  ) {
    return this.workspaceAiBotsService.getBots(tenantId, req.user.id, {
      page,
      limit,
    });
  }

  @Get("bots/:botId")
  @ApiOperation({ summary: "取得特定 AI Bot 的詳細資訊" })
  @ApiParam({ name: "tenantId", description: "租戶 ID" })
  @ApiParam({ name: "botId", description: "Bot ID" })
  async getBot(
    @Param("tenantId") tenantId: string,
    @Param("botId") botId: string,
    @Req() req: AuthenticatedRequest
  ) {
    return this.workspaceAiBotsService.getBot(tenantId, botId, req.user.id);
  }

  @Post("bots")
  @ApiOperation({ summary: "建立新的 AI Bot" })
  @ApiParam({ name: "tenantId", description: "租戶 ID" })
  async createBot(
    @Param("tenantId") tenantId: string,
    @Body() dto: CreateWorkspaceAiBotDto,
    @Req() req: AuthenticatedRequest
  ) {
    return this.workspaceAiBotsService.createBot(tenantId, dto, req.user.id);
  }

  @Put("bots/:botId")
  @ApiOperation({ summary: "更新 AI Bot 設定" })
  @ApiParam({ name: "tenantId", description: "租戶 ID" })
  @ApiParam({ name: "botId", description: "Bot ID" })
  async updateBot(
    @Param("tenantId") tenantId: string,
    @Param("botId") botId: string,
    @Body() dto: UpdateWorkspaceAiBotDto,
    @Req() req: AuthenticatedRequest
  ) {
    return this.workspaceAiBotsService.updateBot(
      tenantId,
      botId,
      dto,
      req.user.id
    );
  }

  @Delete("bots/:botId")
  @ApiOperation({ summary: "刪除 AI Bot" })
  @ApiParam({ name: "tenantId", description: "租戶 ID" })
  @ApiParam({ name: "botId", description: "Bot ID" })
  async deleteBot(
    @Param("tenantId") tenantId: string,
    @Param("botId") botId: string,
    @Req() req: AuthenticatedRequest
  ) {
    return this.workspaceAiBotsService.deleteBot(tenantId, botId, req.user.id);
  }

  @Post("bots/:botId/chat")
  @ApiOperation({ summary: "與指定的 AI Bot 進行對話" })
  @ApiParam({ name: "tenantId", description: "租戶 ID" })
  @ApiParam({ name: "botId", description: "Bot ID" })
  async chat(
    @Param("tenantId") tenantId: string,
    @Param("botId") botId: string,
    @Body() dto: WorkspaceChatDto,
    @Req() req: AuthenticatedRequest
  ) {
    return this.workspaceAiBotsService.chatWithBot(
      tenantId,
      botId,
      dto,
      req.user.id
    );
  }
}
