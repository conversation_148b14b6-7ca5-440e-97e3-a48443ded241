import { ApiProperty, ApiPropertyOptional, PartialType } from '@nestjs/swagger'
import { IsString, IsOptional, IsNumber, IsEnum, Min, Max, IsBoolean, IsObject, IsNotEmpty } from 'class-validator'
import { Type } from 'class-transformer'
import { AiBotProviderType, AiBotScope, AiBotResponseFormat } from '@prisma/client'

export class CreateWorkspaceAiBotDto {
  @ApiProperty({ description: 'Bot 名稱' })
  @IsString()
  @IsNotEmpty()
  name: string

  @ApiPropertyOptional({ description: 'Bot 描述' })
  @IsString()
  @IsOptional()
  description?: string

  @ApiProperty({ description: 'Bot 範圍', enum: AiBotScope })
  @IsEnum(AiBotScope)
  scope: AiBotScope

  @ApiProperty({ description: 'AI Provider 類型', enum: AiBotProviderType })
  @IsEnum(AiBotProviderType)
  provider_type: AiBotProviderType

  @ApiProperty({ description: 'AI Model ID' })
  @IsString()
  @IsNotEmpty()
  model_id: string

  @ApiProperty({ description: 'API Key ID' })
  @IsString()
  @IsNotEmpty()
  key_id: string

  @ApiPropertyOptional({ description: 'Provider 配置覆蓋' })
  @IsObject()
  @IsOptional()
  provider_config_override?: Record<string, any>

  @ApiPropertyOptional({ description: '系統提示詞' })
  @IsString()
  @IsOptional()
  system_prompt?: string

  @ApiPropertyOptional({ description: '溫度', minimum: 0, maximum: 1 })
  @IsNumber()
  @Min(0)
  @Max(1)
  @IsOptional()
  @Type(() => Number)
  temperature?: number

  @ApiPropertyOptional({ description: '最大輸出 Token 數' })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  max_tokens?: number

  @ApiPropertyOptional({ description: '回應格式', enum: AiBotResponseFormat })
  @IsEnum(AiBotResponseFormat)
  @IsOptional()
  response_format?: AiBotResponseFormat

  @ApiPropertyOptional({ description: '是否為範本' })
  @IsBoolean()
  @IsOptional()
  is_template?: boolean

  @ApiPropertyOptional({ description: '場景' })
  @IsString()
  @IsOptional()
  scene?: string

  @ApiPropertyOptional({ description: '租戶 ID' })
  @IsString()
  @IsOptional()
  tenant_id?: string

  @ApiPropertyOptional({ description: '工作區 ID' })
  @IsString()
  @IsOptional()
  workspace_id?: string
}

export class UpdateWorkspaceAiBotDto extends PartialType(CreateWorkspaceAiBotDto) {} 