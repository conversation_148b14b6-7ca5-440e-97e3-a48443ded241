import { ApiProperty } from '@nestjs/swagger'
import { IsString, IsUUID, IsNotEmpty, IsArray, ValidateNested, IsOptional } from 'class-validator'
import { Type } from 'class-transformer'

export class ChatMessageDto {
  @ApiProperty({ description: '角色 (user)', example: 'user' })
  @IsString()
  @IsNotEmpty()
  role: 'user' // Workspace chat 只允許傳 user 訊息

  @ApiProperty({ description: '訊息內容' })
  @IsString()
  @IsNotEmpty()
  content: string
}

export class WorkspaceChatDto {
  @ApiProperty({ description: '目標 Bot ID', format: 'uuid' })
  @IsUUID()
  botId: string

  // 支援單一訊息或多輪對話歷史
  @ApiProperty({ description: '使用者訊息或對話歷史', type: [ChatMessageDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ChatMessageDto)
  messages: ChatMessageDto[]

  @ApiProperty({ description: '臨時溫度設定 (覆蓋 Bot 設定)', required: false })
  @IsOptional()
  @IsString() // Class validator bug? Need string for number validation? Check later.
  temperature?: number;

} 