import { Modu<PERSON> } from "@nestjs/common";
import { WorkspaceAiBotsModule } from "./bots/workspace-ai-bots.module";
import { AiExecutionsModule } from "./executions/ai-executions.module";
import { WorkspaceAiIntegrationsModule } from "./integrations/workspace-ai-integrations.module";

@Module({
  imports: [
    WorkspaceAiBotsModule,
    AiExecutionsModule,
    WorkspaceAiIntegrationsModule,
  ],
  exports: [WorkspaceAiBotsModule, AiExecutionsModule],
})
export class WorkspaceAiModule {}
