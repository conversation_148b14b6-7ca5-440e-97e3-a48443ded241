import { IsE<PERSON>, IsOptional, IsString, IsUUID, MaxLength, IsArray, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
// 定義訊息類型枚舉
enum MessageType {
    TEXT = 'TEXT',
    IMAGE = 'IMAGE',
    FILE = 'FILE',
    AUDIO = 'AUDIO',
    VIDEO = 'VIDEO',
    SYSTEM = 'SYSTEM',
}

export class MessageAttachmentDto {
    @ApiProperty({
        description: '附件名稱',
        example: 'document.pdf',
    })
    @IsString()
    fileName: string;

    @ApiProperty({
        description: '附件類型 (MIME type)',
        example: 'application/pdf',
    })
    @IsString()
    fileType: string;

    @ApiProperty({
        description: '附件大小 (bytes)',
        example: 1024000,
    })
    fileSize: number;

    @ApiProperty({
        description: '附件 URL',
        example: 'https://example.com/files/document.pdf',
    })
    @IsString()
    fileUrl: string;

    @ApiPropertyOptional({
        description: '縮圖 URL',
        example: 'https://example.com/thumbnails/document.jpg',
    })
    @IsOptional()
    @IsString()
    thumbnailUrl?: string;
}

export class SendMessageDto {
    @ApiProperty({
        description: '訊息類型',
        enum: MessageType,
        example: MessageType.TEXT,
    })
    @IsEnum(MessageType)
    type: MessageType;

    @ApiProperty({
        description: '訊息內容',
        example: '你好，這是一條測試訊息',
        maxLength: 4000,
    })
    @IsString()
    @MaxLength(4000)
    content: string;

    @ApiPropertyOptional({
        description: '回覆的訊息 ID',
        example: 'message-uuid',
    })
    @IsOptional()
    @IsUUID('4')
    replyToId?: string;

    @ApiPropertyOptional({
        description: '提及的用戶 ID 列表',
        type: [String],
        example: ['user-1', 'user-2'],
    })
    @IsOptional()
    @IsArray()
    @IsUUID('4', { each: true })
    mentionedUserIds?: string[];

    @ApiPropertyOptional({
        description: '附件列表',
        type: [MessageAttachmentDto],
    })
    @IsOptional()
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => MessageAttachmentDto)
    attachments?: MessageAttachmentDto[];

    @ApiPropertyOptional({
        description: '訊息元數據',
        example: { edited: false, priority: 'normal' },
    })
    @IsOptional()
    metadata?: Record<string, any>;
} 