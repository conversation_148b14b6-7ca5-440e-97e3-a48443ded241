import {
    WebSocketGateway,
    WebSocketServer,
    SubscribeMessage,
    OnGatewayConnection,
    OnGatewayDisconnect,
    ConnectedSocket,
    MessageBody,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { Logger } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ChatService } from '../services/chat.service';
import { SendMessageDto } from '../dto/send-message.dto';

interface AuthenticatedSocket extends Socket {
    userId?: string;
    workspaceId?: string;
}

@WebSocketGateway({
    namespace: '/chat',
    cors: {
        origin: process.env.FRONTEND_URL || 'http://localhost:3000',
        credentials: true,
    },
})
export class ChatGateway implements OnGatewayConnection, OnGatewayDisconnect {
    @WebSocketServer()
    server: Server;

    private readonly logger = new Logger(ChatGateway.name);
    private connectedUsers = new Map<string, Set<string>>(); // userId -> Set of socketIds
    private userTyping = new Map<string, Set<string>>(); // conversationId -> Set of userIds

    constructor(
        private jwtService: JwtService,
        private chatService: ChatService,
    ) { }

    async handleConnection(client: AuthenticatedSocket) {
        try {
            const token = client.handshake.auth?.token || client.handshake.headers?.authorization?.replace('Bearer ', '');

            if (!token) {
                client.disconnect();
                return;
            }

            const payload = this.jwtService.verify(token);
            client.userId = payload.sub;
            client.workspaceId = client.handshake.query.workspaceId as string;

            if (!client.workspaceId) {
                client.disconnect();
                return;
            }

            // 記錄用戶連接
            if (!this.connectedUsers.has(client.userId!)) {
                this.connectedUsers.set(client.userId!, new Set());
            }
            this.connectedUsers.get(client.userId!)!.add(client.id);

            // 加入用戶的個人房間
            await client.join(`user:${client.userId}`);
            await client.join(`workspace:${client.workspaceId}`);

            // 獲取用戶的對話並加入對應房間
            const { conversations } = await this.chatService.getUserConversations(
                client.workspaceId!,
                client.userId!,
                1,
                100,
            );

            for (const conversation of conversations) {
                await client.join(`conversation:${conversation.id}`);
            }

            // 通知其他用戶該用戶上線
            this.server.to(`workspace:${client.workspaceId}`).emit('user:online', {
                userId: client.userId,
                timestamp: new Date(),
            });

            this.logger.log(`User ${client.userId} connected to workspace ${client.workspaceId}`);
        } catch (error) {
            this.logger.error('Connection authentication failed:', error);
            client.disconnect();
        }
    }

    async handleDisconnect(client: AuthenticatedSocket) {
        if (client.userId) {
            const userSockets = this.connectedUsers.get(client.userId);
            if (userSockets) {
                userSockets.delete(client.id);

                // 如果用戶沒有其他連接，標記為離線
                if (userSockets.size === 0) {
                    this.connectedUsers.delete(client.userId);

                    if (client.workspaceId) {
                        this.server.to(`workspace:${client.workspaceId}`).emit('user:offline', {
                            userId: client.userId,
                            timestamp: new Date(),
                        });
                    }
                }
            }

            this.logger.log(`User ${client.userId} disconnected`);
        }
    }

    @SubscribeMessage('message:send')
    async handleSendMessage(
        @ConnectedSocket() client: AuthenticatedSocket,
        @MessageBody() data: { conversationId: string; message: SendMessageDto },
    ) {
        try {
            const { conversationId, message } = data;

            if (!client.userId) {
                return { error: 'Unauthorized' };
            }

            // 發送訊息
            const sentMessage = await this.chatService.sendMessage(
                conversationId,
                client.userId,
                message,
            );

            // 廣播訊息給對話中的所有用戶
            this.server.to(`conversation:${conversationId}`).emit('message:new', {
                message: sentMessage,
                conversationId,
            });

            // 停止輸入狀態
            this.handleStopTyping(client, { conversationId });

            return { success: true, message: sentMessage };
        } catch (error) {
            this.logger.error('Error sending message:', error);
            return { error: error.message };
        }
    }

    @SubscribeMessage('typing:start')
    async handleStartTyping(
        @ConnectedSocket() client: AuthenticatedSocket,
        @MessageBody() data: { conversationId: string },
    ) {
        const { conversationId } = data;

        if (!client.userId) {
            return;
        }

        if (!this.userTyping.has(conversationId)) {
            this.userTyping.set(conversationId, new Set());
        }

        this.userTyping.get(conversationId)!.add(client.userId);

        // 通知其他用戶
        client.to(`conversation:${conversationId}`).emit('typing:start', {
            userId: client.userId,
            conversationId,
        });
    }

    @SubscribeMessage('typing:stop')
    async handleStopTyping(
        @ConnectedSocket() client: AuthenticatedSocket,
        @MessageBody() data: { conversationId: string },
    ) {
        const { conversationId } = data;

        if (!client.userId) {
            return;
        }

        const typingUsers = this.userTyping.get(conversationId);
        if (typingUsers) {
            typingUsers.delete(client.userId);

            if (typingUsers.size === 0) {
                this.userTyping.delete(conversationId);
            }
        }

        // 通知其他用戶
        client.to(`conversation:${conversationId}`).emit('typing:stop', {
            userId: client.userId,
            conversationId,
        });
    }

    @SubscribeMessage('conversation:join')
    async handleJoinConversation(
        @ConnectedSocket() client: AuthenticatedSocket,
        @MessageBody() data: { conversationId: string },
    ) {
        try {
            const { conversationId } = data;

            if (!client.userId) {
                return { error: 'Unauthorized' };
            }

            // 驗證用戶是否有權限加入對話
            await this.chatService.getConversation(conversationId, client.userId);

            // 加入對話房間
            await client.join(`conversation:${conversationId}`);

            return { success: true };
        } catch (error) {
            this.logger.error('Error joining conversation:', error);
            return { error: error.message };
        }
    }

    // 輔助方法：獲取在線用戶
    getOnlineUsers(workspaceId: string): string[] {
        const onlineUsers: string[] = [];

        for (const [userId, sockets] of this.connectedUsers.entries()) {
            if (sockets.size > 0) {
                // 檢查用戶是否在指定工作區
                for (const socketId of sockets) {
                    const socket = this.server.sockets.sockets.get(socketId) as AuthenticatedSocket;
                    if (socket?.workspaceId === workspaceId) {
                        onlineUsers.push(userId);
                        break;
                    }
                }
            }
        }

        return onlineUsers;
    }

    // 輔助方法：向特定用戶發送通知
    sendNotificationToUser(userId: string, notification: any) {
        this.server.to(`user:${userId}`).emit('notification', notification);
    }
} 