import {
    Controller,
    Get,
    Post,
    Put,
    Delete,
    Body,
    Param,
    Query,
    UseGuards,
    Request,
    HttpException,
    HttpStatus,
    ParseIntPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiParam, ApiQuery } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../../core/auth/guards/auth.guard';
import { MessageService } from '../services/message.service';
import { SendMessageDto } from '../dto/send-message.dto';
import { UpdateMessageDto } from '../dto/update-message.dto';
import { AddReactionDto } from '../dto/add-reaction.dto';
import { SearchMessagesDto } from '../dto/search-messages.dto';

@ApiTags('Chat Messages')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('workspace/:workspaceId/chat/messages')
export class MessageController {
    constructor(private readonly messageService: MessageService) { }

    @Get()
    @ApiOperation({ summary: '獲取訊息列表' })
    @ApiParam({ name: 'workspaceId', description: '工作區 ID' })
    @ApiQuery({ name: 'conversationId', required: true, description: '對話 ID' })
    @ApiQuery({ name: 'page', required: false, description: '頁碼', type: Number })
    @ApiQuery({ name: 'limit', required: false, description: '每頁數量', type: Number })
    async getMessages(
        @Param('workspaceId') workspaceId: string,
        @Query('conversationId') conversationId: string,
        @Query('page', new ParseIntPipe({ optional: true })) page = 1,
        @Query('limit', new ParseIntPipe({ optional: true })) limit = 50,
        @Request() req: any,
    ) {
        return this.messageService.getMessages(
            conversationId,
            req.user.id,
            page,
            limit
        );
    }

    @Get('search')
    @ApiOperation({ summary: '搜尋訊息' })
    @ApiParam({ name: 'workspaceId', description: '工作區 ID' })
    @ApiQuery({ name: 'conversationId', required: true, description: '對話 ID' })
    async searchMessages(
        @Param('workspaceId') workspaceId: string,
        @Query('conversationId') conversationId: string,
        @Query() searchDto: SearchMessagesDto,
        @Request() req: any,
    ) {
        return this.messageService.searchMessages(
            conversationId,
            req.user.id,
            searchDto.query || '',
            searchDto.page || 1,
            searchDto.limit || 20
        );
    }

    @Get(':messageId')
    @ApiOperation({ summary: '獲取單個訊息' })
    @ApiParam({ name: 'workspaceId', description: '工作區 ID' })
    @ApiParam({ name: 'messageId', description: '訊息 ID' })
    async getMessageById(
        @Param('workspaceId') workspaceId: string,
        @Param('messageId') messageId: string,
        @Request() req: any,
    ) {
        return this.messageService.getMessageById(messageId, req.user.id);
    }

    @Post(':conversationId')
    @ApiOperation({ summary: '發送訊息' })
    @ApiParam({ name: 'workspaceId', description: '工作區 ID' })
    @ApiParam({ name: 'conversationId', description: '對話 ID' })
    async sendMessage(
        @Param('workspaceId') workspaceId: string,
        @Param('conversationId') conversationId: string,
        @Body() sendMessageDto: SendMessageDto,
        @Request() req: any,
    ) {
        return this.messageService.sendMessage(
            conversationId,
            req.user.id,
            sendMessageDto
        );
    }

    @Put(':messageId')
    @ApiOperation({ summary: '編輯訊息' })
    @ApiParam({ name: 'workspaceId', description: '工作區 ID' })
    @ApiParam({ name: 'messageId', description: '訊息 ID' })
    async updateMessage(
        @Param('workspaceId') workspaceId: string,
        @Param('messageId') messageId: string,
        @Body() updateMessageDto: UpdateMessageDto,
        @Request() req: any,
    ) {
        return this.messageService.updateMessage(
            messageId,
            req.user.id,
            updateMessageDto.content || ''
        );
    }

    @Delete(':messageId')
    @ApiOperation({ summary: '刪除訊息' })
    @ApiParam({ name: 'workspaceId', description: '工作區 ID' })
    @ApiParam({ name: 'messageId', description: '訊息 ID' })
    async deleteMessage(
        @Param('workspaceId') workspaceId: string,
        @Param('messageId') messageId: string,
        @Request() req: any,
    ) {
        return this.messageService.deleteMessage(messageId, req.user.id);
    }

    @Post(':messageId/reactions')
    @ApiOperation({ summary: '添加反應' })
    @ApiParam({ name: 'workspaceId', description: '工作區 ID' })
    @ApiParam({ name: 'messageId', description: '訊息 ID' })
    async addReaction(
        @Param('workspaceId') workspaceId: string,
        @Param('messageId') messageId: string,
        @Body() addReactionDto: AddReactionDto,
        @Request() req: any,
    ) {
        return this.messageService.addReaction(messageId, req.user.id, addReactionDto.emoji);
    }

    @Delete(':messageId/reactions/:emoji')
    @ApiOperation({ summary: '移除反應' })
    @ApiParam({ name: 'workspaceId', description: '工作區 ID' })
    @ApiParam({ name: 'messageId', description: '訊息 ID' })
    @ApiParam({ name: 'emoji', description: '表情符號' })
    async removeReaction(
        @Param('workspaceId') workspaceId: string,
        @Param('messageId') messageId: string,
        @Param('emoji') emoji: string,
        @Request() req: any,
    ) {
        return this.messageService.removeReaction(messageId, req.user.id, emoji);
    }

    @Post(':messageId/read')
    @ApiOperation({ summary: '標記單個訊息為已讀' })
    @ApiParam({ name: 'workspaceId', description: '工作區 ID' })
    @ApiParam({ name: 'messageId', description: '訊息 ID' })
    async markMessageAsRead(
        @Param('workspaceId') workspaceId: string,
        @Param('messageId') messageId: string,
        @Request() req: any,
    ) {
        return this.messageService.markMessagesAsRead(messageId, req.user.id, [messageId]);
    }

    @Post('mark-read')
    @ApiOperation({ summary: '批量標記訊息為已讀' })
    @ApiParam({ name: 'workspaceId', description: '工作區 ID' })
    @ApiQuery({ name: 'conversationId', required: true, description: '對話 ID' })
    @ApiQuery({ name: 'messageIds', required: false, description: '訊息 ID 列表（逗號分隔）' })
    async markMessagesAsRead(
        @Param('workspaceId') workspaceId: string,
        @Query('conversationId') conversationId: string,
        @Query('messageIds') messageIds?: string,
        @Request() req?: any,
    ) {
        const messageIdArray = messageIds ? messageIds.split(',') : undefined;
        return this.messageService.markMessagesAsRead(conversationId, req.user.id, messageIdArray);
    }
} 