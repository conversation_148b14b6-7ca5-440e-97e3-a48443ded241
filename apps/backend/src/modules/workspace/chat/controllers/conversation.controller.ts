import {
    Controller,
    Get,
    Post,
    Put,
    Delete,
    Body,
    Param,
    Query,
    UseGuards,
    Request,
    ParseIntPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../../core/auth/guards/auth.guard';
import { WorkspaceGuard } from '../../../admin/workspaces/guards/workspace.guard';
import { ChatService } from '../services/chat.service';
import { ConversationService } from '../services/conversation.service';
import { CreateConversationDto } from '../dto/create-conversation.dto';
import { UpdateConversationDto } from '../dto/update-conversation.dto';

@ApiTags('聊天 - 對話管理')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, WorkspaceGuard)
@Controller('workspaces/:workspaceId/conversations')
export class ConversationController {
    constructor(
        private readonly chatService: ChatService,
        private readonly conversationService: ConversationService,
    ) { }

    @Post()
    @ApiOperation({ summary: '創建新對話' })
    @ApiResponse({ status: 201, description: '對話創建成功' })
    async createConversation(
        @Param('workspaceId') workspaceId: string,
        @Request() req: any,
        @Body() createConversationDto: CreateConversationDto,
    ) {
        const userId = req.user.id;
        return this.chatService.createConversation(workspaceId, userId, createConversationDto);
    }

    @Get()
    @ApiOperation({ summary: '獲取用戶的對話列表' })
    @ApiResponse({ status: 200, description: '對話列表獲取成功' })
    @ApiQuery({ name: 'page', required: false, type: Number, description: '頁碼' })
    @ApiQuery({ name: 'limit', required: false, type: Number, description: '每頁數量' })
    async getUserConversations(
        @Param('workspaceId') workspaceId: string,
        @Request() req: any,
        @Query('page', new ParseIntPipe({ optional: true })) page = 1,
        @Query('limit', new ParseIntPipe({ optional: true })) limit = 20,
    ) {
        const userId = req.user.id;
        return this.chatService.getUserConversations(workspaceId, userId, page, limit);
    }

    @Get(':conversationId')
    @ApiOperation({ summary: '獲取特定對話詳情' })
    @ApiResponse({ status: 200, description: '對話詳情獲取成功' })
    async getConversation(
        @Param('conversationId') conversationId: string,
        @Request() req: any,
    ) {
        const userId = req.user.id;
        return this.chatService.getConversation(conversationId, userId);
    }

    @Put(':conversationId')
    @ApiOperation({ summary: '更新對話資訊' })
    @ApiResponse({ status: 200, description: '對話更新成功' })
    async updateConversation(
        @Param('conversationId') conversationId: string,
        @Request() req: any,
        @Body() updateConversationDto: UpdateConversationDto,
    ) {
        const userId = req.user.id;
        return this.chatService.updateConversation(conversationId, userId, updateConversationDto);
    }

    @Delete(':conversationId')
    @ApiOperation({ summary: '刪除對話' })
    @ApiResponse({ status: 200, description: '對話刪除成功' })
    async deleteConversation(
        @Param('conversationId') conversationId: string,
        @Request() req: any,
    ) {
        const userId = req.user.id;
        await this.chatService.deleteConversation(conversationId, userId);
        return { message: '對話已成功刪除' };
    }

    @Post(':conversationId/pin')
    @ApiOperation({ summary: '置頂對話' })
    @ApiResponse({ status: 200, description: '對話置頂成功' })
    async pinConversation(
        @Param('conversationId') conversationId: string,
        @Request() req: any,
    ) {
        const userId = req.user.id;
        await this.conversationService.pinConversation(conversationId, userId);
        return { message: '對話已置頂' };
    }

    @Delete(':conversationId/pin')
    @ApiOperation({ summary: '取消置頂對話' })
    @ApiResponse({ status: 200, description: '取消置頂成功' })
    async unpinConversation(
        @Param('conversationId') conversationId: string,
        @Request() req: any,
    ) {
        const userId = req.user.id;
        await this.conversationService.unpinConversation(conversationId, userId);
        return { message: '已取消置頂' };
    }

    @Post(':conversationId/mute')
    @ApiOperation({ summary: '靜音對話' })
    @ApiResponse({ status: 200, description: '對話靜音成功' })
    async muteConversation(
        @Param('conversationId') conversationId: string,
        @Request() req: any,
    ) {
        const userId = req.user.id;
        await this.conversationService.muteConversation(conversationId, userId);
        return { message: '對話已靜音' };
    }

    @Delete(':conversationId/mute')
    @ApiOperation({ summary: '取消靜音對話' })
    @ApiResponse({ status: 200, description: '取消靜音成功' })
    async unmuteConversation(
        @Param('conversationId') conversationId: string,
        @Request() req: any,
    ) {
        const userId = req.user.id;
        await this.conversationService.unmuteConversation(conversationId, userId);
        return { message: '已取消靜音' };
    }

    @Post(':conversationId/archive')
    @ApiOperation({ summary: '封存對話' })
    @ApiResponse({ status: 200, description: '對話封存成功' })
    async archiveConversation(
        @Param('conversationId') conversationId: string,
        @Request() req: any,
    ) {
        const userId = req.user.id;
        await this.conversationService.archiveConversation(conversationId, userId);
        return { message: '對話已封存' };
    }

    @Delete(':conversationId/archive')
    @ApiOperation({ summary: '取消封存對話' })
    @ApiResponse({ status: 200, description: '取消封存成功' })
    async unarchiveConversation(
        @Param('conversationId') conversationId: string,
        @Request() req: any,
    ) {
        const userId = req.user.id;
        await this.conversationService.unarchiveConversation(conversationId, userId);
        return { message: '已取消封存' };
    }

    @Post(':conversationId/participants')
    @ApiOperation({ summary: '添加對話參與者' })
    @ApiResponse({ status: 201, description: '參與者添加成功' })
    async addParticipant(
        @Param('conversationId') conversationId: string,
        @Request() req: any,
        @Body('userId') targetUserId: string,
    ) {
        const userId = req.user.id;
        return this.chatService.addParticipant(conversationId, userId, targetUserId);
    }

    @Delete(':conversationId/participants/:userId')
    @ApiOperation({ summary: '移除對話參與者' })
    @ApiResponse({ status: 200, description: '參與者移除成功' })
    async removeParticipant(
        @Param('conversationId') conversationId: string,
        @Param('userId') targetUserId: string,
        @Request() req: any,
    ) {
        const userId = req.user.id;
        await this.chatService.removeParticipant(conversationId, userId, targetUserId);
        return { message: '參與者已成功移除' };
    }

    @Post(':conversationId/mark-read')
    @ApiOperation({ summary: '標記對話為已讀' })
    @ApiResponse({ status: 200, description: '對話已標記為已讀' })
    async markAsRead(
        @Param('conversationId') conversationId: string,
        @Request() req: any,
        @Body('messageId') messageId?: string,
    ) {
        const userId = req.user.id;
        await this.chatService.markConversationAsRead(conversationId, userId, messageId);
        return { message: '對話已標記為已讀' };
    }

    @Get(':conversationId/messages')
    @ApiOperation({ summary: '獲取對話訊息列表' })
    @ApiResponse({ status: 200, description: '訊息列表獲取成功' })
    @ApiQuery({ name: 'page', required: false, type: Number, description: '頁碼' })
    @ApiQuery({ name: 'limit', required: false, type: Number, description: '每頁數量' })
    @ApiQuery({ name: 'before', required: false, type: String, description: '在此訊息之前' })
    async getConversationMessages(
        @Param('conversationId') conversationId: string,
        @Request() req: any,
        @Query('page', new ParseIntPipe({ optional: true })) page = 1,
        @Query('limit', new ParseIntPipe({ optional: true })) limit = 50,
        @Query('before') beforeMessageId?: string,
    ) {
        const userId = req.user.id;
        return this.chatService.getConversationMessages(
            conversationId,
            userId,
            page,
            limit,
            beforeMessageId,
        );
    }

    @Get(':conversationId/search')
    @ApiOperation({ summary: '搜尋對話中的訊息' })
    @ApiResponse({ status: 200, description: '搜尋結果獲取成功' })
    @ApiQuery({ name: 'q', required: true, type: String, description: '搜尋關鍵字' })
    @ApiQuery({ name: 'page', required: false, type: Number, description: '頁碼' })
    @ApiQuery({ name: 'limit', required: false, type: Number, description: '每頁數量' })
    async searchMessages(
        @Param('conversationId') conversationId: string,
        @Request() req: any,
        @Query('q') query: string,
        @Query('page', new ParseIntPipe({ optional: true })) page = 1,
        @Query('limit', new ParseIntPipe({ optional: true })) limit = 20,
    ) {
        const userId = req.user.id;
        return this.chatService.searchMessages(conversationId, userId, query, page, limit);
    }

    @Get(':conversationId/statistics')
    @ApiOperation({ summary: '獲取對話統計資訊' })
    @ApiResponse({ status: 200, description: '統計資訊獲取成功' })
    async getMessageStatistics(@Param('conversationId') conversationId: string) {
        return this.chatService.getMessageStatistics(conversationId);
    }
} 