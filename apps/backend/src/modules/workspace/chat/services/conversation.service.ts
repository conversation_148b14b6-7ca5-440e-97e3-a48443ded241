import { Injectable, NotFoundException, ForbiddenException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../../../core/prisma/prisma.service';
import { CreateConversationDto } from '../dto/create-conversation.dto';
import { UpdateConversationDto } from '../dto/update-conversation.dto';
import { Prisma } from '@prisma/client';

@Injectable()
export class ConversationService {
    constructor(private readonly prisma: PrismaService) { }

    async createConversation(
        workspaceId: string,
        userId: string,
        createDto: CreateConversationDto,
    ) {
        const { type, name, description, participantIds, avatarUrl, isPrivate } = createDto;

        // 驗證參與者數量
        if (type === 'DIRECT' && participantIds.length !== 1) {
            throw new BadRequestException('直接對話必須包含一個參與者');
        }

        if (type === 'GROUP' && participantIds.length < 2) {
            throw new BadRequestException('群組對話至少需要兩個參與者');
        }

        // 檢查是否已存在直接對話
        if (type === 'DIRECT') {
            const existingConversation = await this.findDirectConversation(
                workspaceId,
                userId,
                participantIds[0],
            );
            if (existingConversation) {
                return existingConversation;
            }
        }

        // 創建對話
        const conversation = await this.prisma.conversations.create({
            data: {
                type,
                name,
                description,
                avatar: avatarUrl,
                isPrivate: isPrivate || false,
                workspaceId,
                createdBy: userId,
                lastActivityAt: new Date(),
                participants: {
                    create: [
                        // 添加創建者為擁有者
                        {
                            userId,
                            role: 'OWNER',
                            status: 'ACTIVE',
                        },
                        // 添加其他參與者
                        ...participantIds
                            .filter(id => id !== userId)
                            .map(participantId => ({
                                userId: participantId,
                                role: 'MEMBER' as const,
                                status: 'ACTIVE' as const,
                            })),
                    ],
                },
            },
            include: {
                participants: true,
                lastMessage: true,
            },
        });

        // 手動填充參與者的用戶資訊
        const participantsWithUserInfo = await Promise.all(
            conversation.participants.map(async (participant) => {
                // 查找用戶資訊
                const user = await this.prisma.tenant_users.findUnique({
                    where: { id: participant.userId },
                    select: {
                        id: true,
                        name: true,
                        email: true,
                        avatar: true,
                    },
                });

                return {
                    ...participant,
                    userName: user?.name || 'Unknown User',
                    userEmail: user?.email || '',
                    userAvatar: user?.avatar || null,
                };
            })
        );

        return {
            ...conversation,
            participants: participantsWithUserInfo,
        };
    }

    async findConversationById(conversationId: string, userId: string) {
        const conversation = await this.prisma.conversations.findFirst({
            where: {
                id: conversationId,
                participants: {
                    some: {
                        userId,
                        status: 'ACTIVE',
                    },
                },
            },
            include: {
                participants: true,
                lastMessage: true,
                _count: {
                    select: {
                        messages: true,
                    },
                },
            },
        });

        if (!conversation) {
            throw new NotFoundException('對話不存在或您沒有權限訪問');
        }

        return conversation;
    }

    async findUserConversations(
        workspaceId: string,
        userId: string,
        page: number = 1,
        limit: number = 20,
    ) {
        const skip = (page - 1) * limit;

        const [conversations, total] = await Promise.all([
            this.prisma.conversations.findMany({
                where: {
                    workspaceId,
                    participants: {
                        some: {
                            userId,
                            status: 'ACTIVE',
                        },
                    },
                    isArchived: false,
                },
                include: {
                    participants: true,
                    lastMessage: true,
                    _count: {
                        select: {
                            messages: true,
                        },
                    },
                },
                orderBy: {
                    lastActivityAt: 'desc',
                },
                skip,
                take: limit,
            }),
            this.prisma.conversations.count({
                where: {
                    workspaceId,
                    participants: {
                        some: {
                            userId,
                            status: 'ACTIVE',
                        },
                    },
                    isArchived: false,
                },
            }),
        ]);

        // 手動填充參與者的用戶資訊
        const conversationsWithUserInfo = await Promise.all(
            conversations.map(async (conversation) => {
                const participantsWithUserInfo = await Promise.all(
                    conversation.participants.map(async (participant) => {
                        // 查找用戶資訊
                        const user = await this.prisma.tenant_users.findUnique({
                            where: { id: participant.userId },
                            select: {
                                id: true,
                                name: true,
                                email: true,
                                avatar: true,
                            },
                        });

                        return {
                            ...participant,
                            userName: user?.name || 'Unknown User',
                            userEmail: user?.email || '',
                            userAvatar: user?.avatar || null,
                        };
                    })
                );

                return {
                    ...conversation,
                    participants: participantsWithUserInfo,
                };
            })
        );

        return {
            conversations: conversationsWithUserInfo,
            total,
            page,
            limit,
            totalPages: Math.ceil(total / limit),
        };
    }

    async updateConversation(
        conversationId: string,
        userId: string,
        updateDto: UpdateConversationDto,
    ) {
        // 檢查權限
        const participant = await this.prisma.conversation_participants.findFirst({
            where: {
                conversationId,
                userId,
                role: {
                    in: ['OWNER', 'ADMIN'],
                },
            },
        });

        if (!participant) {
            throw new ForbiddenException('您沒有權限修改此對話');
        }

        const conversation = await this.prisma.conversations.update({
            where: { id: conversationId },
            data: updateDto,
            include: {
                participants: true,
                lastMessage: true,
                _count: {
                    select: {
                        messages: true,
                    },
                },
            },
        });

        return conversation;
    }

    async deleteConversation(conversationId: string, userId: string): Promise<void> {
        // 檢查權限
        const participant = await this.prisma.conversation_participants.findFirst({
            where: {
                conversationId,
                userId,
                role: 'OWNER',
            },
        });

        if (!participant) {
            throw new ForbiddenException('只有對話擁有者可以刪除對話');
        }

        await this.prisma.conversations.delete({
            where: { id: conversationId },
        });
    }

    async addParticipant(conversationId: string, userId: string, role: string = 'MEMBER') {
        const participant = await this.prisma.conversation_participants.create({
            data: {
                conversationId,
                userId,
                role: role as any,
                status: 'ACTIVE',
            },
        });

        return participant;
    }

    async removeParticipant(conversationId: string, userId: string, targetUserId: string): Promise<void> {
        // 檢查權限
        const requesterParticipant = await this.prisma.conversation_participants.findFirst({
            where: {
                conversationId,
                userId,
                role: {
                    in: ['OWNER', 'ADMIN'],
                },
            },
        });

        if (!requesterParticipant) {
            throw new ForbiddenException('您沒有權限移除參與者');
        }

        const targetParticipant = await this.prisma.conversation_participants.findFirst({
            where: {
                conversationId,
                userId: targetUserId,
            },
        });

        if (!targetParticipant) {
            throw new NotFoundException('目標用戶不在此對話中');
        }

        // 更新狀態為已離開
        await this.prisma.conversation_participants.update({
            where: { id: targetParticipant.id },
            data: {
                status: 'LEFT',
                leftAt: new Date(),
            },
        });
    }

    async markAsRead(conversationId: string, userId: string, messageId?: string): Promise<void> {
        const participant = await this.prisma.conversation_participants.findFirst({
            where: {
                conversationId,
                userId,
            },
        });

        if (!participant) {
            throw new NotFoundException('您不是此對話的參與者');
        }

        await this.prisma.conversation_participants.update({
            where: { id: participant.id },
            data: {
                lastReadMessageId: messageId,
                lastReadAt: new Date(),
            },
        });
    }

    async updateLastActivity(conversationId: string): Promise<void> {
        await this.prisma.conversations.update({
            where: { id: conversationId },
            data: { lastActivityAt: new Date() },
        });
    }

    async pinConversation(conversationId: string, userId: string): Promise<void> {
        // 檢查用戶是否為對話參與者
        const participant = await this.prisma.conversation_participants.findFirst({
            where: {
                conversationId,
                userId,
                status: 'ACTIVE',
            },
        });

        if (!participant) {
            throw new ForbiddenException('您不是此對話的參與者');
        }

        // 更新參與者設定
        await this.prisma.conversation_participants.update({
            where: {
                conversationId_userId: {
                    conversationId,
                    userId,
                },
            },
            data: {
                isPinned: true,
            },
        });
    }

    async unpinConversation(conversationId: string, userId: string): Promise<void> {
        // 檢查用戶是否為對話參與者
        const participant = await this.prisma.conversation_participants.findFirst({
            where: {
                conversationId,
                userId,
                status: 'ACTIVE',
            },
        });

        if (!participant) {
            throw new ForbiddenException('您不是此對話的參與者');
        }

        // 更新參與者設定
        await this.prisma.conversation_participants.update({
            where: {
                conversationId_userId: {
                    conversationId,
                    userId,
                },
            },
            data: {
                isPinned: false,
            },
        });
    }

    async muteConversation(conversationId: string, userId: string): Promise<void> {
        // 檢查用戶是否為對話參與者
        const participant = await this.prisma.conversation_participants.findFirst({
            where: {
                conversationId,
                userId,
                status: 'ACTIVE',
            },
        });

        if (!participant) {
            throw new ForbiddenException('您不是此對話的參與者');
        }

        // 更新參與者設定
        await this.prisma.conversation_participants.update({
            where: {
                conversationId_userId: {
                    conversationId,
                    userId,
                },
            },
            data: {
                isMuted: true,
            },
        });
    }

    async unmuteConversation(conversationId: string, userId: string): Promise<void> {
        // 檢查用戶是否為對話參與者
        const participant = await this.prisma.conversation_participants.findFirst({
            where: {
                conversationId,
                userId,
                status: 'ACTIVE',
            },
        });

        if (!participant) {
            throw new ForbiddenException('您不是此對話的參與者');
        }

        // 更新參與者設定
        await this.prisma.conversation_participants.update({
            where: {
                conversationId_userId: {
                    conversationId,
                    userId,
                },
            },
            data: {
                isMuted: false,
            },
        });
    }

    async archiveConversation(conversationId: string, userId: string): Promise<void> {
        // 檢查用戶權限
        const participant = await this.prisma.conversation_participants.findFirst({
            where: {
                conversationId,
                userId,
                status: 'ACTIVE',
            },
        });

        if (!participant) {
            throw new ForbiddenException('您沒有權限歸檔此對話');
        }

        // 更新參與者狀態為非活躍（代表歸檔）
        await this.prisma.conversation_participants.update({
            where: {
                conversationId_userId: {
                    conversationId,
                    userId,
                },
            },
            data: {
                status: 'INACTIVE',
            },
        });
    }

    async unarchiveConversation(conversationId: string, userId: string): Promise<void> {
        // 檢查用戶權限
        const participant = await this.prisma.conversation_participants.findFirst({
            where: {
                conversationId,
                userId,
            },
        });

        if (!participant) {
            throw new ForbiddenException('您沒有權限取消歸檔此對話');
        }

        // 更新參與者狀態為活躍
        await this.prisma.conversation_participants.update({
            where: {
                conversationId_userId: {
                    conversationId,
                    userId,
                },
            },
            data: {
                status: 'ACTIVE',
            },
        });
    }

    private async findDirectConversation(
        workspaceId: string,
        userId1: string,
        userId2: string,
    ) {
        const conversation = await this.prisma.conversations.findFirst({
            where: {
                workspaceId,
                type: 'DIRECT',
                participants: {
                    every: {
                        OR: [
                            { userId: userId1, status: 'ACTIVE' },
                            { userId: userId2, status: 'ACTIVE' },
                        ],
                    },
                },
            },
            include: {
                participants: true,
                lastMessage: true,
            },
        });

        return conversation;
    }
} 