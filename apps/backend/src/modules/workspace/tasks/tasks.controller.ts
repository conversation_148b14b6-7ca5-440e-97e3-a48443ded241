import {
    Controller,
    Get,
    Post,
    Body,
    Patch,
    Param,
    Delete,
    Req,
    UnauthorizedException,
    Query,
    ValidationPipe,
    ParseIntPipe,
    DefaultValuePipe,
    UseGuards
} from '@nestjs/common';
import { TasksService } from './tasks.service';
import { CreateTaskDto, UpdateTaskDto, TaskResponseDto } from './dto';
import { Request } from 'express';
import { ApiTags, ApiBearerAuth, ApiOperation, ApiResponse, ApiQuery, ApiBody } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../core/auth/guards/auth.guard';
import { PoliciesGuard } from '../../../casl/guards/permission.guard';
import { CheckPolicies } from '../../../casl/decorators/check-policies.decorator';
import { AppAbility } from '../../../types/models/casl.model';
import { Actions, Subjects } from '@horizai/permissions';

// 擴展 Request 介面
interface RequestWithUser extends Request {
    user?: {
        id: string;
        tenantId: string;
        [key: string]: any;
    };
}

@ApiTags('workspace/tasks')
@ApiBearerAuth()
@Controller('workspace/tasks')
export class TasksController {
    constructor(private readonly tasksService: TasksService) { }

    @Post()
    @ApiOperation({ summary: '創建新任務' })
    @ApiResponse({
        status: 201,
        description: '任務創建成功',
        type: TaskResponseDto
    })
    @ApiResponse({ status: 400, description: '請求參數錯誤' })
    @ApiResponse({ status: 401, description: '未授權' })
    async create(
        @Body(ValidationPipe) createTaskDto: CreateTaskDto,
        @Req() req: RequestWithUser
    ): Promise<TaskResponseDto> {
        const userId = req.user?.id;
        const tenantId = req.user?.tenantId;

        if (!userId || !tenantId) {
            throw new UnauthorizedException('使用者未登入或缺少租戶資訊');
        }

        return this.tasksService.create(createTaskDto, userId, tenantId);
    }

    @Get()
    @ApiOperation({ summary: '獲取任務列表' })
    @ApiQuery({ name: 'page', required: false, type: Number, description: '頁碼' })
    @ApiQuery({ name: 'limit', required: false, type: Number, description: '每頁數量' })
    @ApiQuery({ name: 'search', required: false, type: String, description: '搜尋關鍵字' })
    @ApiQuery({ name: 'status', required: false, type: String, description: '任務狀態' })
    @ApiQuery({ name: 'priority', required: false, type: String, description: '任務優先級' })
    @ApiQuery({ name: 'projectId', required: false, type: String, description: '專案 ID' })
    @ApiQuery({ name: 'assigneeId', required: false, type: String, description: '指派人 ID' })
    @ApiResponse({
        status: 200,
        description: '任務列表獲取成功',
        schema: {
            type: 'object',
            properties: {
                tasks: { type: 'array', items: { $ref: '#/components/schemas/TaskResponseDto' } },
                total: { type: 'number' },
                page: { type: 'number' },
                limit: { type: 'number' },
                totalPages: { type: 'number' }
            }
        }
    })
    async findAll(
        @Req() req: RequestWithUser,
        @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
        @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
        @Query('search') search?: string,
        @Query('status') status?: string,
        @Query('priority') priority?: string,
        @Query('projectId') projectId?: string,
        @Query('assigneeId') assigneeId?: string
    ) {
        const tenantId = req.user?.tenantId;

        if (!tenantId) {
            throw new UnauthorizedException('缺少租戶資訊');
        }

        return this.tasksService.findAll(
            tenantId,
            page,
            limit,
            search,
            status,
            priority,
            projectId,
            assigneeId
        );
    }

    @Get('stats')
    @ApiOperation({ summary: '獲取任務統計資料' })
    @ApiQuery({ name: 'projectId', required: false, type: String, description: '專案 ID（可選）' })
    @ApiResponse({
        status: 200,
        description: '任務統計資料獲取成功',
        schema: {
            type: 'object',
            properties: {
                total: { type: 'number' },
                byStatus: { type: 'object' },
                byPriority: { type: 'object' },
                overdueTasks: { type: 'number' }
            }
        }
    })
    async getStats(
        @Req() req: RequestWithUser,
        @Query('projectId') projectId?: string
    ) {
        const tenantId = req.user?.tenantId;

        if (!tenantId) {
            throw new UnauthorizedException('缺少租戶資訊');
        }

        return this.tasksService.getTaskStats(tenantId, projectId);
    }

    @Get(':id')
    @ApiOperation({ summary: '獲取單一任務詳情' })
    @ApiResponse({
        status: 200,
        description: '任務詳情獲取成功',
        type: TaskResponseDto
    })
    @ApiResponse({ status: 404, description: '任務不存在' })
    async findOne(
        @Param('id') id: string,
        @Req() req: RequestWithUser
    ): Promise<TaskResponseDto> {
        const tenantId = req.user?.tenantId;

        if (!tenantId) {
            throw new UnauthorizedException('缺少租戶資訊');
        }

        return this.tasksService.findOne(id, tenantId);
    }

    @Patch(':id')
    @ApiOperation({ summary: '更新任務' })
    @ApiResponse({
        status: 200,
        description: '任務更新成功',
        type: TaskResponseDto
    })
    @ApiResponse({ status: 400, description: '請求參數錯誤' })
    @ApiResponse({ status: 404, description: '任務不存在' })
    async update(
        @Param('id') id: string,
        @Body(ValidationPipe) updateTaskDto: UpdateTaskDto,
        @Req() req: RequestWithUser
    ): Promise<TaskResponseDto> {
        const userId = req.user?.id;
        const tenantId = req.user?.tenantId;

        if (!userId || !tenantId) {
            throw new UnauthorizedException('使用者未登入或缺少租戶資訊');
        }

        return this.tasksService.update(id, updateTaskDto, userId, tenantId);
    }

    @Delete(':id')
    @ApiOperation({ summary: '刪除任務' })
    @ApiResponse({ status: 200, description: '任務刪除成功' })
    @ApiResponse({ status: 404, description: '任務不存在' })
    async remove(
        @Param('id') id: string,
        @Req() req: RequestWithUser
    ): Promise<{ message: string }> {
        const tenantId = req.user?.tenantId;

        if (!tenantId) {
            throw new UnauthorizedException('缺少租戶資訊');
        }

        await this.tasksService.remove(id, tenantId);
        return { message: '任務刪除成功' };
    }

    @Patch(':id/assign')
    @ApiOperation({ summary: '指派任務給用戶' })
    @ApiBody({
        schema: {
            type: 'object',
            properties: {
                assigneeId: { type: 'string', description: '指派給的用戶 ID' }
            },
            required: ['assigneeId']
        }
    })
    @ApiResponse({
        status: 200,
        description: '任務指派成功',
        type: TaskResponseDto
    })
    @ApiResponse({ status: 404, description: '任務或用戶不存在' })
    async assignTask(
        @Param('id') id: string,
        @Body('assigneeId') assigneeId: string,
        @Req() req: RequestWithUser
    ): Promise<TaskResponseDto> {
        const tenantId = req.user?.tenantId;

        if (!tenantId) {
            throw new UnauthorizedException('缺少租戶資訊');
        }

        return this.tasksService.assignTask(id, assigneeId, tenantId);
    }

    @Patch(':id/status')
    @ApiOperation({ summary: '更新任務狀態' })
    @ApiBody({
        schema: {
            type: 'object',
            properties: {
                status: {
                    type: 'string',
                    enum: ['todo', 'in-progress', 'review', 'done', 'cancelled'],
                    description: '新的任務狀態'
                }
            },
            required: ['status']
        }
    })
    @ApiResponse({
        status: 200,
        description: '任務狀態更新成功',
        type: TaskResponseDto
    })
    @ApiResponse({ status: 404, description: '任務不存在' })
    async updateStatus(
        @Param('id') id: string,
        @Body('status') status: string,
        @Req() req: RequestWithUser
    ): Promise<TaskResponseDto> {
        const tenantId = req.user?.tenantId;

        if (!tenantId) {
            throw new UnauthorizedException('缺少租戶資訊');
        }

        return this.tasksService.updateTaskStatus(id, status, tenantId);
    }

    @Get(':id/progress-stats')
    @ApiOperation({ summary: '獲取任務進度統計' })
    @ApiResponse({
        status: 200,
        description: '任務進度統計獲取成功',
        schema: {
            type: 'object',
            properties: {
                task: {
                    type: 'object',
                    properties: {
                        id: { type: 'string' },
                        title: { type: 'string' },
                        status: { type: 'string' },
                        priority: { type: 'string' },
                        estimatedHours: { type: 'number', nullable: true },
                        actualHours: { type: 'number', nullable: true }
                    }
                },
                progress: {
                    type: 'object',
                    properties: {
                        totalEntries: { type: 'number' },
                        latestProgress: { type: 'number', nullable: true },
                        averageProgress: { type: 'number', nullable: true },
                        progressHistory: {
                            type: 'array',
                            items: {
                                type: 'object',
                                properties: {
                                    date: { type: 'string', format: 'date-time' },
                                    value: { type: 'number', nullable: true },
                                    type: { type: 'string' }
                                }
                            }
                        }
                    }
                },
                timeTracking: {
                    type: 'object',
                    properties: {
                        estimatedVsActual: {
                            type: 'object',
                            properties: {
                                estimated: { type: 'number', nullable: true },
                                actual: { type: 'number', nullable: true },
                                variance: { type: 'number', nullable: true },
                                efficiency: { type: 'number', nullable: true }
                            }
                        }
                    }
                }
            }
        }
    })
    @ApiResponse({ status: 404, description: '任務不存在' })
    async getProgressStats(
        @Param('id') id: string,
        @Req() req: RequestWithUser
    ) {
        const tenantId = req.user?.tenantId;

        if (!tenantId) {
            throw new UnauthorizedException('缺少租戶資訊');
        }

        return this.tasksService.getTaskProgressStats(id, tenantId);
    }
} 