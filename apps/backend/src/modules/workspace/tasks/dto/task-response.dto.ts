import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class TaskResponseDto {
    @ApiProperty({
        description: '任務 ID',
        example: 'clxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx'
    })
    id: string;

    @ApiProperty({
        description: '任務標題',
        example: '實作用戶登入功能'
    })
    title: string;

    @ApiPropertyOptional({
        description: '任務描述',
        example: '實作用戶登入功能，包含 JWT 驗證和密碼加密'
    })
    description?: string;

    @ApiProperty({
        description: '所屬專案 ID',
        example: 'clxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx'
    })
    projectId: string;

    @ApiProperty({
        description: '任務狀態',
        example: 'todo',
        enum: ['todo', 'in-progress', 'review', 'done', 'cancelled']
    })
    status: string;

    @ApiProperty({
        description: '任務優先級',
        example: 'medium',
        enum: ['low', 'medium', 'high', 'urgent']
    })
    priority: string;

    @ApiPropertyOptional({
        description: '指派給的用戶 ID',
        example: 'clxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx'
    })
    assigneeId?: string;

    @ApiPropertyOptional({
        description: '任務截止日期',
        example: '2024-12-31T23:59:59.999Z'
    })
    dueDate?: Date;

    @ApiPropertyOptional({
        description: '預估工時（小時）',
        example: 8
    })
    estimatedHours?: number;

    @ApiPropertyOptional({
        description: '實際工時（小時）',
        example: 10
    })
    actualHours?: number;

    @ApiProperty({
        description: '租戶 ID',
        example: 'clxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx'
    })
    tenantId: string;

    @ApiProperty({
        description: '創建者 ID',
        example: 'clxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx'
    })
    createdBy: string;

    @ApiProperty({
        description: '創建時間',
        example: '2024-01-01T00:00:00.000Z'
    })
    createdAt: Date;

    @ApiProperty({
        description: '更新時間',
        example: '2024-01-01T12:00:00.000Z'
    })
    updatedAt: Date;

    @ApiPropertyOptional({
        description: '指派給的用戶資訊'
    })
    assignee?: {
        id: string;
        name: string;
        email: string;
    };

    @ApiPropertyOptional({
        description: '所屬專案資訊'
    })
    project?: {
        id: string;
        name: string;
        status: string;
    };
} 