import { IsString, IsOptional, IsNotEmpty, <PERSON><PERSON>ength, <PERSON><PERSON><PERSON>th, <PERSON>UUID, IsDateString } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateTaskDto {
    @ApiProperty({
        description: '任務標題',
        example: '實作用戶登入功能',
        minLength: 1,
        maxLength: 200
    })
    @IsString()
    @IsNotEmpty()
    @MinLength(1)
    @MaxLength(200)
    title: string;

    @ApiPropertyOptional({
        description: '任務描述',
        example: '實作用戶登入功能，包含 JWT 驗證和密碼加密',
        maxLength: 1000
    })
    @IsOptional()
    @IsString()
    @MaxLength(1000)
    description?: string;

    @ApiProperty({
        description: '所屬專案 ID',
        example: 'clxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx'
    })
    @IsString()
    @IsNotEmpty()
    @IsUUID()
    projectId: string;

    @ApiPropertyOptional({
        description: '任務狀態',
        example: 'todo',
        enum: ['todo', 'in-progress', 'review', 'done', 'cancelled']
    })
    @IsOptional()
    @IsString()
    status?: 'todo' | 'in-progress' | 'review' | 'done' | 'cancelled';

    @ApiPropertyOptional({
        description: '任務優先級',
        example: 'medium',
        enum: ['low', 'medium', 'high', 'urgent']
    })
    @IsOptional()
    @IsString()
    priority?: 'low' | 'medium' | 'high' | 'urgent';

    @ApiPropertyOptional({
        description: '指派給的用戶 ID',
        example: 'clxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx'
    })
    @IsOptional()
    @IsString()
    @IsUUID()
    assigneeId?: string;

    @ApiPropertyOptional({
        description: '任務截止日期',
        example: '2024-12-31T23:59:59.999Z'
    })
    @IsOptional()
    @IsDateString()
    dueDate?: string;

    @ApiPropertyOptional({
        description: '預估工時（小時）',
        example: 8
    })
    @IsOptional()
    estimatedHours?: number;
} 