import { Injectable, NotFoundException, ForbiddenException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../../core/prisma/prisma.service';
import { CreateTaskDto, UpdateTaskDto, TaskResponseDto } from './dto';
import { Prisma } from '@prisma/client';

@Injectable()
export class TasksService {
    constructor(private readonly prisma: PrismaService) { }

    async create(
        createTaskDto: CreateTaskDto,
        userId: string,
        tenantId: string
    ): Promise<TaskResponseDto> {
        try {
            // 驗證專案是否存在且屬於該租戶
            const project = await this.prisma.projects.findFirst({
                where: { id: createTaskDto.projectId, tenantId },
            });

            if (!project) {
                throw new NotFoundException('專案不存在');
            }

            // 如果指定了 assigneeId，驗證用戶是否存在且屬於該租戶
            if (createTaskDto.assigneeId) {
                const assignee = await this.prisma.tenant_users.findFirst({
                    where: { id: createTaskDto.assigneeId, tenantId },
                });

                if (!assignee) {
                    throw new NotFoundException('指派的用戶不存在');
                }
            }

            const task = await this.prisma.tasks.create({
                data: {
                    title: createTaskDto.title,
                    description: createTaskDto.description,
                    projectId: createTaskDto.projectId,
                    status: createTaskDto.status || 'todo',
                    priority: createTaskDto.priority || 'medium',
                    assigneeId: createTaskDto.assigneeId,
                    dueDate: createTaskDto.dueDate ? new Date(createTaskDto.dueDate) : null,
                    estimatedHours: createTaskDto.estimatedHours,
                    createdById: userId,
                    tenantId,
                },
                include: {
                    project: {
                        select: { id: true, name: true, status: true },
                    },
                },
            });

            return this.mapToResponseDto(task);
        } catch (error) {
            if (error instanceof Prisma.PrismaClientKnownRequestError) {
                if (error.code === 'P2002') {
                    throw new BadRequestException('任務標題已存在');
                }
            }
            throw error;
        }
    }

    async findAll(
        tenantId: string,
        page: number = 1,
        limit: number = 10,
        search?: string,
        status?: string,
        priority?: string,
        projectId?: string,
        assigneeId?: string
    ): Promise<{
        tasks: TaskResponseDto[];
        total: number;
        page: number;
        limit: number;
        totalPages: number;
    }> {
        const skip = (page - 1) * limit;

        const where: Prisma.tasksWhereInput = {
            tenantId,
            ...(search && {
                OR: [
                    { title: { contains: search, mode: 'insensitive' } },
                    { description: { contains: search, mode: 'insensitive' } },
                ],
            }),
            ...(status && { status }),
            ...(priority && { priority }),
            ...(projectId && { projectId }),
            ...(assigneeId && { assigneeId }),
        };

        const [tasks, total] = await Promise.all([
            this.prisma.tasks.findMany({
                where,
                skip,
                take: limit,
                orderBy: { createdAt: 'desc' },
                include: {
                    project: {
                        select: { id: true, name: true, status: true },
                    },
                },
            }),
            this.prisma.tasks.count({ where }),
        ]);

        return {
            tasks: tasks.map(task => this.mapToResponseDto(task)),
            total,
            page,
            limit,
            totalPages: Math.ceil(total / limit),
        };
    }

    async findOne(id: string, tenantId: string): Promise<TaskResponseDto> {
        const task = await this.prisma.tasks.findFirst({
            where: { id, tenantId },
            include: {
                project: {
                    select: { id: true, name: true, status: true },
                },
                progress_entries: {
                    select: {
                        id: true,
                        title: true,
                        progressType: true,
                        progressValue: true,
                        recordedAt: true,
                        notes: true,
                    },
                    orderBy: { recordedAt: 'desc' },
                    take: 10, // 最近 10 個進度記錄
                },
                _count: {
                    select: {
                        progress_entries: true,
                    },
                },
            },
        });

        if (!task) {
            throw new NotFoundException('任務不存在');
        }

        return this.mapToResponseDto(task);
    }

    async update(
        id: string,
        updateTaskDto: UpdateTaskDto,
        userId: string,
        tenantId: string
    ): Promise<TaskResponseDto> {
        // 檢查任務是否存在且屬於該租戶
        const existingTask = await this.prisma.tasks.findFirst({
            where: { id, tenantId },
        });

        if (!existingTask) {
            throw new NotFoundException('任務不存在');
        }

        // 如果要更新專案，驗證專案是否存在且屬於該租戶
        if (updateTaskDto.projectId) {
            const project = await this.prisma.projects.findFirst({
                where: { id: updateTaskDto.projectId, tenantId },
            });

            if (!project) {
                throw new NotFoundException('專案不存在');
            }
        }

        // 如果要更新 assigneeId，驗證用戶是否存在且屬於該租戶
        if (updateTaskDto.assigneeId) {
            const assignee = await this.prisma.tenant_users.findFirst({
                where: { id: updateTaskDto.assigneeId, tenantId },
            });

            if (!assignee) {
                throw new NotFoundException('指派的用戶不存在');
            }
        }

        try {
            const task = await this.prisma.tasks.update({
                where: { id },
                data: {
                    ...updateTaskDto,
                    dueDate: updateTaskDto.dueDate ? new Date(updateTaskDto.dueDate) : undefined,
                    updatedAt: new Date(),
                },
                include: {
                    project: {
                        select: { id: true, name: true, status: true },
                    },
                },
            });

            return this.mapToResponseDto(task);
        } catch (error) {
            if (error instanceof Prisma.PrismaClientKnownRequestError) {
                if (error.code === 'P2002') {
                    throw new BadRequestException('任務標題已存在');
                }
            }
            throw error;
        }
    }

    async remove(id: string, tenantId: string): Promise<void> {
        const task = await this.prisma.tasks.findFirst({
            where: { id, tenantId },
        });

        if (!task) {
            throw new NotFoundException('任務不存在');
        }

        await this.prisma.tasks.delete({
            where: { id },
        });
    }

    async getTaskStats(tenantId: string, projectId?: string): Promise<{
        total: number;
        byStatus: Record<string, number>;
        byPriority: Record<string, number>;
        overdueTasks: number;
    }> {
        const where: Prisma.tasksWhereInput = {
            tenantId,
            ...(projectId && { projectId }),
        };

        const [total, statusStats, priorityStats, overdueTasks] = await Promise.all([
            this.prisma.tasks.count({ where }),
            this.prisma.tasks.groupBy({
                by: ['status'],
                where,
                _count: { status: true },
            }),
            this.prisma.tasks.groupBy({
                by: ['priority'],
                where,
                _count: { priority: true },
            }),
            this.prisma.tasks.count({
                where: {
                    ...where,
                    dueDate: { lt: new Date() },
                    status: { notIn: ['done', 'cancelled'] },
                },
            }),
        ]);

        const byStatus = statusStats.reduce((acc, stat) => {
            acc[stat.status] = stat._count.status;
            return acc;
        }, {} as Record<string, number>);

        const byPriority = priorityStats.reduce((acc, stat) => {
            acc[stat.priority] = stat._count.priority;
            return acc;
        }, {} as Record<string, number>);

        return { total, byStatus, byPriority, overdueTasks };
    }

    async assignTask(
        taskId: string,
        assigneeId: string,
        tenantId: string
    ): Promise<TaskResponseDto> {
        // 驗證任務是否存在
        const task = await this.prisma.tasks.findFirst({
            where: { id: taskId, tenantId },
        });

        if (!task) {
            throw new NotFoundException('任務不存在');
        }

        // 驗證用戶是否存在且屬於該租戶
        const assignee = await this.prisma.tenant_users.findFirst({
            where: { id: assigneeId, tenantId },
        });

        if (!assignee) {
            throw new NotFoundException('指派的用戶不存在');
        }

        const updatedTask = await this.prisma.tasks.update({
            where: { id: taskId },
            data: { assigneeId, updatedAt: new Date() },
            include: {
                project: {
                    select: { id: true, name: true, status: true },
                },
            },
        });

        return this.mapToResponseDto(updatedTask);
    }

    async updateTaskStatus(
        taskId: string,
        status: string,
        tenantId: string
    ): Promise<TaskResponseDto> {
        const task = await this.prisma.tasks.findFirst({
            where: { id: taskId, tenantId },
        });

        if (!task) {
            throw new NotFoundException('任務不存在');
        }

        const updatedTask = await this.prisma.tasks.update({
            where: { id: taskId },
            data: { status, updatedAt: new Date() },
            include: {
                project: {
                    select: { id: true, name: true, status: true },
                },
            },
        });

        return this.mapToResponseDto(updatedTask);
    }

    async getTaskProgressStats(taskId: string, tenantId: string): Promise<{
        task: {
            id: string;
            title: string;
            status: string;
            priority: string;
            estimatedHours: number | null;
            actualHours: number | null;
        };
        progress: {
            totalEntries: number;
            latestProgress: number | null;
            averageProgress: number | null;
            progressHistory: Array<{
                date: Date;
                value: number | null;
                type: string;
            }>;
        };
        timeTracking: {
            estimatedVsActual: {
                estimated: number | null;
                actual: number | null;
                variance: number | null;
                efficiency: number | null;
            };
        };
    }> {
        // 驗證任務存在
        const task = await this.prisma.tasks.findFirst({
            where: { id: taskId, tenantId },
            select: {
                id: true,
                title: true,
                status: true,
                priority: true,
                estimatedHours: true,
                actualHours: true,
            },
        });

        if (!task) {
            throw new NotFoundException('任務不存在');
        }

        // 獲取進度記錄
        const progressEntries = await this.prisma.progress_entries.findMany({
            where: { taskId, tenantId },
            select: {
                progressValue: true,
                progressType: true,
                recordedAt: true,
            },
            orderBy: { recordedAt: 'desc' },
        });

        // 處理進度統計
        const validProgressValues = progressEntries
            .map(entry => entry.progressValue)
            .filter(value => value !== null && value !== undefined) as number[];

        const latestProgress = validProgressValues.length > 0 ? validProgressValues[0] : null;
        const averageProgress = validProgressValues.length > 0
            ? validProgressValues.reduce((sum, val) => sum + val, 0) / validProgressValues.length
            : null;

        // 進度歷史（按日期分組）
        const progressHistory = progressEntries.map(entry => ({
            date: entry.recordedAt,
            value: entry.progressValue,
            type: entry.progressType,
        }));

        // 時間追蹤統計
        const estimated = task.estimatedHours;
        const actual = task.actualHours;
        let variance: number | null = null;
        let efficiency: number | null = null;

        if (estimated !== null && actual !== null) {
            variance = actual - estimated;
            efficiency = estimated > 0 ? (estimated / actual) * 100 : null;
        }

        return {
            task: {
                id: task.id,
                title: task.title,
                status: task.status,
                priority: task.priority,
                estimatedHours: task.estimatedHours,
                actualHours: task.actualHours,
            },
            progress: {
                totalEntries: progressEntries.length,
                latestProgress,
                averageProgress: averageProgress ? Math.round(averageProgress * 100) / 100 : null,
                progressHistory,
            },
            timeTracking: {
                estimatedVsActual: {
                    estimated,
                    actual,
                    variance,
                    efficiency: efficiency ? Math.round(efficiency * 100) / 100 : null,
                },
            },
        };
    }

    private mapToResponseDto(task: any): TaskResponseDto {
        return {
            id: task.id,
            title: task.title,
            description: task.description,
            projectId: task.projectId,
            status: task.status,
            priority: task.priority,
            assigneeId: task.assigneeId,
            dueDate: task.dueDate,
            estimatedHours: task.estimatedHours,
            actualHours: task.actualHours,
            tenantId: task.tenantId,
            createdBy: task.createdById,
            createdAt: task.createdAt,
            updatedAt: task.updatedAt,
            project: task.project ? {
                id: task.project.id,
                name: task.project.name,
                status: task.project.status,
            } : undefined,
        };
    }
} 