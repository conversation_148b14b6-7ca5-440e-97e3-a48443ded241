import {
    Controller,
    Get,
    Post,
    Patch,
    Delete,
    Body,
    Param,
    Query,
    UseGuards,
    UseInterceptors,
    UploadedFiles,
    ParseIntPipe,
    BadRequestException,
    ForbiddenException,
} from '@nestjs/common';
import { FilesInterceptor } from '@nestjs/platform-express';
import {
    ApiTags,
    ApiOperation,
    ApiResponse,
    ApiBearerAuth,
    ApiConsumes,
    ApiQuery,
} from '@nestjs/swagger';
import { MessageCenterService } from '../services/message-center.service';
import {
    CreateConversationDto,
    UpdateConversationDto,
    ConversationResponseDto,
    SendMessageDto,
    MessageResponseDto,
    CreateNotificationDto,
    UpdateNotificationDto,
    NotificationResponseDto,
    MessageAttachmentDto,
} from '../dto';
import { JwtAuthGuard } from '../../../core/auth/guards/auth.guard';
import { CurrentUser } from '../../../core/auth/decorators/current-user.decorator';

@ApiTags('Message Center')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('workspace/message-center')
export class MessageCenterController {
    constructor(private readonly messageCenterService: MessageCenterService) { }

    // 對話相關端點
    @Post('conversations')
    @ApiOperation({ summary: '創建新對話' })
    @ApiResponse({ type: ConversationResponseDto })
    async createConversation(
        @Body() dto: CreateConversationDto,
        @CurrentUser() user: any
    ): Promise<ConversationResponseDto> {
        return this.messageCenterService.createConversation(
            dto,
            user.tenantId,
            user.id
        );
    }

    @Get('conversations')
    @ApiOperation({ summary: '獲取對話列表' })
    @ApiQuery({ name: 'workspaceId', required: false, description: '工作區 ID' })
    @ApiResponse({ type: [ConversationResponseDto] })
    async getConversations(
        @Query('workspaceId') workspaceId?: string,
        @CurrentUser() user?: any
    ): Promise<ConversationResponseDto[]> {
        // 僅允許租戶使用者訪問
        if (user?.userType !== 'tenant') {
            throw new ForbiddenException('僅租戶使用者可訪問此端點');
        }

        if (!user?.tenantId) {
            throw new BadRequestException('用戶缺少 tenantId 資訊');
        }

        return this.messageCenterService.getConversations(
            user.tenantId,
            workspaceId,
            user.id
        );
    }

    @Get('conversations/:id')
    @ApiOperation({ summary: '獲取單個對話' })
    @ApiResponse({ type: ConversationResponseDto })
    async getConversation(
        @Param('id') id: string,
        @CurrentUser() user: any
    ): Promise<ConversationResponseDto> {
        return this.messageCenterService.getConversation(id, user.tenantId, user.id);
    }

    @Patch('conversations/:id')
    @ApiOperation({ summary: '更新對話' })
    @ApiResponse({ type: ConversationResponseDto })
    async updateConversation(
        @Param('id') id: string,
        @Body() dto: UpdateConversationDto,
        @CurrentUser() user: any
    ): Promise<ConversationResponseDto> {
        return this.messageCenterService.updateConversation(
            id,
            dto,
            user.tenantId,
            user.id
        );
    }

    @Delete('conversations/:id')
    @ApiOperation({ summary: '刪除對話' })
    async deleteConversation(
        @Param('id') id: string,
        @CurrentUser() user: any
    ): Promise<{ message: string }> {
        await this.messageCenterService.deleteConversation(id, user.tenantId, user.id);
        return { message: '對話已刪除' };
    }

    // 訊息相關端點
    @Post('messages')
    @ApiOperation({ summary: '發送訊息' })
    @ApiConsumes('multipart/form-data')
    @UseInterceptors(FilesInterceptor('attachments', 10))
    @ApiResponse({ type: MessageResponseDto })
    async sendMessage(
        @Body() dto: SendMessageDto,
        @UploadedFiles() files: Express.Multer.File[],
        @CurrentUser() user: any
    ): Promise<MessageResponseDto> {
        let attachments: MessageAttachmentDto[] = [];

        if (files && files.length > 0) {
            // 這裡應該實現檔案上傳邏輯
            // 目前先返回基本的檔案資訊
            attachments = files.map(file => ({
                filename: file.originalname,
                size: file.size,
                mimeType: file.mimetype,
                url: `/uploads/${file.filename}`, // 實際實作中應該使用實際的檔案 URL
            }));
        }

        return this.messageCenterService.sendMessage(
            dto,
            user.tenantId,
            user.id,
            user.userType || 'tenant',
            user.name || user.email,
            attachments
        );
    }

    @Get('conversations/:conversationId/messages')
    @ApiOperation({ summary: '獲取對話中的訊息' })
    @ApiQuery({ name: 'limit', required: false, description: '每頁數量', type: Number })
    @ApiQuery({ name: 'offset', required: false, description: '偏移量', type: Number })
    @ApiResponse({ type: [MessageResponseDto] })
    async getMessages(
        @Param('conversationId') conversationId: string,
        @Query('limit') limit = 50,
        @Query('offset') offset = 0,
        @CurrentUser() user: any
    ): Promise<MessageResponseDto[]> {
        const limitNum = typeof limit === 'string' ? parseInt(limit, 10) : limit;
        const offsetNum = typeof offset === 'string' ? parseInt(offset, 10) : offset;

        if (limitNum > 100) {
            throw new BadRequestException('限制最多100條訊息');
        }

        return this.messageCenterService.getMessages(
            conversationId,
            user.tenantId,
            user.id,
            limitNum,
            offsetNum
        );
    }

    @Patch('messages/:messageId/read')
    @ApiOperation({ summary: '標記訊息為已讀' })
    async markMessageAsRead(
        @Param('messageId') messageId: string,
        @CurrentUser() user: any
    ): Promise<{ message: string }> {
        await this.messageCenterService.markMessageAsRead(
            messageId,
            user.tenantId,
            user.id
        );
        return { message: '訊息已標記為已讀' };
    }

    // 通知相關端點
    @Post('notifications')
    @ApiOperation({ summary: '創建通知' })
    @ApiResponse({ type: NotificationResponseDto })
    async createNotification(
        @Body() dto: CreateNotificationDto,
        @CurrentUser() user: any
    ): Promise<NotificationResponseDto> {
        // 僅允許租戶使用者訪問
        if (user?.userType !== 'tenant') {
            throw new ForbiddenException('僅租戶使用者可訪問此端點');
        }
        if (!user?.tenantId) {
            throw new BadRequestException('用戶缺少 tenantId 資訊');
        }
        return this.messageCenterService.createNotification(dto, user.tenantId);
    }

    @Get('notifications')
    @ApiOperation({ summary: '獲取通知列表' })
    @ApiQuery({ name: 'workspaceId', required: false, description: '工作區 ID' })
    @ApiQuery({ name: 'limit', required: false, description: '每頁數量', type: Number })
    @ApiQuery({ name: 'offset', required: false, description: '偏移量', type: Number })
    @ApiResponse({ type: [NotificationResponseDto] })
    async getNotifications(
        @Query('workspaceId') workspaceId?: string,
        @Query('limit') limit = 50,
        @Query('offset') offset = 0,
        @CurrentUser() user?: any
    ): Promise<NotificationResponseDto[]> {
        const limitNum = typeof limit === 'string' ? parseInt(limit, 10) : limit;
        const offsetNum = typeof offset === 'string' ? parseInt(offset, 10) : offset;

        if (limitNum > 100) {
            throw new BadRequestException('限制最多100條通知');
        }

        // 僅允許租戶使用者訪問
        if (user?.userType !== 'tenant') {
            throw new ForbiddenException('僅租戶使用者可訪問此端點');
        }
        if (!user?.tenantId) {
            throw new BadRequestException('用戶缺少 tenantId 資訊');
        }
        return this.messageCenterService.getNotifications(
            user.tenantId,
            user.id,
            workspaceId,
            limitNum,
            offsetNum
        );
    }

    @Patch('notifications/:notificationId/read')
    @ApiOperation({ summary: '標記通知為已讀' })
    async markNotificationAsRead(
        @Param('notificationId') notificationId: string,
        @CurrentUser() user: any
    ): Promise<{ message: string }> {
        // 僅允許租戶使用者訪問
        if (user?.userType !== 'tenant') {
            throw new ForbiddenException('僅租戶使用者可訪問此端點');
        }
        if (!user?.tenantId) {
            throw new BadRequestException('用戶缺少 tenantId 資訊');
        }
        await this.messageCenterService.markNotificationAsRead(
            notificationId,
            user.tenantId,
            user.id
        );
        return { message: '通知已標記為已讀' };
    }

    @Patch('notifications/mark-all-read')
    @ApiOperation({ summary: '標記所有通知為已讀' })
    @ApiQuery({ name: 'workspaceId', required: false, description: '工作區 ID' })
    async markAllNotificationsAsRead(
        @Query('workspaceId') workspaceId?: string,
        @CurrentUser() user?: any
    ): Promise<{ message: string }> {
        // 僅允許租戶使用者訪問
        if (user?.userType !== 'tenant') {
            throw new ForbiddenException('僅租戶使用者可訪問此端點');
        }
        if (!user?.tenantId) {
            throw new BadRequestException('用戶缺少 tenantId 資訊');
        }
        await this.messageCenterService.markAllNotificationsAsRead(
            user.tenantId,
            user.id,
            workspaceId
        );
        return { message: '所有通知已標記為已讀' };
    }
} 