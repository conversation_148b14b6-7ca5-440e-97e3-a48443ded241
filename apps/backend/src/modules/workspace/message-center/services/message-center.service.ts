import { Injectable, NotFoundException, ForbiddenException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '@/modules/core/prisma/prisma.service';
import { RealtimeEventsService } from '../../../workspace/websocket/realtime-events.service';
import {
    CreateConversationDto,
    UpdateConversationDto,
    ConversationResponseDto,
    SendMessageDto,
    UpdateMessageDto,
    MessageResponseDto,
    CreateNotificationDto,
    UpdateNotificationDto,
    NotificationResponseDto,
    MessageAttachmentDto
} from '../dto';
import { Prisma, ConversationType, MessageContentType } from '@prisma/client';

@Injectable()
export class MessageCenterService {
    constructor(
        private readonly prisma: PrismaService,
        private readonly realtimeEventsService: RealtimeEventsService,
    ) { }

    // 對話相關方法
    async createConversation(
        dto: CreateConversationDto,
        tenantId: string,
        createdBy: string
    ): Promise<ConversationResponseDto> {
        const conversation = await this.prisma.message_conversations.create({
            data: {
                title: dto.title,
                type: dto.type || ConversationType.DIRECT,
                participantIds: dto.participantIds,
                tenantId,
                workspaceId: dto.workspaceId,
                createdBy,
            },
        });

        const response = this.formatConversationResponse(conversation);

        // 發送即時通知給參與者
        if (Array.isArray(dto.participantIds)) {
            for (const participantId of dto.participantIds) {
                if (participantId !== createdBy) {
                    await this.realtimeEventsService.notifyNewNotification(
                        participantId,
                        {
                            type: 'conversation_created',
                            title: '新對話',
                            message: `您被添加到新對話: ${dto.title}`,
                            entityType: 'conversation',
                            entityId: conversation.id,
                        }
                    );
                }
            }
        }

        return response;
    }

    async getConversations(
        tenantId: string,
        workspaceId?: string,
        userId?: string
    ): Promise<ConversationResponseDto[]> {
        const where: Prisma.message_conversationsWhereInput = {
            tenantId,
            isActive: true,
            ...(workspaceId && { workspaceId }),
            ...(userId && {
                participantIds: {
                    array_contains: [userId]
                }
            })
        };

        const conversations = await this.prisma.message_conversations.findMany({
            where,
            orderBy: {
                lastMessageAt: 'desc'
            }
        });

        const conversationsWithUnread = await Promise.all(
            conversations.map(async (conv) => {
                const unreadCount = await this.getUnreadMessageCount(conv.id, userId);
                return this.formatConversationResponse(conv, unreadCount);
            })
        );

        return conversationsWithUnread;
    }

    async getConversation(
        id: string,
        tenantId: string,
        userId?: string
    ): Promise<ConversationResponseDto> {
        const conversation = await this.prisma.message_conversations.findFirst({
            where: {
                id,
                tenantId,
                isActive: true,
                ...(userId && {
                    participantIds: {
                        array_contains: [userId]
                    }
                })
            }
        });

        if (!conversation) {
            throw new NotFoundException('對話不存在');
        }

        const unreadCount = await this.getUnreadMessageCount(id, userId);
        return this.formatConversationResponse(conversation, unreadCount);
    }

    async updateConversation(
        id: string,
        dto: UpdateConversationDto,
        tenantId: string,
        userId?: string
    ): Promise<ConversationResponseDto> {
        // 檢查權限
        const conversation = await this.getConversation(id, tenantId, userId);

        const updated = await this.prisma.message_conversations.update({
            where: { id },
            data: dto
        });

        const response = this.formatConversationResponse(updated);

        // 通知對話更新
        await this.realtimeEventsService.notifyNewMessageCenterMessage(
            id,
            {
                type: 'conversation_updated',
                content: '對話已更新',
                changes: dto,
            },
            userId || 'system'
        );

        return response;
    }

    async deleteConversation(
        id: string,
        tenantId: string,
        userId?: string
    ): Promise<void> {
        // 檢查權限
        await this.getConversation(id, tenantId, userId);

        await this.prisma.message_conversations.update({
            where: { id },
            data: { isActive: false }
        });

        // 通知對話刪除
        await this.realtimeEventsService.notifyNewMessageCenterMessage(
            id,
            {
                type: 'conversation_deleted',
                content: '對話已刪除',
            },
            userId || 'system'
        );
    }

    // 訊息相關方法
    async sendMessage(
        dto: SendMessageDto,
        tenantId: string,
        senderId: string,
        senderType: string,
        senderName: string,
        attachments?: MessageAttachmentDto[]
    ): Promise<MessageResponseDto> {
        // 檢查對話權限
        await this.getConversation(dto.conversationId, tenantId, senderId);

        const message = await this.prisma.message_center_messages.create({
            data: {
                conversationId: dto.conversationId,
                content: dto.content,
                contentType: dto.contentType || MessageContentType.TEXT,
                senderId,
                senderType,
                senderName,
                replyToMessageId: dto.replyToMessageId,
                attachments: attachments as any || [],
                tenantId,
            },
        });

        // 更新對話的最後訊息時間
        await this.prisma.message_conversations.update({
            where: { id: dto.conversationId },
            data: {
                lastMessageId: message.id,
                lastMessageAt: message.sentAt,
            },
        });

        const response = this.formatMessageResponse(message);

        // 發送即時通知
        await this.realtimeEventsService.notifyNewMessageCenterMessage(
            dto.conversationId,
            response,
            senderId
        );

        // 更新未讀計數給其他參與者
        const conversation = await this.prisma.message_conversations.findUnique({
            where: { id: dto.conversationId }
        });

        if (conversation?.participantIds && Array.isArray(conversation.participantIds)) {
            for (const participantId of conversation.participantIds as string[]) {
                if (participantId !== senderId) {
                    const unreadCount = await this.getUnreadMessageCount(dto.conversationId, participantId);
                    await this.realtimeEventsService.notifyUnreadCountUpdate(participantId, unreadCount);
                }
            }
        }

        return response;
    }

    async getMessages(
        conversationId: string,
        tenantId: string,
        userId?: string,
        limit = 50,
        offset = 0
    ): Promise<MessageResponseDto[]> {
        // 檢查對話權限
        await this.getConversation(conversationId, tenantId, userId);

        const messages = await this.prisma.message_center_messages.findMany({
            where: {
                conversationId,
                tenantId,
                isDeleted: false,
            },
            orderBy: {
                sentAt: 'desc'
            },
            take: limit,
            skip: offset,
        });

        return messages.map(msg => this.formatMessageResponse(msg));
    }

    async markMessageAsRead(
        messageId: string,
        tenantId: string,
        userId?: string
    ): Promise<void> {
        const message = await this.prisma.message_center_messages.findFirst({
            where: {
                id: messageId,
                tenantId,
                isDeleted: false,
            }
        });

        if (!message) {
            throw new NotFoundException('訊息不存在');
        }

        // 這裡可以實作已讀狀態的邏輯
        // 例如在 message_read_status 表中記錄

        // 通知已讀狀態更新
        if (userId) {
            await this.realtimeEventsService.notifyUnreadCountUpdate(
                userId,
                await this.getUnreadMessageCount(message.conversationId, userId)
            );
        }
    }

    // 通知相關方法
    async createNotification(
        dto: CreateNotificationDto,
        tenantId: string
    ): Promise<NotificationResponseDto> {
        const notification = await this.prisma.message_center_notifications.create({
            data: {
                title: dto.title,
                message: dto.message,
                type: dto.type,
                priority: dto.priority,
                recipientId: dto.recipientId,
                recipientType: dto.recipientType,
                entityType: dto.entityType,
                entityId: dto.entityId,
                actionUrl: dto.actionUrl,
                tenantId,
                workspaceId: dto.workspaceId,
                expiresAt: dto.expiresAt,
            },
        });

        const response = this.formatNotificationResponse(notification);

        // 發送即時通知
        await this.realtimeEventsService.notifyNewNotification(dto.recipientId, response);

        return response;
    }

    async getNotifications(
        tenantId: string,
        recipientId?: string,
        workspaceId?: string,
        limit = 50,
        offset = 0
    ): Promise<NotificationResponseDto[]> {
        const where: Prisma.message_center_notificationsWhereInput = {
            tenantId,
            ...(recipientId && { recipientId }),
            ...(workspaceId && { workspaceId }),
        };

        const notifications = await this.prisma.message_center_notifications.findMany({
            where,
            orderBy: {
                createdAt: 'desc'
            },
            take: limit,
            skip: offset,
        });

        return notifications.map(notification => this.formatNotificationResponse(notification));
    }

    async markNotificationAsRead(
        notificationId: string,
        tenantId: string,
        recipientId?: string
    ): Promise<void> {
        const where: Prisma.message_center_notificationsWhereInput = {
            id: notificationId,
            tenantId,
            ...(recipientId && { recipientId }),
        };

        const notification = await this.prisma.message_center_notifications.findFirst({
            where
        });

        if (!notification) {
            throw new NotFoundException('通知不存在');
        }

        await this.prisma.message_center_notifications.update({
            where: { id: notificationId },
            data: {
                isRead: true,
                readAt: new Date(),
            },
        });

        // 通知已讀狀態更新
        if (recipientId) {
            await this.realtimeEventsService.notifyNotificationRead(recipientId, notificationId);
        }
    }

    async markAllNotificationsAsRead(
        tenantId: string,
        recipientId?: string,
        workspaceId?: string
    ): Promise<void> {
        const where: Prisma.message_center_notificationsWhereInput = {
            tenantId,
            isRead: false,
            ...(recipientId && { recipientId }),
            ...(workspaceId && { workspaceId }),
        };

        await this.prisma.message_center_notifications.updateMany({
            where,
            data: {
                isRead: true,
                readAt: new Date(),
            },
        });

        // 通知批量已讀
        if (recipientId) {
            await this.realtimeEventsService.notifyUnreadCountUpdate(recipientId, 0);
        }
    }

    // 私有輔助方法
    private async getUnreadMessageCount(conversationId: string, userId?: string): Promise<number> {
        if (!userId) return 0;

        // 這裡需要實作未讀訊息計數邏輯
        // 可能需要一個 message_read_status 表來追蹤已讀狀態
        return 0;
    }

    private formatConversationResponse(
        conversation: any,
        unreadCount = 0
    ): ConversationResponseDto {
        return {
            id: conversation.id,
            title: conversation.title,
            type: conversation.type,
            participantIds: conversation.participantIds,
            lastMessageId: conversation.lastMessageId,
            lastMessageAt: conversation.lastMessageAt,
            unreadCount,
            tenantId: conversation.tenantId,
            workspaceId: conversation.workspaceId,
            createdBy: conversation.createdBy,
            createdAt: conversation.createdAt,
            updatedAt: conversation.updatedAt,
            isActive: conversation.isActive,
            isArchived: conversation.isArchived || false,
        };
    }

    private formatMessageResponse(message: any): MessageResponseDto {
        return {
            id: message.id,
            conversationId: message.conversationId,
            content: message.content,
            contentType: message.contentType,
            senderId: message.senderId,
            senderType: message.senderType,
            senderName: message.senderName,
            replyToMessageId: message.replyToMessageId,
            attachments: message.attachments || [],
            isRead: message.isRead || false,
            isEdited: message.isEdited || false,
            isDeleted: message.isDeleted || false,
            tenantId: message.tenantId,
            sentAt: message.sentAt,
            editedAt: message.editedAt,
            readAt: message.readAt,
            deletedAt: message.deletedAt,
        };
    }

    private formatNotificationResponse(notification: any): NotificationResponseDto {
        return {
            id: notification.id,
            title: notification.title,
            message: notification.message,
            type: notification.type,
            priority: notification.priority,
            recipientId: notification.recipientId,
            recipientType: notification.recipientType,
            entityType: notification.entityType,
            entityId: notification.entityId,
            actionUrl: notification.actionUrl,
            isRead: notification.isRead,
            isArchived: notification.isArchived,
            tenantId: notification.tenantId,
            workspaceId: notification.workspaceId,
            createdAt: notification.createdAt,
            readAt: notification.readAt,
            archivedAt: notification.archivedAt,
            expiresAt: notification.expiresAt,
        };
    }
} 