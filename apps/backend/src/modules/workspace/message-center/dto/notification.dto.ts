import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsEnum, IsBoolean } from 'class-validator';
import { NotificationType, NotificationPriority } from '@prisma/client';

export class CreateNotificationDto {
    @ApiProperty({ description: '通知標題' })
    @IsString()
    title: string;

    @ApiProperty({ description: '通知訊息' })
    @IsString()
    message: string;

    @ApiPropertyOptional({
        description: '通知類型',
        enum: NotificationType,
        default: NotificationType.INFO
    })
    @IsOptional()
    @IsEnum(NotificationType)
    type?: NotificationType;

    @ApiPropertyOptional({
        description: '通知優先級',
        enum: NotificationPriority,
        default: NotificationPriority.NORMAL
    })
    @IsOptional()
    @IsEnum(NotificationPriority)
    priority?: NotificationPriority;

    @ApiProperty({ description: '接收者 ID' })
    @IsString()
    recipientId: string;

    @ApiProperty({ description: '接收者類型' })
    @IsString()
    recipientType: string;

    @ApiPropertyOptional({ description: '實體類型' })
    @IsOptional()
    @IsString()
    entityType?: string;

    @ApiPropertyOptional({ description: '實體 ID' })
    @IsOptional()
    @IsString()
    entityId?: string;

    @ApiPropertyOptional({ description: '動作 URL' })
    @IsOptional()
    @IsString()
    actionUrl?: string;

    @ApiPropertyOptional({ description: '工作區 ID' })
    @IsOptional()
    @IsString()
    workspaceId?: string;

    @ApiPropertyOptional({ description: '過期時間' })
    @IsOptional()
    expiresAt?: Date;
}

export class UpdateNotificationDto {
    @ApiPropertyOptional({ description: '是否已讀' })
    @IsOptional()
    @IsBoolean()
    isRead?: boolean;

    @ApiPropertyOptional({ description: '是否歸檔' })
    @IsOptional()
    @IsBoolean()
    isArchived?: boolean;
}

export class NotificationResponseDto {
    @ApiProperty({ description: '通知 ID' })
    id: string;

    @ApiProperty({ description: '通知標題' })
    title: string;

    @ApiProperty({ description: '通知訊息' })
    message: string;

    @ApiProperty({ description: '通知類型' })
    type: NotificationType;

    @ApiProperty({ description: '通知優先級' })
    priority: NotificationPriority;

    @ApiProperty({ description: '接收者 ID' })
    recipientId: string;

    @ApiProperty({ description: '接收者類型' })
    recipientType: string;

    @ApiProperty({ description: '實體類型' })
    entityType?: string;

    @ApiProperty({ description: '實體 ID' })
    entityId?: string;

    @ApiProperty({ description: '動作 URL' })
    actionUrl?: string;

    @ApiProperty({ description: '是否已讀' })
    isRead: boolean;

    @ApiProperty({ description: '是否歸檔' })
    isArchived: boolean;

    @ApiProperty({ description: '租戶 ID' })
    tenantId: string;

    @ApiProperty({ description: '工作區 ID' })
    workspaceId?: string;

    @ApiProperty({ description: '創建時間' })
    createdAt: Date;

    @ApiProperty({ description: '閱讀時間' })
    readAt?: Date;

    @ApiProperty({ description: '歸檔時間' })
    archivedAt?: Date;

    @ApiProperty({ description: '過期時間' })
    expiresAt?: Date;
} 