import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsEnum, IsArray } from 'class-validator';
import { MessageContentType } from '@prisma/client';

export class SendMessageDto {
    @ApiProperty({ description: '對話 ID' })
    @IsString()
    conversationId: string;

    @ApiProperty({ description: '訊息內容' })
    @IsString()
    content: string;

    @ApiPropertyOptional({
        description: '內容類型',
        enum: MessageContentType,
        default: MessageContentType.TEXT
    })
    @IsOptional()
    @IsEnum(MessageContentType)
    contentType?: MessageContentType;

    @ApiPropertyOptional({ description: '回覆的訊息 ID' })
    @IsOptional()
    @IsString()
    replyToMessageId?: string;

    @ApiPropertyOptional({ description: '附件' })
    @IsOptional()
    @IsArray()
    attachments?: File[];
}

export class UpdateMessageDto {
    @ApiPropertyOptional({ description: '訊息內容' })
    @IsOptional()
    @IsString()
    content?: string;

    @ApiPropertyOptional({ description: '附件' })
    @IsOptional()
    @IsArray()
    attachments?: any[];
}

export class MessageAttachmentDto {
    @ApiProperty({ description: '檔案名稱' })
    filename: string;

    @ApiProperty({ description: '檔案大小' })
    size: number;

    @ApiProperty({ description: '檔案類型' })
    mimeType: string;

    @ApiProperty({ description: '檔案 URL' })
    url: string;
}

export class MessageResponseDto {
    @ApiProperty({ description: '訊息 ID' })
    id: string;

    @ApiProperty({ description: '對話 ID' })
    conversationId: string;

    @ApiProperty({ description: '訊息內容' })
    content: string;

    @ApiProperty({ description: '內容類型' })
    contentType: MessageContentType;

    @ApiProperty({ description: '發送者 ID' })
    senderId: string;

    @ApiProperty({ description: '發送者類型' })
    senderType: string;

    @ApiProperty({ description: '發送者名稱' })
    senderName: string;

    @ApiProperty({ description: '回覆的訊息 ID' })
    replyToMessageId?: string;

    @ApiProperty({ description: '附件', type: [MessageAttachmentDto] })
    attachments?: MessageAttachmentDto[];

    @ApiProperty({ description: '是否已讀' })
    isRead: boolean;

    @ApiProperty({ description: '是否已編輯' })
    isEdited: boolean;

    @ApiProperty({ description: '是否已刪除' })
    isDeleted: boolean;

    @ApiProperty({ description: '租戶 ID' })
    tenantId: string;

    @ApiProperty({ description: '發送時間' })
    sentAt: Date;

    @ApiProperty({ description: '編輯時間' })
    editedAt?: Date;

    @ApiProperty({ description: '閱讀時間' })
    readAt?: Date;

    @ApiProperty({ description: '刪除時間' })
    deletedAt?: Date;
} 