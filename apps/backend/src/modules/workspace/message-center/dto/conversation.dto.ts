import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsArray, IsEnum, IsBoolean } from 'class-validator';
import { ConversationType } from '@prisma/client';

export class CreateConversationDto {
    @ApiProperty({ description: '對話標題' })
    @IsString()
    title: string;

    @ApiPropertyOptional({
        description: '對話類型',
        enum: ConversationType,
        default: ConversationType.DIRECT
    })
    @IsOptional()
    @IsEnum(ConversationType)
    type?: ConversationType;

    @ApiProperty({ description: '參與者 ID 列表' })
    @IsArray()
    @IsString({ each: true })
    participantIds: string[];

    @ApiPropertyOptional({ description: '工作區 ID' })
    @IsOptional()
    @IsString()
    workspaceId?: string;
}

export class UpdateConversationDto {
    @ApiPropertyOptional({ description: '對話標題' })
    @IsOptional()
    @IsString()
    title?: string;

    @ApiPropertyOptional({ description: '是否歸檔' })
    @IsOptional()
    @IsBoolean()
    isArchived?: boolean;
}

export class ConversationResponseDto {
    @ApiProperty({ description: '對話 ID' })
    id: string;

    @ApiProperty({ description: '對話標題' })
    title: string;

    @ApiProperty({ description: '對話類型' })
    type: ConversationType;

    @ApiProperty({ description: '參與者 ID 列表' })
    participantIds: string[];

    @ApiProperty({ description: '最後訊息 ID' })
    lastMessageId?: string;

    @ApiProperty({ description: '最後訊息時間' })
    lastMessageAt?: Date;

    @ApiProperty({ description: '未讀訊息數量' })
    unreadCount: number;

    @ApiProperty({ description: '租戶 ID' })
    tenantId: string;

    @ApiProperty({ description: '工作區 ID' })
    workspaceId?: string;

    @ApiProperty({ description: '是否活躍' })
    isActive: boolean;

    @ApiProperty({ description: '是否歸檔' })
    isArchived: boolean;

    @ApiProperty({ description: '創建時間' })
    createdAt: Date;

    @ApiProperty({ description: '更新時間' })
    updatedAt: Date;

    @ApiProperty({ description: '創建者 ID' })
    createdBy: string;
} 