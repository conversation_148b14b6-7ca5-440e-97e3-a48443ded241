import { IsString, <PERSON>NotEmpty, <PERSON><PERSON><PERSON>, IsOptional } from 'class-validator';

export enum CommentUpdateType {
    CREATED = 'created',
    UPDATED = 'updated',
    DELETED = 'deleted',
    REACTION_ADDED = 'reaction_added',
    REACTION_REMOVED = 'reaction_removed',
}

export class CommentUpdateDto {
    @IsString()
    @IsNotEmpty()
    commentId: string;

    @IsEnum(CommentUpdateType)
    updateType: CommentUpdateType;

    @IsString()
    @IsNotEmpty()
    entityType: string;

    @IsString()
    @IsNotEmpty()
    entityId: string;

    @IsString()
    @IsOptional()
    content?: string;

    @IsString()
    @IsOptional()
    parentCommentId?: string;
} 