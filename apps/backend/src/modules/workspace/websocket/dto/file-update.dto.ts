import { IsString, <PERSON>NotEmpty, <PERSON><PERSON><PERSON>, IsOptional } from 'class-validator';

export enum FileUpdateType {
    UPLOADED = 'uploaded',
    UPDATED = 'updated',
    DELETED = 'deleted',
    SHARED = 'shared',
    PERMISSION_CHANGED = 'permission_changed',
}

export class FileUpdateDto {
    @IsString()
    @IsNotEmpty()
    fileId: string;

    @IsEnum(FileUpdateType)
    updateType: FileUpdateType;

    @IsString()
    @IsNotEmpty()
    entityType: string;

    @IsString()
    @IsNotEmpty()
    entityId: string;

    @IsString()
    @IsOptional()
    fileName?: string;

    @IsString()
    @IsOptional()
    description?: string;
} 