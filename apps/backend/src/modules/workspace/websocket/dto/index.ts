export * from './join-room.dto';
export * from './leave-room.dto';
export * from './send-message.dto';
export * from './file-update.dto';
export * from './comment-update.dto';
export * from './task-update.dto';
export * from './project-update.dto';

// 聊天相關事件DTO
export interface ChatJoinConversationDto {
    conversationId: string;
}

export interface ChatLeaveConversationDto {
    conversationId: string;
}

export interface ChatSendMessageDto {
    conversationId: string;
    content: string;
    type?: string;
    replyToId?: string;
}

export interface ChatTypingDto {
    conversationId: string;
}

export interface ChatMessageReactionDto {
    messageId: string;
    emoji: string;
}

// 訊息中心相關事件DTO
export interface MessageCenterSendMessageDto {
    conversationId: string;
    content: string;
    contentType?: string;
    replyToMessageId?: string;
}

export interface MessageCenterJoinConversationDto {
    conversationId: string;
}

export interface MessageCenterNotificationDto {
    recipientId: string;
    type: string;
    title: string;
    content: string;
    workspaceId?: string;
}

// 通用事件響應DTO
export interface WebSocketEventResponse<T = any> {
    success: boolean;
    data?: T;
    error?: string;
    timestamp: Date;
}

export interface UserOnlineStatusDto {
    userId: string;
    username: string;
    isOnline: boolean;
    timestamp: Date;
}

export interface RoomUserJoinedDto {
    userId: string;
    username: string;
    roomType: string;
    roomId: string;
    timestamp: Date;
}

export interface RoomUserLeftDto {
    userId: string;
    username: string;
    roomType: string;
    roomId: string;
    timestamp: Date;
} 