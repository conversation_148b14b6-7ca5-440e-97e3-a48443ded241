import { IsString, <PERSON>NotEmpt<PERSON>, <PERSON><PERSON><PERSON>, IsOptional } from 'class-validator';

export enum TaskUpdateType {
    CREATED = 'created',
    UPDATED = 'updated',
    STATUS_CHANGED = 'status_changed',
    ASSIGNED = 'assigned',
    UNASSIGNED = 'unassigned',
    DELETED = 'deleted',
}

export class TaskUpdateDto {
    @IsString()
    @IsNotEmpty()
    taskId: string;

    @IsEnum(TaskUpdateType)
    updateType: TaskUpdateType;

    @IsString()
    @IsNotEmpty()
    projectId: string;

    @IsString()
    @IsOptional()
    title?: string;

    @IsString()
    @IsOptional()
    status?: string;

    @IsString()
    @IsOptional()
    assignedTo?: string;

    @IsString()
    @IsOptional()
    description?: string;
} 