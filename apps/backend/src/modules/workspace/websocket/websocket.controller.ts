import { Controller, Get, UseGuards, Request, Query } from '@nestjs/common';
import { JwtAuthGuard } from '../../core/auth/guards/auth.guard';
import { WorkspaceGuard } from '../../admin/workspaces/guards/workspace.guard';
import { WebSocketService } from './websocket.service';

@Controller('workspace/:workspaceId/websocket')
@UseGuards(JwtAuthGuard, WorkspaceGuard)
export class WebSocketController {
    constructor(private readonly websocketService: WebSocketService) { }

    @Get('stats')
    async getConnectionStats(@Request() req: any) {
        return this.websocketService.getConnectionStats();
    }

    @Get('online-users')
    async getOnlineUsers(@Request() req: any) {
        return {
            users: this.websocketService.getOnlineUsers(),
            count: this.websocketService.getOnlineUsers().length,
        };
    }

    @Get('room-users')
    async getRoomUsers(
        @Query('roomType') roomType: string,
        @Query('roomId') roomId: string,
        @Request() req: any,
    ) {
        if (!roomType || !roomId) {
            return { error: 'roomType and roomId are required' };
        }

        const users = await this.websocketService.getRoomOnlineUsers(roomType, roomId);
        return {
            roomType,
            roomId,
            users,
            count: users.length,
        };
    }
} 