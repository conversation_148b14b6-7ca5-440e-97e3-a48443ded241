import { Injectable, Logger } from '@nestjs/common';
import { Server, Socket } from 'socket.io';
import { JwtService } from '@nestjs/jwt';
import { PrismaService } from '../../core/prisma/prisma.service';

interface ConnectedUser {
    id: string;
    username: string;
    email: string;
    tenantId: string | null;
    userType: 'system' | 'tenant';
    workspaceId?: string;
}

interface UserConnection {
    socketId: string;
    user: ConnectedUser;
    connectedAt: Date;
    lastActivity: Date;
}

@Injectable()
export class WebSocketService {
    private server: Server;
    private connections = new Map<string, UserConnection>();
    private userSockets = new Map<string, Set<string>>(); // userId -> Set of socketIds
    private readonly logger = new Logger(WebSocketService.name);

    constructor(
        private readonly jwtService: JwtService,
        private readonly prisma: PrismaService,
    ) { }

    setServer(server: Server) {
        this.server = server;
    }

    // 認證 Socket 連線
    async authenticateSocket(socket: Socket): Promise<ConnectedUser | null> {
        try {
            this.logger.log(`🔐 Authenticating socket ${socket.id}`);
            this.logger.log(`Socket handshake auth: ${JSON.stringify(socket.handshake.auth)}`);
            this.logger.log(`Socket handshake query: ${JSON.stringify(socket.handshake.query)}`);
            this.logger.log(`Socket handshake headers: ${JSON.stringify(socket.handshake.headers)}`);

            // 支援多種方式獲取 token，優先順序：auth -> authorization header -> query -> cookie
            let token = socket.handshake.auth?.token ||
                socket.handshake.headers?.authorization?.replace('Bearer ', '') ||
                (socket.handshake.query as any)?.token;

            // 如果沒有 token，嘗試從 cookie 中獲取
            if (!token) {
                const cookies = socket.handshake.headers.cookie;
                this.logger.debug(`Socket cookies: ${cookies}`);

                if (cookies) {
                    // 嘗試多種 cookie 名稱，與後端設置保持一致
                    const authTokenMatch = cookies.match(/auth_token=([^;]+)/);
                    const accessTokenMatch = cookies.match(/access_token=([^;]+)/);

                    if (authTokenMatch) {
                        token = authTokenMatch[1];
                        this.logger.log(`✅ Found auth_token in cookies for socket ${socket.id}`);
                    } else if (accessTokenMatch) {
                        token = accessTokenMatch[1];
                        this.logger.log(`✅ Found access_token in cookies for socket ${socket.id}`);
                    } else {
                        this.logger.debug(`Available cookies: ${cookies}`);
                    }
                }
            }

            if (!token) {
                this.logger.warn(`❌ No token provided for socket ${socket.id}. Checked auth, authorization header, and cookies.`);
                return null;
            }

            this.logger.log(`✅ Token found for socket ${socket.id}: ${token.substring(0, 20)}...`);

            // 驗證 JWT token
            const payload = this.jwtService.verify(token);
            const userType = socket.handshake.auth?.user_type || payload.userType;

            let user: any;
            if (userType === 'system') {
                user = await this.prisma.system_users.findUnique({
                    where: { id: payload.sub },
                });
            } else {
                user = await this.prisma.tenant_users.findUnique({
                    where: { id: payload.sub },
                    include: {
                        tenant: true,
                    },
                });
            }

            if (!user) {
                this.logger.warn(`User not found for socket ${socket.id} with userType: ${userType}`);
                return null;
            }

            // 對於租戶用戶，確保租戶存在
            if (userType === 'tenant' && !user.tenant) {
                this.logger.warn(`Tenant not found for tenant_user with socket ${socket.id}`);
                return null;
            }

            return {
                id: user.id,
                username: user.name || user.email,
                email: user.email,
                tenantId: user.tenant?.id || null,
                userType: userType,
            };

        } catch (error) {
            this.logger.error(`Authentication failed for socket ${socket.id}: ${error.message}`);
            return null;
        }
    }

    // 添加連線
    async addConnection(socketId: string, user: ConnectedUser): Promise<void> {
        const connection: UserConnection = {
            socketId,
            user,
            connectedAt: new Date(),
            lastActivity: new Date(),
        };

        this.connections.set(socketId, connection);

        // 維護用戶 -> socket 映射
        if (!this.userSockets.has(user.id)) {
            this.userSockets.set(user.id, new Set());
        }
        this.userSockets.get(user.id)!.add(socketId);

        this.logger.log(`Added connection for user ${user.id} (socket: ${socketId})`);
    }

    // 移除連線
    async removeConnection(socketId: string): Promise<ConnectedUser | null> {
        const connection = this.connections.get(socketId);
        if (!connection) return null;

        const user = connection.user;

        // 移除連線記錄
        this.connections.delete(socketId);

        // 更新用戶 socket 映射
        const userSocketSet = this.userSockets.get(user.id);
        if (userSocketSet) {
            userSocketSet.delete(socketId);
            if (userSocketSet.size === 0) {
                this.userSockets.delete(user.id);
            }
        }

        this.logger.log(`Removed connection for user ${user.id} (socket: ${socketId})`);
        return user;
    }

    // 獲取連線的用戶資訊
    async getConnectionUser(socketId: string): Promise<ConnectedUser | null> {
        const connection = this.connections.get(socketId);
        if (!connection) return null;

        // 更新最後活動時間
        connection.lastActivity = new Date();
        return connection.user;
    }

    // 驗證房間存取權限
    async validateRoomAccess(
        userId: string,
        roomType: string,
        roomId: string,
    ): Promise<boolean> {
        try {
            switch (roomType) {
                case 'user':
                    // 用戶只能加入自己的個人房間
                    return userId === roomId;
                case 'workspace':
                    return await this.validateWorkspaceAccess(userId, roomId);
                case 'project':
                    return await this.validateProjectAccess(userId, roomId);
                case 'task':
                    return await this.validateTaskAccess(userId, roomId);
                case 'file':
                    return await this.validateFileAccess(userId, roomId);
                default:
                    return false;
            }
        } catch (error) {
            this.logger.error(`Room access validation failed: ${error.message}`);
            return false;
        }
    }

    // 驗證工作區存取權限
    private async validateWorkspaceAccess(userId: string, workspaceId: string): Promise<boolean> {
        const member = await this.prisma.workspace_members.findFirst({
            where: {
                userId,
                workspaceId,
            },
        });
        return !!member;
    }

    // 驗證專案存取權限
    private async validateProjectAccess(userId: string, projectId: string): Promise<boolean> {
        const project = await this.prisma.projects.findFirst({
            where: {
                id: projectId,
                workspaces: {
                    workspace_members: {
                        some: {
                            userId,
                        },
                    },
                },
            },
        });
        return !!project;
    }

    // 驗證任務存取權限
    private async validateTaskAccess(userId: string, taskId: string): Promise<boolean> {
        const task = await this.prisma.tasks.findFirst({
            where: {
                id: taskId,
                project: {
                    workspaces: {
                        workspace_members: {
                            some: {
                                userId,
                            },
                        },
                    },
                },
            },
        });
        return !!task;
    }

    // 驗證檔案存取權限
    private async validateFileAccess(userId: string, fileId: string): Promise<boolean> {
        // 檢查檔案權限
        const filePermission = await this.prisma.file_permissions.findFirst({
            where: {
                fileId,
                userId,
            },
        });

        if (filePermission) return true;

        // 檢查是否為檔案所有者
        const file = await this.prisma.shared_files.findFirst({
            where: {
                id: fileId,
                uploaderId: userId,
            },
        });

        if (file) return true;

        // 檢查工作區權限
        const fileInWorkspace = await this.prisma.shared_files.findFirst({
            where: {
                id: fileId,
                workspaces: {
                    workspace_members: {
                        some: {
                            userId,
                        },
                    },
                },
            },
        });

        return !!fileInWorkspace;
    }

    // 廣播到特定用戶的所有連線
    async broadcastToUser(userId: string, event: string, data: any): Promise<void> {
        const socketIds = this.userSockets.get(userId);
        if (!socketIds || socketIds.size === 0) return;

        socketIds.forEach(socketId => {
            this.server.to(socketId).emit(event, data);
        });

        this.logger.log(`Broadcasted ${event} to user ${userId} (${socketIds.size} connections)`);
    }

    // 廣播到房間
    async broadcastToRoom(roomType: string, roomId: string, event: string, data: any): Promise<void> {
        const roomName = `${roomType}:${roomId}`;
        this.server.to(roomName).emit(event, data);
        this.logger.log(`Broadcasted ${event} to room ${roomName}`);
    }

    // 廣播到工作區所有成員
    async broadcastToWorkspace(workspaceId: string, event: string, data: any): Promise<void> {
        await this.broadcastToRoom('workspace', workspaceId, event, data);
    }

    // 廣播到專案所有成員
    async broadcastToProject(projectId: string, event: string, data: any): Promise<void> {
        await this.broadcastToRoom('project', projectId, event, data);
    }

    // 獲取線上用戶列表
    getOnlineUsers(): ConnectedUser[] {
        const onlineUsers = new Map<string, ConnectedUser>();

        this.connections.forEach(connection => {
            onlineUsers.set(connection.user.id, connection.user);
        });

        return Array.from(onlineUsers.values());
    }

    // 獲取房間內的線上用戶
    async getRoomOnlineUsers(roomType: string, roomId: string): Promise<ConnectedUser[]> {
        const roomName = `${roomType}:${roomId}`;
        const room = this.server.sockets.adapter.rooms.get(roomName);

        if (!room) return [];

        const onlineUsers: ConnectedUser[] = [];
        room.forEach(socketId => {
            const connection = this.connections.get(socketId);
            if (connection) {
                onlineUsers.push(connection.user);
            }
        });

        return onlineUsers;
    }

    // 清理過期連線
    async cleanupExpiredConnections(): Promise<void> {
        const now = new Date();
        const expiredThreshold = 30 * 60 * 1000; // 30 分鐘

        const expiredConnections: string[] = [];

        this.connections.forEach((connection, socketId) => {
            if (now.getTime() - connection.lastActivity.getTime() > expiredThreshold) {
                expiredConnections.push(socketId);
            }
        });

        expiredConnections.forEach(socketId => {
            this.removeConnection(socketId);
        });

        if (expiredConnections.length > 0) {
            this.logger.log(`Cleaned up ${expiredConnections.length} expired connections`);
        }
    }

    // 獲取連線統計
    getConnectionStats() {
        const totalConnections = this.connections.size;
        const uniqueUsers = this.userSockets.size;
        const onlineUsers = this.getOnlineUsers();

        return {
            totalConnections,
            uniqueUsers,
            onlineUsers: onlineUsers.length,
            connections: Array.from(this.connections.values()).map(conn => ({
                socketId: conn.socketId,
                userId: conn.user.id,
                username: conn.user.username,
                connectedAt: conn.connectedAt,
                lastActivity: conn.lastActivity,
            })),
        };
    }
} 