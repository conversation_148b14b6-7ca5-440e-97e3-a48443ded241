import { Modu<PERSON> } from '@nestjs/common'
import { WorkspaceAiModule } from './ai/workspace-ai.module'
import { ProjectsModule } from './projects/projects.module'
import { TasksModule } from './tasks/tasks.module'
import { PhotosModule } from './photos/photos.module'
import { WorkspaceUsersModule } from './users/users.module'
import { ProgressModule } from './progress/progress.module'
import { CommentsModule } from './comments/comments.module'
import { FilesModule } from './files/files.module'
import { WebSocketModule } from './websocket/websocket.module'
import { MessageCenterModule } from './message-center/message-center.module'
import { ChatModule } from './chat/chat.module'

@Module({
  imports: [
    WorkspaceAiModule,
    ProjectsModule,
    TasksModule,
    PhotosModule,
    WorkspaceUsersModule,
    ProgressModule,
    CommentsModule,
    FilesModule,
    WebSocketModule,
    MessageCenterModule,
    ChatModule,
  ],
})
export class WorkspaceModule { } 