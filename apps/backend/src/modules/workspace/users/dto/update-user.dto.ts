import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsEnum } from 'class-validator';
import { Role } from '../../../../common/enums/role.enum';
import { UserStatus } from '../../../../common/enums/user-status.enum';

export class UpdateUserDto {
  @ApiProperty({ description: '名稱' })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({ description: '密碼' })
  @IsString()
  @IsOptional()
  password?: string;

  @ApiProperty({ 
    description: '角色（工作區僅支援租戶相關角色）', 
    enum: [Role.TENANT_ADMIN, Role.TENANT_USER],
    example: Role.TENANT_USER
  })
  @IsEnum([Role.TENANT_ADMIN, Role.TENANT_USER])
  @IsOptional()
  role?: Role.TENANT_ADMIN | Role.TENANT_USER;

  @ApiProperty({
    description: '使用者狀態',
    enum: UserStatus,
    example: 'active'
  })
  @IsEnum(UserStatus)
  @IsOptional()
  status?: UserStatus;
} 