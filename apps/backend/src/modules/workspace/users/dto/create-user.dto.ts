import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsString, IsOptional, IsEnum } from 'class-validator';
import { Role } from '@/common/enums/role.enum';
import { UserStatus } from '@/common/enums/user-status.enum';

export class CreateUserDto {
  @ApiProperty({ description: '使用者信箱' })
  @IsEmail()
  email: string;

  @ApiProperty({ description: '使用者密碼' })
  @IsString()
  password: string;

  @ApiProperty({ description: '使用者名稱' })
  @IsString()
  name: string;

  @ApiProperty({ 
    description: '使用者角色（工作區僅支援租戶相關角色）', 
    enum: [Role.TENANT_ADMIN, Role.TENANT_USER],
    example: Role.TENANT_USER
  })
  @IsEnum([Role.TENANT_ADMIN, Role.TENANT_USER])
  @IsOptional()
  role?: Role.TENANT_ADMIN | Role.TENANT_USER;

  @ApiProperty({ description: '使用者狀態', enum: UserStatus })
  @IsEnum(UserStatus)
  @IsOptional()
  status?: UserStatus;

  @ApiProperty({ description: '租戶 ID', required: true })
  @IsString()
  tenantId: string;
} 