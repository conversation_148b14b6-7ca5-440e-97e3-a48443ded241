import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
} from "@nestjs/common";
import {
  ApiT<PERSON>s,
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiQuery,
} from "@nestjs/swagger";
import { UsersService } from "./users.service";
import { JwtAuthGuard } from "../../core/auth/guards/auth.guard";
import { RolesGuard } from "../../core/auth/guards/roles.guard";
import { Roles } from "../../core/auth/decorators/roles.decorator";
import { Role, SYSTEM_ROLES } from "../../../common/enums/role.enum";
import { CurrentUser } from "../../core/auth/decorators/current-user.decorator";
import { CreateUserDto } from "./dto/create-user.dto";
import { UpdateUserDto } from "./dto/update-user.dto";
import { PoliciesGuard } from "../../../casl/guards/permission.guard";
import { CheckPolicies } from "../../../casl/decorators/check-policies.decorator";
import { AppAbility } from "../../../types/models/casl.model";
import { Actions, Subjects } from "@horizai/permissions";
import { tenant_users as TenantUser } from "@prisma/client";
import { USER_ROLES } from "../../../types/models/user.model";

@ApiTags("workspace/users")
@ApiBearerAuth()
@Controller("workspace/users")
@UseGuards(JwtAuthGuard, RolesGuard, PoliciesGuard)
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Post()
  @CheckPolicies((ability: AppAbility) =>
    ability.can(Actions.CREATE, Subjects.USER)
  )
  @ApiOperation({ summary: "建立工作區使用者" })
  @ApiResponse({ status: 201, description: "工作區使用者建立成功" })
  async create(
    @Body() createUserDto: CreateUserDto,
    @CurrentUser() user
  ): Promise<TenantUser> {
    return this.usersService.create({
      ...createUserDto,
      tenantId: user.tenantId, // 工作區使用者必須屬於當前租戶
    });
  }

  @Get()
  @CheckPolicies((ability: AppAbility) =>
    ability.can(Actions.READ, Subjects.USER)
  )
  @ApiOperation({ summary: "讀取工作區使用者列表" })
  @ApiResponse({ status: 200, description: "成功讀取工作區使用者列表" })
  async findAll(@CurrentUser() user): Promise<TenantUser[]> {
    // 只返回當前使用者所屬租戶的使用者
    const filters = {
      tenantId: user.tenantId,
    };
    return this.usersService.findAll(filters);
  }

  @Get(":id")
  @CheckPolicies((ability: AppAbility) =>
    ability.can(Actions.READ, Subjects.USER)
  )
  @ApiOperation({ summary: "讀取指定工作區使用者" })
  @ApiResponse({ status: 200, description: "成功讀取指定工作區使用者" })
  async findOne(
    @Param("id") id: string,
    @CurrentUser() user
  ): Promise<TenantUser> {
    return this.usersService.findOne(id, user.tenantId);
  }

  @Patch(":id")
  @CheckPolicies((ability: AppAbility) =>
    ability.can(Actions.UPDATE, Subjects.USER)
  )
  @ApiOperation({ summary: "更新工作區使用者" })
  @ApiResponse({ status: 200, description: "工作區使用者更新成功" })
  async update(
    @Param("id") id: string,
    @Body() updateUserDto: UpdateUserDto,
    @CurrentUser() user
  ): Promise<TenantUser> {
    // 先確認使用者存在且屬於同一租戶
    await this.usersService.findOne(id, user.tenantId);
    return this.usersService.update(id, updateUserDto);
  }

  @Delete(":id")
  @CheckPolicies((ability: AppAbility) =>
    ability.can(Actions.DELETE, Subjects.USER)
  )
  @ApiOperation({ summary: "刪除工作區使用者" })
  @ApiResponse({ status: 200, description: "工作區使用者刪除成功" })
  async remove(
    @Param("id") id: string,
    @CurrentUser() user
  ): Promise<TenantUser> {
    // 先確認使用者存在且屬於同一租戶
    await this.usersService.findOne(id, user.tenantId);
    return this.usersService.remove(id);
  }
}
