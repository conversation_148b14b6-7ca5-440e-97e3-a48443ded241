import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from "@nestjs/common";
import { PrismaService } from "../../core/prisma/prisma.service";
import * as bcrypt from "bcryptjs";
import { tenant_users as TenantUser, Prisma } from "@prisma/client";
import { CreateUserDto } from "./dto/create-user.dto";
import { UpdateUserDto } from "./dto/update-user.dto";
import { UserRole } from "../../../common/enums/role.enum";
import { Role } from "../../../common/enums/role.enum";

@Injectable()
export class UsersService {
  constructor(private prisma: PrismaService) {}

  async findOne(id: string, tenantId?: string): Promise<TenantUser> {
    const where: any = { id };

    // 如果提供了 tenantId，則限制只能查詢該租戶的使用者
    if (tenantId) {
      where.tenantId = tenantId;
    }

    const user = await this.prisma.tenant_users.findUnique({
      where,
      include: {
        tenant: {
          select: {
            id: true,
            name: true,
            status: true,
          },
        },
      },
    });

    if (!user) {
      throw new NotFoundException(`使用者 ID ${id} 不存在`);
    }

    return user;
  }

  async findByEmail(email: string): Promise<TenantUser | null> {
    return this.prisma.tenant_users.findUnique({
      where: { email },
      include: {
        tenant: {
          select: {
            id: true,
            name: true,
            status: true,
          },
        },
      },
    });
  }

  async findAll(filters?: {
    tenantId?: string;
    role?: (Role | string)[];
  }): Promise<TenantUser[]> {
    const where: Prisma.tenant_usersWhereInput = {};

    if (filters?.tenantId) {
      where.tenantId = filters.tenantId;
    }

    if (filters?.role?.length) {
      // 統一轉為 string，避免 enum 傳入
      (where as any).role = {
        in: filters.role.map((r) => r.toString()),
      };
    }

    return this.prisma.tenant_users.findMany({
      where,
      include: {
        tenant: {
          select: {
            id: true,
            name: true,
            status: true,
          },
        },
      },
    });
  }

  async findById(id: string): Promise<TenantUser | null> {
    return this.prisma.tenant_users.findUnique({
      where: { id },
    });
  }

  async create(createUserDto: CreateUserDto): Promise<TenantUser> {
    if (!createUserDto.tenantId) {
      throw new BadRequestException("租戶 ID 是必要欄位");
    }

    // 驗證角色是否為租戶相關角色
    if (
      createUserDto.role &&
      !["TENANT_ADMIN", "TENANT_USER"].includes(createUserDto.role.toString())
    ) {
      throw new BadRequestException("工作區僅支援租戶相關角色");
    }

    const hashedPassword = await bcrypt.hash(createUserDto.password, 10);

    // 修改 userData 型別定義，直接指定為 any 避免型別錯誤
    const userData: any = {
      id: undefined, // 讓 Prisma 自動生成
      email: createUserDto.email,
      password: hashedPassword,
      name: createUserDto.name,
      // 工作區使用者預設為租戶使用者角色
      role: createUserDto.role ? createUserDto.role.toString() : "TENANT_USER",
      status: createUserDto.status || "PENDING",
      tenantId: createUserDto.tenantId, // 明確指定 tenantId 而非使用 tenant 關聯
      created_at: new Date(),
      updated_at: new Date(),
    };

    return this.prisma.tenant_users.create({
      data: userData,
      include: {
        tenant: {
          select: {
            id: true,
            name: true,
            status: true,
          },
        },
      },
    });
  }

  async update(id: string, updateUserDto: UpdateUserDto): Promise<TenantUser> {
    const data: any = { ...updateUserDto };

    // 處理密碼更新
    if (updateUserDto.password) {
      data.password = await bcrypt.hash(updateUserDto.password, 10);
    }

    // 處理角色欄位，確保是字串且為租戶相關角色
    if (updateUserDto.role) {
      if (
        !["TENANT_ADMIN", "TENANT_USER"].includes(updateUserDto.role.toString())
      ) {
        throw new BadRequestException("工作區僅支援租戶相關角色");
      }
      data.role = updateUserDto.role.toString();
    }

    // 添加更新時間
    data.updated_at = new Date();

    return this.prisma.tenant_users.update({
      where: { id },
      data,
      include: {
        tenant: {
          select: {
            id: true,
            name: true,
            status: true,
          },
        },
      },
    });
  }

  async delete(id: string): Promise<TenantUser> {
    const user = await this.findOne(id);

    // 工作區使用者管理只處理租戶用戶，不會有系統管理員角色
    // 但為了安全起見，檢查角色字串值
    if (
      user.role.toString() === "SUPER_ADMIN" ||
      user.role.toString() === "SYSTEM_ADMIN"
    ) {
      throw new BadRequestException("無法在工作區中刪除系統管理員");
    }

    return this.prisma.tenant_users.delete({
      where: { id },
      include: {
        tenant: {
          select: {
            id: true,
            name: true,
            status: true,
          },
        },
      },
    });
  }

  async remove(id: string): Promise<TenantUser> {
    const user = await this.prisma.tenant_users.findUnique({ where: { id } });
    if (!user) {
      throw new NotFoundException("User not found");
    }
    return this.prisma.tenant_users.delete({ where: { id } });
  }

  private async getDefaultTenantId(): Promise<string> {
    const defaultTenant = await this.prisma.tenants.findFirst({
      where: {
        domain: "horizai.com",
      },
    });

    if (!defaultTenant) {
      throw new Error("預設租戶不存在");
    }

    return defaultTenant.id;
  }
}
