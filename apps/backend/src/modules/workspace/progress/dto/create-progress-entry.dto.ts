import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsNumber, IsArray, IsEnum, IsUUID, Min, Max, IsDateString } from 'class-validator';
import { ProgressType } from '@prisma/client';

export class CreateProgressEntryDto {
    @ApiProperty({
        description: '進度條目標題',
        example: '完成前端頁面設計'
    })
    @IsString()
    title: string;

    @ApiPropertyOptional({
        description: '進度條目描述',
        example: '完成了用戶登錄頁面和主頁面的設計'
    })
    @IsOptional()
    @IsString()
    description?: string;

    @ApiProperty({
        description: '進度類型',
        enum: ProgressType,
        example: ProgressType.TASK_UPDATE
    })
    @IsEnum(ProgressType)
    progressType: ProgressType;

    @ApiPropertyOptional({
        description: '進度百分比 (0-100)',
        example: 75,
        minimum: 0,
        maximum: 100
    })
    @IsOptional()
    @IsNumber()
    @Min(0)
    @Max(100)
    progressValue?: number;

    @ApiPropertyOptional({
        description: '狀態更新',
        example: 'in-progress'
    })
    @IsOptional()
    @IsString()
    status?: string;

    @ApiPropertyOptional({
        description: '備註',
        example: '需要進一步優化響應式設計'
    })
    @IsOptional()
    @IsString()
    notes?: string;

    @ApiPropertyOptional({
        description: '相關照片 URLs',
        type: [String],
        example: ['https://example.com/photo1.jpg', 'https://example.com/photo2.jpg']
    })
    @IsOptional()
    @IsArray()
    @IsString({ each: true })
    photoUrls?: string[];

    @ApiPropertyOptional({
        description: '關聯的項目 ID',
        example: 'clxxxxx'
    })
    @IsOptional()
    @IsUUID()
    projectId?: string;

    @ApiPropertyOptional({
        description: '關聯的任務 ID',
        example: 'clxxxxx'
    })
    @IsOptional()
    @IsUUID()
    taskId?: string;

    @ApiPropertyOptional({
        description: '實際發生時間 (ISO 8601)',
        example: '2024-06-07T10:30:00Z'
    })
    @IsOptional()
    @IsDateString()
    recordedAt?: string;

    @ApiPropertyOptional({
        description: '額外的元數據',
        example: { location: '台北辦公室', weather: '晴天' }
    })
    @IsOptional()
    metadata?: any;
} 