import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { ReportType } from '@prisma/client';

export class ProgressReportResponseDto {
    @ApiProperty({
        description: '報告 ID',
        example: 'clxxxxx'
    })
    id: string;

    @ApiProperty({
        description: '報告標題',
        example: '2024年第25週進度報告'
    })
    title: string;

    @ApiProperty({
        description: '報告類型',
        enum: ReportType,
        example: ReportType.WEEKLY
    })
    reportType: ReportType;

    @ApiProperty({
        description: '報告期間',
        example: '2024-W25'
    })
    period: string;

    @ApiProperty({
        description: '總任務數',
        example: 50
    })
    totalTasks: number;

    @ApiProperty({
        description: '已完成任務數',
        example: 35
    })
    completedTasks: number;

    @ApiProperty({
        description: '進行中任務數',
        example: 10
    })
    inProgressTasks: number;

    @ApiProperty({
        description: '逾期任務數',
        example: 5
    })
    overdueTasks: number;

    @ApiProperty({
        description: '完成率',
        example: 70.0
    })
    completionRate: number;

    @ApiPropertyOptional({
        description: '預測完成日期',
        example: '2024-08-15T00:00:00Z'
    })
    predictedCompletionDate?: Date;

    @ApiPropertyOptional({
        description: '風險等級',
        example: 'medium'
    })
    riskLevel?: string;

    @ApiPropertyOptional({
        description: 'AI 建議',
        example: {
            suggestions: ['增加人力資源', '調整任務優先級'],
            risks: ['可能延期交付'],
            opportunities: ['提前完成核心功能']
        }
    })
    recommendations?: any;

    @ApiPropertyOptional({
        description: '關聯的項目 ID',
        example: 'clxxxxx'
    })
    projectId?: string;

    @ApiProperty({
        description: '租戶 ID',
        example: 'clxxxxx'
    })
    tenantId: string;

    @ApiProperty({
        description: '生成者 ID',
        example: 'clxxxxx'
    })
    generatedBy: string;

    @ApiProperty({
        description: '報告日期',
        example: '2024-06-07T10:30:00Z'
    })
    reportDate: Date;

    @ApiProperty({
        description: '創建時間',
        example: '2024-06-07T10:30:00Z'
    })
    createdAt: Date;

    @ApiProperty({
        description: '更新時間',
        example: '2024-06-07T10:30:00Z'
    })
    updatedAt: Date;

    @ApiPropertyOptional({
        description: '關聯的項目信息',
        type: 'object',
        properties: {
            id: { type: 'string' },
            name: { type: 'string' },
            status: { type: 'string' }
        }
    })
    project?: {
        id: string;
        name: string;
        status: string;
    };
} 