import { PartialType, ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsEnum, IsDateString } from 'class-validator';
import { MilestoneStatus } from '@prisma/client';
import { CreateMilestoneDto } from './create-milestone.dto';

export class UpdateMilestoneDto extends PartialType(CreateMilestoneDto) {
    @ApiPropertyOptional({
        description: '里程碑狀態',
        enum: MilestoneStatus,
        example: MilestoneStatus.IN_PROGRESS
    })
    @IsOptional()
    @IsEnum(MilestoneStatus)
    status?: MilestoneStatus;

    @ApiPropertyOptional({
        description: '實際完成時間',
        example: '2024-06-30T15:30:00Z'
    })
    @IsOptional()
    @IsDateString()
    completedAt?: string;
} 