import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../../core/prisma/prisma.service';
import {
  CreateProgressEntryDto,
  UpdateProgressEntryDto,
  ProgressEntryResponseDto,
  CreateMilestoneDto,
  UpdateMilestoneDto,
  MilestoneResponseDto,
  ProgressReportResponseDto
} from './dto';
import { Prisma, ProgressType, MilestoneStatus, ReportType } from '@prisma/client';

@Injectable()
export class ProgressService {
  constructor(private readonly prisma: PrismaService) { }

  // ==================== 進度條目管理 ====================

  async createProgressEntry(
    createProgressEntryDto: CreateProgressEntryDto,
    userId: string,
    tenantId: string
  ): Promise<ProgressEntryResponseDto> {
    // 驗證項目和任務是否存在且屬於該租戶
    if (createProgressEntryDto.projectId) {
      const project = await this.prisma.projects.findFirst({
        where: { id: createProgressEntryDto.projectId, tenantId },
      });
      if (!project) {
        throw new NotFoundException('項目不存在');
      }
    }

    if (createProgressEntryDto.taskId) {
      const task = await this.prisma.tasks.findFirst({
        where: { id: createProgressEntryDto.taskId, tenantId },
      });
      if (!task) {
        throw new NotFoundException('任務不存在');
      }
    }

    const progressEntry = await this.prisma.progress_entries.create({
      data: {
        title: createProgressEntryDto.title,
        description: createProgressEntryDto.description,
        progressType: createProgressEntryDto.progressType,
        progressValue: createProgressEntryDto.progressValue,
        status: createProgressEntryDto.status,
        notes: createProgressEntryDto.notes,
        photoUrls: createProgressEntryDto.photoUrls || [],
        metadata: createProgressEntryDto.metadata,
        projectId: createProgressEntryDto.projectId,
        taskId: createProgressEntryDto.taskId,
        userId,
        tenantId,
        recordedAt: createProgressEntryDto.recordedAt ? new Date(createProgressEntryDto.recordedAt) : new Date(),
      },
      include: {
        project: {
          select: { id: true, name: true, status: true },
        },
        task: {
          select: { id: true, title: true, status: true },
        },
      },
    });

    return this.mapProgressEntryToResponseDto(progressEntry);
  }

  async findAllProgressEntries(
    tenantId: string,
    page: number = 1,
    limit: number = 10,
    projectId?: string,
    taskId?: string,
    progressType?: ProgressType,
    startDate?: string,
    endDate?: string
  ): Promise<{
    entries: ProgressEntryResponseDto[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    const skip = (page - 1) * limit;

    const where: Prisma.progress_entriesWhereInput = {
      tenantId,
      ...(projectId && { projectId }),
      ...(taskId && { taskId }),
      ...(progressType && { progressType }),
      ...(startDate && endDate && {
        recordedAt: {
          gte: new Date(startDate),
          lte: new Date(endDate),
        },
      }),
    };

    const [entries, total] = await Promise.all([
      this.prisma.progress_entries.findMany({
        where,
        skip,
        take: limit,
        orderBy: { recordedAt: 'desc' },
        include: {
          project: {
            select: { id: true, name: true, status: true },
          },
          task: {
            select: { id: true, title: true, status: true },
          },
        },
      }),
      this.prisma.progress_entries.count({ where }),
    ]);

    return {
      entries: entries.map(entry => this.mapProgressEntryToResponseDto(entry)),
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async findOneProgressEntry(id: string, tenantId: string): Promise<ProgressEntryResponseDto> {
    const entry = await this.prisma.progress_entries.findFirst({
      where: { id, tenantId },
      include: {
        project: {
          select: { id: true, name: true, status: true },
        },
        task: {
          select: { id: true, title: true, status: true },
        },
      },
    });

    if (!entry) {
      throw new NotFoundException('進度條目不存在');
    }

    return this.mapProgressEntryToResponseDto(entry);
  }

  async updateProgressEntry(
    id: string,
    updateProgressEntryDto: UpdateProgressEntryDto,
    tenantId: string
  ): Promise<ProgressEntryResponseDto> {
    const existingEntry = await this.prisma.progress_entries.findFirst({
      where: { id, tenantId },
    });

    if (!existingEntry) {
      throw new NotFoundException('進度條目不存在');
    }

    // 驗證項目和任務
    if (updateProgressEntryDto.projectId) {
      const project = await this.prisma.projects.findFirst({
        where: { id: updateProgressEntryDto.projectId, tenantId },
      });
      if (!project) {
        throw new NotFoundException('項目不存在');
      }
    }

    if (updateProgressEntryDto.taskId) {
      const task = await this.prisma.tasks.findFirst({
        where: { id: updateProgressEntryDto.taskId, tenantId },
      });
      if (!task) {
        throw new NotFoundException('任務不存在');
      }
    }

    const updatedEntry = await this.prisma.progress_entries.update({
      where: { id },
      data: {
        ...updateProgressEntryDto,
        recordedAt: updateProgressEntryDto.recordedAt ? new Date(updateProgressEntryDto.recordedAt) : undefined,
      },
      include: {
        project: {
          select: { id: true, name: true, status: true },
        },
        task: {
          select: { id: true, title: true, status: true },
        },
      },
    });

    return this.mapProgressEntryToResponseDto(updatedEntry);
  }

  async removeProgressEntry(id: string, tenantId: string): Promise<void> {
    const entry = await this.prisma.progress_entries.findFirst({
      where: { id, tenantId },
    });

    if (!entry) {
      throw new NotFoundException('進度條目不存在');
    }

    await this.prisma.progress_entries.delete({
      where: { id },
    });
  }

  // ==================== 里程碑管理 ====================

  async createMilestone(
    createMilestoneDto: CreateMilestoneDto,
    userId: string,
    tenantId: string
  ): Promise<MilestoneResponseDto> {
    // 驗證項目是否存在
    const project = await this.prisma.projects.findFirst({
      where: { id: createMilestoneDto.projectId, tenantId },
    });

    if (!project) {
      throw new NotFoundException('項目不存在');
    }

    const milestone = await this.prisma.project_milestones.create({
      data: {
        title: createMilestoneDto.title,
        description: createMilestoneDto.description,
        targetDate: new Date(createMilestoneDto.targetDate),
        priority: createMilestoneDto.priority || 'medium',
        projectId: createMilestoneDto.projectId,
        tenantId,
        createdById: userId,
      },
      include: {
        project: {
          select: { id: true, name: true, status: true },
        },
      },
    });

    return this.mapMilestoneToResponseDto(milestone);
  }

  async findAllMilestones(
    tenantId: string,
    projectId?: string,
    status?: MilestoneStatus
  ): Promise<MilestoneResponseDto[]> {
    const where: Prisma.project_milestonesWhereInput = {
      tenantId,
      ...(projectId && { projectId }),
      ...(status && { status }),
    };

    const milestones = await this.prisma.project_milestones.findMany({
      where,
      orderBy: { targetDate: 'asc' },
      include: {
        project: {
          select: { id: true, name: true, status: true },
        },
      },
    });

    return milestones.map(milestone => this.mapMilestoneToResponseDto(milestone));
  }

  async updateMilestone(
    id: string,
    updateMilestoneDto: UpdateMilestoneDto,
    tenantId: string
  ): Promise<MilestoneResponseDto> {
    const existingMilestone = await this.prisma.project_milestones.findFirst({
      where: { id, tenantId },
    });

    if (!existingMilestone) {
      throw new NotFoundException('里程碑不存在');
    }

    const updatedMilestone = await this.prisma.project_milestones.update({
      where: { id },
      data: {
        ...updateMilestoneDto,
        targetDate: updateMilestoneDto.targetDate ? new Date(updateMilestoneDto.targetDate) : undefined,
        completedAt: updateMilestoneDto.completedAt ? new Date(updateMilestoneDto.completedAt) : undefined,
      },
      include: {
        project: {
          select: { id: true, name: true, status: true },
        },
      },
    });

    return this.mapMilestoneToResponseDto(updatedMilestone);
  }

  // ==================== 進度報告 ====================

  async generateProgressReport(
    tenantId: string,
    projectId: string | null,
    reportType: ReportType,
    userId: string
  ): Promise<ProgressReportResponseDto> {
    // 計算報告期間
    const now = new Date();
    const period = this.calculateReportPeriod(now, reportType);

    // 獲取任務統計
    const taskStats = await this.getTaskStatistics(tenantId, projectId);

    // 生成報告標題
    const title = this.generateReportTitle(reportType, period, projectId);

    // 創建或更新報告
    const report = await this.prisma.progress_reports.upsert({
      where: {
        projectId_reportType_period: {
          projectId: projectId || '',
          reportType,
          period,
        },
      },
      update: {
        ...taskStats,
        generatedBy: userId,
        reportDate: now,
      },
      create: {
        title,
        reportType,
        period,
        ...taskStats,
        projectId,
        tenantId,
        generatedBy: userId,
        reportDate: now,
      },
      include: {
        project: projectId ? {
          select: { id: true, name: true, status: true },
        } : undefined,
      },
    });

    return this.mapReportToResponseDto(report);
  }

  // ==================== 私有方法 ====================

  private mapProgressEntryToResponseDto(entry: any): ProgressEntryResponseDto {
    return {
      id: entry.id,
      title: entry.title,
      description: entry.description,
      progressType: entry.progressType,
      progressValue: entry.progressValue,
      status: entry.status,
      notes: entry.notes,
      photoUrls: entry.photoUrls,
      projectId: entry.projectId,
      taskId: entry.taskId,
      userId: entry.userId,
      tenantId: entry.tenantId,
      recordedAt: entry.recordedAt,
      createdAt: entry.createdAt,
      updatedAt: entry.updatedAt,
      metadata: entry.metadata,
      project: entry.project,
      task: entry.task,
    };
  }

  private mapMilestoneToResponseDto(milestone: any): MilestoneResponseDto {
    return {
      id: milestone.id,
      title: milestone.title,
      description: milestone.description,
      targetDate: milestone.targetDate,
      completedAt: milestone.completedAt,
      status: milestone.status,
      priority: milestone.priority,
      projectId: milestone.projectId,
      tenantId: milestone.tenantId,
      createdById: milestone.createdById,
      createdAt: milestone.createdAt,
      updatedAt: milestone.updatedAt,
      project: milestone.project,
    };
  }

  private mapReportToResponseDto(report: any): ProgressReportResponseDto {
    return {
      id: report.id,
      title: report.title,
      reportType: report.reportType,
      period: report.period,
      totalTasks: report.totalTasks,
      completedTasks: report.completedTasks,
      inProgressTasks: report.inProgressTasks,
      overdueTasks: report.overdueTasks,
      completionRate: report.completionRate,
      predictedCompletionDate: report.predictedCompletionDate,
      riskLevel: report.riskLevel,
      recommendations: report.recommendations,
      projectId: report.projectId,
      tenantId: report.tenantId,
      generatedBy: report.generatedBy,
      reportDate: report.reportDate,
      createdAt: report.createdAt,
      updatedAt: report.updatedAt,
      project: report.project,
    };
  }

  private async getTaskStatistics(tenantId: string, projectId: string | null) {
    const where: Prisma.tasksWhereInput = {
      tenantId,
      ...(projectId && { projectId }),
    };

    const [total, completed, inProgress, overdue] = await Promise.all([
      this.prisma.tasks.count({ where }),
      this.prisma.tasks.count({ where: { ...where, status: 'done' } }),
      this.prisma.tasks.count({ where: { ...where, status: 'in-progress' } }),
      this.prisma.tasks.count({
        where: {
          ...where,
          dueDate: { lt: new Date() },
          status: { not: 'done' },
        },
      }),
    ]);

    const completionRate = total > 0 ? (completed / total) * 100 : 0;

    return {
      totalTasks: total,
      completedTasks: completed,
      inProgressTasks: inProgress,
      overdueTasks: overdue,
      completionRate: Math.round(completionRate * 100) / 100,
    };
  }

  private calculateReportPeriod(date: Date, reportType: ReportType): string {
    const year = date.getFullYear();

    switch (reportType) {
      case ReportType.DAILY:
        return `${year}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
      case ReportType.WEEKLY:
        const weekNumber = this.getWeekNumber(date);
        return `${year}-W${String(weekNumber).padStart(2, '0')}`;
      case ReportType.MONTHLY:
        return `${year}-${String(date.getMonth() + 1).padStart(2, '0')}`;
      case ReportType.QUARTERLY:
        const quarter = Math.floor(date.getMonth() / 3) + 1;
        return `${year}-Q${quarter}`;
      default:
        return `${year}-${String(date.getMonth() + 1).padStart(2, '0')}`;
    }
  }

  private getWeekNumber(date: Date): number {
    const firstDayOfYear = new Date(date.getFullYear(), 0, 1);
    const pastDaysOfYear = (date.getTime() - firstDayOfYear.getTime()) / 86400000;
    return Math.ceil((pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7);
  }

  private generateReportTitle(reportType: ReportType, period: string, projectId: string | null): string {
    const typeMap = {
      [ReportType.DAILY]: '日報',
      [ReportType.WEEKLY]: '週報',
      [ReportType.MONTHLY]: '月報',
      [ReportType.QUARTERLY]: '季報',
      [ReportType.PROJECT]: '項目報告',
      [ReportType.CUSTOM]: '自定義報告',
    };

    const prefix = projectId ? '項目' : '整體';
    return `${prefix}${typeMap[reportType]} - ${period}`;
  }

  async getProgressOverview(tenantId: string, projectId?: string): Promise<{
    summary: {
      totalProjects: number;
      totalTasks: number;
      totalProgressEntries: number;
      totalMilestones: number;
    };
    progress: {
      averageProjectProgress: number | null;
      completedTasks: number;
      inProgressTasks: number;
      pendingTasks: number;
      overdueTasks: number;
    };
    milestones: {
      completed: number;
      pending: number;
      overdue: number;
    };
    recentActivity: Array<{
      id: string;
      title: string;
      type: string;
      date: Date;
      projectName?: string;
      taskTitle?: string;
    }>;
  }> {
    const where = projectId ? { projectId, tenantId } : { tenantId };
    const projectWhere = projectId ? { id: projectId, tenantId } : { tenantId };

    // 基本統計
    const [
      totalProjects,
      totalTasks,
      totalProgressEntries,
      totalMilestones,
      tasksByStatus,
      milestonesByStatus,
      recentProgressEntries,
      progressValues,
    ] = await Promise.all([
      this.prisma.projects.count({ where: projectWhere }),
      this.prisma.tasks.count({ where }),
      this.prisma.progress_entries.count({ where }),
      this.prisma.project_milestones.count({ where }),
      this.prisma.tasks.groupBy({
        by: ['status'],
        where,
        _count: { status: true },
      }),
      this.prisma.project_milestones.groupBy({
        by: ['status'],
        where,
        _count: { status: true },
      }),
      this.prisma.progress_entries.findMany({
        where,
        select: {
          id: true,
          title: true,
          progressType: true,
          recordedAt: true,
          project: {
            select: { name: true },
          },
          task: {
            select: { title: true },
          },
        },
        orderBy: { recordedAt: 'desc' },
        take: 10,
      }),
      this.prisma.progress_entries.findMany({
        where: {
          ...where,
          progressValue: { not: null },
        },
        select: { progressValue: true },
      }),
    ]);

    // 處理任務統計
    const taskStats = tasksByStatus.reduce((acc, stat) => {
      acc[stat.status] = stat._count.status;
      return acc;
    }, {} as Record<string, number>);

    const completedTasks = taskStats['completed'] || taskStats['done'] || 0;
    const inProgressTasks = taskStats['in-progress'] || 0;
    const pendingTasks = taskStats['pending'] || taskStats['todo'] || 0;

    // 計算逾期任務
    const overdueTasks = await this.prisma.tasks.count({
      where: {
        ...where,
        dueDate: { lt: new Date() },
        status: { notIn: ['completed', 'done', 'cancelled'] },
      },
    });

    // 處理里程碑統計
    const milestoneStats = milestonesByStatus.reduce((acc, stat) => {
      acc[stat.status] = stat._count.status;
      return acc;
    }, {} as Record<string, number>);

    const completedMilestones = milestoneStats['COMPLETED'] || 0;
    const pendingMilestones = milestoneStats['PENDING'] || 0;
    const overdueMilestones = milestoneStats['OVERDUE'] || 0;

    // 計算平均進度
    const validProgressValues = progressValues
      .map(entry => entry.progressValue)
      .filter(value => value !== null) as number[];

    const averageProjectProgress = validProgressValues.length > 0
      ? validProgressValues.reduce((sum, val) => sum + val, 0) / validProgressValues.length
      : null;

    // 處理最近活動
    const recentActivity = recentProgressEntries.map(entry => ({
      id: entry.id,
      title: entry.title,
      type: entry.progressType,
      date: entry.recordedAt,
      projectName: entry.project?.name,
      taskTitle: entry.task?.title,
    }));

    return {
      summary: {
        totalProjects,
        totalTasks,
        totalProgressEntries,
        totalMilestones,
      },
      progress: {
        averageProjectProgress: averageProjectProgress ? Math.round(averageProjectProgress * 100) / 100 : null,
        completedTasks,
        inProgressTasks,
        pendingTasks,
        overdueTasks,
      },
      milestones: {
        completed: completedMilestones,
        pending: pendingMilestones,
        overdue: overdueMilestones,
      },
      recentActivity,
    };
  }
} 