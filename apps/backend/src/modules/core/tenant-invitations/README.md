# 租戶邀請功能重構說明

## 重構目的

將原本分散在 `admin` 和 `api` 模組中的租戶邀請功能整合到一個核心模組，以避免程式碼重複及責任重疊。

## 實施方案

採用"方案一"：建立獨立的核心租戶邀請模組（`core/tenant-invitations`），並保持分離的控制器以支持不同使用者角色的需求。

## 主要變更

### 1. 新增核心模組

建立了以下新檔案：

- `modules/core/tenant-invitations/tenant-invitations.module.ts`: 定義模組相依性
- `modules/core/tenant-invitations/tenant-invitations.service.ts`: 集中所有邀請相關邏輯
- `modules/core/tenant-invitations/controllers/tenant-invitations-admin.controller.ts`: 管理員層級控制器
- `modules/core/tenant-invitations/controllers/tenant-invitations-member.controller.ts`: 租戶管理員層級控制器
- `modules/core/tenant-invitations/dto/create-invitation.dto.ts`: 相關 DTO 定義

### 2. 功能增強

- 整合了邀請發送、接受、驗證、批准、拒絕等功能到同一個服務
- 為批准和拒絕邀請增加了電子郵件通知
- 統一了錯誤處理與日誌記錄

### 3. 相容性與遷移

原有的 `admin/tenant-invitations` 和 `api/tenant-invitations` 模組仍然保留，但現在只是作為包裝器，重新導出核心模組的功能。這種方式可以確保現有的程式碼不會因為重構而損壞。

## 後續工作

1. 前端代碼可能需要根據新的 API 結構進行調整
2. 舊的模組應在確認系統穩定後逐步移除
3. 單元測試和整合測試應更新以覆蓋新的核心模組功能
