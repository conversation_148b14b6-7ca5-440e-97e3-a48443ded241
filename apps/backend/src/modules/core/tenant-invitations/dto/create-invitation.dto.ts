import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsEnum, IsString } from 'class-validator';
import { Role } from '../../../../common/enums/role.enum';

export class CreateInvitationDto {
  @ApiProperty({ description: '邀請的電子郵件地址' })
  @IsEmail({}, { message: '請輸入有效的電子郵件地址' })
  email: string;

  @ApiProperty({ description: '租戶 ID' })
  @IsString({ message: '租戶 ID 必須為字串' })
  tenantId: string;

  @ApiProperty({ 
    description: '角色',
    enum: Role,
    default: "user",
    example: "user"
  })
  @IsEnum(Role, {
    message: '無效的角色，請選擇：TENANT_ADMIN（租戶管理員）或 TENANT_USER（租戶使用者）'
  })
  role: Role;
}
