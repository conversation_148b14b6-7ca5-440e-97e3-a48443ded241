import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as crypto from 'crypto';

/**
 * 加密服務 - 提供應用程式通用的加密和解密功能
 * 用於保護敏感數據如 API 金鑰、密碼等
 */
@Injectable()
export class EncryptionService {
  private readonly logger = new Logger(EncryptionService.name);
  private readonly encryptionKey: Buffer;
  private readonly algorithm = 'aes-256-cbc';

  constructor(private readonly configService: ConfigService) {
    // 從環境變數取得加密密鑰
    this.encryptionKey = Buffer.from(
      this.configService.get<string>('ENCRYPTION_KEY', ''),
      'hex',
    );
    
    // 檢查加密金鑰是否有效
    if (this.encryptionKey.length !== 32) {
      this.logger.warn(
        '警告: ENCRYPTION_KEY 環境變數未設定或不是有效的 32 位元組長度。加密功能可能無法正常工作。',
      );
    }
  }

  /**
   * 加密文字
   * @param plain 明文
   * @returns 加密後的 iv:cipherText 格式 (hex)
   */
  encrypt(plain: string): string {
    if (!plain) return plain;
    
    try {
      const iv = crypto.randomBytes(16);
      const cipher = crypto.createCipheriv(this.algorithm, this.encryptionKey, iv);
      let encrypted = cipher.update(plain, 'utf8', 'hex');
      encrypted += cipher.final('hex');
      return iv.toString('hex') + ':' + encrypted;
    } catch (error) {
      this.logger.error(`加密失敗: ${error.message}`, error.stack);
      throw new Error('加密操作失敗');
    }
  }

  /**
   * 解密文字
   * @param cipherText 密文 (格式: iv:cipherText，兩者均為 hex)
   * @returns 解密後的明文
   */
  decrypt(cipherText: string): string {
    if (!cipherText || !this.isEncrypted(cipherText)) return cipherText;
    
    try {
      const [ivHex, encryptedHex] = cipherText.split(':');
      const iv = Buffer.from(ivHex, 'hex');
      const decipher = crypto.createDecipheriv(
        this.algorithm,
        this.encryptionKey,
        iv,
      );
      let decrypted = decipher.update(encryptedHex, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      return decrypted;
    } catch (error) {
      this.logger.error(`解密失敗: ${error.message}`, error.stack);
      // 返回原始密文，避免因解密錯誤而中斷程序
      return cipherText;
    }
  }

  /**
   * 檢查字串是否為加密格式
   * @param text 待檢查的字串
   * @returns 是否符合加密格式
   */
  isEncrypted(text: string): boolean {
    // 檢查是否符合 iv:cipherText 格式 (iv 為 32 字符的 hex，密文為 hex)
    return typeof text === 'string' && /^[a-f0-9]{32}:[a-f0-9]+$/.test(text);
  }

  /**
   * 生成安全的隨機 token
   * @param length token 長度 (以位元組為單位，預設 32)
   * @returns hex 格式的隨機 token
   */
  generateRandomToken(length: number = 32): string {
    return crypto.randomBytes(length).toString('hex');
  }
} 