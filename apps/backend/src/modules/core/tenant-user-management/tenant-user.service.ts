import {
  Injectable,
  Logger,
  NotFoundException,
  ConflictException,
  ForbiddenException,
} from "@nestjs/common";
import { PrismaService } from "../prisma/prisma.service";
import * as bcrypt from "bcryptjs";
import { TenantUserRole, TenantUserStatus } from "@prisma/client";
import {
  ITenantUser,
  ICreateTenantUser,
  IUpdateTenantUser,
  ITenantUserProfile,
  ITenantUserInvitation,
} from "../../../types/models/tenant-user.model";
import { Prisma } from "@prisma/client";

@Injectable()
export class TenantUserService {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * 建立租戶使用者
   */
  async createTenantUser(data: ICreateTenantUser): Promise<ITenantUserProfile> {
    // 驗證租戶是否存在
    const tenant = await this.prisma.tenants.findUnique({
      where: { id: data.tenantId },
    });

    if (!tenant) {
      throw new NotFoundException("Tenant not found");
    }

    // 檢查 email 是否已存在
    const existingUser = await this.findByEmail(data.email);
    if (existingUser) {
      throw new ConflictException("Email already exists");
    }

    // 雜湊密碼
    const hashedPassword = await bcrypt.hash(data.password, 12);

    const tenantUser = await this.prisma.tenant_users.create({
      data: {
        email: data.email,
        password: hashedPassword,
        name: data.name,
        role: data.role || TenantUserRole.TENANT_USER,
        status: data.status || TenantUserStatus.ACTIVE,
        tenantId: data.tenantId,
        invitedBy: data.invitedBy,
        avatar: data.avatar,
        phone: data.phone,
        title: data.title,
        department: data.department,
      },
      include: {
        tenant: true,
      },
    });

    return this.toProfile(tenantUser);
  }

  /**
   * 邀請租戶使用者
   */
  async inviteTenantUser(
    invitation: ITenantUserInvitation
  ): Promise<ITenantUserProfile> {
    // 檢查邀請者是否有權限
    const inviter = await this.findById(invitation.invitedBy);
    if (!inviter || inviter.tenantId !== invitation.tenantId) {
      throw new ForbiddenException("Inviter does not have permission");
    }

    // 檢查邀請者是否為管理員
    if (inviter.role !== TenantUserRole.TENANT_ADMIN) {
      throw new ForbiddenException("Only tenant admin can invite users");
    }

    // 生成臨時密碼（實際應用中應發送邀請郵件）
    const tempPassword = this.generateTempPassword();

    const userData: ICreateTenantUser = {
      email: invitation.email,
      password: tempPassword,
      name: invitation.name,
      tenantId: invitation.tenantId,
      role: invitation.role || TenantUserRole.TENANT_USER,
      status: TenantUserStatus.PENDING, // 待激活狀態
      invitedBy: invitation.invitedBy,
    };

    return this.createTenantUser(userData);
  }

  /**
   * 根據 ID 查找租戶使用者
   */
  async findById(id: string): Promise<ITenantUser | null> {
    const user = await this.prisma.tenant_users.findUnique({
      where: { id },
      include: {
        tenant: true,
      },
    });

    return user as ITenantUser | null;
  }

  /**
   * 根據 email 查找租戶使用者
   */
  async findByEmail(email: string): Promise<ITenantUser | null> {
    const user = await this.prisma.tenant_users.findUnique({
      where: { email },
      include: {
        tenant: true,
      },
    });

    return user as ITenantUser | null;
  }

  /**
   * 根據租戶 ID 獲取使用者列表
   */
  async findByTenantId(tenantId: string): Promise<ITenantUserProfile[]> {
    const users = await this.prisma.tenant_users.findMany({
      where: { tenantId },
      include: {
        tenant: true,
      },
      orderBy: { createdAt: "desc" },
    });

    return users.map((user) => this.toProfile(user as any));
  }

  /**
   * 獲取租戶使用者資料（不含密碼）
   */
  async getProfile(id: string): Promise<ITenantUserProfile> {
    const user = await this.findById(id);
    if (!user) {
      throw new NotFoundException("Tenant user not found");
    }
    return this.toProfile(user);
  }

  /**
   * 更新租戶使用者
   */
  async updateTenantUser(
    id: string,
    data: IUpdateTenantUser
  ): Promise<ITenantUserProfile> {
    const existingUser = await this.findById(id);
    if (!existingUser) {
      throw new NotFoundException("Tenant user not found");
    }

    // 如果更新 email，檢查是否與其他使用者衝突
    if (data.email && data.email !== existingUser.email) {
      const emailExists = await this.findByEmail(data.email);
      if (emailExists) {
        throw new ConflictException("Email already exists");
      }
    }

    const updatedUser = await this.prisma.tenant_users.update({
      where: { id },
      data,
      include: {
        tenant: true,
      },
    });

    return this.toProfile(updatedUser as any);
  }

  /**
   * 刪除租戶使用者
   */
  async deleteTenantUser(id: string): Promise<void> {
    const existingUser = await this.findById(id);
    if (!existingUser) {
      throw new NotFoundException("Tenant user not found");
    }

    await this.prisma.tenant_users.delete({
      where: { id },
    });
  }

  /**
   * 驗證密碼
   */
  async validatePassword(
    user: ITenantUser,
    password: string
  ): Promise<boolean> {
    // 需要從資料庫獲取完整的使用者資料（包含密碼）
    const fullUser = await this.prisma.tenant_users.findUnique({
      where: { id: user.id },
      select: { password: true },
    });

    if (!fullUser) {
      return false;
    }

    return bcrypt.compare(password, fullUser.password);
  }

  /**
   * 更新密碼
   */
  async updatePassword(id: string, newPassword: string): Promise<void> {
    const hashedPassword = await bcrypt.hash(newPassword, 12);

    await this.prisma.tenant_users.update({
      where: { id },
      data: {
        password: hashedPassword,
        passwordLastChangedAt: new Date(),
      },
    });
  }

  /**
   * 更新最後登入時間
   */
  async updateLastLogin(id: string, ip?: string): Promise<void> {
    await this.prisma.tenant_users.update({
      where: { id },
      data: {
        lastLoginAt: new Date(),
        lastLoginIp: ip,
      },
    });
  }

  /**
   * 更新最後登出時間
   */
  async updateLastLogout(id: string): Promise<void> {
    // 注意：TenantUser 模型只有 lastLoginAt，沒有 lastLogoutAt 欄位
    // 登出時不需要更新任何欄位，或者可以在其他地方記錄登出事件
    // 這個方法保留是為了 API 一致性
  }

  /**
   * 激活使用者帳號
   */
  async activateUser(
    id: string,
    newPassword: string
  ): Promise<ITenantUserProfile> {
    const hashedPassword = await bcrypt.hash(newPassword, 12);

    const updatedUser = await this.prisma.tenant_users.update({
      where: { id },
      data: {
        password: hashedPassword,
        status: TenantUserStatus.ACTIVE,
        passwordLastChangedAt: new Date(),
      },
      include: {
        tenant: true,
      },
    });

    return this.toProfile(updatedUser as any);
  }

  /**
   * 生成臨時密碼
   */
  private generateTempPassword(): string {
    const chars =
      "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*";
    let password = "";
    for (let i = 0; i < 12; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return password;
  }

  /**
   * 轉換為 Profile 格式（不含密碼）
   */
  private toProfile(user: any): ITenantUserProfile {
    const { password, ...profile } = user;
    return profile as ITenantUserProfile;
  }
}
