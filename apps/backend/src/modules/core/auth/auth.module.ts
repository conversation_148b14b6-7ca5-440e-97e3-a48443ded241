import { Module } from '@nestjs/common';
import { AuthService } from './auth.service';
import { AuthController } from './auth.controller';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { JwtStrategy, JwtRefreshStrategy } from './strategies/jwt.strategy';
import { LocalStrategy } from './strategies/local.strategy';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { PrismaModule } from '../prisma/prisma.module';
import { CaslModule } from '../../../casl/casl.module';
import { WorkspaceUsersModule } from '../../workspace/users/users.module';
import { GoogleStrategy } from './strategies/google.strategy';
import { LineStrategy } from './strategies/line.strategy';
import { CaslAbilityFactory } from '../../../casl/casl-ability.factory';
import { SystemUsersService } from '../../admin/system-users/system-users.service';
import { TenantUsersService } from '../../admin/tenant-users/tenant-users.service';
import { MailModule } from '../mail/mail.module';
import { MfaService } from './mfa.service';
import { CommonModule } from '../../../common/common.module';
import { UserManagementModule } from '../user-management/user-management.module';
import { StorageModule } from '../storage/storage.module';
import { TenantsModule } from '../../admin/tenants/tenants.module';

@Module({
  imports: [
    PassportModule.register({ defaultStrategy: 'jwt' }),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => {
        const secret = configService.get<string>('JWT_ACCESS_SECRET');
        if (!secret) {
          throw new Error('JWT_ACCESS_SECRET is not defined in environment variables.');
        }
        return {
          secret,
          signOptions: {
            expiresIn: configService.get<string | number>('JWT_ACCESS_EXPIRATION_TIME') || '1h',
          },
        };
      },
      inject: [ConfigService],
    }),
    PrismaModule,
    CaslModule,
    WorkspaceUsersModule,
    MailModule,
    CommonModule,
    UserManagementModule,
    StorageModule,
    TenantsModule,
  ],
  controllers: [AuthController],
  providers: [
    AuthService,
    JwtStrategy,
    JwtRefreshStrategy,
    LocalStrategy,
    {
      provide: GoogleStrategy,
      useFactory: (configService: ConfigService, authService: AuthService) => {
        const clientId = configService.get<string>('GOOGLE_CLIENT_ID');
        const clientSecret = configService.get<string>('GOOGLE_CLIENT_SECRET');

        if (clientId && clientSecret) {
          return new GoogleStrategy(configService, authService);
        }
        return null;
      },
      inject: [ConfigService, AuthService],
    },
    {
      provide: LineStrategy,
      useFactory: (configService: ConfigService, authService: AuthService) => {
        const clientId = configService.get<string>('LINE_CLIENT_ID');
        const clientSecret = configService.get<string>('LINE_CLIENT_SECRET');

        if (clientId && clientSecret) {
          return new LineStrategy(configService, authService);
        }
        return null;
      },
      inject: [ConfigService, AuthService],
    },
    CaslAbilityFactory,
    SystemUsersService,
    TenantUsersService,
    MfaService,
  ],
  exports: [AuthService, JwtModule, CaslAbilityFactory, MfaService],
})
export class AuthModule {}
