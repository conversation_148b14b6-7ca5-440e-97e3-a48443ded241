import { IsString, IsE<PERSON>, <PERSON><PERSON><PERSON>th, IsBoolean, IsOptional, IsDefined, IsEnum } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

/**
 * 第一階段：個人資訊註冊 DTO
 */
export class StageOneRegisterDto {
  @ApiProperty({ 
    example: '<EMAIL>', 
    description: '公司信箱（不接受免費信箱）' 
  })
  @IsDefined({ message: '電子郵件必填' })
  @IsEmail({}, { message: '電子郵件格式不正確' })
  email: string;

  @ApiProperty({ 
    example: 'password123', 
    description: '密碼（至少8個字元）' 
  })
  @IsDefined({ message: '密碼必填' })
  @IsString({ message: '密碼必填' })
  @MinLength(8, { message: '密碼至少8個字元' })
  password: string;

  @ApiProperty({ 
    example: 'John Doe', 
    description: '使用者名稱' 
  })
  @IsDefined({ message: '使用者名稱必填' })
  @IsString({ message: '使用者名稱必填' })
  name: string;

  @ApiProperty({ 
    example: '+886912345678', 
    description: '手機號碼',
    required: false 
  })
  @IsOptional()
  @IsString({ message: '手機號碼必須是字串' })
  phone?: string;

  @ApiProperty({ 
    example: '專案經理', 
    description: '職稱',
    required: false 
  })
  @IsOptional()
  @IsString({ message: '職稱必須是字串' })
  title?: string;
}

/**
 * 第二階段：公司設置 DTO
 */
export class StageTwoRegisterDto {
  @ApiProperty({ 
    example: 'stage-one-token-here', 
    description: '第一階段註冊返回的 token' 
  })
  @IsDefined({ message: '第一階段 token 必填' })
  @IsString({ message: '第一階段 token 必填' })
  stageOneToken: string;

  @ApiProperty({ 
    example: '創新科技有限公司', 
    description: '公司名稱' 
  })
  @IsDefined({ message: '公司名稱必填' })
  @IsString({ message: '公司名稱必填' })
  companyName: string;

  @ApiProperty({ 
    example: 'TW', 
    description: '國家代碼（ISO 3166-1 alpha-2）' 
  })
  @IsDefined({ message: '國家代碼必填' })
  @IsString({ message: '國家代碼必填' })
  country: string;

  @ApiProperty({ 
    example: '11-50', 
    description: '公司規模',
    enum: ['1-10', '11-50', '51-200', '201-500', '500+'] 
  })
  @IsDefined({ message: '公司規模必填' })
  @IsEnum(['1-10', '11-50', '51-200', '201-500', '500+'], { 
    message: '公司規模必須是：1-10, 11-50, 51-200, 201-500, 500+' 
  })
  companySize: string;

  @ApiProperty({ 
    example: '資訊科技', 
    description: '產業類型' 
  })
  @IsDefined({ message: '產業類型必填' })
  @IsString({ message: '產業類型必填' })
  industry: string;

  @ApiProperty({ 
    example: 'example-company', 
    description: '公司域名（用於子域名）',
    required: false 
  })
  @IsOptional()
  @IsString({ message: '公司域名必須是字串' })
  subdomain?: string;

  @ApiProperty({ 
    example: '我們是一家專注於創新技術解決方案的公司', 
    description: '公司描述',
    required: false 
  })
  @IsOptional()
  @IsString({ message: '公司描述必須是字串' })
  description?: string;
}

/**
 * 加入現有租戶 DTO
 */
export class JoinTenantDto {
  @ApiProperty({ 
    example: 'invitation-token-here', 
    description: '邀請 token' 
  })
  @IsDefined({ message: '邀請 token 必填' })
  @IsString({ message: '邀請 token 必填' })
  invitationToken: string;

  @ApiProperty({ 
    example: '<EMAIL>', 
    description: '電子郵件' 
  })
  @IsDefined({ message: '電子郵件必填' })
  @IsEmail({}, { message: '電子郵件格式不正確' })
  email: string;

  @ApiProperty({ 
    example: 'password123', 
    description: '密碼（至少8個字元）' 
  })
  @IsDefined({ message: '密碼必填' })
  @IsString({ message: '密碼必填' })
  @MinLength(8, { message: '密碼至少8個字元' })
  password: string;

  @ApiProperty({ 
    example: 'John Doe', 
    description: '使用者名稱' 
  })
  @IsDefined({ message: '使用者名稱必填' })
  @IsString({ message: '使用者名稱必填' })
  name: string;

  @ApiProperty({ 
    example: '+886912345678', 
    description: '手機號碼',
    required: false 
  })
  @IsOptional()
  @IsString({ message: '手機號碼必須是字串' })
  phone?: string;

  @ApiProperty({ 
    example: '專案經理', 
    description: '職稱',
    required: false 
  })
  @IsOptional()
  @IsString({ message: '職稱必須是字串' })
  title?: string;
}

/**
 * 第一階段註冊回應 DTO
 */
export class StageOneRegisterResponseDto {
  @ApiProperty({ description: '第一階段 token，用於第二階段註冊' })
  stageOneToken: string;

  @ApiProperty({ description: '使用者 ID' })
  userId: string;

  @ApiProperty({ description: '使用者信箱' })
  email: string;

  @ApiProperty({ description: '使用者名稱' })
  name: string;

  @ApiProperty({ description: '下一步指示' })
  message: string;
}

/**
 * 完整註冊回應 DTO
 */
export class CompleteRegistrationResponseDto {
  @ApiProperty({ description: '使用者資訊' })
  user: {
    id: string;
    email: string;
    name: string;
    userType: 'system' | 'tenant';
    tenantId: string | null;
    role: string;
  };

  @ApiProperty({ description: '租戶資訊' })
  tenant: {
    id: string;
    name: string;
    domain: string;
    subdomain?: string;
    status: string;
  };

  @ApiProperty({ description: '存取 token' })
  accessToken: string;

  @ApiProperty({ description: '註冊完成訊息' })
  message: string;
}

/**
 * 檢查租戶唯一性 DTO
 */
export class CheckTenantUniquenessDto {
  @ApiProperty({ 
    example: '創新科技有限公司', 
    description: '公司名稱' 
  })
  @IsDefined({ message: '公司名稱必填' })
  @IsString({ message: '公司名稱必填' })
  companyName: string;

  @ApiProperty({ 
    example: 'TW', 
    description: '國家代碼（ISO 3166-1 alpha-2）' 
  })
  @IsDefined({ message: '國家代碼必填' })
  @IsString({ message: '國家代碼必填' })
  country: string;

  @ApiProperty({ 
    example: 'example-company', 
    description: '公司域名（用於子域名）',
    required: false 
  })
  @IsOptional()
  @IsString({ message: '公司域名必須是字串' })
  subdomain?: string;
}

/**
 * 租戶唯一性檢查回應 DTO
 */
export class TenantUniquenessResponseDto {
  @ApiProperty({ description: '是否可用' })
  available: boolean;

  @ApiProperty({ description: '檢查結果訊息' })
  message: string;

  @ApiProperty({ description: '衝突的欄位', required: false })
  conflicts?: string[];

  @ApiProperty({ description: '建議的替代方案', required: false })
  suggestions?: {
    companyName?: string[];
    subdomain?: string[];
  };
}
