import { Is<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsBoolean, IsOptional, IsDefined, IsIn } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class LoginDto {
  @ApiProperty({ example: '<EMAIL>' })
  @IsDefined({ message: '電子郵件必填' })
  @IsEmail({}, { message: '電子郵件格式不正確' })
  email: string;

  @ApiProperty({ example: 'password123' })
  @IsDefined({ message: '密碼必填' })
  @IsString({ message: '密碼必填' })
  @MinLength(6, { message: '密碼至少6個字元' })
  password: string;

  @ApiProperty({ example: false, required: false })
  @IsOptional()
  @IsBoolean({ message: 'rememberMe 必須是布林值' })
  rememberMe?: boolean;

  @ApiPropertyOptional({
    description: "使用者類型（可選，如果不提供則自動判斷）",
    enum: ["system", "tenant"],
    example: "tenant",
  })
  @IsOptional()
  @IsIn(["system", "tenant"])
  userType?: "system" | "tenant";
}