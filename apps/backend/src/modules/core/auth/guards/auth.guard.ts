import {
  Injectable,
  CanActivate,
  ExecutionContext,
  Logger,
  UnauthorizedException,
  Type,
  mixin,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { AuthGuard as PassportAuthGuard } from '@nestjs/passport';
import { IS_PUBLIC_KEY } from '../../../../common/decorators/public.decorator';
import { ROLES_KEY } from '../decorators/roles.decorator';
import { Role } from '../../../../common/enums/role.enum';

/**
 * 建立一個整合認證守衛
 * @param strategy 認證策略，默認為 'jwt'
 * @returns 一個可注入的守衛類
 */
export function createAuthGuard(
  strategy: 'jwt' | 'local' | 'jwt-refresh' = 'jwt',
): Type<CanActivate> {
  @Injectable()
  class MixinAuthGuard implements CanActivate {
    private readonly logger = new Logger(`AuthGuard(${strategy})`);
    private passportGuard: any;

    constructor(private reflector: Reflector) {
      this.passportGuard = new (PassportAuthGuard(strategy))();
    }

    async canActivate(context: ExecutionContext): Promise<boolean> {
      // 對於 jwt-refresh 策略，直接執行簡化的認證流程
      if (strategy === 'jwt-refresh') {
        return this.handleRefreshToken(context);
      }

      // 檢查是否為公共路由
      const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
        context.getHandler(),
        context.getClass(),
      ]);

      if (isPublic) {
        return true;
      }

      // 檢查是否為 Swagger UI 路由或其靜態資源
      const request = context.switchToHttp().getRequest();
      const path = request.path;

      if (path.startsWith('/api/docs') || path.startsWith('/swagger-static')) {
        return true;
      }

      // 使用選定的認證策略進行認證
      try {
        // 使用 Promise 處理 canActivate 的結果
        const canActivateAuth = (await this.passportGuard.canActivate(context)) as boolean;

        if (!canActivateAuth) {
          this.logger.warn(`認證失敗: ${strategy} 策略`);
          return false;
        }

        // 如果是本地認證策略，不需要檢查角色
        if (strategy === 'local') {
          return true;
        }

        // 檢查角色權限
        return this.checkRoles(context);
      } catch (error) {
        this.logger.error(`認證過程中發生錯誤: ${error.message}`);
        throw new UnauthorizedException('認證失敗');
      }
    }

    /**
     * 處理刷新令牌的簡化認證流程
     * @param context 執行上下文
     * @returns 認證結果
     */
    private async handleRefreshToken(context: ExecutionContext): Promise<boolean> {
      try {
        const result = await this.passportGuard.canActivate(context);
        if (!result) {
          throw new UnauthorizedException('刷新令牌認證失敗');
        }

        // 獲取已經由 Passport 策略解析並附加到請求的使用者
        const request = context.switchToHttp().getRequest();
        return !!request.user;
      } catch (error) {
        this.logger.error(`刷新令牌過程中發生錯誤: ${error.message}`);
        throw new UnauthorizedException('無效或缺少刷新令牌');
      }
    }

    private checkRoles(context: ExecutionContext): boolean {
      const requiredRoles = this.reflector.getAllAndOverride<(Role | string)[]>(ROLES_KEY, [
        context.getHandler(),
        context.getClass(),
      ]);

      if (!requiredRoles || requiredRoles.length === 0) {
        this.logger.debug('此路由不需要角色權限');
        return true;
      }

      const request = context.switchToHttp().getRequest();
      const { user, path, method } = request;

      this.logger.debug(`檢查路由權限: ${method} ${path}`);
      this.logger.debug(`需要的角色: ${JSON.stringify(requiredRoles)}`);
      this.logger.debug(
        `使用者資訊: ${JSON.stringify({
          id: user?.id,
          email: user?.email,
          role: user?.role,
          tenantId: user?.tenantId,
        })}`,
      );

      if (!user || !user.role) {
        this.logger.warn('請求中找不到使用者或使用者角色');
        return false;
      }

      const hasRole = requiredRoles.some((role) => {
        const userRole = user.role.toString().toUpperCase();
        const requiredRole = role.toString().toUpperCase();
        return userRole === requiredRole;
      });

      this.logger.debug(`角色檢查結果: ${hasRole}`);
      return hasRole;
    }
  }

  /**
   * 守衛工廠函數
   * 用於簡化守衛的使用
   */
  return mixin(MixinAuthGuard);
}

/**
 * JWT 認證守衛
 * 用於保護需要 JWT 認證的路由
 */
export const JwtAuthGuard = createAuthGuard('jwt');

/**
 * 本地認證守衛
 * 用於使用者名/密碼認證的路由
 */
export const LocalAuthGuard = createAuthGuard('local');

/**
 * JWT 刷新令牌守衛
 * 用於處理刷新令牌的路由
 */
export const JwtRefreshGuard = createAuthGuard('jwt-refresh');
