import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Role } from '../../../../common/enums/role.enum';
import { ROLES_KEY } from '../decorators/roles.decorator';
import { JwtUser } from '../../../../types/jwt-user.type';
import { PrismaService } from '../../prisma/prisma.service';

/**
 * 角色守衛 - 用於驗證用戶是否具有訪問特定資源所需的角色
 * 支援系統用戶(system_users)和租戶用戶(tenant_users)的分離架構
 */
@Injectable()
export class RolesGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private prisma: PrismaService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    // 從控制器方法或類別上獲取所需角色列表
    const requiredRoles = this.reflector.getAllAndOverride<Role[]>(ROLES_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    // 如果未設置角色要求，則允許訪問
    if (!requiredRoles) {
      return true;
    }

    // 從請求中獲取用戶信息
    const { user } = context.switchToHttp().getRequest<{ user: JwtUser }>();

    // 驗證用戶是否存在及用戶類型
    if (!user || !user.userType) {
      return false;
    }

    let userRole: string | undefined;

    // 根據用戶類型從相應資料表獲取角色信息
    if (user.userType === 'system') {
      const systemUser = await this.prisma.system_users.findUnique({ where: { id: user.id } });
      userRole = systemUser?.role;
    } else {
      const tenantUser = await this.prisma.tenant_users.findUnique({ where: { id: user.id } });
      userRole = tenantUser?.role;
    }

    // 若無法獲取角色，拒絕訪問
    if (!userRole) {
      return false;
    }

    // 檢查用戶角色是否滿足要求
    return requiredRoles.some((role) => role === userRole);
  }
}
