import {
  Controller,
  Post,
  Body,
  Get,
  Put,
  UseGuards,
  Req,
  UseInterceptors,
  UploadedFile,
  HttpCode,
  HttpStatus,
  Query,
  UnauthorizedException,
  Res,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { AuthService } from './auth.service';
import { LoginDto } from './dto/auth.dto';
import { StageOneRegisterDto, StageTwoRegisterDto } from './dto/two-stage-register.dto';
import { AcceptInvitationDto } from './dto/accept-invitation.dto';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiConsumes } from '@nestjs/swagger';
import { Public } from '../../../common/decorators/public.decorator';
import { JwtAuthGuard, JwtRefreshGuard } from './guards/auth.guard';
import { CurrentUser } from './decorators/current-user.decorator';
import { Request, Response } from 'express';
import { FileInterceptor } from '@nestjs/platform-express';
import { diskStorage } from 'multer';
import { extname } from 'path';
import { Role as RoleEnum } from '../../../common/enums/role.enum';
import { PrismaService } from '../prisma/prisma.service';
import { AuthGuard } from '@nestjs/passport';
import { JwtUser } from '../../../types/jwt-user.type';
import { JwtService } from '@nestjs/jwt';
import { CaslAbilityFactory } from '../../../casl/casl-ability.factory';
import { Throttle } from '@nestjs/throttler';
import { ConfigService } from '@nestjs/config';
import { ForgotPasswordDto } from './dto/forgot-password.dto';
import { ResetPasswordDto } from './dto/reset-password.dto';
import { ChangePasswordDto } from './dto/change-password.dto';
import { RefreshTokenDto } from './dto/refresh-token.dto';
import { MfaService } from './mfa.service';
import { SystemLogService } from '@/common/services/system-log.service';
import { ImpersonateTenantDto } from './dto/impersonate-tenant.dto';
import { Roles } from './decorators/roles.decorator';
import { RolesGuard } from './guards/roles.guard';

interface UpdateProfileDto {
  name?: string;
  phone?: string;
  title?: string;
  company?: string;
  department?: string;
  location?: string;
  bio?: string;
  socialLinks?: Array<{ platform: string; url: string }>;
  preferences?: Record<string, any>;
  avatar?: Express.Multer.File;
}

@ApiTags('core/auth')
@Controller('auth')
export class AuthController {
  private readonly logger = new Logger(AuthController.name);

  constructor(
    private readonly authService: AuthService,
    private readonly prisma: PrismaService,
    private readonly jwtService: JwtService,
    private readonly caslAbilityFactory: CaslAbilityFactory,
    private readonly configService: ConfigService,
    private readonly mfaService: MfaService,
    private readonly systemLogService: SystemLogService,
  ) {}

  private getCookieOptions() {
    return {
      httpOnly: true,
      secure: this.configService.get('COOKIE_SECURE') === 'true',
      sameSite: this.configService.get('COOKIE_SAME_SITE') || 'lax',
      maxAge: parseInt(this.configService.get('COOKIE_MAX_AGE') || '86400000'),
      domain: this.configService.get('COOKIE_DOMAIN'),
      path: '/',
    };
  }

  @Throttle({ default: { limit: 5, ttl: 300000 } })
  @Public()
  @Post('login')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '統一使用者登入 - 支援系統使用者和租戶使用者' })
  @ApiResponse({ status: 200, description: '登入成功' })
  @ApiResponse({ status: 401, description: '登入失敗' })
  async login(
    @Body() loginDto: LoginDto & { userType?: 'system' | 'tenant' },
    @Req() req: Request,
    @Res({ passthrough: true }) response: Response,
  ) {
    if (!loginDto.email || !loginDto.password) {
      throw new BadRequestException('請輸入電子郵件和密碼');
    }

    try {
      // 使用統一登入方法
      const deviceInfo = req.headers['user-agent'] || 'Unknown Device';
      const loginResult = await this.authService.login(loginDto, deviceInfo);

      // 寫入審計日誌：登入成功
      await this.systemLogService.logAudit({
        message: `${loginResult.user.userType} user login`,
        userId: loginResult.user.id,
        ip: req.ip,
        path: req.path,
        method: req.method,
        action: 'LOGIN',
        status: 'SUCCESS',
        details: { userType: loginResult.user.userType },
      });

      // 設定 accessToken cookie
      response.cookie('auth_token', loginResult.accessToken, {
        ...this.getCookieOptions(),
        maxAge: 86400 * 1000, // 24小時
        expires: new Date(Date.now() + 86400 * 1000),
      });

      // 新增：取得使用者權限並將 userType 與 permissions 包含至 user 物件
      const permissions = await this.authService.getUnifiedUserPermissions(
        loginResult.user.id,
        loginResult.userType,
      );

      return {
        accessToken: loginResult.accessToken,
        refreshToken: loginResult.refreshToken,
        user: {
          ...loginResult.user,
          userType: loginResult.userType,
          permissions,
        },
      };
    } catch (error) {
      // 寫入審計日誌：登入失敗
      await this.systemLogService.logAudit({
        message: `Login failed for email: ${loginDto.email}`,
        ip: req.ip,
        path: req.path,
        method: req.method,
        action: 'LOGIN',
        status: 'FAILED',
        errorMessage: error.message,
      });

      throw error;
    }
  }

  // 移除舊版登入方法 - 現在僅支援統一認證

  @Public()
  @Post('stage-one-register')
  @ApiOperation({ summary: '兩階段註冊 - 第一階段：個人帳號建立' })
  @ApiResponse({ status: 201, description: '個人帳號建立成功' })
  @ApiResponse({ status: 400, description: '註冊失敗' })
  async stageOneRegister(@Body() registerDto: StageOneRegisterDto) {
    return this.authService.registerPersonalAccount(registerDto);
  }

  @Get('me')
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: '讀取當前使用者資料 - 支援統一認證' })
  @ApiResponse({ status: 200, description: '讀取成功' })
  @ApiResponse({ status: 401, description: '未授權' })
  async getProfile(@Req() req: Request) {
    const jwtUser = req.user as any;

    // 統一認證：必須有 userType 欄位
    if (!jwtUser.userType) {
      throw new UnauthorizedException('Invalid token format - userType required');
    }

    const userInfo = await this.authService.getUnifiedUserInfo(jwtUser.id, jwtUser.userType);

    if (!userInfo) {
      throw new UnauthorizedException('使用者不存在');
    }

    // 獲取使用者權限
    const permissions = await this.authService.getUnifiedUserPermissions(
      jwtUser.id,
      jwtUser.userType,
    );

    // 為了前端兼容性，暫時創建一個簡單的權限規則集
    // TODO: 實現完整的 CASL 規則生成
    const basicAbilityRules: any[] = [];

    // 為 SUPER_ADMIN 添加基本的管理權限
    if (jwtUser.userType === 'system' && userInfo.role === 'SUPER_ADMIN') {
      basicAbilityRules.push({
        action: 'manage',
        subject: 'all',
      });
    }

    // 返回完整的使用者資訊，包含 userType 和權限規則
    return {
      user: {
        ...userInfo,
        userType: jwtUser.userType,
        permissions: permissions,
      },
      abilityRules: basicAbilityRules,
    };
  }

  @Put('me')
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: '更新當前使用者資料' })
  @ApiResponse({ status: 200, description: '更新成功' })
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(
    FileInterceptor('avatar', {
      storage: diskStorage({
        destination: './uploads/avatars',
        filename: (req, file, cb) => {
          const userId = (req.user as JwtUser).id;
          const randomName = Array(32)
            .fill(null)
            .map(() => Math.round(Math.random() * 16).toString(16))
            .join('');
          return cb(null, `${userId}-${randomName}${extname(file.originalname)}`);
        },
      }),
      fileFilter: (req, file, cb) => {
        if (!file.originalname.match(/\.(jpg|jpeg|png|gif)$/)) {
          return cb(new Error('只允許上傳圖片檔案'), false);
        }
        cb(null, true);
      },
      limits: {
        fileSize: 2 * 1024 * 1024, // 2MB
      },
    }),
  )
  async updateProfile(
    @CurrentUser() user: JwtUser,
    @Body() updateProfileDto: UpdateProfileDto,
    @UploadedFile() avatar?: Express.Multer.File,
  ) {
    if (!user.userType) {
      throw new UnauthorizedException('Invalid token format - userType required');
    }
    if (avatar) {
      updateProfileDto.avatar = avatar;
    }
    return this.authService.updateProfile(user.id, user.userType, updateProfileDto);
  }

  @Post('logout')
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: '使用者登出' })
  @ApiResponse({ status: 200, description: '登出成功' })
  async logout(@CurrentUser() user: JwtUser, @Req() req: Request) {
    if (!user.userType) {
      throw new UnauthorizedException('Invalid token format - userType required');
    }
    const result = await this.authService.logout(user.id, user.userType);
    // 寫入審計日誌：登出
    await this.systemLogService.logAudit({
      message: `User logout`,
      userId: user.id,
      ip: req.ip,
      path: req.path,
      method: req.method,
      action: 'LOGOUT',
      status: 'SUCCESS',
    });
    return result;
  }

  @Post('refresh-token')
  @UseGuards(JwtRefreshGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Refresh access token' })
  @HttpCode(HttpStatus.OK)
  async refreshAccessToken(
    @CurrentUser() user: JwtUser,
    @Body() body: RefreshTokenDto,
    @Res({ passthrough: true }) response: Response,
  ) {
    // 確保userType不是undefined
    if (!user.userType) {
      throw new UnauthorizedException('Invalid token: userType is required');
    }

    const { accessToken, refreshToken: newRefreshToken } = await this.authService.refreshTokens(
      user.id,
      user.userType,
      body.refreshToken,
    );

    response.cookie('auth_token', accessToken, {
      ...this.getCookieOptions(),
      maxAge: 86400 * 1000, // 24小時
      expires: new Date(Date.now() + 86400 * 1000),
    });

    return {
      accessToken,
      refreshToken: newRefreshToken,
    };
  }

  /**
   * 檢查使用者是否有租戶
   * @deprecated 與 checkTenantUser 功能重複，推薦使用 tenant/check 端點代替
   */
  @Get('check-tenant')
  @UseGuards(JwtAuthGuard)
  async checkTenant(@CurrentUser() user: any) {
    return this.checkHasTenant(user);
  }

  /**
   * 檢查使用者是否有租戶（推薦使用此方法）
   */
  @Get('tenant/check')
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  async checkTenantUser(@CurrentUser() user: JwtUser) {
    return this.checkHasTenant(user);
  }

  /**
   * 封裝查詢租戶的邏輯，避免代碼重複
   */
  private async checkHasTenant(user: any) {
    let hasTenant = false;

    if (user.userType === 'tenant') {
      hasTenant = true;
    } else if (user.userType === 'system') {
      // 檢查系統使用者是否有對應的租戶使用者記錄
      const tenantUser = await this.prisma.tenant_users.findFirst({
        where: { email: user.email },
      });
      hasTenant = !!tenantUser;
    }

    return { hasTenant };
  }

  @Public()
  @Post('invitations/accept')
  @ApiOperation({ summary: '接受租戶邀請並註冊' })
  @ApiResponse({ status: 201, description: '註冊並加入租戶成功' })
  @ApiResponse({ status: 400, description: '邀請無效或註冊失敗' })
  @ApiResponse({ status: 404, description: '邀請不存在' })
  async acceptInvitation(@Body() acceptInvitationDto: AcceptInvitationDto) {
    return this.authService.acceptInvitation(acceptInvitationDto);
  }

  @Public()
  @Get('google')
  @UseGuards(AuthGuard('google'))
  @ApiOperation({ summary: 'Initiate Google OAuth login' })
  async googleAuth() {
    // Guard will redirect
  }

  @Public()
  @Get('google/callback')
  @UseGuards(AuthGuard('google'))
  @ApiOperation({ summary: 'Google OAuth callback' })
  async googleAuthRedirect(@Req() req: Request, @Res({ passthrough: true }) response: Response) {
    if (!req.user) {
      this.logger.error('Google OAuth callback received no user from strategy.');
      // Redirect to a frontend error page or login page
      const frontendUrl = this.configService.get<string>('FRONTEND_URL') || '/';
      return response.redirect(`${frontendUrl}/login?error=google_oauth_failed`);
    }

    this.logger.debug('Google OAuth callback successful, user:', req.user);
    const userFromStrategy = req.user as any; // User from GoogleStrategy

    // Prepare user object for CASL
    const caslUser = {
      id: userFromStrategy.id,
      role: userFromStrategy.role as unknown as RoleEnum,
      tenantId: userFromStrategy.tenantId,
    };
    // 獲取權限信息 (僅作記錄，未使用返回值)
    await this.caslAbilityFactory.createForUser(caslUser);

    // 生成 JWT payload
    const payload = {
      email: userFromStrategy.email,
      id: userFromStrategy.id,
      role: userFromStrategy.role,
      userType: 'tenant', // Google OAuth 登入的使用者都是租戶使用者
    };

    // 生成 access token
    const accessToken = this.jwtService.sign(payload, {
      expiresIn: '1h',
    });

    const tokens = { accessToken };

    response.cookie('access_token', tokens.accessToken, {
      httpOnly: true,
      secure: this.configService.get<string>('NODE_ENV') === 'production',
      sameSite: 'lax',
      maxAge: (this.configService.get<number>('JWT_ACCESS_EXPIRATION_TIME') || 3600) * 1000, // Default 1hr
    });

    // TODO: 更新登入時間 - 需要根據 userType 選擇正確的服務
    const frontendSuccessRedirect =
      this.configService.get<string>('FRONTEND_GOOGLE_SUCCESS_REDIRECT_URL') ||
      this.configService.get<string>('FRONTEND_URL') ||
      '/';

    // Optionally, pass user info or a session token via query params if frontend needs it immediately without another /me call,
    // but secure cookie is generally preferred for the main token.
    // For simplicity, we redirect and let frontend pick up the cookie.
    response.redirect(frontendSuccessRedirect);
    // Note: NestJS normally sends response after handler. Redirecting here might prevent subsequent actions if not careful.
    // However, for OAuth callback, redirect is the standard final action.
  }

  @Public()
  @Get('line')
  @UseGuards(AuthGuard('line'))
  @ApiOperation({ summary: 'Initiate LINE OAuth login' })
  async lineAuth() {
    // Guard will redirect
  }

  @Public()
  @Get('line/callback')
  @UseGuards(AuthGuard('line'))
  @ApiOperation({ summary: 'LINE OAuth callback' })
  async lineAuthRedirect(@Req() req: Request, @Res({ passthrough: true }) response: Response) {
    if (!req.user) {
      this.logger.error('LINE OAuth callback received no user from strategy.');
      // Redirect to a frontend error page or login page
      const frontendUrl = this.configService.get<string>('FRONTEND_URL') || '/';
      return response.redirect(`${frontendUrl}/login?error=line_oauth_failed`);
    }

    this.logger.debug('LINE OAuth callback successful, user:', req.user);
    const userFromStrategy = req.user as any; // User from LineStrategy

    // Prepare user object for CASL
    const caslUser = {
      id: userFromStrategy.id,
      role: userFromStrategy.role as unknown as RoleEnum,
      tenantId: userFromStrategy.tenantId,
    };
    // 獲取權限信息 (僅作記錄，未使用返回值)
    await this.caslAbilityFactory.createForUser(caslUser);

    // 生成 JWT payload
    const payload = {
      email: userFromStrategy.email,
      id: userFromStrategy.id,
      role: userFromStrategy.role,
      userType: 'tenant', // LINE OAuth 登入的使用者都是租戶使用者
    };

    // 生成 access token
    const accessToken = this.jwtService.sign(payload, {
      expiresIn: '1h',
    });

    const tokens = { accessToken };

    response.cookie('access_token', tokens.accessToken, {
      httpOnly: true,
      secure: this.configService.get<string>('NODE_ENV') === 'production',
      sameSite: 'lax',
      maxAge: (this.configService.get<number>('JWT_ACCESS_EXPIRATION_TIME') || 3600) * 1000, // Default 1hr
    });

    // 前端成功重定向URL
    const frontendSuccessRedirect =
      this.configService.get<string>('FRONTEND_LINE_SUCCESS_REDIRECT_URL') ||
      this.configService.get<string>('FRONTEND_URL') ||
      '/';

    response.redirect(frontendSuccessRedirect);
  }

  @Public()
  @Post('forgot-password')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '忘記密碼：發送密碼重設郵件' })
  @ApiResponse({ status: 200, description: '郵件已發送' })
  @ApiResponse({ status: 400, description: '請求失敗' })
  async forgotPassword(@Body() dto: ForgotPasswordDto) {
    return this.authService.forgotPassword(dto);
  }

  @Public()
  @Post('reset-password')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '重設密碼' })
  @ApiResponse({ status: 200, description: '密碼重設成功' })
  @ApiResponse({ status: 400, description: '重設失敗' })
  async resetPassword(@Body() dto: ResetPasswordDto) {
    return this.authService.resetPassword(dto);
  }

  @Put('change-password')
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: '已登入使用者修改密碼 - 支援統一認證' })
  @ApiResponse({ status: 200, description: '修改成功' })
  @ApiResponse({ status: 400, description: '修改失敗' })
  async changePassword(@CurrentUser() user: JwtUser, @Body() dto: ChangePasswordDto) {
    // 統一認證：必須有 userType 欄位
    if (!user.userType) {
      throw new UnauthorizedException('Invalid token format - userType required');
    }

    return this.authService.changePassword(user.id, user.userType, dto);
  }

  @Get('mfa/setup')
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: '產生並回傳 MFA 秘鑰' })
  async mfaSetup(@CurrentUser() user: JwtUser) {
    const secret = await this.mfaService.generateSecret(user.id);
    return { secret };
  }

  @Post('mfa/verify')
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: '驗證 MFA 一次性密碼並啟用 MFA' })
  async mfaVerify(@CurrentUser() user: JwtUser, @Body('token') token: string) {
    const ok = await this.mfaService.enableMfa(user.id, token);
    if (!ok) throw new BadRequestException('MFA 驗證失敗');
    return { success: true };
  }

  @Post('mfa/disable')
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: '停用 MFA' })
  async mfaDisable(@CurrentUser() user: JwtUser) {
    await this.mfaService.disableMfa(user.id);
    return { success: true };
  }

  /**
   * 兩階段註冊相關端點
   */

  @Public()
  @Get('search-companies')
  @ApiOperation({ summary: '兩階段註冊 - 搜尋現有公司' })
  @ApiResponse({ status: 200, description: '搜尋成功' })
  @ApiResponse({ status: 400, description: '搜尋失敗' })
  async searchCompanies(@Query('name') name?: string, @Query('domain') domain?: string) {
    return this.authService.searchCompanies({ name, domain });
  }

  @Public()
  @Post('stage-two/create-company')
  @ApiOperation({ summary: '兩階段註冊 - 第二階段：建立新公司' })
  @ApiResponse({ status: 201, description: '公司建立成功' })
  @ApiResponse({ status: 400, description: '建立失敗' })
  async stageTwoCreateCompany(@Body() dto: StageTwoRegisterDto) {
    try {
      const userId = dto.stageOneToken;
      const result = await this.authService.createCompanyForUser(userId, {
        name: dto.companyName,
        domain: dto.subdomain,
        industry: dto.industry,
        companySize: dto.companySize,
        contactEmail: undefined,
        contactName: undefined,
      });
      return result;
    } catch (error) {
      this.logger.error(`建立新公司失敗: ${error.message}`);
      if (error instanceof BadRequestException) throw error;
      throw new BadRequestException('建立公司失敗');
    }
  }

  @Public()
  @Post('stage-two/join-company')
  @ApiOperation({ summary: '兩階段註冊 - 第二階段：申請加入現有公司' })
  @ApiResponse({ status: 201, description: '申請已提交' })
  @ApiResponse({ status: 400, description: '申請失敗' })
  async stageTwoJoinCompany(
    @Body()
    dto: {
      stageOneToken: string;
      tenantId: string;
      message?: string;
      department?: string;
      title?: string;
    },
  ) {
    try {
      const userId = dto.stageOneToken;
      const result = await this.authService.requestJoinCompany(userId, dto.tenantId, {
        message: dto.message,
        department: dto.department,
        title: dto.title,
      });
      return result;
    } catch (error) {
      this.logger.error(`申請加入公司失敗: ${error.message}`);
      if (error instanceof BadRequestException) throw error;
      throw new BadRequestException('申請加入公司失敗');
    }
  }

  @Public()
  @Post('stage-two/skip-setup')
  @ApiOperation({ summary: '兩階段註冊 - 第二階段：稍後設定（跳過公司設置）' })
  @ApiResponse({ status: 200, description: '設定完成' })
  @ApiResponse({ status: 400, description: '設定失敗' })
  async stageTwoSkipSetup(@Body() dto: { stageOneToken: string }) {
    try {
      const userId = dto.stageOneToken;
      const result = await this.authService.completeUserSetup(userId);
      return result;
    } catch (error) {
      this.logger.error(`完成用戶設置失敗: ${error.message}`);
      if (error instanceof BadRequestException) throw error;
      throw new BadRequestException('完成用戶設置失敗');
    }
  }

  @Post('impersonate')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(RoleEnum.SUPER_ADMIN)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Impersonate a tenant (Super Admins only)' })
  @HttpCode(HttpStatus.OK)
  async impersonateTenant(@CurrentUser() admin: JwtUser, @Body() body: ImpersonateTenantDto) {
    const { tenantId } = body;
    return this.authService.impersonateTenant(admin.id, tenantId);
  }
}
