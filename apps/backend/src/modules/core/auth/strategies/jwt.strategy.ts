import { Injectable, UnauthorizedException, Logger } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../prisma/prisma.service';
import { Request } from 'express';
import { JwtUser } from '../../../../types/jwt-user.type';
import { TenantUserStatus } from '@prisma/client';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy, 'jwt') {
  private readonly logger = new Logger(JwtStrategy.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly prisma: PrismaService,
  ) {
    const jwtAccessSecret = configService.get<string>('JWT_ACCESS_SECRET');
    if (!jwtAccessSecret) {
      throw new Error('JWT_ACCESS_SECRET is not defined in the environment');
    }

    super({
      jwtFromRequest: ExtractJwt.fromExtractors([
        (req: Request) => {
          const token = req.cookies?.auth_token;
          if (token) return token;
          return null;
        },
        ExtractJwt.fromAuthHeaderAsBearerToken(),
      ]),
      ignoreExpiration: false,
      secretOrKey: jwtAccessSecret,
    });
  }

  async validate(payload: JwtUser): Promise<any> {
    this.logger.debug(`JWT validate payload: ${JSON.stringify(payload)}`);

    const userId = payload.id;
    if (!userId) {
      throw new UnauthorizedException('Invalid token payload: missing subject');
    }

    if (payload.userType) {
      const user = await (payload.userType === 'system'
        ? this.prisma.system_users.findUnique({ where: { id: userId } })
        : this.prisma.tenant_users.findUnique({ where: { id: userId } }));

      if (!user) {
        throw new UnauthorizedException('User not found');
      }

      const isActive =
        payload.userType === 'system'
          ? user.status === 'active'
          : user.status === TenantUserStatus.ACTIVE;

      if (!isActive) {
        throw new UnauthorizedException('User is inactive');
      }

      return {
        id: userId,
        email: payload.email,
        name: user.name,
        userType: payload.userType,
        tenantId: payload.tenantId ?? (user as any).tenantId ?? null,
        role: payload.role ?? user.role,
      };
    }

    // Fallback for older tokens
    const systemUser = await this.prisma.system_users.findUnique({
      where: { id: userId },
    });
    if (systemUser && systemUser.status === 'active') {
      return {
        id: userId,
        email: payload.email,
        tenantId: null,
        role: systemUser.role,
        userType: 'system',
      };
    }

    const tenantUser = await this.prisma.tenant_users.findUnique({
      where: { id: userId },
    });
    if (tenantUser && tenantUser.status === TenantUserStatus.ACTIVE) {
      return {
        id: userId,
        email: payload.email,
        tenantId: tenantUser.tenantId,
        role: tenantUser.role,
        userType: 'tenant',
      };
    }

    throw new UnauthorizedException('User not found or inactive');
  }
}

@Injectable()
export class JwtRefreshStrategy extends PassportStrategy(Strategy, 'jwt-refresh') {
  constructor(private readonly configService: ConfigService) {
    const jwtAccessSecret = configService.get<string>('JWT_ACCESS_SECRET');
    if (!jwtAccessSecret) {
      throw new Error('JWT_ACCESS_SECRET is not defined in the environment');
    }

    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: true, // IMPORTANT: Only for refresh token validation
      secretOrKey: jwtAccessSecret,
      passReqToCallback: false,
    });
  }

  async validate(payload: JwtUser) {
    // Passport automatically attaches the payload as `req.user`
    return payload;
  }
}
