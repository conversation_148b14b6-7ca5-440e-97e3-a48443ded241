import { PassportStrategy } from '@nestjs/passport';
import { Strategy, VerifyCallback, Profile } from 'passport-google-oauth20';
import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AuthService } from '../auth.service';

@Injectable()
export class GoogleStrategy extends PassportStrategy(Strategy, 'google') {
  private readonly logger = new Logger(GoogleStrategy.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly authService: AuthService,
  ) {
    super({
      clientID: configService.get<string>('GOOGLE_CLIENT_ID') || '',
      clientSecret: configService.get<string>('GOOGLE_CLIENT_SECRET') || '',
      callbackURL: configService.get<string>('GOOGLE_CALLBACK_URL') || '',
      scope: ['email', 'profile'],
      passReqToCallback: false,
    });
  }

  async validate(
    accessToken: string,
    refreshToken: string | undefined,
    profile: Profile,
    done: VerifyCallback,
  ): Promise<any> {
    this.logger.debug(`Google profile received: ${profile.id} - ${profile.displayName}`);
    const { id, name, emails, photos } = profile;

    if (!emails || emails.length === 0) {
      this.logger.error('Google profile does not contain an email address.');
      return done(new Error('Google profile does not contain an email address.'), false);
    }

    try {
      // 建立標準的 Google 使用者資訊
      const googleUserInfo = {
        provider: 'google',
        providerId: id,
        email: emails[0].value,
        name: name
          ? name.givenName && name.familyName
            ? `${name.givenName} ${name.familyName}`
            : profile.displayName
          : emails[0].value.split('@')[0],
        avatar: photos && photos.length > 0 ? photos[0].value : undefined,
      };

      // 使用 email 在現有用戶中查找
      const tenantUser = await this.authService.findTenantUserByEmail(googleUserInfo.email);

      if (tenantUser) {
        this.logger.debug(`Found existing tenant user with email: ${googleUserInfo.email}`);

        // 返回租戶用戶
        return done(null, {
          ...tenantUser,
          userType: 'tenant',
        });
      }

      // 查找系統用戶
      const systemUser = await this.authService.findSystemUserByEmail(googleUserInfo.email);

      if (systemUser) {
        this.logger.debug(`Found existing system user with email: ${googleUserInfo.email}`);

        // 返回系統用戶
        return done(null, {
          ...systemUser,
          userType: 'system',
        });
      }

      // 如果沒有找到現有用戶，創建新的租戶用戶
      this.logger.debug(`Creating new tenant user for email: ${googleUserInfo.email}`);
      const newUser = await this.authService.createTenantUserFromOAuth(googleUserInfo);

      if (!newUser) {
        throw new UnauthorizedException('Failed to create user from Google OAuth');
      }

      return done(null, {
        ...newUser,
        userType: 'tenant',
      });
    } catch (err) {
      this.logger.error('Error during Google OAuth user validation', err);
      done(err, false);
    }
  }
}
