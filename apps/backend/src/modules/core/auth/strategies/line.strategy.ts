import { PassportStrategy } from '@nestjs/passport';
import { Strategy } from 'passport-line-auth';
import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AuthService } from '../auth.service';

@Injectable()
export class LineStrategy extends PassportStrategy(Strategy, 'line') {
  private readonly logger = new Logger(LineStrategy.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly authService: AuthService,
  ) {
    super({
      channelID: configService.get<string>('LINE_CLIENT_ID') || '',
      channelSecret: configService.get<string>('LINE_CLIENT_SECRET') || '',
      callbackURL: configService.get<string>('LINE_REDIRECT_URI') || '',
      scope: ['profile', 'openid', 'email'],
      botPrompt: 'normal',
      uiLocales: 'zh-TW',
    });
  }

  async validate(
    accessToken: string,
    refreshToken: string,
    params: any,
    profile: any,
    done: any,
  ): Promise<any> {
    this.logger.debug(
      `LINE profile received: ${profile.id} - ${profile.displayName || 'No display name'}`,
    );

    try {
      // 建立標準的 LINE 使用者資訊
      const lineUserInfo = {
        provider: 'line',
        providerId: profile.id,
        email: profile.email || null, // LINE 可能沒有提供 email
        name: profile.displayName || `LINE User ${profile.id.substring(0, 5)}`,
        avatar: profile.pictureUrl || undefined,
      };

      // 如果有 email，嘗試用 email 查找現有用戶
      if (lineUserInfo.email) {
        // 使用 email 在現有用戶中查找
        const tenantUser = await this.authService.findTenantUserByEmail(lineUserInfo.email);

        if (tenantUser) {
          this.logger.debug(`Found existing tenant user with email: ${lineUserInfo.email}`);

          // 返回租戶用戶
          return done(null, {
            ...tenantUser,
            userType: 'tenant',
          });
        }

        // 查找系統用戶
        const systemUser = await this.authService.findSystemUserByEmail(lineUserInfo.email);

        if (systemUser) {
          this.logger.debug(`Found existing system user with email: ${lineUserInfo.email}`);

          // 返回系統用戶
          return done(null, {
            ...systemUser,
            userType: 'system',
          });
        }
      }

      // 嘗試查找匹配 providerId 的 OAuth 帳戶
      const oauthAccount = await this.authService.findUserByProviderId(
        'line',
        lineUserInfo.providerId,
      );

      if (oauthAccount) {
        this.logger.debug(`Found existing user by LINE providerId: ${lineUserInfo.providerId}`);
        return done(null, {
          ...oauthAccount.user,
          userType: oauthAccount.userType,
        });
      }

      // 如果沒有找到現有用戶，創建新的租戶用戶
      this.logger.debug(`Creating new tenant user for LINE user: ${lineUserInfo.name}`);
      const newUser = await this.authService.createTenantUserFromOAuth(lineUserInfo);

      if (!newUser) {
        throw new UnauthorizedException('Failed to create user from LINE OAuth');
      }

      return done(null, {
        ...newUser,
        userType: 'tenant',
      });
    } catch (err) {
      this.logger.error('Error during LINE OAuth user validation', err);
      done(err, false);
    }
  }
}
