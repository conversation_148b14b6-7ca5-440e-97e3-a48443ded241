import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  Logger,
  UnauthorizedException,
  NotFoundException,
  ConflictException,
  ForbiddenException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { Request, Response } from 'express';
import * as bcrypt from 'bcryptjs';
import { v4 as uuidv4 } from 'uuid';
import * as crypto from 'crypto';
import { addDays } from 'date-fns';
import { Prisma } from '@prisma/client';
import { randomBytes, scrypt as _scrypt, timingSafeEqual } from 'crypto';
import { promisify } from 'util';

import { PrismaService } from '../prisma/prisma.service';
import { CaslAbilityFactory } from '../../../casl/casl-ability.factory';
import { TenantInvitationsService } from '../tenant-invitations/tenant-invitations.service';
import { MailService } from '../mail/mail.service';
import { EncryptionService } from '../encryption/encryption.service';
import { MfaService } from '../../../modules/core/auth/mfa.service';
import { SystemUserService } from '../system-user-management/system-user.service';
import { TenantUserService } from '../tenant-user-management/tenant-user.service';
import { StorageService } from '../storage/storage.service';
import { TenantsService } from '../../admin/tenants/tenants.service';

import { LoginDto } from './dto/auth.dto';
import { ForgotPasswordDto } from './dto/forgot-password.dto';
import { ResetPasswordDto } from './dto/reset-password.dto';
import { ChangePasswordDto } from './dto/change-password.dto';
import { AcceptInvitationDto } from './dto/accept-invitation.dto';
import { UserLoginResponseDto, JwtUser } from './dto';
import { RefreshTokenDto } from './dto/refresh-token.dto';
import { StageOneRegisterDto } from './dto/two-stage-register.dto';

// 暫時使用 any 類型，直到分離用戶模型完全實現
type ISystemUser = any;
type ITenantUser = any;

// 暫時使用字符串常量，直到枚舉完全實現
const TenantUserStatus = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  PENDING: 'PENDING',
} as const;

const TenantUserRole = {
  TENANT_ADMIN: 'TENANT_ADMIN',
  TENANT_USER: 'TENANT_USER',
} as const;

// OAuth 用戶資訊介面
export interface OAuthUserInfo {
  provider: string;
  providerId: string;
  email: string;
  name: string;
  avatar?: string;
}

export interface LogoutResponse {
  message: string;
  timestamp: string;
  userId: string;
  lastLogoutAt: Date | null;
}

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);
  private readonly LINE_CLIENT_ID: string;
  private readonly LINE_CLIENT_SECRET: string;
  private readonly LINE_REDIRECT_URI: string;
  private readonly REFRESH_TOKEN_SALT_ROUNDS = 10;
  private readonly REFRESH_TOKEN_EXPIRY_DAYS: number;

  constructor(
    private readonly prisma: PrismaService,
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
    private readonly caslAbilityFactory: CaslAbilityFactory,
    private readonly tenantInvitationsService: TenantInvitationsService,
    private readonly mailService: MailService,
    private readonly encryptionService: EncryptionService,
    private readonly mfaService: MfaService,
    private readonly systemUserService: SystemUserService,
    private readonly tenantUserService: TenantUserService,
    private readonly tenantsService: TenantsService,
    private readonly storageService: StorageService,
  ) {
    this.LINE_CLIENT_ID = this.configService.get<string>('LINE_CLIENT_ID') || '';
    this.LINE_CLIENT_SECRET = this.configService.get<string>('LINE_CLIENT_SECRET') || '';
    this.LINE_REDIRECT_URI = this.configService.get<string>('LINE_REDIRECT_URI') || '';
    this.REFRESH_TOKEN_EXPIRY_DAYS = parseInt(
      this.configService.get<string>('REFRESH_TOKEN_EXPIRY_DAYS', '7'),
    );
  }

  /**
   * 根據電子郵件查詢租戶用戶
   * @param email 用戶電子郵件
   * @returns 找到的租戶用戶，若不存在則返回 null
   */
  async findTenantUserByEmail(email: string): Promise<ITenantUser | null> {
    try {
      return await this.prisma.tenant_users.findFirst({
        where: { email: email },
      });
    } catch (error) {
      this.logger.debug(`Error finding tenant user by email: ${error.message}`);
      return null;
    }
  }

  /**
   * 根據電子郵件查詢系統用戶
   * @param email 用戶電子郵件
   * @returns 找到的系統用戶，若不存在則返回 null
   */
  async findSystemUserByEmail(email: string): Promise<ISystemUser | null> {
    try {
      return await this.prisma.system_users.findFirst({
        where: { email: email },
      });
    } catch (error) {
      this.logger.debug(`Error finding system user by email: ${error.message}`);
      return null;
    }
  }

  /**
   * 從 OAuth 資訊建立新的租戶用戶
   * @param oauthUserInfo OAuth 用戶資訊
   * @returns 新建立的租戶用戶
   */
  async createTenantUserFromOAuth(oauthUserInfo: OAuthUserInfo): Promise<ITenantUser> {
    this.logger.debug(`Creating new tenant user from OAuth: ${oauthUserInfo.email}`);

    // 生成隨機密碼（因為 OAuth 登入不需要密碼，但資料庫可能要求密碼欄位）
    const randomPassword = crypto.randomBytes(16).toString('hex');
    const hashedPassword = await bcrypt.hash(randomPassword, 10);

    try {
      // 獲取第一個可用的租戶供 OAuth 用戶使用
      const defaultTenant = await this.prisma.tenants.findFirst({
        where: {
          status: 'active',
        },
        orderBy: {
          createdAt: 'asc',
        },
      });

      if (!defaultTenant) {
        throw new NotFoundException('No active tenant found for OAuth user');
      }

      // 創建新的租戶用戶
      const newUser = await this.prisma.tenant_users.create({
        data: {
          name: oauthUserInfo.name,
          email: oauthUserInfo.email,
          password: hashedPassword,
          role: TenantUserRole.TENANT_USER, // 默認角色
          status: TenantUserStatus.ACTIVE,
          avatar: oauthUserInfo.avatar,
          tenantId: defaultTenant.id,
        },
      });

      this.logger.debug(`Successfully created new tenant user with ID: ${newUser.id}`);
      return newUser;
    } catch (error) {
      this.logger.error(`Failed to create tenant user from OAuth: ${error.message}`, error.stack);
      throw new InternalServerErrorException('Failed to create user account');
    }
  }

  /**
   * 處理 OAuth 登入 - 尋找或建立用戶
   * @param oauthUserInfo OAuth 供應商提供的用戶資訊
   * @returns 用戶對象
   */
  async findOrCreateUserFromOAuth(oauthUserInfo: OAuthUserInfo): Promise<any> {
    this.logger.debug(
      `OAuth login attempt for: ${oauthUserInfo.email} from ${oauthUserInfo.provider}`,
    );

    try {
      // 先檢查是否有同樣 email 的用戶
      // 先檢查租戶用戶，優先使用租戶用戶
      let existingUser = await this.findTenantUserByEmail(oauthUserInfo.email);

      if (existingUser) {
        return {
          ...existingUser,
          userType: 'tenant',
        };
      }

      // 檢查是否有系統用戶
      existingUser = await this.findSystemUserByEmail(oauthUserInfo.email);

      if (existingUser) {
        return {
          ...existingUser,
          userType: 'system',
        };
      }

      // 如果沒有找到任何用戶，創建新的租戶用戶
      const newUser = await this.createTenantUserFromOAuth(oauthUserInfo);

      return {
        ...newUser,
        userType: 'tenant',
      };
    } catch (error) {
      this.logger.error(`Error in OAuth authentication: ${error.message}`, error.stack);
      throw new InternalServerErrorException('Failed to process OAuth login');
    }
  }

  async impersonateTenant(adminId: string, tenantId: string): Promise<{ accessToken: string }> {
    this.logger.log(`Admin ${adminId} attempting to impersonate tenant ${tenantId}`);

    // Verify the target tenant exists
    const tenant = await this.prisma.tenants.findUnique({ where: { id: tenantId } });
    if (!tenant) {
      throw new NotFoundException(`Tenant with ID ${tenantId} not found.`);
    }

    // Find the primary admin of the target tenant to use their identity for the token
    const tenantAdmin = await this.prisma.tenant_users.findFirst({
      where: {
        tenantId: tenantId,
        role: 'TENANT_ADMIN',
      },
    });

    if (!tenantAdmin) {
      throw new NotFoundException(
        `No admin user found for tenant ${tenantId}. Cannot impersonate.`,
      );
    }

    const payload: JwtUser & { impersonatorId?: string } = {
      sub: tenantAdmin.id,
      id: tenantAdmin.id,
      email: tenantAdmin.email,
      name: tenantAdmin.name,
      userType: 'tenant',
      tenantId: tenant.id,
      impersonatorId: adminId, // Add impersonator's ID for auditing
    };

    const accessToken = this.jwtService.sign(payload);

    this.logger.log(
      `Successfully generated impersonation token for admin ${adminId} to access tenant ${tenantId}`,
    );

    return { accessToken };
  }

  // 由於 password_reset_tokens 模型不知名，使用 any 類型的 bracket 訪問
  private get passwordResetTokens(): any {
    return (this.prisma as any)['password_reset_tokens'];
  }

  private async _hashRefreshToken(token: string): Promise<string> {
    return bcrypt.hash(token, this.REFRESH_TOKEN_SALT_ROUNDS);
  }

  /**
   * 統一使用者驗證 - 支援 SystemUser 和 TenantUser
   */
  async validateUnifiedUser(
    email: string,
    password: string,
    userType?: 'system' | 'tenant',
  ): Promise<{
    user: ISystemUser | ITenantUser;
    userType: 'system' | 'tenant';
  } | null> {
    this.logger.debug(`Validating unified user: ${email}, type: ${userType || 'auto'}`);

    // 如果指定了用戶類型，只在該類型中查找
    if (userType === 'system') {
      const systemUser = await this.systemUserService.findByEmail(email);
      if (systemUser && (await bcrypt.compare(password, (systemUser as any).password))) {
        return { user: systemUser, userType: 'system' };
      }
      return null;
    }

    if (userType === 'tenant') {
      const tenantUser = await this.tenantUserService.findByEmail(email);
      if (tenantUser && (await bcrypt.compare(password, (tenantUser as any).password))) {
        return { user: tenantUser, userType: 'tenant' };
      }
      return null;
    }

    // 自動檢測用戶類型
    // 先檢查系統用戶
    try {
      const systemUser = await this.systemUserService.findByEmail(email);
      if (systemUser && (await bcrypt.compare(password, systemUser.password))) {
        return { user: systemUser, userType: 'system' };
      }
    } catch (error) {
      this.logger.debug(`System user not found for email: ${email}`);
    }

    // 再檢查租戶用戶
    try {
      const tenantUser = await this.tenantUserService.findByEmail(email);
      if (tenantUser && (await bcrypt.compare(password, (tenantUser as any).password))) {
        return { user: tenantUser, userType: 'tenant' };
      }
    } catch (error) {
      this.logger.debug(`Tenant user not found for email: ${email}`);
    }

    return null;
  }

  /**
   * 統一登入方法 - 支援 SystemUser 和 TenantUser
   */
  async login(dto: LoginDto, deviceInfo?: string): Promise<UserLoginResponseDto> {
    const { email, password } = dto;
    this.logger.debug(`Login attempt for email: ${email}`);

    try {
      // 首先嘗試系統用戶登入
      let user: ISystemUser | ITenantUser | null = null;
      let userType: 'system' | 'tenant' = 'system';

      try {
        user = await this.systemUserService.findByEmail(email);
        userType = 'system';
      } catch (error) {
        // 系統用戶未找到，嘗試租戶用戶
        try {
          user = await this.tenantUserService.findByEmail(email);
          userType = 'tenant';
        } catch (error) {
          throw new UnauthorizedException('用戶不存在或密碼錯誤');
        }
      }

      if (!user || !(await bcrypt.compare(password, user.password))) {
        this.logger.warn(`Failed login attempt for email: ${email}`);
        throw new UnauthorizedException('用戶不存在或密碼錯誤');
      }

      // 檢查用戶狀態
      if (user.status !== 'active' && user.status !== 'ACTIVE') {
        throw new UnauthorizedException('帳戶已被停用');
      }

      // 生成 tokens
      const payload: JwtUser = {
        sub: user.id,
        id: user.id,
        email: user.email,
        name: user.name,
        userType,
        tenantId: userType === 'tenant' ? (user as any).tenantId : null,
      };

      const accessToken = this.jwtService.sign(payload, {
        expiresIn: this.configService.get('jwt.accessTokenExpiresIn', '15m'),
      });

      const refreshToken = uuidv4();
      const hashedRefreshToken = await this._hashRefreshToken(refreshToken);

      // 創建新的 refresh token
      await this.prisma.refresh_tokens.create({
        data: {
          id: crypto.randomUUID(),
          token: hashedRefreshToken,
          userType,
          [userType === 'system' ? 'systemUserId' : 'tenantUserId']: user.id,
          expiresAt: addDays(new Date(), this.REFRESH_TOKEN_EXPIRY_DAYS),
          deviceInfo,
        },
      });

      // 更新最後登入時間
      if (userType === 'system') {
        await this.systemUserService.updateLastLogin(user.id);
      } else {
        await this.tenantUserService.updateLastLogin(user.id);
      }

      // 記錄登入日誌
      await this.prisma.login_logs.create({
        data: {
          id: uuidv4(),
          userId: user.id,
          success: true,
          ipAddress: '',
          userAgent: deviceInfo || '',
          loginAt: new Date(),
        },
      });

      this.logger.log(`Successful login for ${userType} user: ${user.email}`);

      return {
        accessToken,
        refreshToken,
        user: this.omitPassword(user),
        userType,
      };
    } catch (error) {
      this.logger.error(`Login error for ${email}:`, error);
      throw error;
    }
  }

  async verifyToken(token: string): Promise<any> {
    try {
      const decoded = this.jwtService.verify(token);
      return decoded;
    } catch (error) {
      this.logger.warn(`Token verification failed: ${error.message}`);
      throw new UnauthorizedException('Invalid token');
    }
  }

  async logout(userId: string, userType: 'system' | 'tenant'): Promise<void> {
    this.logger.debug(`Logout for ${userType} user: ${userId}`);

    try {
      // 撤銷所有該用戶的 refresh tokens
      const whereCondition =
        userType === 'system' ? { systemUserId: userId } : { tenantUserId: userId };

      const tokensToRevoke = await this.prisma.refresh_tokens.findMany({
        where: whereCondition,
        select: { id: true, deviceInfo: true },
      });

      await this.prisma.refresh_tokens.updateMany({
        where: whereCondition,
        data: { isValid: false, revokedAt: new Date() },
      });

      // 記錄被撤銷的 tokens
      if (tokensToRevoke.length > 0) {
        tokensToRevoke.forEach((token) => {
          this.logger.log(
            `Refresh token ${token.id} (device: ${token.deviceInfo}) for user ${userId} revoked.`,
          );
        });
      }

      // 將當前用戶的所有 access token 加入黑名單
      // 我們通過更新用戶的最後登出時間來實現，JWT 守衛會檢查這個時間
      const now = new Date();

      // 更新最後登出時間
      if (userType === 'system') {
        await this.systemUserService.updateLastLogout(userId);
        // 如果 system_users 表有 tokenValidAfter 欄位，也更新它
        try {
          await this.prisma.system_users.update({
            where: { id: userId },
            data: { lastLogoutAt: now },
          });
        } catch (error) {
          this.logger.warn(`Could not update system user lastLogoutAt: ${error.message}`);
        }
      } else {
        await this.tenantUserService.updateLastLogout(userId);
        // 如果 tenant_users 表有 tokenValidAfter 欄位，也更新它
        try {
          await this.prisma.tenant_users.update({
            where: { id: userId },
            data: { lastLogoutAt: now },
          });
        } catch (error) {
          this.logger.warn(`Could not update tenant user lastLogoutAt: ${error.message}`);
        }
      }

      this.logger.log(`User ${userId} logged out successfully, all tokens invalidated`);
    } catch (error) {
      this.logger.error(`Error during logout for user ${userId}:`, error);
      throw new InternalServerErrorException('登出時發生錯誤');
    }
  }

  /**
   * 統一取得使用者權限 - 支援 SystemUser 和 TenantUser
   */
  async getUnifiedUserPermissions(
    userId: string,
    userType: 'system' | 'tenant',
  ): Promise<string[]> {
    this.logger.debug(`Getting unified permissions for ${userType} user: ${userId}`);

    try {
      // TODO: 實現完整的權限邏輯
      // 暫時返回空陣列，讓登入功能先正常工作
      this.logger.debug(`Returning empty permissions array for now`);
      return [];
    } catch (error) {
      this.logger.error(`Error getting permissions for user ${userId}:`, error);
      return [];
    }
  }

  async acceptInvitation(dto: AcceptInvitationDto) {
    const { token, name } = dto;
    const invitation = await this.tenantInvitationsService.verifyInvitation(token);

    const saltRounds = parseInt(this.configService.get<string>('BCRYPT_SALT', '10'));
    const hashedPassword = await bcrypt.hash(dto.password, saltRounds);

    // 使用新的 TenantUser 模型創建使用者
    const newUser = await this.tenantUserService.createTenantUser({
      email: invitation.email,
      password: hashedPassword,
      name: name,
      tenantId: invitation.tenantId,
      status: TenantUserStatus.ACTIVE,
      role: TenantUserRole.TENANT_USER,
    });

    await this.tenantInvitationsService.acceptInvitation(token, newUser.id);

    return newUser;
  }

  private omitPassword(user: any) {
    const { password, ...userWithoutPassword } = user;
    return userWithoutPassword;
  }

  async revokeAllRefreshTokensForUser(userId: string, excludeTokenId?: string): Promise<void> {
    const whereClause: Prisma.refresh_tokensWhereInput = {
      OR: [{ systemUserId: userId }, { tenantUserId: userId }],
      revokedAt: null,
    };

    if (excludeTokenId) {
      whereClause.id = { not: excludeTokenId };
    }

    await this.prisma.refresh_tokens.updateMany({
      where: whereClause,
      data: { revokedAt: new Date() },
    });
  }

  async refreshTokens(
    userId: string,
    userType: 'system' | 'tenant',
    providedToken: string,
  ): Promise<{
    accessToken: string;
    refreshToken: string;
  }> {
    this.logger.debug(`Attempting to refresh token for user ${userId} of type ${userType}`);

    if (!providedToken) {
      throw new UnauthorizedException('Refresh token not provided');
    }

    const now = new Date();
    const userTokens = await this.prisma.refresh_tokens.findMany({
      where: {
        [userType === 'system' ? 'systemUserId' : 'tenantUserId']: userId,
        expiresAt: { gt: now },
        revokedAt: null,
      },
    });

    if (!userTokens.length) {
      this.logger.warn(`No valid refresh tokens found for user ${userId}`);
      throw new UnauthorizedException('Invalid or expired refresh token');
    }

    let matchedTokenRecord: (typeof userTokens)[0] | null = null;
    for (const tokenRecord of userTokens) {
      const isMatch = await bcrypt.compare(providedToken, tokenRecord.token);
      if (isMatch) {
        matchedTokenRecord = tokenRecord;
        break;
      }
    }

    if (!matchedTokenRecord) {
      this.logger.warn(`Invalid refresh token provided for user ${userId}. Revoking all tokens.`);
      await this.revokeAllRefreshTokensForUser(userId);
      throw new UnauthorizedException('Invalid or expired refresh token');
    }

    // Invalidate the old token
    await this.prisma.refresh_tokens.update({
      where: { id: matchedTokenRecord.id },
      data: { revokedAt: now },
    });

    let user;
    if (userType === 'system') {
      user = await this.prisma.system_users.findUnique({ where: { id: userId } });
    } else {
      user = await this.prisma.tenant_users.findUnique({ where: { id: userId } });
    }

    if (!user) {
      throw new InternalServerErrorException('User not found during token refresh');
    }

    const payload: JwtUser = {
      sub: user.id,
      id: user.id,
      email: user.email,
      name: user.name,
      userType,
      tenantId: userType === 'tenant' ? user.tenant_id : null,
    };

    const accessToken = this.jwtService.sign(payload, {
      expiresIn: this.configService.get('jwt.accessTokenExpiresIn', '15m'),
    });

    const newRefreshToken = uuidv4();
    const newHashedRefreshToken = await this._hashRefreshToken(newRefreshToken);

    await this.prisma.refresh_tokens.create({
      data: {
        id: crypto.randomUUID(),
        token: newHashedRefreshToken,
        userType,
        [userType === 'system' ? 'systemUserId' : 'tenantUserId']: user.id,
        expiresAt: addDays(new Date(), this.REFRESH_TOKEN_EXPIRY_DAYS),
      },
    });

    return {
      accessToken,
      refreshToken: newRefreshToken, // Return the plaintext token to the client
    };
  }

  /**
   * 統一取得使用者資訊 - 支援 SystemUser 和 TenantUser
   */
  async getUnifiedUserInfo(userId: string, userType: 'system' | 'tenant') {
    this.logger.debug(`Getting unified user info for ${userType} user: ${userId}`);

    try {
      let user: ISystemUser | ITenantUser | null = null;

      if (userType === 'system') {
        user = await this.systemUserService.findById(userId);
      } else {
        user = await this.tenantUserService.findById(userId);
      }

      if (!user) {
        throw new UnauthorizedException('用戶不存在');
      }

      return this.omitPassword(user);
    } catch (error) {
      this.logger.error(`Error getting user info for ${userId}:`, error);
      throw new InternalServerErrorException('無法獲取使用者資訊');
    }
  }

  /**
   * 統一更新個人資料方法 - 支援 SystemUser 和 TenantUser
   */
  async updateProfile(
    userId: string,
    userType: 'system' | 'tenant',
    updateData: {
      name?: string;
      phone?: string;
      title?: string;
      department?: string;
      location?: string;
      bio?: string;
      socialLinks?: Array<{ platform: string; url: string }>;
      preferences?: Record<string, any>;
      avatar?: Express.Multer.File;
    },
  ): Promise<{ success: boolean; user: any }> {
    this.logger.debug(`Updating profile for ${userType} user: ${userId}`, updateData);

    try {
      let user: ISystemUser | ITenantUser | null = null;
      const updatePayload: any = {};

      // 基本欄位處理
      if (updateData.name !== undefined) updatePayload.name = updateData.name;
      if (updateData.phone !== undefined) updatePayload.phone = updateData.phone;

      // 租戶用戶特有欄位
      if (userType === 'tenant') {
        if (updateData.title !== undefined) updatePayload.title = updateData.title;
        if (updateData.department !== undefined) updatePayload.department = updateData.department;
      }

      // 頭像處理
      if (updateData.avatar) {
        // 使用 multer diskStorage 已保存的檔案路徑
        updatePayload.avatar = updateData.avatar.path;
      }

      // 更新用戶
      if (userType === 'system') {
        user = await this.systemUserService.updateSystemUser(userId, updatePayload);
      } else {
        user = await this.tenantUserService.updateTenantUser(userId, updatePayload);
      }

      this.logger.log(`Profile updated for ${userType} user: ${userId}`);
      return {
        success: true,
        user: this.omitPassword(user),
      };
    } catch (error) {
      this.logger.error(`Error updating profile for ${userId}:`, error);
      throw new InternalServerErrorException('更新個人資料失敗');
    }
  }

  /**
   * 兩階段註冊 - 第一階段：個人帳號建立
   */
  async registerPersonalAccount(dto: StageOneRegisterDto): Promise<{
    message: string;
    userId: string;
    requiresEmailVerification?: boolean;
  }> {
    const { email, password, name } = dto;
    this.logger.debug(`Stage one registration for email: ${email}`);

    try {
      // 檢查 Email 是否已經被使用
      const existingSystemUser = await this.systemUserService.findByEmail(email).catch(() => null);
      const existingTenantUser = await this.tenantUserService.findByEmail(email).catch(() => null);

      if (existingSystemUser || existingTenantUser) {
        throw new BadRequestException('此 Email 已經被使用');
      }

      // 建立系統用戶（個人帳號）
      const hashedPassword = await bcrypt.hash(password, 12);

      const newUser = await this.systemUserService.createSystemUser({
        email,
        password: hashedPassword,
        name,
        role: 'SYSTEM_ADMIN',
        status: 'active',
      });

      this.logger.log(`Personal account created for email: ${email}`);
      return {
        message: '個人帳號建立成功',
        userId: newUser.id,
        requiresEmailVerification: false, // 暫時關閉 Email 驗證
      };
    } catch (error) {
      this.logger.error(`Registration error for ${email}:`, error);
      throw error;
    }
  }

  /**
   * 兩階段註冊 - 搜尋現有公司
   */
  async searchCompanies(dto: { name?: string; domain?: string }): Promise<any[]> {
    // 使用 TenantsService 搜尋活躍租戶
    const query = dto.name ?? dto.domain ?? '';
    return this.tenantsService.searchTenants(query);
  }

  /**
   * 兩階段註冊 - 第二階段：建立新公司
   */
  async createCompanyForUser(userId: string, companyData: any): Promise<any> {
    // 從第一階段註冊的使用者建立租戶管理員
    // 根據傳入資料映射 CreateTenantDto
    const createDto: any = {
      name: companyData.name,
      domain: companyData.domain,
      companySize: companyData.companySize,
      industry: companyData.industry,
      adminName: companyData.contactName,
      adminEmail: companyData.contactEmail,
    };
    // 建立租戶
    const tenant = await this.tenantsService.create(createDto);
    return { message: '公司建立成功', tenant };
  }

  /**
   * 兩階段註冊 - 第二階段：申請加入現有公司
   */
  async requestJoinCompany(userId: string, tenantId: string, options: any): Promise<any> {
    // 使用 TenantInvitationsService 建立加入申請
    return this.tenantInvitationsService.createJoinRequest(tenantId, userId);
  }

  /**
   * 兩階段註冊 - 第二階段：跳過公司設置
   */
  async completeUserSetup(userId: string): Promise<any> {
    // 跳過公司設置，直接返回成功
    return { success: true };
  }

  /**
   * 統一忘記密碼方法 - 支援 SystemUser 和 TenantUser
   */
  async forgotPassword(dto: ForgotPasswordDto): Promise<{ message: string }> {
    const { email } = dto;
    this.logger.debug(`Forgot password request for email: ${email}`);

    try {
      // 查找用戶
      let user: ISystemUser | ITenantUser | null = null;
      let userType: 'system' | 'tenant' = 'system';

      try {
        user = await this.systemUserService.findByEmail(email);
        userType = 'system';
      } catch (error) {
        try {
          user = await this.tenantUserService.findByEmail(email);
          userType = 'tenant';
        } catch (error) {
          // 為了安全，即使用戶不存在也返回成功訊息
          this.logger.warn(`Forgot password request for non-existent email: ${email}`);
          return { message: '如果該 Email 存在，我們已發送重設密碼連結' };
        }
      }

      if (!user) {
        return { message: '如果該 Email 存在，我們已發送重設密碼連結' };
      }

      // 生成重設 token
      const resetToken = this.encryptionService.generateRandomToken();
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + 1); // 1小時過期

      // 儲存重設 token
      await this.passwordResetTokens.create({
        data: {
          id: uuidv4(),
          email: user.email,
          token: resetToken,
          user_type: userType,
          user_id: user.id,
          expires_at: expiresAt,
        },
      });

      // 發送重設密碼 Email
      await this.mailService.sendPasswordResetEmail(user.email, user.name || '用戶', resetToken);

      this.logger.log(`Password reset email sent to: ${email}`);
      return { message: '如果該 Email 存在，我們已發送重設密碼連結' };
    } catch (error) {
      this.logger.error(`Forgot password error for ${email}:`, error);
      throw new InternalServerErrorException('發送重設密碼郵件失敗');
    }
  }

  /**
   * 統一重設密碼方法 - 支援 SystemUser 和 TenantUser
   */
  async resetPassword(dto: ResetPasswordDto): Promise<{ message: string }> {
    const { token, newPassword } = dto;
    this.logger.debug(`Reset password attempt with token: ${token.substring(0, 8)}...`);

    try {
      // 查找重設 token
      const resetToken = await this.passwordResetTokens.findUnique({
        where: { token },
      });

      if (!resetToken) {
        throw new BadRequestException('無效的重設連結');
      }

      if (resetToken.used_at) {
        throw new BadRequestException('重設連結已被使用');
      }

      if (new Date() > resetToken.expires_at) {
        throw new BadRequestException('重設連結已過期');
      }

      // 更新密碼
      const hashedPassword = await bcrypt.hash(newPassword, 12);

      if (resetToken.user_type === 'system') {
        await this.systemUserService.updatePassword(resetToken.user_id, hashedPassword);
      } else {
        await this.tenantUserService.updatePassword(resetToken.user_id, hashedPassword);
      }

      // 標記 token 為已使用
      await this.passwordResetTokens.update({
        where: { id: resetToken.id },
        data: { used_at: new Date() },
      });

      // 撤銷所有該用戶的 refresh tokens
      const whereCondition =
        resetToken.user_type === 'system'
          ? { systemUserId: resetToken.user_id }
          : { tenantUserId: resetToken.user_id };

      await this.prisma.refresh_tokens.updateMany({
        where: whereCondition,
        data: { isValid: false, revokedAt: new Date() },
      });

      this.logger.log(`Password reset successful for user: ${resetToken.user_id}`);
      return { message: '密碼重設成功' };
    } catch (error) {
      this.logger.error(`Reset password error:`, error);
      throw error;
    }
  }

  /**
   * 統一密碼修改方法 - 支援 SystemUser 和 TenantUser
   */
  async changePassword(
    userId: string,
    userType: 'system' | 'tenant',
    dto: ChangePasswordDto,
  ): Promise<{ message: string }> {
    const { oldPassword, newPassword } = dto;
    this.logger.debug(`Change password for ${userType} user: ${userId}`);

    try {
      // 獲取用戶
      let user: ISystemUser | ITenantUser | null = null;

      if (userType === 'system') {
        user = await this.systemUserService.findById(userId);
      } else {
        user = await this.tenantUserService.findById(userId);
      }

      if (!user) {
        throw new UnauthorizedException('用戶不存在');
      }

      // 驗證舊密碼
      if (!(await bcrypt.compare(oldPassword, user.password))) {
        throw new BadRequestException('目前密碼不正確');
      }

      // 更新密碼
      const hashedPassword = await bcrypt.hash(newPassword, 12);

      if (userType === 'system') {
        await this.systemUserService.updatePassword(userId, hashedPassword);
      } else {
        await this.tenantUserService.updatePassword(userId, hashedPassword);
      }

      // 撤銷所有該用戶的 refresh tokens（強制重新登入）
      const whereCondition =
        userType === 'system' ? { systemUserId: userId } : { tenantUserId: userId };

      await this.prisma.refresh_tokens.updateMany({
        where: whereCondition,
        data: { isValid: false, revokedAt: new Date() },
      });

      this.logger.log(`Password changed for ${userType} user: ${userId}`);
      return { message: '密碼修改成功' };
    } catch (error) {
      this.logger.error(`Change password error for ${userId}:`, error);
      throw error;
    }
  }

  /**
   * 根據 provider 和 providerId 查找用戶
   * @param provider OAuth 提供商名稱
   * @param providerId OAuth 提供商用戶 ID
   * @returns 找到的用戶資訊，包含用戶和用戶類型
   */
  async findUserByProviderId(
    provider: string,
    providerId: string,
  ): Promise<{
    user: ISystemUser | ITenantUser;
    userType: 'system' | 'tenant';
  } | null> {
    try {
      // 查找 OAuth 帳戶
      const oauthAccount = await this.prisma.$queryRaw<any[]>`
        SELECT oa.*, tu.*, su.*
        FROM oauth_accounts oa
        LEFT JOIN tenant_users tu ON oa.userId = tu.id AND oa.userType = 'tenant'
        LEFT JOIN system_users su ON oa.userId = su.id AND oa.userType = 'system'
        WHERE oa.provider = ${provider} AND oa.providerId = ${providerId}
        LIMIT 1
      `;

      if (!oauthAccount || oauthAccount.length === 0) {
        return null;
      }

      const account = oauthAccount[0];

      if (account.userType === 'tenant') {
        return {
          user: {
            id: account.userId,
            email: account.email,
            name: account.name,
            role: account.role,
            tenantId: account.tenantId,
            status: account.status,
          },
          userType: 'tenant',
        };
      }

      if (account.userType === 'system') {
        return {
          user: {
            id: account.userId,
            email: account.email,
            name: account.name,
            role: account.role,
            status: account.status,
          },
          userType: 'system',
        };
      }

      return null;
    } catch (error) {
      this.logger.debug(`Error finding user by provider ID: ${error.message}`);
      return null;
    }
  }
}
