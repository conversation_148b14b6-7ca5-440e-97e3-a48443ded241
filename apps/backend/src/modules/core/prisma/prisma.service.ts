import { Injectable, OnModuleInit, OnModuleDestroy } from "@nestjs/common";
import { PrismaClient } from "@prisma/client";
import { ClsService } from 'nestjs-cls';
import "./prisma-helpers";

/**
 * 擴展的 Prisma 服務，提供對數據庫的訪問
 * 包含了對原 PrismaClient 的擴展，添加了缺失模型的代理
 */
@Injectable()
export class PrismaService
  extends PrismaClient
  implements OnModuleInit, OnModuleDestroy {
  constructor(private readonly cls: ClsService) {
    super({
      log: ["error", "warn"],
    });

    this.$use(async (params, next) => {
      const tenantId = this.cls.get('tenantId');

      if (!tenantId) {
        return next(params);
      }

      const modelsWithTenantId = [
        'tenant_users', 'workspaces', 'projects', 'tasks', 'photos',
        'comments', 'files', 'progress', 'message_center', 'chat',
        'workspace_ai_bots', 'ai_executions', 'workspace_ai_integrations',
        // Add any other models that have a tenantId field
      ];

      if (modelsWithTenantId.includes(params.model || '')) {
        const actionsRequiringTenantId = ['findUnique', 'findFirst', 'findMany', 'update', 'updateMany', 'delete', 'deleteMany', 'count'];

        if (actionsRequiringTenantId.includes(params.action)) {
          if (params.args.where) {
            params.args.where = {
              ...params.args.where,
              tenantId: tenantId,
            };
          } else {
            params.args.where = {
              tenantId: tenantId,
            };
          }
        }
      }

      return next(params);
    });
  }

  async onModuleInit() {
    await this.$connect();
  }

  async onModuleDestroy() {
    await this.$disconnect();
  }
}
// 注意：Prisma 會根據 schema.prisma 中的 @@map 指令自動映射模型名稱
