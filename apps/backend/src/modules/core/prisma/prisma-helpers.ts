// 定義 Prisma 擴充功能
export const sql = (strings: TemplateStringsArray, ...values: any[]) => ({ text: String.raw(strings, ...values.map((v, i) => `$${i + 1}`)), values });
export const empty = Symbol('empty');

// 擴充 Prisma 命名空間
declare global {
  namespace Prisma {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    export const sql: typeof import('./prisma-helpers').sql;
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    export const empty: typeof import('./prisma-helpers').empty;
  }
}

// 掛載到 Prisma 命名空間
// import { PrismaClient } from "@prisma/client"; // PrismaClient is for instantiating the client, not for the namespace itself.
// (Prisma as any).sql = sql; // This is not how Prisma namespace works for runtime assignment.
// (Prisma as any).empty = empty; // This is not how Prisma namespace works for runtime assignment.