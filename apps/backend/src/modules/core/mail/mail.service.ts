import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { MailOptions, SmtpConfig, SendGridConfig } from './mail.model';
import { SendGridMailService } from './sendgrid-mail.service';
import { SmtpMailService } from './smtp-mail.service';

@Injectable()
export class MailService {
  private readonly logger = new Logger(MailService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly sendGridMailService: SendGridMailService,
    private readonly smtpMailService: SmtpMailService,
  ) { }

  /**
   * 發送郵件（支援 SMTP 與 SendGrid，含模板）
   */
  async sendMail(options: MailOptions): Promise<boolean> {
    const {
      to, subject, html, template, context, provider, sendGridApiKey, from,
      templateId, dynamicTemplateData
    } = options;
    if (provider === 'sendgrid') {
      this.logger.debug(`使用 SendGrid 發送郵件至 ${to}`);
      return this.sendGridMailService.sendMail({
        apiKey: sendGridApiKey!,
        from: from || this.configService.get<string>('MAIL_FROM', '<EMAIL>'),
        to,
        subject,
        html,
        templateId,
        dynamicTemplateData,
      });
    }
    // SMTP
    const htmlContent = html || this.createSimpleHtml(template, context);
    const smtpConfig: SmtpConfig = {
      host: this.configService.get<string>('MAIL_HOST', 'smtp.example.com'),
      port: this.configService.get<number>('MAIL_PORT', 587),
      secure: this.configService.get<boolean>('MAIL_SECURE', false),
      auth: {
        user: this.configService.get<string>('MAIL_USER', '<EMAIL>'),
        pass: this.configService.get<string>('MAIL_PASSWORD', 'password'),
      },
    };
    return this.smtpMailService.sendMail({
      from: from || this.configService.get<string>('MAIL_FROM', '<EMAIL>'),
      to,
      subject,
      html: htmlContent,
    }, smtpConfig);
  }

  /**
   * 發送測試郵件
   * @param emailSettings 郵件設定
   * @returns 發送結果
   */
  async sendTestEmail(emailSettings: any): Promise<boolean> {
    const toAddress = emailSettings.to?.trim() || emailSettings.fromEmailAddress;
    if (emailSettings.provider === 'sendgrid') {
      const sendGridConfig: SendGridConfig = {
        apiKey: emailSettings.sendGridApiKey,
        from: `${emailSettings.fromName} <${emailSettings.fromEmailAddress}>`,
        to: toAddress,
        subject: '【測試郵件】HorizAI 郵件設定測試',
        html: '<div>這是一封測試郵件，代表您的 SendGrid 郵件設定成功！</div>',
      };
      if (emailSettings.selectedTemplateId && emailSettings.selectedTemplateId.trim() !== '') {
        sendGridConfig.templateId = emailSettings.selectedTemplateId;
        if (emailSettings.dynamicTemplateData && typeof emailSettings.dynamicTemplateData === 'object') {
          sendGridConfig.dynamicTemplateData = {
            ...emailSettings.dynamicTemplateData,
            subject: emailSettings.dynamicTemplateData.subject || '【測試郵件】HorizAI 郵件設定測試'
          };
        } else {
          sendGridConfig.dynamicTemplateData = {
            subject: '【測試郵件】HorizAI 郵件設定測試',
            content: '這是一封使用 SendGrid 模板發送的測試郵件，代表您的郵件設定成功！',
            date: new Date().toLocaleDateString(),
            system: { name: 'HorizAI', version: '1.0.0' }
          };
        }
        delete sendGridConfig.subject;
        delete sendGridConfig.html;
      }
      return this.sendGridMailService.sendMail(sendGridConfig);
    }
    // SMTP
    const smtpConfig: SmtpConfig = {
      host: emailSettings.smtpHost,
      port: emailSettings.smtpPort,
      secure: emailSettings.smtpSecure,
      auth: {
        user: emailSettings.smtpUser,
        pass: emailSettings.smtpPassword,
      },
    };
    await this.smtpMailService.verifyConfig(smtpConfig);
    return this.smtpMailService.sendMail({
      from: `"${emailSettings.fromName}" <${emailSettings.fromEmailAddress}>`,
      to: toAddress,
      subject: '【測試郵件】HorizAI 郵件設定測試',
      html: '<div>這是一封測試郵件，代表您的 SMTP 郵件設定成功！</div>',
    }, smtpConfig);
  }

  /**
   * 建立簡單的 HTML 內容
   * @param template 模板名稱
   * @param context 內容資料
   * @returns HTML 內容
   */
  private createSimpleHtml(template: string | undefined, context: any): string {
    if (!template) return '';
    if (template === 'tenant-invitation') {
      return this.createInvitationHtml(context);
    } else if (template === 'invitation-accepted') {
      return this.createAcceptanceHtml(context);
    } else {
      return `<div>無法識別的郵件模板: ${template}</div>`;
    }
  }

  private createInvitationHtml(context: any): string {
    const { tenantName, inviteUrl, senderName, expiresAt } = context;
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
        <h2 style="color: #333;">邀請您加入 ${tenantName}</h2>
        <p style="color: #666; font-size: 16px; line-height: 1.5;">
          您好，<br><br>
          ${senderName || '租戶管理員'} 邀請您加入 <strong>${tenantName}</strong> 租戶。<br>
          請點擊下方按鈕接受邀請：
        </p>
        <div style="text-align: center; margin: 30px 0;">
          <a href="${inviteUrl}" style="background-color: #4f46e5; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;">
            接受邀請
          </a>
        </div>
        <p style="color: #666; font-size: 14px; line-height: 1.5;">
          或者，您可以複製以下連結至瀏覽器中訪問：<br>
          <a href="${inviteUrl}" style="color: #4f46e5; word-break: break-all;">${inviteUrl}</a>
        </p>
        <p style="color: #999; font-size: 14px; line-height: 1.5;">
          此邀請將於 ${expiresAt} 到期。
        </p>
        <p style="color: #999; font-size: 14px; margin-top: 40px; border-top: 1px solid #e0e0e0; padding-top: 20px;">
          此郵件為系統自動發送，請勿直接回覆。<br>
          若您並未要求此邀請，請忽略此郵件。
        </p>
      </div>
    `;
  }

  private createAcceptanceHtml(context: any): string {
    const { tenantName, inviteeEmail, acceptedAt } = context;
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
        <h2 style="color: #333;">邀請已被接受</h2>
        <p style="color: #666; font-size: 16px; line-height: 1.5;">
          您好，<br><br>
          ${inviteeEmail} 已接受您的邀請，加入 <strong>${tenantName}</strong> 租戶。<br>
          接受時間：${acceptedAt}
        </p>
        <p style="color: #999; font-size: 14px; margin-top: 40px; border-top: 1px solid #e0e0e0; padding-top: 20px;">
          此郵件為系統自動發送，請勿直接回覆。
        </p>
      </div>
    `;
  }

  /**
   * 發送租戶邀請郵件
   */
  async sendTenantInvitation(
    to: string,
    inviteLink: string,
    tenantName: string,
    inviterName: string
  ): Promise<boolean> {
    const subject = `邀請您加入 ${tenantName} 租戶`;
    const html = this.createInvitationHtml({ tenantName, inviteUrl: inviteLink, senderName: inviterName });
    return this.sendMail({ to, subject, html });
  }

  /**
   * 發送邀請狀態更新郵件
   * @param to 收件人
   * @param tenantName 租戶名稱
   * @param status 邀請狀態 ('approved' | 'rejected')
   * @param handlerName 處理邀請者的名稱
   */
  async sendInvitationStatusUpdate(
    to: string,
    tenantName: string,
    status: 'approved' | 'rejected',
    handlerName: string = '租戶管理員'
  ): Promise<boolean> {
    const isApproved = status === 'approved';
    const statusText = isApproved ? '已通過' : '已拒絕';
    const subject = `您的加入申請${statusText} - ${tenantName}`;

    let html: string;
    if (isApproved) {
      html = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
          <h2 style="color: #22c55e;">🎉 申請已通過</h2>
          <p style="color: #666; font-size: 16px; line-height: 1.5;">
            恭喜！您的加入申請已被 ${handlerName} 通過。<br>
            您現在可以登入 HorizAI 系統，開始使用 <strong>${tenantName}</strong> 的服務。
          </p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${this.configService.get('FRONTEND_URL')}/auth/login" style="background-color: #22c55e; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;">
              立即登入
            </a>
          </div>
          <p style="color: #999; font-size: 14px; margin-top: 40px; border-top: 1px solid #e0e0e0; padding-top: 20px;">
            此郵件為系統自動發送，請勿直接回覆。
          </p>
        </div>
      `;
    } else {
      html = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
          <h2 style="color: #ef4444;">申請已拒絕</h2>
          <p style="color: #666; font-size: 16px; line-height: 1.5;">
            很抱歉，您的加入申請已被 ${handlerName} 拒絕。<br>
            如有疑問，請聯繫 <strong>${tenantName}</strong> 的管理員。
          </p>
          <p style="color: #999; font-size: 14px; margin-top: 40px; border-top: 1px solid #e0e0e0; padding-top: 20px;">
            此郵件為系統自動發送，請勿直接回覆。
          </p>
        </div>
      `;
    }

    return this.sendMail({ to, subject, html });
  }

  /**
   * 發送租戶暫停通知郵件
   * @param to 收件人
   * @param tenantName 租戶名稱
   * @param reason 暫停原因
   */
  async sendTenantSuspensionNotice(
    to: string,
    tenantName: string,
    reason: string
  ): Promise<boolean> {
    const subject = `租戶 ${tenantName} 已被暫停`;
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
        <h2 style="color: #d32f2f;">租戶暫停通知</h2>
        <p style="color: #666; font-size: 16px; line-height: 1.5;">
          您好，<br><br>
          您的租戶 <strong>${tenantName}</strong> 已被暫停。<br>
          暫停原因：${reason}
        </p>
        <p style="color: #666; font-size: 16px; line-height: 1.5;">
          如有疑問，請聯繫系統管理員。
        </p>
        <p style="color: #999; font-size: 14px; margin-top: 40px; border-top: 1px solid #e0e0e0; padding-top: 20px;">
          此郵件為系統自動發送，請勿直接回覆。
        </p>
      </div>
    `;
    return this.sendMail({ to, subject, html });
  }

  /**
   * 發送租戶重啟通知郵件
   * @param to 收件人
   * @param tenantName 租戶名稱
   */
  async sendTenantReactivationNotice(
    to: string,
    tenantName: string
  ): Promise<boolean> {
    const subject = `租戶 ${tenantName} 已重新啟用`;
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
        <h2 style="color: #2e7d32;">租戶重啟通知</h2>
        <p style="color: #666; font-size: 16px; line-height: 1.5;">
          您好，<br><br>
          您的租戶 <strong>${tenantName}</strong> 已重新啟用。<br>
          您現在可以正常使用所有功能。
        </p>
        <p style="color: #666; font-size: 16px; line-height: 1.5;">
          如有疑問，請聯繫系統管理員。
        </p>
        <p style="color: #999; font-size: 14px; margin-top: 40px; border-top: 1px solid #e0e0e0; padding-top: 20px;">
          此郵件為系統自動發送，請勿直接回覆。
        </p>
      </div>
    `;
    return this.sendMail({ to, subject, html });
  }

  /**
   * 發送密碼重設郵件
   */
  async sendPasswordResetEmail(
    to: string,
    userName: string,
    resetUrl: string
  ): Promise<boolean> {
    const subject = 'HorizAI - 重設您的密碼';
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
        <h2 style="color: #333;">🔒 重設您的密碼</h2>
        <p style="color: #666; font-size: 16px; line-height: 1.5;">
          ${userName} 您好，<br><br>
          我們收到了您重設密碼的請求。請點擊下方按鈕來重設您的密碼：
        </p>
        <div style="text-align: center; margin: 30px 0;">
          <a href="${resetUrl}" style="background-color: #4f46e5; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;">
            重設密碼
          </a>
        </div>
        <p style="color: #666; font-size: 14px; line-height: 1.5;">
          或者，您可以複製以下連結至瀏覽器中訪問：<br>
          <a href="${resetUrl}" style="color: #4f46e5; word-break: break-all;">${resetUrl}</a>
        </p>
        <p style="color: #dc2626; font-size: 14px; line-height: 1.5; margin: 20px 0; padding: 15px; background-color: #fef2f2; border-left: 4px solid #dc2626;">
          <strong>重要提醒：</strong><br>
          • 此連結將在 24 小時後失效<br>
          • 如果您沒有要求重設密碼，請忽略此郵件<br>
          • 為了您的帳戶安全，請勿將此連結分享給他人
        </p>
        <p style="color: #999; font-size: 14px; margin-top: 40px; border-top: 1px solid #e0e0e0; padding-top: 20px;">
          此郵件為系統自動發送，請勿直接回覆。<br>
          如需協助，請聯繫我們的客戶服務團隊。
        </p>
      </div>
    `;

    return this.sendMail({ to, subject, html });
  }
}