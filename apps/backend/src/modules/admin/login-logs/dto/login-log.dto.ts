import { ApiProperty } from "@nestjs/swagger";

export class LoginLogDto {
  @ApiProperty({ description: "登入紀錄 ID" })
  id: string;

  @ApiProperty({ description: "使用者 ID", required: false })
  userId: string | null;

  @ApiProperty({ description: "IP 位址", required: false })
  ipAddress: string | null;

  @ApiProperty({ description: "User Agent", required: false })
  userAgent: string | null;

  @ApiProperty({ description: "登入時間" })
  loginAt: Date;

  @ApiProperty({ description: "是否成功" })
  success: boolean;

  @ApiProperty({ description: "失敗原因", required: false })
  failReason?: string | null;

  @ApiProperty({ description: "使用者資訊", required: false })
  user?: {
    id: string;
    email: string;
    name?: string | null;
  } | null;
}
