import { Modu<PERSON> } from '@nestjs/common';
import { LoginLogsController } from './login-logs.controller';
import { LoginLogsService } from './login-logs.service';
import { PrismaService } from '../../core/prisma/prisma.service';
import { AuthModule } from '../../core/auth/auth.module';

@Module({
  imports: [AuthModule],
  controllers: [LoginLogsController],
  providers: [LoginLogsService, PrismaService],
  exports: [LoginLogsService]
})
export class LoginLogsModule {} 