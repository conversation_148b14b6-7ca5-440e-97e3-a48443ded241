import { Injectable } from "@nestjs/common";
import { PrismaService } from "@/modules/core/prisma/prisma.service";
import { Prisma } from "@prisma/client";

@Injectable()
export class LoginLogsService {
  constructor(private readonly prisma: PrismaService) {}

  async findMany(where?: Prisma.login_logsWhereInput) {
    return this.prisma.login_logs.findMany({
      where,
      orderBy: { loginAt: "desc" },
      // 注意：login_logs 模型中只有 userId 字段，沒有直接與用戶表的關聯
      // 如果需要獲取用戶資訊，需要在業務邏輯中進行額外查詢
    });
  }
}
