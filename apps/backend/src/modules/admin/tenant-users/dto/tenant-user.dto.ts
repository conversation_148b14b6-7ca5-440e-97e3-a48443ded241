import { ApiProperty } from "@nestjs/swagger";
import { IsEnum, IsOptional, IsString, IsUUID, IsEmail } from "class-validator";
import { TenantUserRole, TenantUserStatus } from "@prisma/client";

export class CreateTenantUserDto {
  @ApiProperty({ description: "電子郵件地址" })
  @IsEmail()
  email: string;

  @ApiProperty({ description: "使用者姓名" })
  @IsString()
  name: string;

  @ApiProperty({ description: "租戶 ID" })
  @IsUUID()
  tenantId: string;

  @ApiProperty({ description: "密碼", required: false })
  @IsString()
  @IsOptional()
  password?: string;

  @ApiProperty({ description: "角色", enum: TenantUserRole, required: false })
  @IsEnum(TenantUserRole)
  @IsOptional()
  role?: TenantUserRole;

  @ApiProperty({ description: "電話號碼", required: false })
  @IsString()
  @IsOptional()
  phone?: string;

  @ApiProperty({ description: "職位", required: false })
  @IsString()
  @IsOptional()
  title?: string;

  @ApiProperty({ description: "部門", required: false })
  @IsString()
  @IsOptional()
  department?: string;

  @ApiProperty({ description: "邀請人 ID", required: false })
  @IsUUID()
  @IsOptional()
  invitedBy?: string;

  @ApiProperty({
    description: "使用者狀態",
    enum: TenantUserStatus,
    required: false,
  })
  @IsEnum(TenantUserStatus)
  @IsOptional()
  status?: TenantUserStatus;
}

export class UpdateTenantUserDto {
  @ApiProperty({ description: "電子郵件地址", required: false })
  @IsEmail()
  @IsOptional()
  email?: string;

  @ApiProperty({ description: "使用者姓名", required: false })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({ description: "角色", enum: TenantUserRole, required: false })
  @IsEnum(TenantUserRole)
  @IsOptional()
  role?: TenantUserRole;

  @ApiProperty({ description: "電話號碼", required: false })
  @IsString()
  @IsOptional()
  phone?: string;

  @ApiProperty({ description: "職位", required: false })
  @IsString()
  @IsOptional()
  title?: string;

  @ApiProperty({ description: "部門", required: false })
  @IsString()
  @IsOptional()
  department?: string;

  @ApiProperty({
    description: "使用者狀態",
    enum: TenantUserStatus,
    required: false,
  })
  @IsEnum(TenantUserStatus)
  @IsOptional()
  status?: TenantUserStatus;
}

export class ToggleUserStatusDto {
  @ApiProperty({ description: "狀態", enum: TenantUserStatus })
  @IsEnum(TenantUserStatus)
  status: TenantUserStatus;
}

export class HandleEmployeeLeavingDto {
  @ApiProperty({ description: "離職原因", required: false })
  @IsString()
  @IsOptional()
  reason?: string;

  @ApiProperty({ description: "資料轉移目標使用者 ID", required: false })
  @IsUUID()
  @IsOptional()
  transferToUserId?: string;
}

export class UpdateDataTransferStatusDto {
  @ApiProperty({
    description: "資料轉移狀態",
    enum: ["pending", "in_progress", "completed", "failed"],
  })
  @IsEnum(["pending", "in_progress", "completed", "failed"])
  status: "pending" | "in_progress" | "completed" | "failed";

  @ApiProperty({ description: "備註", required: false })
  @IsString()
  @IsOptional()
  note?: string;
}
