import { Controller, Get, Post, Body, Param, Delete, Put, Query, UseGuards, HttpCode, HttpStatus } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { TenantUsersService } from './tenant-users.service';
import { CreateTenantUserDto, UpdateTenantUserDto, ToggleUserStatusDto } from './dto/tenant-user.dto';
import { JwtAuthGuard } from '../../core/auth/guards/auth.guard';
import { Roles } from '../../core/auth/decorators/roles.decorator';
import { Role } from '../../../common/enums/role.enum';

@ApiTags('admin/tenant-users')
@ApiBearerAuth()
@Controller('admin/tenant-users')
@UseGuards(JwtAuthGuard)
export class TenantUsersController {
  constructor(private readonly tenantUsersService: TenantUsersService) {}

  @Get()
  @Roles(Role.SUPER_ADMIN, Role.SYSTEM_ADMIN, Role.TENANT_ADMIN)
  @ApiOperation({ summary: '讀取租戶使用者列表' })
  @ApiResponse({ status: 200, description: '成功讀取租戶使用者列表' })
  findAll(@Query('tenantId') tenantId?: string) {
    return this.tenantUsersService.findAll(tenantId);
  }

  @Get('available')
  @Roles(Role.SUPER_ADMIN, Role.SYSTEM_ADMIN, Role.TENANT_ADMIN)
  @ApiOperation({ summary: '讀取可新增到租戶的使用者列表' })
  @ApiResponse({ status: 200, description: '成功讀取可用使用者列表' })
  getAvailableUsers() {
    return this.tenantUsersService.findAvailableUsers();
  }

  @Get(':id')
  @Roles(Role.SUPER_ADMIN, Role.SYSTEM_ADMIN, Role.TENANT_ADMIN)
  @ApiOperation({ summary: '根據 ID 讀取租戶使用者' })
  @ApiResponse({ status: 200, description: '成功讀取租戶使用者' })
  @ApiResponse({ status: 404, description: '找不到租戶使用者' })
  findOne(@Param('id') id: string, @Query('tenantId') tenantId: string) {
    return this.tenantUsersService.findOne(id, tenantId);
  }

  @Post()
  @Roles(Role.SUPER_ADMIN, Role.SYSTEM_ADMIN, Role.TENANT_ADMIN)
  @ApiOperation({ summary: '將使用者新增到租戶' })
  @ApiResponse({ status: 201, description: '成功新增使用者到租戶' })
  @ApiResponse({ status: 400, description: '新增失敗' })
  @ApiResponse({ status: 404, description: '找不到使用者或租戶' })
  @ApiResponse({ status: 409, description: '使用者已在租戶中' })
  create(@Body() createTenantUserDto: CreateTenantUserDto) {
    return this.tenantUsersService.create(createTenantUserDto);
  }

  @Put(':id')
  @Roles(Role.SUPER_ADMIN, Role.SYSTEM_ADMIN, Role.TENANT_ADMIN)
  @ApiOperation({ summary: '更新租戶使用者' })
  @ApiResponse({ status: 200, description: '成功更新租戶使用者' })
  @ApiResponse({ status: 400, description: '更新失敗' })
  @ApiResponse({ status: 404, description: '找不到租戶使用者' })
  update(@Param('id') id: string, @Body() updateTenantUserDto: UpdateTenantUserDto) {
    return this.tenantUsersService.update(id, updateTenantUserDto);
  }

  @Delete(':id')
  @Roles(Role.SUPER_ADMIN, Role.SYSTEM_ADMIN, Role.TENANT_ADMIN)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '從租戶中移除使用者' })
  @ApiResponse({ status: 200, description: '成功從租戶中移除使用者' })
  @ApiResponse({ status: 400, description: '移除失敗' })
  @ApiResponse({ status: 404, description: '找不到租戶使用者' })
  remove(@Param('id') id: string) {
    return this.tenantUsersService.remove(id);
  }

  @Put(':id/status')
  @Roles(Role.SUPER_ADMIN, Role.SYSTEM_ADMIN, Role.TENANT_ADMIN)
  @ApiOperation({ summary: '切換使用者狀態 (啟用/停用)' })
  @ApiResponse({ status: 200, description: '成功切換使用者狀態' })
  @ApiResponse({ status: 400, description: '切換失敗' })
  @ApiResponse({ status: 404, description: '找不到使用者' })
  toggleStatus(@Param('id') id: string, @Body() toggleUserStatusDto: ToggleUserStatusDto) {
    return this.tenantUsersService.toggleStatus(id, toggleUserStatusDto);
  }
}