import { Modu<PERSON> } from '@nestjs/common';
import { WorkspacesController } from './workspaces.controller';
import { WorkspacesService } from './workspaces.service';
import { WorkspaceTemplateService } from './services/workspace-template.service';
import { PrismaModule } from '../../core/prisma/prisma.module';
import { CaslModule } from '../../../casl/casl.module';
import { WorkspaceGuard } from './guards/workspace.guard';
import { TenantQuotaService } from '../tenants/services/tenant-quota.service';

@Module({
  imports: [PrismaModule, CaslModule],
  controllers: [WorkspacesController],
  providers: [
    WorkspacesService,
    WorkspaceTemplateService,
    WorkspaceGuard,
    TenantQuotaService
  ],
  exports: [WorkspacesService, WorkspaceTemplateService, WorkspaceGuard],
})
export class WorkspacesModule { } 