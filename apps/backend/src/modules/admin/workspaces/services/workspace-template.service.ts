import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../../../core/prisma/prisma.service';
import { createId } from '@paralleldrive/cuid2';

export interface WorkspaceTemplate {
    id: string;
    name: string;
    description?: string | null;
    defaultSettings: Record<string, any> | null;
    defaultMemberRole: string;
    isSystem: boolean;
    tenantId?: string | null;
    createdBy: string;
    createdAt: Date;
    updatedAt: Date;
}

@Injectable()
export class WorkspaceTemplateService {
    constructor(private readonly prisma: PrismaService) { }

    /**
     * 創建工作區模板
     */
    async create(data: {
        name: string;
        description?: string;
        defaultSettings: Record<string, any>;
        defaultMemberRole?: string;
        isSystem?: boolean;
        tenantId?: string;
        createdBy: string;
    }): Promise<WorkspaceTemplate> {
        const result = await this.prisma.workspace_templates.create({
            data: {
                id: createId(),
                name: data.name,
                description: data.description,
                defaultSettings: data.defaultSettings,
                defaultMemberRole: data.defaultMemberRole || 'member',
                isSystem: data.isSystem || false,
                tenantId: data.tenantId,
                createdBy: data.createdBy,
            },
        });

        return result as WorkspaceTemplate;
    }

    /**
     * 獲取模板列表
     */
    async findAll(tenantId?: string): Promise<WorkspaceTemplate[]> {
        const where: any = {
            OR: [
                { isSystem: true }, // 系統模板
                { tenantId: tenantId }, // 租戶專用模板
            ],
        };

        const results = await this.prisma.workspace_templates.findMany({
            where,
            orderBy: [
                { isSystem: 'desc' }, // 系統模板優先
                { createdAt: 'desc' },
            ],
        });

        return results as WorkspaceTemplate[];
    }

    /**
     * 獲取單個模板
     */
    async findOne(id: string, tenantId?: string): Promise<WorkspaceTemplate> {
        const template = await this.prisma.workspace_templates.findFirst({
            where: {
                id,
                OR: [
                    { isSystem: true },
                    { tenantId: tenantId },
                ],
            },
        });

        if (!template) {
            throw new NotFoundException('工作區模板不存在');
        }

        return template as WorkspaceTemplate;
    }

    /**
     * 更新模板
     */
    async update(
        id: string,
        data: {
            name?: string;
            description?: string;
            defaultSettings?: Record<string, any>;
            defaultMemberRole?: string;
        },
        tenantId?: string
    ): Promise<WorkspaceTemplate> {
        // 檢查模板是否存在且有權限修改
        const template = await this.findOne(id, tenantId);

        if (template.isSystem) {
            throw new BadRequestException('無法修改系統模板');
        }

        if (template.tenantId !== tenantId) {
            throw new BadRequestException('無權限修改此模板');
        }

        const result = await this.prisma.workspace_templates.update({
            where: { id },
            data: {
                ...data,
                updatedAt: new Date(),
            },
        });

        return result as WorkspaceTemplate;
    }

    /**
     * 刪除模板
     */
    async remove(id: string, tenantId?: string): Promise<void> {
        const template = await this.findOne(id, tenantId);

        if (template.isSystem) {
            throw new BadRequestException('無法刪除系統模板');
        }

        if (template.tenantId !== tenantId) {
            throw new BadRequestException('無權限刪除此模板');
        }

        await this.prisma.workspace_templates.delete({
            where: { id },
        });
    }

    /**
     * 複製模板
     */
    async duplicate(
        sourceId: string,
        data: {
            name: string;
            description?: string;
            tenantId?: string;
            createdBy: string;
        }
    ): Promise<WorkspaceTemplate> {
        const sourceTemplate = await this.findOne(sourceId, data.tenantId);

        return this.create({
            name: data.name,
            description: data.description || sourceTemplate.description || undefined,
            defaultSettings: sourceTemplate.defaultSettings as Record<string, any> || {},
            defaultMemberRole: sourceTemplate.defaultMemberRole,
            isSystem: false, // 複製的模板不是系統模板
            tenantId: data.tenantId,
            createdBy: data.createdBy,
        });
    }

    /**
     * 獲取模板使用統計
     */
    async getTemplateStats(templateId: string, tenantId?: string) {
        await this.findOne(templateId, tenantId);

        const usageCount = await this.prisma.workspaces.count({
            where: {
                // 假設 workspaces 表有 templateId 欄位
                // templateId: templateId,
            },
        });

        return {
            templateId,
            usageCount,
            lastUsed: await this.getLastUsedDate(templateId),
        };
    }

    private async getLastUsedDate(templateId: string): Promise<Date | null> {
        const lastWorkspace = await this.prisma.workspaces.findFirst({
            where: {
                // templateId: templateId,
            },
            orderBy: {
                createdAt: 'desc',
            },
            select: {
                createdAt: true,
            },
        });

        return lastWorkspace?.createdAt || null;
    }

    /**
     * 創建預設系統模板
     */
    async createDefaultTemplates(createdBy: string): Promise<WorkspaceTemplate[]> {
        const defaultTemplates = [
            {
                name: '基本工作區',
                description: '適合小型團隊的基本工作區設定',
                defaultSettings: {
                    features: ['projects', 'tasks', 'files'],
                    permissions: {
                        allowGuestAccess: false,
                        requireApprovalForNewMembers: true,
                    },
                    notifications: {
                        emailNotifications: true,
                        slackIntegration: false,
                    },
                },
                defaultMemberRole: 'member',
            },
            {
                name: '專案管理工作區',
                description: '專為專案管理設計的工作區',
                defaultSettings: {
                    features: ['projects', 'tasks', 'files', 'gantt', 'reports'],
                    permissions: {
                        allowGuestAccess: true,
                        requireApprovalForNewMembers: false,
                    },
                    notifications: {
                        emailNotifications: true,
                        slackIntegration: true,
                    },
                },
                defaultMemberRole: 'member',
            },
            {
                name: '開發團隊工作區',
                description: '適合軟體開發團隊的工作區設定',
                defaultSettings: {
                    features: ['projects', 'tasks', 'files', 'code', 'ci_cd', 'issues'],
                    permissions: {
                        allowGuestAccess: false,
                        requireApprovalForNewMembers: true,
                    },
                    integrations: {
                        github: true,
                        jira: true,
                        slack: true,
                    },
                },
                defaultMemberRole: 'member',
            },
        ];

        const createdTemplates: WorkspaceTemplate[] = [];
        for (const template of defaultTemplates) {
            const created = await this.create({
                ...template,
                isSystem: true,
                createdBy,
            });
            createdTemplates.push(created);
        }

        return createdTemplates;
    }
} 