import { IsString, IsOptional, IsObject, IsEnum } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateWorkspaceDto {
  @ApiProperty({ description: '工作區名稱' })
  @IsString()
  name: string;

  @ApiPropertyOptional({ description: '工作區描述' })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({ description: '工作區設定' })
  @IsObject()
  @IsOptional()
  settings?: Record<string, any>;

  @ApiPropertyOptional({ description: '租戶 ID (僅限 SUPER_ADMIN 使用)' })
  @IsString()
  @IsOptional()
  tenantId?: string;
}

export class UpdateWorkspaceDto {
  @ApiPropertyOptional({ description: '工作區名稱' })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiPropertyOptional({ description: '工作區描述' })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({ description: '工作區設定' })
  @IsObject()
  @IsOptional()
  settings?: Record<string, any>;

  @ApiPropertyOptional({ description: '工作區狀態', enum: ['active', 'inactive', 'archived'] })
  @IsEnum(['active', 'inactive', 'archived'])
  @IsOptional()
  status?: string;
}

export class AddWorkspaceMemberDto {
  @ApiProperty({ description: '使用者 ID' })
  @IsString()
  userId: string;

  @ApiProperty({ description: '成員角色', enum: ['admin', 'member', 'viewer'] })
  @IsEnum(['admin', 'member', 'viewer'])
  role: string;
}

export class UpdateWorkspaceMemberDto {
  @ApiProperty({ description: '成員角色', enum: ['admin', 'member', 'viewer'] })
  @IsEnum(['admin', 'member', 'viewer'])
  role: string;
}

export class WorkspaceTemplateDto {
  @ApiProperty({ description: '模板名稱' })
  @IsString()
  name: string;

  @ApiPropertyOptional({ description: '模板描述' })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({ description: '預設設定' })
  @IsObject()
  defaultSettings: Record<string, any>;

  @ApiPropertyOptional({ description: '預設成員角色' })
  @IsEnum(['admin', 'member', 'viewer'])
  @IsOptional()
  defaultMemberRole?: string;
}

export class WorkspaceInviteDto {
  @ApiProperty({ description: '邀請的電子郵件地址', type: [String] })
  @IsString({ each: true })
  emails: string[];

  @ApiProperty({ description: '邀請角色', enum: ['admin', 'member', 'viewer'] })
  @IsEnum(['admin', 'member', 'viewer'])
  role: string;

  @ApiPropertyOptional({ description: '邀請訊息' })
  @IsString()
  @IsOptional()
  message?: string;
}
