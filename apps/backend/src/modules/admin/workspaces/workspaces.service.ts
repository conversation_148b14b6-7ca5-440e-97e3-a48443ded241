import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from "@nestjs/common";
import { PrismaService } from "@/modules/core/prisma/prisma.service";
import { createId } from "@paralleldrive/cuid2";
import { TenantQuotaService } from "../tenants/services/tenant-quota.service";

@Injectable()
export class WorkspacesService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly tenantQuotaService: TenantQuotaService
  ) {}

  async create(data: {
    name: string;
    description?: string;
    tenantId: string;
    ownerId: string;
    settings?: Record<string, any>;
  }) {
    try {
      return await this.prisma.workspaces.create({
        data: {
          id: createId(),
          name: data.name,
          description: data.description,
          tenantId: data.tenantId,
          ownerId: data.ownerId,
          settings: data.settings || {},
          status: "active",
          updatedAt: new Date(),
        },
      });
    } catch (error) {
      throw new BadRequestException(`Failed to create workspace: ${error}`);
    }
  }

  async findAll(tenantId: string) {
    return this.prisma.workspaces.findMany({
      where: { tenantId: tenantId },
    });
  }

  async findOne(id: string, tenantId: string) {
    const workspace = await this.prisma.workspaces.findFirst({
      where: { id, tenantId: tenantId },
    });

    if (!workspace) {
      throw new NotFoundException(`Workspace with ID ${id} not found`);
    }

    return workspace;
  }

  async update(
    id: string,
    tenantId: string,
    data: {
      name?: string;
      description?: string;
      settings?: Record<string, any>;
      status?: string;
    }
  ) {
    try {
      return await this.prisma.workspaces.update({
        where: { id },
        data: {
          ...data,
          updatedAt: new Date(),
        },
      });
    } catch (error) {
      throw new NotFoundException(`Workspace with ID ${id} not found`);
    }
  }

  async remove(id: string, tenantId: string) {
    try {
      await this.findOne(id, tenantId); // Check if workspace exists
      return await this.prisma.workspaces.delete({
        where: { id },
      });
    } catch (error) {
      throw new NotFoundException(`Workspace with ID ${id} not found`);
    }
  }

  async addMember(
    workspaceId: string,
    userId: string,
    role: string
  ): Promise<void> {
    try {
      await this.prisma.workspace_members.create({
        data: {
          id: createId(),
          workspaceId,
          userId,
          role,
          updatedAt: new Date(),
        },
      });
    } catch (error) {
      if (error instanceof Error && "code" in error && error.code === "P2002") {
        throw new BadRequestException(
          "User is already a member of this workspace"
        );
      }
      throw error;
    }
  }

  async removeMember(workspaceId: string, userId: string): Promise<void> {
    try {
      await this.prisma.workspace_members.deleteMany({
        where: {
          workspaceId,
          userId: userId,
        },
      });
    } catch (error) {
      if (error instanceof Error && "code" in error && error.code === "P2025") {
        throw new NotFoundException("Workspace member not found");
      }
      throw error;
    }
  }

  async getMembers(workspaceId: string) {
    return this.prisma.workspace_members.findMany({
      where: { workspaceId },
      include: {
        tenant_user: {
          select: {
            id: true,
            email: true,
            name: true,
            avatar: true,
            title: true,
            department: true,
            status: true,
            lastLoginAt: true,
          },
        },
      },
    });
  }

  async getMembersByTenant(workspaceId: string, tenantId: string) {
    return this.prisma.workspace_members.findMany({
      where: {
        workspaceId,
        tenant_user: {
          tenantId: tenantId,
        },
      },
      include: {
        tenant_user: {
          select: {
            id: true,
            email: true,
            name: true,
            avatar: true,
            title: true,
            department: true,
            status: true,
            lastLoginAt: true,
            tenantId: true,
          },
        },
      },
    });
  }

  async updateMemberRole(
    workspaceId: string,
    userId: string,
    role: string
  ): Promise<void> {
    try {
      await this.prisma.workspace_members.updateMany({
        where: {
          workspaceId,
          userId,
        },
        data: {
          role,
          updatedAt: new Date(),
        },
      });
    } catch (error) {
      throw new NotFoundException("Workspace member not found");
    }
  }

  async getWorkspacesByUser(userId: string) {
    const memberships = await this.prisma.workspace_members.findMany({
      where: { userId },
      include: {
        workspaces: {
          select: {
            id: true,
            name: true,
            description: true,
            status: true,
            tenantId: true,
            createdAt: true,
            updatedAt: true,
          },
        },
      },
    });

    return memberships.map((membership) => ({
      ...membership.workspaces,
      userRole: membership.role,
    }));
  }

  /**
   * 獲取工作區統計資訊
   */
  async getWorkspaceStats(workspaceId: string, tenantId: string) {
    // 驗證工作區存在且屬於租戶
    await this.findOne(workspaceId, tenantId);

    const [memberCount, projectCount, recentActivity, membersByRole] =
      await Promise.all([
        // 成員總數
        this.prisma.workspace_members.count({
          where: { workspaceId },
        }),

        // 專案總數
        this.prisma.projects.count({
          where: { workspaceId },
        }),

        // 最近活動 (最近 7 天的專案更新)
        this.prisma.projects.count({
          where: {
            workspaceId,
            updatedAt: {
              gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
            },
          },
        }),

        // 按角色分組的成員數
        this.prisma.workspace_members.groupBy({
          by: ["role"],
          where: { workspaceId },
          _count: { role: true },
        }),
      ]);

    return {
      memberCount,
      projectCount,
      recentActivity,
      membersByRole: membersByRole.reduce(
        (acc, item) => {
          acc[item.role] = item._count.role;
          return acc;
        },
        {} as Record<string, number>
      ),
      lastUpdated: new Date(),
    };
  }

  /**
   * 從模板創建工作區
   */
  async createFromTemplate(
    templateId: string,
    data: {
      name: string;
      description?: string;
      tenantId: string;
      ownerId: string;
    }
  ) {
    const template = await this.prisma.workspace_templates.findUnique({
      where: { id: templateId },
    });

    if (!template) {
      throw new NotFoundException("工作區模板不存在");
    }

    return this.create({
      ...data,
      settings: template.defaultSettings as Record<string, any>,
    });
  }

  /**
   * 批量邀請用戶到工作區
   */
  async inviteUsers(
    workspaceId: string,
    tenantId: string,
    inviteData: {
      emails: string[];
      role: string;
      message?: string;
      invitedBy: string;
    }
  ): Promise<{
    total: number;
    successful: number;
    results: Array<{
      email: string;
      status: string;
      message: string;
    }>;
  }> {
    // 驗證工作區存在且屬於租戶
    await this.findOne(workspaceId, tenantId);

    const results: Array<{
      email: string;
      status: string;
      message: string;
    }> = [];

    for (const email of inviteData.emails) {
      try {
        // 檢查用戶是否已存在於租戶中
        const existingUser = await this.prisma.tenant_users.findFirst({
          where: {
            email,
            tenantId,
          },
        });

        if (existingUser) {
          // 檢查是否已經是工作區成員
          const existingMember = await this.prisma.workspace_members.findFirst({
            where: {
              workspaceId,
              userId: existingUser.id,
            },
          });

          if (!existingMember) {
            // 直接添加為成員
            await this.addMember(workspaceId, existingUser.id, inviteData.role);
            results.push({
              email,
              status: "added",
              message: "用戶已直接添加到工作區",
            });
          } else {
            results.push({
              email,
              status: "already_member",
              message: "用戶已經是工作區成員",
            });
          }
        } else {
          // 創建邀請記錄
          await this.prisma.workspace_invitations.create({
            data: {
              id: createId(),
              workspaceId,
              email,
              role: inviteData.role,
              invitedBy: inviteData.invitedBy,
              token: createId(), // 生成邀請令牌
              expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 天後過期
              status: "pending",
            },
          });

          results.push({
            email,
            status: "invited",
            message: "邀請已發送",
          });
        }
      } catch (error) {
        results.push({
          email,
          status: "error",
          message: `邀請失敗: ${error instanceof Error ? error.message : "未知錯誤"}`,
        });
      }
    }

    return {
      total: inviteData.emails.length,
      successful: results.filter((r) => ["added", "invited"].includes(r.status))
        .length,
      results,
    };
  }

  /**
   * 獲取工作區活動日誌
   */
  async getWorkspaceActivity(
    workspaceId: string,
    tenantId: string,
    options: {
      limit?: number;
      offset?: number;
      startDate?: Date;
      endDate?: Date;
    } = {}
  ) {
    // 驗證工作區存在且屬於租戶
    await this.findOne(workspaceId, tenantId);

    const { limit = 50, offset = 0, startDate, endDate } = options;

    const whereClause: any = {
      workspaceId,
    };

    if (startDate || endDate) {
      whereClause.created_at = {};
      if (startDate) whereClause.created_at.gte = startDate;
      if (endDate) whereClause.created_at.lte = endDate;
    }

    return this.prisma.workspace_activity_logs.findMany({
      where: whereClause,
      orderBy: {
        created_at: "desc",
      },
      take: limit,
      skip: offset,
    });
  }

  /**
   * 複製工作區
   */
  async duplicateWorkspace(
    sourceWorkspaceId: string,
    tenantId: string,
    data: {
      name: string;
      description?: string;
      copyMembers?: boolean;
      copyProjects?: boolean;
    }
  ) {
    const sourceWorkspace = await this.findOne(sourceWorkspaceId, tenantId);

    return this.prisma.$transaction(async (tx) => {
      // 創建新工作區
      const newWorkspace = await tx.workspaces.create({
        data: {
          id: createId(),
          name: data.name,
          description: data.description || sourceWorkspace.description,
          tenantId,
          ownerId: sourceWorkspace.ownerId,
          settings: sourceWorkspace.settings as any,
          status: "active",
          updatedAt: new Date(),
        },
      });

      // 複製成員 (如果需要)
      if (data.copyMembers) {
        const members = await tx.workspace_members.findMany({
          where: { workspaceId: sourceWorkspaceId },
        });

        for (const member of members) {
          await tx.workspace_members.create({
            data: {
              id: createId(),
              workspaceId: newWorkspace.id,
              userId: member.userId,
              role: member.role,
              updatedAt: new Date(),
            },
          });
        }
      }

      // 複製專案 (如果需要)
      if (data.copyProjects) {
        const projects = await tx.projects.findMany({
          where: { workspaceId: sourceWorkspaceId },
        });

        for (const project of projects) {
          await tx.projects.create({
            data: {
              id: createId(),
              name: `${project.name} (複製)`,
              description: project.description,
              tenantId,
              workspaceId: newWorkspace.id,
              userId: project.userId,
              status: "active",
            },
          });
        }
      }

      return newWorkspace;
    });
  }
}
