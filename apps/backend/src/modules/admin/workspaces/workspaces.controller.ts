import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  BadRequestException,
} from "@nestjs/common";
import {
  ApiTags,
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
} from "@nestjs/swagger";
import { WorkspacesService } from "./workspaces.service";
import { JwtAuthGuard } from "../../core/auth/guards/auth.guard";
import { RolesGuard } from "../../core/auth/guards/roles.guard";
import { Roles } from "../../core/auth/decorators/roles.decorator";
import { Role } from "../../../common/enums/role.enum";
import { CurrentUser } from "../../core/auth/decorators/current-user.decorator";
import { CurrentTenant } from "../tenants/decorators/current-tenant.decorator";
import { PoliciesGuard } from "../../../casl/guards/permission.guard";
import { CheckPolicies } from "../../../casl/decorators/check-policies.decorator";
import { AppAbility } from "../../../types/models/casl.model";
import { Actions, Subjects } from "@horizai/permissions";
import { TenantScoped } from "../../../common/decorators/tenant-scoped.decorator";

@ApiTags("admin/workspaces")
@Controller("admin/workspaces")
@UseGuards(JwtAuthGuard, RolesGuard, PoliciesGuard)
@ApiBearerAuth()
export class WorkspacesController {
  constructor(private readonly workspacesService: WorkspacesService) { }

  @Post()
  @CheckPolicies((ability: AppAbility) =>
    ability.can(Actions.CREATE, Subjects.WORKSPACE)
  )
  @ApiOperation({ summary: "建立工作區" })
  @ApiResponse({ status: 201, description: "工作區建立成功" })
  async create(
    @Body() data: { name: string; description?: string; settings?: any; tenantId?: string },
    @CurrentUser("id") userId: string,
    @CurrentUser() user: any,
    @CurrentTenant("id") tenantId?: string
  ) {
    console.log('=== Workspace Create Request ===');
    console.log('Request data:', data);
    console.log('User ID:', userId);
    console.log('User role:', user.role);
    console.log('Current tenant ID:', tenantId);

    // 對於 SUPER_ADMIN，允許指定 tenantId；對於其他使用者，使用當前租戶 ID
    const targetTenantId = user.role === "SUPER_ADMIN" ?
      (data.tenantId || tenantId) : tenantId;

    console.log('Target tenant ID:', targetTenantId);

    if (!targetTenantId) {
      console.log('ERROR: No tenant ID provided');
      throw new BadRequestException("租戶 ID 是必需的");
    }

    const createData = {
      ...data,
      ownerId: userId,
      tenantId: targetTenantId,
    };

    console.log('Creating workspace with data:', createData);

    try {
      const result = await this.workspacesService.create(createData);
      console.log('Workspace created successfully:', result);
      return result;
    } catch (error) {
      console.log('ERROR creating workspace:', error);
      throw error;
    }
  }

  @Get()
  @TenantScoped()
  @CheckPolicies((ability: AppAbility) =>
    ability.can(Actions.READ, Subjects.WORKSPACE)
  )
  @ApiOperation({ summary: "讀取工作區列表" })
  @ApiResponse({ status: 200, description: "成功讀取工作區列表" })
  async findAll(@CurrentTenant("id") tenantId: string) {
    return this.workspacesService.findAll(tenantId);
  }

  @Get(":id")
  @CheckPolicies((ability: AppAbility) =>
    ability.can(Actions.READ, Subjects.WORKSPACE)
  )
  @ApiOperation({ summary: "讀取指定工作區" })
  @ApiResponse({ status: 200, description: "成功讀取指定工作區" })
  async findOne(
    @Param("id") id: string,
    @CurrentTenant("id") tenantId: string
  ) {
    return this.workspacesService.findOne(id, tenantId);
  }

  @Put(":id")
  @CheckPolicies((ability: AppAbility) =>
    ability.can(Actions.UPDATE, Subjects.WORKSPACE)
  )
  @ApiOperation({ summary: "更新工作區" })
  @ApiResponse({ status: 200, description: "工作區更新成功" })
  async update(
    @Param("id") id: string,
    @Body() data: { name?: string; description?: string; settings?: any },
    @CurrentTenant("id") tenantId: string
  ) {
    return this.workspacesService.update(id, tenantId, data);
  }

  @Delete(":id")
  @CheckPolicies((ability: AppAbility) =>
    ability.can(Actions.DELETE, Subjects.WORKSPACE)
  )
  @ApiOperation({ summary: "刪除工作區" })
  @ApiResponse({ status: 200, description: "工作區刪除成功" })
  async remove(@Param("id") id: string, @CurrentTenant("id") tenantId: string) {
    await this.workspacesService.remove(id, tenantId);
    return { message: "工作區已刪除" };
  }

  @Get(":id/members")
  @CheckPolicies((ability: AppAbility) =>
    ability.can(Actions.READ, Subjects.WORKSPACE)
  )
  @ApiOperation({ summary: "讀取工作區成員列表" })
  @ApiResponse({ status: 200, description: "成功讀取工作區成員列表" })
  async getMembers(
    @Param("id") workspaceId: string,
    @CurrentUser() user: any,
    @CurrentTenant("id") tenantId?: string
  ) {
    // SUPER_ADMIN 可以獲取所有工作區成員，不受 tenantId 限制
    if (user.role === "SUPER_ADMIN") {
      return this.workspacesService.getMembers(workspaceId);
    }

    // 普通用戶只能獲取自己租戶的工作區成員
    if (!tenantId) {
      throw new BadRequestException("Tenant ID is required for non-admin users");
    }

    return this.workspacesService.getMembersByTenant(workspaceId, tenantId);
  }

  @Post(":id/members")
  @CheckPolicies((ability: AppAbility) =>
    ability.can(Actions.UPDATE, Subjects.WORKSPACE)
  )
  @ApiOperation({ summary: "添加工作區成員" })
  @ApiResponse({ status: 201, description: "成功添加工作區成員" })
  async addMember(
    @Param("id") workspaceId: string,
    @Body() data: { userId: string; role: string },
    @CurrentTenant("id") tenantId: string
  ) {
    // 驗證工作區屬於當前租戶
    await this.workspacesService.findOne(workspaceId, tenantId);
    return this.workspacesService.addMember(workspaceId, data.userId, data.role);
  }

  @Put(":id/members/:userId")
  @CheckPolicies((ability: AppAbility) =>
    ability.can(Actions.UPDATE, Subjects.WORKSPACE)
  )
  @ApiOperation({ summary: "更新工作區成員角色" })
  @ApiResponse({ status: 200, description: "成功更新成員角色" })
  async updateMemberRole(
    @Param("id") workspaceId: string,
    @Param("userId") userId: string,
    @Body() data: { role: string },
    @CurrentTenant("id") tenantId: string
  ) {
    // 驗證工作區屬於當前租戶
    await this.workspacesService.findOne(workspaceId, tenantId);
    return this.workspacesService.updateMemberRole(workspaceId, userId, data.role);
  }

  @Delete(":id/members/:userId")
  @CheckPolicies((ability: AppAbility) =>
    ability.can(Actions.UPDATE, Subjects.WORKSPACE)
  )
  @ApiOperation({ summary: "移除工作區成員" })
  @ApiResponse({ status: 200, description: "成功移除工作區成員" })
  async removeMember(
    @Param("id") workspaceId: string,
    @Param("userId") userId: string,
    @CurrentTenant("id") tenantId: string
  ) {
    // 驗證工作區屬於當前租戶
    await this.workspacesService.findOne(workspaceId, tenantId);
    return this.workspacesService.removeMember(workspaceId, userId);
  }

  @Get(":id/stats")
  @CheckPolicies((ability: AppAbility) =>
    ability.can(Actions.READ, Subjects.WORKSPACE)
  )
  @ApiOperation({ summary: "獲取工作區統計資訊" })
  @ApiResponse({ status: 200, description: "成功獲取工作區統計" })
  async getWorkspaceStats(
    @Param("id") workspaceId: string,
    @CurrentTenant("id") tenantId: string
  ) {
    return this.workspacesService.getWorkspaceStats(workspaceId, tenantId);
  }

  @Post(":id/invite")
  @CheckPolicies((ability: AppAbility) =>
    ability.can(Actions.UPDATE, Subjects.WORKSPACE)
  )
  @ApiOperation({ summary: "批量邀請用戶到工作區" })
  @ApiResponse({ status: 200, description: "邀請發送成功" })
  async inviteUsers(
    @Param("id") workspaceId: string,
    @Body() inviteData: { emails: string[]; role: string; message?: string },
    @CurrentUser("id") userId: string,
    @CurrentTenant("id") tenantId: string
  ) {
    return this.workspacesService.inviteUsers(workspaceId, tenantId, {
      ...inviteData,
      invitedBy: userId,
    });
  }

  @Get(":id/activity")
  @CheckPolicies((ability: AppAbility) =>
    ability.can(Actions.READ, Subjects.WORKSPACE)
  )
  @ApiOperation({ summary: "獲取工作區活動日誌" })
  @ApiResponse({ status: 200, description: "成功獲取活動日誌" })
  async getWorkspaceActivity(
    @Param("id") workspaceId: string,
    @CurrentTenant("id") tenantId: string,
    @Query("limit") limit?: number,
    @Query("offset") offset?: number,
    @Query("startDate") startDate?: string,
    @Query("endDate") endDate?: string
  ) {
    const options: any = {};
    if (limit) options.limit = Number(limit);
    if (offset) options.offset = Number(offset);
    if (startDate) options.startDate = new Date(startDate);
    if (endDate) options.endDate = new Date(endDate);

    return this.workspacesService.getWorkspaceActivity(workspaceId, tenantId, options);
  }

  @Post(":id/duplicate")
  @CheckPolicies((ability: AppAbility) =>
    ability.can(Actions.CREATE, Subjects.WORKSPACE)
  )
  @ApiOperation({ summary: "複製工作區" })
  @ApiResponse({ status: 201, description: "工作區複製成功" })
  async duplicateWorkspace(
    @Param("id") sourceWorkspaceId: string,
    @Body() data: {
      name: string;
      description?: string;
      copyMembers?: boolean;
      copyProjects?: boolean
    },
    @CurrentTenant("id") tenantId: string
  ) {
    return this.workspacesService.duplicateWorkspace(sourceWorkspaceId, tenantId, data);
  }

  @Post("from-template/:templateId")
  @CheckPolicies((ability: AppAbility) =>
    ability.can(Actions.CREATE, Subjects.WORKSPACE)
  )
  @ApiOperation({ summary: "從模板創建工作區" })
  @ApiResponse({ status: 201, description: "工作區創建成功" })
  async createFromTemplate(
    @Param("templateId") templateId: string,
    @Body() data: { name: string; description?: string },
    @CurrentUser("id") userId: string,
    @CurrentTenant("id") tenantId: string
  ) {
    return this.workspacesService.createFromTemplate(templateId, {
      ...data,
      tenantId,
      ownerId: userId,
    });
  }
}
