import { Injectable, CanActivate, ExecutionContext, Logger } from '@nestjs/common';
import { Role } from '../../../../common/enums/role.enum';
import { WorkspacesService } from '../workspaces.service';

@Injectable()
export class WorkspaceGuard implements CanActivate {
  private readonly logger = new Logger(WorkspaceGuard.name);

  constructor(private readonly workspacesService: WorkspacesService) { }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const { user, params } = request;
    const workspaceId = params.id || params.workspaceId;

    this.logger.debug(`Checking workspace access for user: ${JSON.stringify({
      id: user?.id,
      email: user?.email,
      role: user?.role,
      tenantId: user?.tenantId
    })}`);

    // SUPER_ADMIN 擁有所有工作區的訪問權限
    if (user.role === "SUPER_ADMIN") {
      this.logger.debug('User is SUPER_ADMIN, granting access');
      return true;
    }

    if (!workspaceId) {
      this.logger.warn('No workspace ID found in request params');
      return false;
    }

    try {
      const workspace = await this.workspacesService.findOne(workspaceId, user.tenantId);

      // 檢查工作區是否屬於使用者的租戶
      if (workspace.tenantId !== user.tenantId) {
        this.logger.warn(`Workspace ${workspaceId} does not belong to tenant ${user.tenantId}`);
        return false;
      }

      this.logger.debug('Workspace access granted');
      return true;
    } catch (error) {
      this.logger.error(`Error checking workspace access: ${error.message}`);
      return false;
    }
  }
} 