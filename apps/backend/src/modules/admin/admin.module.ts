import { Module } from "@nestjs/common";
import { EventEmitterModule } from "@nestjs/event-emitter";

import { CaslModule } from "../../casl/casl.module";
import { PrismaModule } from "../core/prisma/prisma.module";

import { AiBotsModule } from "./ai/bots/ai-bots.module";
import { DashboardModule } from "./dashboard/dashboard.module";
import { OrdersModule } from "./orders/orders.module";
import { PlansModule } from "./plans/plans.module";
import { RolesModule } from "./roles/roles.module";
import { SettingsModule } from "./settings/settings.module";
import { SystemUsersModule } from "./system-users/system-users.module";
import { TenantInvitationsModule } from "../core/tenant-invitations/tenant-invitations.module";
import { TenantUsersModule } from "./tenant-users/tenant-users.module";
import { TenantsModule } from "./tenants/tenants.module";
import { WorkspacesModule } from "./workspaces/workspaces.module";
import { LoginLogsModule } from "./login-logs/login-logs.module";
import { PermissionsModule } from "./permissions/permissions.module";
import { SystemLogsModule } from "./system-logs/system-logs.module";
import { AiModule } from "./ai/ai.module";

@Module({
  imports: [
    PrismaModule,
    CaslModule,
    EventEmitterModule.forRoot(),
    AiBotsModule,
    DashboardModule,
    OrdersModule,
    PlansModule,
    RolesModule,
    SettingsModule,
    SystemUsersModule,
    TenantInvitationsModule,
    TenantUsersModule,
    TenantsModule,
    WorkspacesModule,
    LoginLogsModule,
    PermissionsModule,
    SystemLogsModule,
    AiModule,
  ],
})
export class AdminModule {}
