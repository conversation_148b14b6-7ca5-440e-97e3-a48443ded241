import { Injectable, NotFoundException, BadRequestException, Logger } from '@nestjs/common'
import { EventEmitter2 } from '@nestjs/event-emitter'
import { PrismaService } from '../../core/prisma/prisma.service'
import { ConfigService } from '@nestjs/config'
import { MailService } from '../../core/mail/mail.service'
import { EncryptionService } from '../../core/encryption/encryption.service'

// 定義 Setting 類型，因為 Prisma 客戶端不再導出此類型
export interface Setting {
  id: string;
  type: string;
  key_name: string;
  value: any;
  createdAt: Date;
  updatedAt: Date;
  createdBy?: string;
  updatedBy?: string;
}

// 引入 DTO
import { UpdateGeneralSettingsDto } from './dto/update-general-settings.dto'
import { UpdateSecuritySettingsDto } from './dto/update-security-settings.dto'
import { UpdateLineSettingsDto } from './dto/update-line-settings.dto'
import { UpdateUserSettingsDto } from './dto/update-user-settings.dto'
import { UpdateTenantSettingsDto } from './dto/update-tenant-settings.dto'
import { UpdateEmailSettingsDto } from './dto/update-email-settings.dto'
import { UpdateStorageSettingsDto } from './dto/update-storage-settings.dto'
import { UpdateBillingSettingsDto } from './dto/update-billing-settings.dto'
import { UpdateAppearanceSettingsDto } from './dto/update-appearance-settings.dto'
import { UpdateLegalSettingsDto } from './dto/update-legal-settings.dto'

// 引入常數和接口
import { SettingCategory } from './constants/setting-categories.enum'
import {
  SettingItem,
  SettingQueryParams,
  SettingUpdateParams,
  CategoryUpdateParams,
  SettingsResponse
} from './interfaces/settings.interface'

/**
 * 郵件設定介面
 */
export interface EmailSettings {
  provider: string
  smtpHost: string
  smtpPort: number
  smtpUser: string
  smtpPassword: string
  smtpSecure: boolean
  sendGridApiKey: string
  isSendGridKeySet: boolean
  fromEmailAddress: string
  fromName: string
  sendGridTemplates?: Array<{ id: string; name: string; }>
  selectedTemplateId?: string;
  dynamicTemplateData?: Record<string, any>;
}

/**
 * 設定服務，提供細粒度的設定管理功能
 */
@Injectable()
export class SettingsService {
  private readonly logger = new Logger(SettingsService.name);
  // 定義需加密的欄位對應到各設定類別
  private sensitiveKeysMap: Record<string, string[]> = {
    [SettingCategory.AI]: ['openaiApiKey', 'claudeApiKey'],
    [SettingCategory.LINE]: ['channelSecret', 'botSecret', 'botToken'],
    [SettingCategory.EMAIL]: ['smtpPassword', 'sendGridApiKey'],
    [SettingCategory.STORAGE]: ['s3AccessKey'],
  }

  constructor(
    private readonly prisma: PrismaService,
    private readonly eventEmitter: EventEmitter2,
    private readonly configService: ConfigService,
    private readonly mailService: MailService,
    private readonly encryptionService: EncryptionService
  ) { }

  //=================================================================
  // 通用設定方法 - 這些方法適用於所有類型的設定
  //=================================================================

  /**
   * 讀取單一設定
   * @param params 查詢參數，包含類別和鍵
   * @returns 設定值或 null
   */
  async getSetting(params: SettingQueryParams): Promise<any> {
    const { category, key } = params;

    try {
      // 嘗試從數據庫讀取設定
      const result = await this.prisma.settings.findFirst({
        where: {
          type: category,
          name: key
        }
      });

      if (result) {
        return result.value;
      }

      // 如果設定不存在，返回 null
      return null;
    } catch (error) {
      this.logger.error(`讀取設定失敗: ${error.message}`, error.stack);
      throw new BadRequestException(`讀取設定失敗: ${error.message}`);
    }
  }

  /**
   * 更新單一設定
   * @param params 更新參數，包含類別、鍵和值
   * @returns 更新後的設定
   */
  async updateSetting(params: SettingUpdateParams): Promise<Setting> {
    const { category, key, value, userId } = params;
    // 判斷是否為敏感欄位，需要加密存儲
    const keys = this.sensitiveKeysMap[category] || []
    let storeValue: any = value
    if (keys.includes(key) && typeof value === 'string') {
      storeValue = this.encryptionService.encrypt(value)
    }

    try {
      // 檢查設定是否存在
      const existingSetting = await this.prisma.settings.findFirst({
        where: {
          type: category,
          name: key
        }
      });

      // 如果設定已存在，則更新
      if (existingSetting) {
        const updated = await this.prisma.settings.update({
          where: {
            id: existingSetting.id
          },
          data: {
            value: storeValue,
            updatedAt: new Date(),
            updatedBy: userId || null
          }
        });

        // 發射設定更新事件
        this.eventEmitter.emit('setting.updated', {
          category,
          key,
          value,
          userId
        });

        return {
          id: updated.id,
          type: updated.type,
          key_name: updated.name,
          value: updated.value,
          createdAt: updated.createdAt,
          updatedAt: updated.updatedAt,
          createdBy: updated.createdBy ?? undefined,
          updatedBy: updated.updatedBy ?? undefined
        };
      }

      // 如果設定不存在，則建立
      const created = await this.prisma.settings.create({
        data: {
          id: `setting_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          type: category,
          name: key,
          value: storeValue ?? null, // 確保 value 欄位有值，即使是 null
          createdAt: new Date(),
          updatedAt: new Date(),
          createdBy: userId || null,
          updatedBy: userId || null
        }
      });

      // 發射設定建立事件
      this.eventEmitter.emit('setting.created', {
        category,
        key,
        value,
        userId
      });

      return {
        id: created.id,
        type: created.type,
        key_name: created.name,
        value: created.value,
        createdAt: created.createdAt,
        updatedAt: created.updatedAt,
        createdBy: created.createdBy ?? undefined,
        updatedBy: created.updatedBy ?? undefined
      };
    } catch (error) {
      this.logger.error(`更新設定失敗: ${error.message}`, error.stack);
      throw new BadRequestException(`更新設定失敗: ${error.message}`);
    }
  }

  /**
   * 讀取類別下的所有設定
   * @param category 設定類別
   * @returns 該類別下的所有設定組成的物件
   */
  async getCategorySettings(category: string): Promise<Record<string, any>> {
    try {
      // 從數據庫讀取該類別的所有設定
      const settings = await this.prisma.settings.findMany({
        where: {
          type: category
        }
      });

      // 將結果轉換為 key-value 對象
      const result: Record<string, any> = {};
      for (const setting of settings) {
        let val = setting.value
        // 若為敏感欄位，嘗試解密
        const keys = this.sensitiveKeysMap[category] || []
        if (keys.includes(setting.name) && typeof setting.value === 'string') {
          try {
            val = this.encryptionService.decrypt(setting.value)
          } catch {
            // 解密失敗，保留原始值
            val = setting.value
          }
        }
        result[setting.name] = val
      }

      return result;
    } catch (error) {
      this.logger.error(`讀取類別設定失敗: ${error.message}`, error.stack);
      throw new BadRequestException(`讀取類別設定失敗: ${error.message}`);
    }
  }

  /**
   * 更新某個分類的設定
   * @param params.category 分類
   * @param params.data 更新資料
   * @param params.userId 使用者 ID
   */
  async updateCategorySettings(params: CategoryUpdateParams): Promise<Record<string, any>> {
    const { category, data, userId } = params;
    const operations: Promise<any>[] = [];
    
    try {
      // 對每個要更新的設定進行操作
      for (const [key, value] of Object.entries(data)) {
        operations.push(
          this.updateSetting({
            category,
            key,
            value,
            userId
          })
        );
      }
      
      // 執行所有設定更新操作
      await Promise.all(operations);
      
      // 返回更新後的設定
      return this.getCategorySettings(category);
    } catch (error) {
      this.logger.error(`更新分類 ${category} 設定失敗: ${error.message}`);
      throw new BadRequestException(`更新設定失敗: ${error.message}`);
    }
  }

  /**
   * 刪除設定
   * @param type 設定類別
   * @param key 設定鍵
   */
  async deleteSetting(type: string, key: string): Promise<void> {
    try {
      // 檢查設定是否存在
      const existingSetting = await this.prisma.settings.findFirst({
        where: {
          type,
          name: key
        }
      });

      if (!existingSetting) {
        throw new NotFoundException(`設定 ${type}.${key} 不存在`);
      }

      // 刪除設定
      await this.prisma.settings.delete({
        where: {
          id: existingSetting.id
        }
      });

      // 發射設定刪除事件
      this.eventEmitter.emit('setting.deleted', {
        category: type,
        key
      });
    } catch (error) {
      this.logger.error(`刪除設定失敗: ${error.message}`, error.stack);
      throw new BadRequestException(`刪除設定失敗: ${error.message}`);
    }
  }

  /**
   * 刪除設定（保留向後兼容性）
   */
  async delete(id: string): Promise<{ success: boolean }> {
    try {
      const setting = await this.prisma.settings.findUnique({
        where: { id }
      });

      if (!setting) {
        throw new NotFoundException(`設定ID ${id} 不存在`);
      }

      await this.prisma.settings.delete({
        where: { id }
      });

      // 發射設定刪除事件
      this.eventEmitter.emit('setting.deleted', {
        id,
        category: setting.type,
        key: setting.name
      });

      return { success: true };
    } catch (error) {
      this.logger.error(`刪除設定失敗: ${error.message}`, error.stack);
      throw new BadRequestException(`刪除設定失敗: ${error.message}`);
    }
  }

  //=================================================================
  // 一般設定 (General Settings)
  //=================================================================

  /**
   * 讀取一般設定
   */
  async getGeneralSettings(): Promise<Record<string, any>> {
    return this.getCategorySettings(SettingCategory.GENERAL);
  }

  /**
   * 更新一般設定
   */
  async updateGeneralSettings(data: Record<string, any>, userId?: string): Promise<Record<string, any>> {
    return this.updateCategorySettings({
      category: SettingCategory.GENERAL,
      data,
      userId
    });
  }

  //=================================================================
  // 安全設定 (Security Settings)
  //=================================================================

  /**
   * 讀取安全設定
   */
  async getSecuritySettings(): Promise<Record<string, any>> {
    return this.getCategorySettings(SettingCategory.SECURITY);
  }

  /**
   * 更新安全設定
   */
  async updateSecuritySettings(data: Record<string, any>, userId?: string): Promise<Record<string, any>> {
    return this.updateCategorySettings({
      category: SettingCategory.SECURITY,
      data,
      userId
    });
  }

  //=================================================================
  // LINE 設定 (LINE Settings)
  //=================================================================

  /**
   * 讀取 Line 設定
   */
  async getLineSettings(): Promise<Record<string, any>> {
    const settings = await this.getCategorySettings(SettingCategory.LINE);
    // 處理特殊狀態標記
    return {
      ...settings,
      isLineLoginSecretSet: !!settings.lineLoginSecret,
      isLineBotSecretSet: !!settings.lineBotSecret,
      isLineBotTokenSet: !!settings.lineBotToken
    };
  }

  /**
   * 更新 Line 設定
   */
  async updateLineSettings(dto: UpdateLineSettingsDto, userId?: string): Promise<Record<string, any>> {
    return this.updateCategorySettings({
      category: SettingCategory.LINE,
      data: dto,
      userId
    });
  }

  //=================================================================
  // 使用者設定 (User Settings)
  //=================================================================

  /**
   * 讀取系統設定
   */
  async getSystemSettings(): Promise<Record<string, any>> {
    return this.getCategorySettings(SettingCategory.SYSTEM);
  }
  
  /**
   * 讀取使用者設定
   */
  async getUserSettings(): Promise<Record<string, any>> {
    return this.getCategorySettings(SettingCategory.USER);
  }
  
  /**
   * 讀取Email設定
   */
  async getEmailSettings(): Promise<EmailSettings> {
    const settings = await this.getCategorySettings(SettingCategory.EMAIL);

    // 定義郵件設定的預設值
    const defaultSettings: EmailSettings = {
      provider: 'smtp',
      smtpHost: '',
      smtpPort: 587,
      smtpUser: '',
      smtpPassword: '',
      smtpSecure: false,
      sendGridApiKey: '',
      isSendGridKeySet: false,
      fromEmailAddress: '',
      fromName: '',
      sendGridTemplates: [],
      selectedTemplateId: '',
      dynamicTemplateData: {}
    };

    // 使用默認值填充缺失的設定
    return {
      provider: settings.provider || defaultSettings.provider,
      smtpHost: settings.smtpHost || defaultSettings.smtpHost,
      smtpPort: settings.smtpPort || defaultSettings.smtpPort,
      smtpUser: settings.smtpUser || defaultSettings.smtpUser,
      smtpPassword: settings.smtpPassword || defaultSettings.smtpPassword,
      smtpSecure: settings.smtpSecure ?? defaultSettings.smtpSecure,
      sendGridApiKey: settings.sendGridApiKey || defaultSettings.sendGridApiKey,
      isSendGridKeySet: !!settings.sendGridApiKey,
      fromEmailAddress: settings.fromEmailAddress || defaultSettings.fromEmailAddress,
      fromName: settings.fromName || defaultSettings.fromName,
      sendGridTemplates: settings.sendGridTemplates || defaultSettings.sendGridTemplates,
      selectedTemplateId: settings.selectedTemplateId || defaultSettings.selectedTemplateId,
      dynamicTemplateData: settings.dynamicTemplateData || defaultSettings.dynamicTemplateData
    };
  }
  
  /**
   * 讀取租戶設定
   */
  async getTenantSettings(): Promise<Record<string, any>> {
    return this.getCategorySettings(SettingCategory.TENANT);
  }

  /**
   * 更新租戶設定
   */
  async updateTenantSettings(dto: UpdateTenantSettingsDto, userId?: string): Promise<Record<string, any>> {
    return this.updateCategorySettings({
      category: SettingCategory.TENANT,
      data: dto,
      userId
    });
  }

  //=================================================================
  // 郵件設定 (Email Settings)
  //=================================================================

  /**
   * 更新郵件設定
   */
  async updateEmailSettings(dto: UpdateEmailSettingsDto, userId?: string): Promise<Record<string, any>> {
    return this.updateCategorySettings({
      category: SettingCategory.EMAIL,
      data: dto,
      userId
    });
  }

  /**
   * 測試郵件設定
   */
  async testEmailSetting(emailSettings: Record<string, any>): Promise<boolean> {
    try {
      this.logger.debug(`測試郵件設定: ${JSON.stringify(emailSettings, null, 2)}`);
      
      // 過濾無關的參數，只提取必要資訊
      const cleanedSettings: Record<string, any> = {
        provider: emailSettings.provider,
        fromEmailAddress: emailSettings.fromEmailAddress,
        fromName: emailSettings.fromName,
        to: emailSettings.to?.trim() || emailSettings.fromEmailAddress
      };
      
      if (emailSettings.provider === 'sendgrid') {
        // SendGrid 測試
        cleanedSettings.sendGridApiKey = emailSettings.sendGridApiKey?.trim();
        cleanedSettings.fromEmailAddress = emailSettings.fromEmailAddress?.trim();
        cleanedSettings.fromName = emailSettings.fromName?.trim();
        cleanedSettings.to = emailSettings.to?.trim();
        
        // 加入基本郵件主旨和內容，作為備用方案
        cleanedSettings.subject = "HorizAI 系統郵件測試";
        cleanedSettings.content = "<h1>這是一封測試郵件</h1><p>如果您收到這封郵件，表示郵件設定已成功。</p>";
        
        // 如果有選擇模板，則新增模板相關設定，並確保清理空格
        if (emailSettings.selectedTemplateId && emailSettings.selectedTemplateId.trim() !== '') {
          // 移除模板 ID 中的空格
          let cleanTemplateId = emailSettings.selectedTemplateId.trim();
          
          // 檢查模板 ID 是否需要格式化為標準 GUID 格式
          if (cleanTemplateId.length === 32 && !cleanTemplateId.includes('-')) {
            // 將 32 字元的字串轉換為標準的 UUID 格式
            cleanTemplateId = `${cleanTemplateId.substr(0, 8)}-${cleanTemplateId.substr(8, 4)}-${cleanTemplateId.substr(12, 4)}-${cleanTemplateId.substr(16, 4)}-${cleanTemplateId.substr(20)}`;
            this.logger.debug(`已將模板 ID 格式化為標準 UUID 格式: ${cleanTemplateId}`);
          }
          
          // 驗證模板 ID 格式 (符合 GUID/UUID 格式)
          const guidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
          if (guidRegex.test(cleanTemplateId)) {
            cleanedSettings.selectedTemplateId = cleanTemplateId;
            
            // 確保 dynamicTemplateData 是有效的物件
            if (emailSettings.dynamicTemplateData && typeof emailSettings.dynamicTemplateData === 'object') {
              cleanedSettings.dynamicTemplateData = {
                ...emailSettings.dynamicTemplateData
              };
              
              // 確保模板數據中有主旨，如果沒有則使用預設主旨
              if (!cleanedSettings.dynamicTemplateData.subject) {
                cleanedSettings.dynamicTemplateData.subject = cleanedSettings.subject;
              }
              
              // 紀錄完整的動態模板數據用於除錯
              this.logger.debug(`完整的動態模板數據: ${JSON.stringify(cleanedSettings.dynamicTemplateData, null, 2)}`);
            } else {
              // 如果沒有提供動態模板數據，則建立基本的模板數據
              cleanedSettings.dynamicTemplateData = {
                subject: cleanedSettings.subject,
                name: "測試使用者"
              };
              this.logger.debug(`使用基本的動態模板數據: ${JSON.stringify(cleanedSettings.dynamicTemplateData, null, 2)}`);
            }
            
            this.logger.debug(`使用 SendGrid 模板進行測試，ID: ${cleanedSettings.selectedTemplateId}`);
          } else {
            // 模板 ID 不符合 GUID 格式，記錄警告並使用備用方案
            this.logger.warn(`模板 ID "${cleanTemplateId}" 不符合 GUID 格式，將不使用模板發送郵件`);
            cleanedSettings.selectedTemplateId = null;
          }
        }
        
        this.logger.debug(`郵件收件人: ${cleanedSettings.to}`);
        this.logger.debug(`寄件人資訊: ${cleanedSettings.fromName} <${cleanedSettings.fromEmailAddress}>`);
        
        // 驗證必要欄位
        if (!cleanedSettings.to) {
          throw new BadRequestException('收件人信箱不能為空');
        }
        if (!cleanedSettings.fromEmailAddress) {
          throw new BadRequestException('寄件人信箱不能為空');
        }
        if (!cleanedSettings.fromName) {
          throw new BadRequestException('寄件人名稱不能為空');
        }
        
        this.logger.debug('準備發送 SendGrid 測試郵件，清理後的設定:', JSON.stringify(cleanedSettings, null, 2));
        return await this.mailService.sendTestEmail(cleanedSettings);
      }
      
      // SMTP 測試
      cleanedSettings.smtpHost = emailSettings.smtpHost;
      cleanedSettings.smtpPort = Number(emailSettings.smtpPort);
      cleanedSettings.smtpSecure = emailSettings.smtpSecure === 'true' || emailSettings.smtpSecure === true;
      cleanedSettings.smtpUser = emailSettings.smtpUser;
      cleanedSettings.smtpPassword = emailSettings.smtpPassword;
      
      this.logger.debug('準備發送 SMTP 測試郵件');
      await this.mailService.sendTestEmail(cleanedSettings);
      return true;
    } catch (error) {
      this.logger.error(`郵件測試失敗: ${error.message}`);
      if (error.stack) {
        this.logger.error(`錯誤堆疊: ${error.stack}`);
      }
      throw new BadRequestException(`郵件測試失敗: ${error.message}`);
    }
  }

  //=================================================================
  // 儲存設定 (Storage Settings)
  //=================================================================

  /**
   * 讀取儲存設定
   */
  async getStorageSettings(): Promise<Record<string, any>> {
    return this.getCategorySettings(SettingCategory.STORAGE);
  }
  
  /**
   * 讀取外觀設定
   */
  async getAppearanceSettings(): Promise<Record<string, any>> {
    return this.getCategorySettings(SettingCategory.APPEARANCE);
  }
  
  /**
   * 讀取法律設定
   */
  async getLegalSettings(): Promise<Record<string, any>> {
    return this.getCategorySettings(SettingCategory.LEGAL);
  }
  
  /**
   * 讀取賬單設定
   */
  async getBillingSettings(): Promise<Record<string, any>> {
    return this.getCategorySettings(SettingCategory.BILLING);
  }

  /**
   * 更新儲存設定
   */
  async updateStorageSettings(dto: UpdateStorageSettingsDto, userId?: string): Promise<Record<string, any>> {
    return this.updateCategorySettings({
      category: SettingCategory.STORAGE,
      data: dto,
      userId
    });
  }
  
  /**
   * 更新外觀設定
   */
  async updateAppearanceSettings(dto: UpdateAppearanceSettingsDto, userId?: string): Promise<Record<string, any>> {
    return this.updateCategorySettings({
      category: SettingCategory.APPEARANCE,
      data: dto,
      userId
    });
  }
  
  /**
   * 更新法律設定
   */
  async updateLegalSettings(dto: UpdateLegalSettingsDto, userId?: string): Promise<Record<string, any>> {
    return this.updateCategorySettings({
      category: SettingCategory.LEGAL,
      data: dto,
      userId
    });
  }
  
  /**
   * 更新賬單設定
   */
  async updateBillingSettings(dto: UpdateBillingSettingsDto, userId?: string): Promise<Record<string, any>> {
    return this.updateCategorySettings({
      category: SettingCategory.BILLING,
      data: dto,
      userId
    });
  }

  //=================================================================
  // 向後兼容方法 (Backward Compatibility Methods)
  //=================================================================

  /**
   * 建立設定（向後兼容）
   */
  async create(dto: any, userId?: string): Promise<Setting> {
    return this.updateSetting({
      category: dto.type,
      key: dto.key,
      value: dto.value,
      userId
    });
  }

  /**
   * 根據類型查詢設定（向後兼容）
   */
  async findByType(type: string): Promise<Record<string, any>> {
    return this.getCategorySettings(type);
  }

  /**
   * 更新使用者設定
   */
  async updateUserSettings(dto: UpdateUserSettingsDto, userId?: string): Promise<Record<string, any>> {
    return this.updateCategorySettings({
      category: SettingCategory.USER,
      data: dto,
      userId
    });
  }
}