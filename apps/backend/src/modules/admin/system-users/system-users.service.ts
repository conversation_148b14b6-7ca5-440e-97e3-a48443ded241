import {
  Injectable,
  NotFoundException,
  ConflictException,
  BadRequestException,
  Logger,
} from "@nestjs/common";
import { PrismaService } from "../../core/prisma/prisma.service";
import * as bcrypt from "bcryptjs";
import { CreateSystemUserDto } from "./dto/create-system-user.dto";
import { UpdateSystemUserDto } from "./dto/update-system-user.dto";
import {
  ISystemUser,
  ISystemUserProfile,
} from "../../../types/models/system-user.model";
import { CaslAbilityFactory } from "../../../casl/casl-ability.factory";
import { Actions, Subjects } from "@horizai/permissions";
import { SystemUserRole } from "@prisma/client";
import { SystemLogService } from "../../../common/services/system-log.service";
import { MailService } from "../../core/mail/mail.service";
import { v4 as uuidv4 } from "uuid";

@Injectable()
export class SystemUsersService {
  private readonly logger = new Logger(SystemUsersService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly abilityFactory: CaslAbilityFactory
  ) {}

  /**
   * 建立系統使用者
   */
  async create(data: CreateSystemUserDto): Promise<ISystemUserProfile> {
    // 檢查是否已存在相同的電子郵件
    const existingUser = await this.prisma.system_users.findUnique({
      where: { email: data.email },
    });

    if (existingUser) {
      this.logger.warn(`嘗試建立使用者時發現重複的電子郵件: ${data.email}`);
      throw new ConflictException("此電子郵件已經被使用");
    }

    try {
      // 準備使用者資料
      const userData = {
        name: data.name,
        email: data.email,
        role: data.role || SystemUserRole.SYSTEM_ADMIN,
        status: data.status || "active",
        phone: data.phone,
        avatar: data.avatar,
      };

      // 如果提供了密碼，則進行雜湊
      let hashedPassword: string;
      if (data.password) {
        hashedPassword = await bcrypt.hash(data.password, 12);
      } else {
        // 生成隨機密碼
        const randomPassword = this.generateRandomPassword();
        hashedPassword = await bcrypt.hash(randomPassword, 12);
      }

      const user = await this.prisma.system_users.create({
        data: {
          ...userData,
          password: hashedPassword,
        },
      });

      this.logger.log(`成功建立系統使用者: ${user.email}`);
      return this.toProfile(user);
    } catch (error) {
      this.logger.error(`建立使用者失敗: ${error.message}`, error.stack);
      throw new BadRequestException("建立使用者失敗: " + error.message);
    }
  }

  /**
   * 獲取所有系統使用者
   */
  async findAll(
    params: {
      skip?: number;
      take?: number;
      where?: any;
      orderBy?: any;
    } = {}
  ): Promise<ISystemUserProfile[]> {
    const { skip, take, where, orderBy } = params;

    try {
      const users = await this.prisma.system_users.findMany({
        skip,
        take,
        where,
        orderBy: orderBy || { createdAt: "desc" },
      });

      return users.map((user) => this.toProfile(user));
    } catch (error) {
      this.logger.error(`讀取使用者列表失敗: ${error.message}`, error.stack);
      throw new BadRequestException("讀取使用者列表失敗");
    }
  }

  /**
   * 根據 ID 獲取系統使用者
   */
  async findOne(id: string): Promise<ISystemUserProfile> {
    try {
      const user = await this.prisma.system_users.findUnique({
        where: { id },
      });

      if (!user) {
        throw new NotFoundException(`使用者 ${id} 不存在`);
      }

      return this.toProfile(user);
    } catch (error) {
      this.logger.error(`查詢使用者失敗: ${error.message}`, error.stack);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(`查詢使用者失敗: ${error.message}`);
    }
  }

  /**
   * 更新系統使用者
   */
  async update(
    id: string,
    data: UpdateSystemUserDto
  ): Promise<ISystemUserProfile> {
    try {
      // 確認使用者存在
      await this.findOne(id);

      // 如果更新 email，檢查是否與其他使用者衝突
      if (data.email) {
        const emailExists = await this.prisma.system_users.findFirst({
          where: {
            email: data.email,
            id: { not: id },
          },
        });
        if (emailExists) {
          throw new ConflictException("此電子郵件已經被使用");
        }
      }

      const updatedUser = await this.prisma.system_users.update({
        where: { id },
        data,
      });

      this.logger.log(`成功更新系統使用者: ${updatedUser.email}`);
      return this.toProfile(updatedUser);
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof ConflictException
      ) {
        throw error;
      }
      this.logger.error(`更新使用者失敗: ${error.message}`, error.stack);
      throw new BadRequestException("更新使用者失敗: " + error.message);
    }
  }

  /**
   * 刪除系統使用者
   */
  async remove(id: string): Promise<ISystemUserProfile> {
    try {
      // 確認使用者存在
      const user = await this.findOne(id);

      // 不允許刪除超級管理員
      if (user.role === SystemUserRole.SUPER_ADMIN) {
        throw new BadRequestException("不允許刪除超級管理員帳號");
      }

      const deletedUser = await this.prisma.system_users.delete({
        where: { id },
      });

      this.logger.log(`成功刪除系統使用者: ${deletedUser.email}`);
      return this.toProfile(deletedUser);
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      this.logger.error(`刪除使用者失敗: ${error.message}`, error.stack);
      throw new BadRequestException("刪除使用者失敗: " + error.message);
    }
  }

  /**
   * 重置使用者密碼
   */
  async resetPassword(id: string): Promise<{ temporaryPassword: string }> {
    try {
      // 確認使用者存在
      await this.findOne(id);

      // 生成臨時密碼
      const temporaryPassword = this.generateRandomPassword();
      const hashedPassword = await bcrypt.hash(temporaryPassword, 12);

      await this.prisma.system_users.update({
        where: { id },
        data: {
          password: hashedPassword,
          passwordLastChangedAt: new Date(),
        },
      });

      this.logger.log(`成功重置系統使用者密碼: ${id}`);
      return { temporaryPassword };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`重置密碼失敗: ${error.message}`, error.stack);
      throw new BadRequestException("重置密碼失敗: " + error.message);
    }
  }

  /**
   * 更新使用者狀態
   */
  async updateStatus(
    id: string,
    status: "active" | "inactive"
  ): Promise<ISystemUserProfile> {
    try {
      const user = await this.findOne(id);

      // 不允許停用超級管理員
      if (user.role === SystemUserRole.SUPER_ADMIN && status === "inactive") {
        throw new BadRequestException("不允許停用超級管理員帳號");
      }

      const updatedUser = await this.prisma.system_users.update({
        where: { id },
        data: { status },
      });

      this.logger.log(
        `成功更新系統使用者狀態: ${updatedUser.email} -> ${status}`
      );
      return this.toProfile(updatedUser);
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      this.logger.error(`更新狀態失敗: ${error.message}`, error.stack);
      throw new BadRequestException("更新狀態失敗: " + error.message);
    }
  }

  /**
   * 批量操作
   */
  async batchOperation(
    ids: string[],
    operation: "activate" | "deactivate" | "delete"
  ): Promise<void> {
    try {
      // 篩選出非超級管理員的使用者ID
      const users = await this.prisma.system_users.findMany({
        where: { id: { in: ids } },
        select: { id: true, role: true },
      });

      // 排除超級管理員ID
      const filteredIds = users
        .filter((user) => user.role !== SystemUserRole.SUPER_ADMIN)
        .map((user) => user.id);

      if (filteredIds.length === 0) {
        return;
      }

      switch (operation) {
        case "activate":
          await this.prisma.system_users.updateMany({
            where: { id: { in: filteredIds } },
            data: { status: "active" },
          });
          break;
        case "deactivate":
          await this.prisma.system_users.updateMany({
            where: { id: { in: filteredIds } },
            data: { status: "inactive" },
          });
          break;
        case "delete":
          await this.prisma.system_users.deleteMany({
            where: { id: { in: filteredIds } },
          });
          break;
        default:
          throw new BadRequestException(`不支援的操作: ${operation}`);
      }

      this.logger.log(
        `成功執行批量操作: ${operation}，影響 ${filteredIds.length} 個使用者`
      );
    } catch (error) {
      this.logger.error(`批次操作失敗: ${error.message}`, error.stack);
      throw new BadRequestException("批次操作失敗: " + error.message);
    }
  }

  /**
   * 檢查系統管理權限
   */
  async checkSystemPermission(
    userId: string,
    action: string,
    subject: string
  ): Promise<boolean> {
    try {
      const user = await this.prisma.system_users.findUnique({
        where: { id: userId },
      });

      if (!user) {
        return false;
      }

      // 系統使用者的權限檢查邏輯
      const ability = await this.abilityFactory.createForUser(user);
      return ability.can(action as any, subject as any);
    } catch (error) {
      this.logger.error(`權限檢查失敗: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * 獲取系統角色列表
   */
  async getSystemRoles(): Promise<
    Array<{ role: string; displayName: string; description: string }>
  > {
    return [
      {
        role: SystemUserRole.SUPER_ADMIN,
        displayName: "超級管理員",
        description: "擁有系統的完整控制權限，包括管理其他管理員",
      },
      {
        role: SystemUserRole.SYSTEM_ADMIN,
        displayName: "系統管理員",
        description: "負責系統運營和租戶管理",
      },
    ];
  }

  /**
   * 生成隨機密碼
   */
  private generateRandomPassword(): string {
    const chars =
      "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*";
    let result = "";
    for (let i = 0; i < 12; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  /**
   * 轉換為 Profile 格式（移除敏感資訊）
   */
  private toProfile(user: ISystemUser): ISystemUserProfile {
    const { password, mfaSecret, ...profile } = user;
    return profile;
  }
}
