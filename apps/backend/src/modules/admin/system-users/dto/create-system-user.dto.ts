import { IsEmail, IsString, IsOptional, IsEnum } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { SystemUserRole } from '@prisma/client';

export class CreateSystemUserDto {
  @ApiProperty({ description: '系統使用者名稱' })
  @IsString()
  name: string;

  @ApiProperty({ description: '系統使用者電子郵件' })
  @IsEmail()
  email: string;

  @ApiProperty({ description: '系統使用者密碼', required: false })
  @IsString()
  @IsOptional()
  password?: string;

  @ApiProperty({ 
    description: '系統使用者角色', 
    enum: SystemUserRole, 
    required: false,
    default: SystemUserRole.SYSTEM_ADMIN
  })
  @IsEnum(SystemUserRole)
  @IsOptional()
  role?: SystemUserRole;

  @ApiProperty({ 
    description: '系統使用者狀態', 
    enum: ['active', 'inactive'], 
    required: false,
    default: 'active'
  })
  @IsString()
  @IsOptional()
  status?: 'active' | 'inactive';

  @ApiProperty({ description: '電話號碼', required: false })
  @IsString()
  @IsOptional()
  phone?: string;

  @ApiProperty({ description: '頭像 URL', required: false })
  @IsString()
  @IsOptional()
  avatar?: string;
}