import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Query, Put, HttpCode, HttpStatus } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../core/auth/guards/auth.guard';
import { Roles } from '../../core/auth/decorators/roles.decorator';
import { Role } from '../../../common/enums/role.enum';
import { SystemUsersService } from './system-users.service';
import { CreateSystemUserDto } from './dto/create-system-user.dto';
import { UpdateSystemUserDto } from './dto/update-system-user.dto';
import { CheckPolicies } from '../../../casl/decorators/check-policies.decorator';
import { PoliciesGuard } from '../../../casl/guards/permission.guard';
import { Actions, Subjects } from '@horizai/permissions';
import { CurrentUser } from '../../core/auth/decorators/current-user.decorator';
import { ISystemUserProfile } from '../../../types/models/system-user.model';

@ApiTags('admin/system-users')
@ApiBearerAuth()
@Controller('admin/system-users')
@UseGuards(JwtAuthGuard, PoliciesGuard)
export class SystemUsersController {
  constructor(private readonly systemUsersService: SystemUsersService) {}

  @Post()
  @ApiOperation({ summary: '建立系統使用者' })
  @ApiResponse({ status: 201, description: '成功建立系統使用者' })
  @CheckPolicies((ability) => ability.can(Actions.CREATE, Subjects.SYSTEM_USER))
  async create(@Body() createSystemUserDto: CreateSystemUserDto) {
    return this.systemUsersService.create(createSystemUserDto);
  }

  @Get()
  @ApiOperation({ summary: '獲取所有系統使用者' })
  @ApiResponse({ status: 200, description: '成功獲取系統使用者列表' })
  @CheckPolicies((ability) => ability.can(Actions.READ, Subjects.SYSTEM_USER))
  async findAll(
    @Query('skip') skip?: string,
    @Query('take') take?: string,
    @Query('search') search?: string,
    @Query('status') status?: string,
  ) {
    const params: any = {};
    
    if (skip) params.skip = parseInt(skip);
    if (take) params.take = parseInt(take);
    
    if (search || status) {
      params.where = {};
      if (search) {
        params.where.OR = [
          { name: { contains: search, mode: 'insensitive' } },
          { email: { contains: search, mode: 'insensitive' } },
        ];
      }
      if (status) {
        params.where.status = status;
      }
    }

    return this.systemUsersService.findAll(params);
  }

  @Get('roles')
  @ApiOperation({ summary: '獲取系統角色列表' })
  @ApiResponse({ status: 200, description: '成功獲取系統角色列表' })
  @CheckPolicies((ability) => ability.can(Actions.READ, Subjects.ROLE))
  async getSystemRoles() {
    return this.systemUsersService.getSystemRoles();
  }

  @Get(':id')
  @ApiOperation({ summary: '根據 ID 獲取系統使用者' })
  @ApiResponse({ status: 200, description: '成功獲取系統使用者' })
  @CheckPolicies((ability) => ability.can(Actions.READ, Subjects.SYSTEM_USER))
  async findOne(@Param('id') id: string) {
    return this.systemUsersService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: '更新系統使用者' })
  @ApiResponse({ status: 200, description: '成功更新系統使用者' })
  @CheckPolicies((ability) => ability.can(Actions.UPDATE, Subjects.SYSTEM_USER))
  async update(@Param('id') id: string, @Body() updateSystemUserDto: UpdateSystemUserDto) {
    return this.systemUsersService.update(id, updateSystemUserDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: '刪除系統使用者' })
  @ApiResponse({ status: 200, description: '成功刪除系統使用者' })
  @CheckPolicies((ability) => ability.can(Actions.DELETE, Subjects.SYSTEM_USER))
  async remove(@Param('id') id: string) {
    return this.systemUsersService.remove(id);
  }

  @Post(':id/reset-password')
  @ApiOperation({ summary: '重置系統使用者密碼' })
  @ApiResponse({ status: 200, description: '成功重置密碼' })
  @CheckPolicies((ability) => ability.can(Actions.UPDATE, Subjects.SYSTEM_USER))
  @HttpCode(HttpStatus.OK)
  async resetPassword(@Param('id') id: string) {
    return this.systemUsersService.resetPassword(id);
  }

  @Put(':id/status')
  @ApiOperation({ summary: '更新系統使用者狀態' })
  @ApiResponse({ status: 200, description: '成功更新使用者狀態' })
  @CheckPolicies((ability) => ability.can(Actions.UPDATE, Subjects.SYSTEM_USER))
  async updateStatus(
    @Param('id') id: string,
    @Body('status') status: 'active' | 'inactive'
  ) {
    return this.systemUsersService.updateStatus(id, status);
  }

  @Post('batch')
  @ApiOperation({ summary: '批量操作系統使用者' })
  @ApiResponse({ status: 200, description: '成功執行批量操作' })
  @CheckPolicies((ability) => ability.can(Actions.MANAGE, Subjects.SYSTEM_USER))
  @HttpCode(HttpStatus.OK)
  async batchOperation(
    @Body('ids') ids: string[],
    @Body('operation') operation: 'activate' | 'deactivate' | 'delete'
  ) {
    return this.systemUsersService.batchOperation(ids, operation);
  }

  @Get(':id/permissions/:action/:subject')
  @ApiOperation({ summary: '檢查系統使用者權限' })
  @ApiResponse({ status: 200, description: '權限檢查結果' })
  @CheckPolicies((ability) => ability.can(Actions.READ, Subjects.SYSTEM_USER))
  async checkPermission(
    @Param('id') id: string,
    @Param('action') action: string,
    @Param('subject') subject: string
  ) {
    const canAccess = await this.systemUsersService.checkSystemPermission(id, action, subject);
    return { canAccess };
  }
}