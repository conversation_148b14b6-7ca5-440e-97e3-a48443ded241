import {
  Injectable,
  Logger,
  NotFoundException,
  UnprocessableEntityException,
} from "@nestjs/common";
import { PrismaService } from "@/modules/core/prisma/prisma.service";
import { line_group_verifications, Prisma } from "@prisma/client";

type LineGroupVerification = line_group_verifications;

@Injectable()
export class GroupVerificationService {
  private readonly logger = new Logger(GroupVerificationService.name);

  constructor(private readonly prisma: PrismaService) {}

  /**
   * Records a group when a bot joins, if not already recorded, or finds existing record.
   * Sets initial state to unverified.
   * @param botId The ID of the LineBot.
   * @param groupId The ID of the LINE group.
   * @param tenantId The tenant ID associated with the bot (optional).
   * @returns The created or found LineGroupVerification record.
   */
  async recordOrFindUnverifiedGroup(
    botId: string,
    groupId: string,
    tenantId?: string | null // Allow null from LineBot model
  ): Promise<LineGroupVerification> {
    this.logger.log(
      `Attempting to record/find group: botId=${botId}, groupId=${groupId}, tenantId=${tenantId}`
    );

    const existingVerification =
      await this.prisma.line_group_verifications.findUnique({
        where: {
          bot_id_group_id: {
            // Assuming a unique constraint on bot_id + group_id
            bot_id: botId,
            group_id: groupId,
          },
        },
      });

    if (existingVerification) {
      this.logger.log(
        `Group verification record already exists for botId=${botId}, groupId=${groupId}. Returning existing.`
      );
      return existingVerification;
    }

    this.logger.log(
      `No existing record found. Creating new LineGroupVerification for botId=${botId}, groupId=${groupId}.`
    );
    const newVerificationData: Prisma.line_group_verificationsCreateInput = {
      id: `verification_${botId}_${groupId}_${Date.now()}`,
      line_bots: { connect: { id: botId } },
      group_id: groupId,
      is_verified: false, // Default to not verified
      updated_at: new Date(),
      // tenant and workspace can be linked later during manual verification by an admin
    };

    if (tenantId) {
      newVerificationData.tenants = { connect: { id: tenantId } };
    }

    // workspace_id, verified_at, verified_by_user_id will be null initially

    try {
      const createdVerification =
        await this.prisma.line_group_verifications.create({
          data: newVerificationData,
        });
      this.logger.log(
        `Successfully created LineGroupVerification with id: ${createdVerification.id}`
      );
      return createdVerification;
    } catch (error) {
      this.logger.error(
        `Error creating LineGroupVerification for botId=${botId}, groupId=${groupId}: ${error.message}`,
        error.stack
      );
      // Consider if re-throwing or returning a specific error/null is better
      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error.code === "P2002"
      ) {
        // Unique constraint violation, implies a race condition if findUnique missed it.
        // Attempt to find it again.
        this.logger.warn(
          "Race condition? Unique constraint violated, attempting to find again."
        );
        const foundAfterError =
          await this.prisma.line_group_verifications.findUnique({
            where: {
              bot_id_group_id: { bot_id: botId, group_id: groupId },
            },
          });
        if (foundAfterError) return foundAfterError;
      }
      throw error; // Re-throw if not a P2002 or if still not found
    }
  }

  /**
   * Finds the verification status for a given bot and group.
   * @param botId The ID of the LineBot.
   * @param groupId The ID of the LINE group.
   * @returns The LineGroupVerification record or null if not found.
   */
  async findVerificationStatus(
    botId: string,
    groupId: string
  ): Promise<LineGroupVerification | null> {
    this.logger.log(
      `Finding verification status for botId=${botId}, groupId=${groupId}`
    );
    return this.prisma.line_group_verifications.findUnique({
      where: {
        bot_id_group_id: {
          bot_id: botId,
          group_id: groupId,
        },
      },
    });
  }

  /**
   * Verifies a group, linking it to a workspace and tenant (if applicable),
   * and recording an audit trail.
   * @param botId The ID of the LineBot.
   * @param groupId The ID of the LINE group.
   * @param workspaceId The ID of the Workspace to associate with this group verification.
   * @param verifiedByUserId The ID of the User who performed the verification.
   * @returns The updated LineGroupVerification record.
   * @throws NotFoundException if the LineBot or its tenant association is not found, or if the verification record doesn't exist.
   * @throws UnprocessableEntityException if the group is already verified.
   */
  async verifyGroup(
    botId: string,
    groupId: string,
    workspaceId: string,
    verifiedByUserId: string
  ): Promise<LineGroupVerification> {
    this.logger.log(
      `Verifying group: botId=${botId}, groupId=${groupId}, workspaceId=${workspaceId}, verifiedBy=${verifiedByUserId}`
    );

    const verification = await this.prisma.line_group_verifications.findUnique({
      where: { bot_id_group_id: { bot_id: botId, group_id: groupId } },
      include: { line_bots: true }, // Include bot to get tenant_id
    });

    if (!verification) {
      this.logger.warn(
        `Verification record not found for botId=${botId}, groupId=${groupId}. Cannot verify.`
      );
      throw new NotFoundException(
        `Verification record not found for bot ${botId} and group ${groupId}.`
      );
    }

    if (verification.is_verified) {
      this.logger.warn(
        `Group ${groupId} for bot ${botId} is already verified.`
      );
      throw new UnprocessableEntityException(
        `Group ${groupId} for bot ${botId} is already verified.`
      );
    }

    if (!verification.line_bots) {
      // Should not happen if foreign keys are set up correctly and bot_id is valid
      this.logger.error(
        `Associated bot not found for verification record ${verification.id}. This indicates a data integrity issue.`
      );
      throw new NotFoundException(
        `Associated bot for verification record ${verification.id} not found.`
      );
    }

    const updateData: Prisma.line_group_verificationsUpdateInput = {
      is_verified: true,
      verified_at: new Date(),
      verified_by_user_id: verifiedByUserId,
      workspaces: { connect: { id: workspaceId } },
    };

    // If the bot has a tenant_id, associate the verification with that tenant.
    if (verification.line_bots.tenant_id) {
      updateData.tenants = {
        connect: { id: verification.line_bots.tenant_id },
      };
    } else {
      // If bot has no tenant_id, but workspace must belong to a tenant, this might be an issue
      // or it's a SYSTEM bot and workspace association is direct.
      // For now, we assume workspace can be linked directly.
      // If workspace must have a tenant, further validation for workspace's tenant is needed.
      this.logger.log(
        `Bot ${botId} does not have a tenant_id. Linking workspace ${workspaceId} directly.`
      );
    }

    try {
      const updatedVerification =
        await this.prisma.line_group_verifications.update({
          where: { id: verification.id },
          data: updateData,
        });
      this.logger.log(
        `Successfully verified group ${groupId} for bot ${botId}. Verification ID: ${updatedVerification.id}`
      );
      return updatedVerification;
    } catch (error) {
      this.logger.error(
        `Error verifying group ${groupId} for bot ${botId}: ${error.message}`,
        error.stack
      );
      // Handle potential errors, e.g, if workspaceId or verifiedByUserId is invalid (FK constraint)
      throw error;
    }
  }

  /**
   * Unverifies a group, removing its workspace association and resetting verification status.
   * @param botId The ID of the LineBot.
   * @param groupId The ID of the LINE group.
   * @returns The updated LineGroupVerification record.
   * @throws NotFoundException if the verification record doesn't exist.
   */
  async unverifyGroup(
    botId: string,
    groupId: string
  ): Promise<LineGroupVerification> {
    this.logger.log(`Unverifying group: botId=${botId}, groupId=${groupId}`);

    const verification = await this.prisma.line_group_verifications.findUnique({
      where: { bot_id_group_id: { bot_id: botId, group_id: groupId } },
    });

    if (!verification) {
      this.logger.warn(
        `Verification record not found for botId=${botId}, groupId=${groupId}. Cannot unverify.`
      );
      throw new NotFoundException(
        `Verification record not found for bot ${botId} and group ${groupId}.`
      );
    }

    if (!verification.is_verified) {
      this.logger.log(
        `Group ${groupId} for bot ${botId} is already not verified.`
      );
      // Optionally, could return the current record or throw a specific exception/return a message
      // For now, we'll proceed to ensure other fields are also reset if needed.
    }

    const updateData: Prisma.line_group_verificationsUpdateInput = {
      is_verified: false,
      verified_at: null,
      verified_by_user_id: null, // Remove user association
      workspaces: { disconnect: true }, // Remove workspace association - 使用正確的關聯名稱
      // tenant association could also be disconnected if it was set during verification
      // and should be reset upon un-verification.
      // However, if tenant is derived from bot, it might not need explicit disconnect here.
      // For now, let's assume tenant linkage persists if bot has a tenant.
    };

    // If you want to also clear tenant_id when unverifying:
    // if (verification.tenant_id) {
    //   updateData.tenant = { disconnect: true };
    // }

    try {
      const updatedVerification =
        await this.prisma.line_group_verifications.update({
          where: { id: verification.id },
          data: updateData,
        });
      this.logger.log(
        `Successfully unverified group ${groupId} for bot ${botId}. Verification ID: ${updatedVerification.id}`
      );
      return updatedVerification;
    } catch (error) {
      this.logger.error(
        `Error unverifying group ${groupId} for bot ${botId}: ${error.message}`,
        error.stack
      );
      throw error;
    }
  }

  /**
   * Resets the verification status of a group, typically when a bot leaves or is removed.
   * This is similar to unverifyGroup but might have slightly different semantics or logging.
   * @param botId The ID of the LineBot.
   * @param groupId The ID of the LINE group.
   * @returns The updated LineGroupVerification record or null if not found or not previously verified.
   */
  async resetVerificationOnBotLeave(
    botId: string,
    groupId: string
  ): Promise<LineGroupVerification | null> {
    this.logger.log(
      `Resetting verification on bot leave: botId=${botId}, groupId=${groupId}`
    );

    const verification = await this.prisma.line_group_verifications.findUnique({
      where: { bot_id_group_id: { bot_id: botId, group_id: groupId } },
    });

    if (!verification) {
      this.logger.warn(
        `Verification record not found for botId=${botId}, groupId=${groupId}. Cannot reset.`
      );
      return null; // Or throw NotFoundException if strictness is required
    }

    // Only proceed if the group was previously verified
    if (!verification.is_verified) {
      this.logger.log(
        `Group ${groupId} for bot ${botId} was not verified. No reset needed.`
      );
      return verification; // Return the existing record as no change is made
    }

    const updateData: Prisma.line_group_verificationsUpdateInput = {
      is_verified: false,
      verified_at: null,
      // We might not want to disconnect verified_by_user and workspace here,
      // as the bot might be re-added, and admin might want to re-verify with same settings.
      // PRD F4.6 says "驗證狀態應自動重置為「未驗證」", doesn't explicitly state to clear associations.
      // However, if re-verification implies choosing workspace again, then disconnecting is correct.
      // For now, let's be conservative and only reset is_verified and verified_at.
      // If full un-association is desired, uncomment below:
      // verified_by_user: { disconnect: true },
      // workspace: { disconnect: true },
    };

    try {
      const updatedVerification =
        await this.prisma.line_group_verifications.update({
          where: { id: verification.id },
          data: updateData,
        });
      this.logger.log(
        `Successfully reset verification for group ${groupId} for bot ${botId} on leave. Verification ID: ${updatedVerification.id}`
      );
      return updatedVerification;
    } catch (error) {
      this.logger.error(
        `Error resetting verification for group ${groupId} for bot ${botId} on leave: ${error.message}`,
        error.stack
      );
      throw error;
    }
  }

  // TODO: Add methods for:
  // - Listing unverified/verified groups for admin UI

  /**
   * Lists group verifications for a bot, with optional filtering by verification status.
   * @param botId The ID of the LineBot.
   * @param options Filter and pagination options.
   * @returns Array of LineGroupVerification records with bot, workspace, and user relations.
   */
  async listVerifications(
    botId: string,
    options?: {
      isVerified?: boolean;
      skip?: number;
      take?: number;
      includeBot?: boolean;
      includeWorkspace?: boolean;
      includeUser?: boolean;
    }
  ): Promise<{
    items: LineGroupVerification[];
    total: number;
  }> {
    this.logger.log(
      `Listing verifications for botId=${botId}, options=${JSON.stringify(options)}`
    );

    const {
      isVerified,
      skip = 0,
      take = 50,
      includeBot = false,
      includeWorkspace = false,
      includeUser = false,
    } = options || {};

    // 建構查詢條件
    const where: Prisma.line_group_verificationsWhereInput = { bot_id: botId };

    // 如果提供了驗證狀態過濾
    if (isVerified !== undefined) {
      where.is_verified = isVerified;
    }

    // 計算總記錄數
    const total = await this.prisma.line_group_verifications.count({
      where,
    });

    // 查詢記錄
    const items = await this.prisma.line_group_verifications.findMany({
      where,
      skip,
      take,
      orderBy: [
        // 優先顯示未驗證的群組
        { is_verified: "asc" },
        // 然後按創建時間排序，最新的在前面
        { created_at: "desc" },
      ],
      include: {
        line_bots: includeBot,
        workspaces: includeWorkspace,
        // Note: verified_by relation doesn't exist in schema, only verified_by_user_id field
      },
    });

    this.logger.log(
      `Found ${items.length} verification records for botId=${botId}`
    );
    return { items, total };
  }
}
