import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
  ForbiddenException,
} from "@nestjs/common";
import { PrismaService } from "@/modules/core/prisma/prisma.service";
import { CreateLineBotDto } from "../dto/create-line-bot.dto";
import { UpdateLineBotDto } from "../dto/update-line-bot.dto";
import {
  line_bots,
  LineBotScope,
  Prisma,
  SystemUserRole,
  TenantUserRole,
} from "@prisma/client";

type LineBot = line_bots;
type UserRole = SystemUserRole | TenantUserRole;
import {
  Client,
  WebhookEvent,
  TextMessage,
  MessageAPIResponseBase,
  TemplateMessage,
  FlexMessage,
  FlexContainer,
  FlexBubble,
  MessageEvent,
  Message,
} from "@line/bot-sdk";
import { ConfigService } from "@nestjs/config";
import { EncryptionService } from "@/modules/core/encryption/encryption.service";
import { GroupVerificationService } from "./group-verification.service";
import { MessageLogService } from "./message-log.service";
import { Role as RoleEnum } from "@/common/enums/role.enum";

interface UserContext {
  role: UserRole;
  tenantId?: string | null;
}

@Injectable()
export class LineBotService {
  private readonly logger = new Logger(LineBotService.name);
  private readonly WEBHOOK_PATH_PATTERN = "/api/admin/line-bots/callback";

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
    private readonly encryptionService: EncryptionService,
    private readonly groupVerificationService: GroupVerificationService,
    private readonly messageLogService: MessageLogService
  ) {}

  private generateFullWebhookUrl(path: string): string {
    const apiBaseUrl = this.configService.get<string>(
      "API_BASE_URL",
      "http://localhost:4000"
    );
    return `${apiBaseUrl}${path}`;
  }

  private generateWebhookPath(botId: string): string {
    return `${this.WEBHOOK_PATH_PATTERN}/${botId}`;
  }

  private transformBotResponse(bot: LineBot | null): LineBot | null {
    if (!bot || !bot.webhook_url) {
      return bot;
    }
    return {
      ...bot,
      webhook_url: this.generateFullWebhookUrl(bot.webhook_url),
    };
  }

  private transformBotListResponse(bots: LineBot[]): LineBot[] {
    return bots
      .map((bot) => this.transformBotResponse(bot))
      .filter((bot): bot is LineBot => bot !== null);
  }

  async create(
    createLineBotDto: CreateLineBotDto,
    userContext: UserContext
  ): Promise<LineBot> {
    const {
      bot_secret,
      bot_token,
      token_update_reminder_period_days,
      tenant_id: dtoTenantId,
      scope: dtoScope,
      ...restData
    } = createLineBotDto;

    const data: Prisma.line_botsCreateInput = {
      id: `linebot_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      name: restData.name,
      description: restData.description,
      is_enabled:
        restData.is_enabled !== undefined ? restData.is_enabled : true,
      webhook_url: restData.webhook_url,
      token_update_reminder_period_days: token_update_reminder_period_days,
      scope: LineBotScope.SYSTEM,
      updated_at: new Date(),
    };

    if (userContext.role === TenantUserRole.TENANT_ADMIN) {
      if (!userContext.tenantId) {
        throw new ForbiddenException(
          "Tenant admin must be associated with a tenant."
        );
      }
      data.scope = LineBotScope.TENANT;
      data.tenants = { connect: { id: userContext.tenantId } };
    } else if (userContext.role === SystemUserRole.SYSTEM_ADMIN) {
      const targetScope = dtoScope || LineBotScope.SYSTEM;
      data.scope = targetScope;
      if (targetScope === LineBotScope.TENANT) {
        if (!dtoTenantId) {
          throw new BadRequestException(
            "tenant_id is required when creating a TENANT scope bot as SYSTEM_ADMIN."
          );
        }
        const tenantExists = await this.prisma.tenants.findUnique({
          where: { id: dtoTenantId },
        });
        if (!tenantExists) {
          throw new NotFoundException(
            `Tenant with ID ${dtoTenantId} not found.`
          );
        }
        data.tenants = { connect: { id: dtoTenantId } };
      } else {
      }
    } else if (userContext.role === SystemUserRole.SUPER_ADMIN) {
      const targetScope = dtoScope || LineBotScope.SYSTEM;
      data.scope = targetScope;
      if (targetScope === LineBotScope.TENANT) {
        if (!dtoTenantId) {
          throw new BadRequestException(
            "tenant_id is required for TENANT scope bot."
          );
        }
        const tenantExists = await this.prisma.tenants.findUnique({
          where: { id: dtoTenantId },
        });
        if (!tenantExists) {
          throw new NotFoundException(
            `Tenant with ID ${dtoTenantId} not found.`
          );
        }
        data.tenants = { connect: { id: dtoTenantId } };
      }
    } else {
      throw new ForbiddenException(
        `User with role ${userContext.role} cannot create LineBots.`
      );
    }

    if (bot_secret) {
      data.bot_secret = this.encryptionService.encrypt(bot_secret);
    }
    if (bot_token) {
      data.bot_token = this.encryptionService.encrypt(bot_token);
      data.token_last_updated_at = new Date();
    }

    const lineBot = await this.prisma.line_bots.create({ data });
    this.logger.log(
      `LineBot created: ${lineBot.id} with scope ${lineBot.scope} for tenant: ${lineBot.tenant_id || "SYSTEM"}`
    );
    return this.transformBotResponse(lineBot) as LineBot;
  }

  async update(
    id: string,
    updateLineBotDto: UpdateLineBotDto,
    userContext: UserContext
  ): Promise<LineBot> {
    const existingBot = await this.prisma.line_bots.findUnique({
      where: { id },
    });
    if (!existingBot) {
      throw new NotFoundException(`LineBot ${id} not found.`);
    }

    if (userContext.role === TenantUserRole.TENANT_ADMIN) {
      if (existingBot.tenant_id !== userContext.tenantId) {
        throw new ForbiddenException(
          "Tenant admin can only update their own tenant's LineBots."
        );
      }
      if (
        updateLineBotDto.scope &&
        updateLineBotDto.scope !== LineBotScope.TENANT
      ) {
        throw new ForbiddenException(
          "Tenant admins cannot change the scope of a LineBot."
        );
      }
      if (
        updateLineBotDto.tenant_id &&
        updateLineBotDto.tenant_id !== userContext.tenantId
      ) {
        throw new ForbiddenException(
          "Tenant admins cannot change the tenant_id of a LineBot."
        );
      }
      delete updateLineBotDto.scope;
      delete updateLineBotDto.tenant_id;
    }

    const data: Prisma.line_botsUpdateInput = {};
    let hasChanges = false;

    const {
      name,
      description,
      scope,
      tenant_id: dto_tenant_id,
      webhook_url,
      is_enabled,
      bot_secret,
      bot_token,
      token_update_reminder_period_days,
    } = updateLineBotDto;

    if (name !== undefined && name !== existingBot.name) {
      data.name = name;
      hasChanges = true;
    }
    if (description !== undefined && description !== existingBot.description) {
      data.description = description;
      hasChanges = true;
    }

    if (
      userContext.role === "SYSTEM_ADMIN" ||
      userContext.role === "SUPER_ADMIN"
    ) {
      if (scope !== undefined && scope !== existingBot.scope) {
        data.scope = scope;
        if (scope === LineBotScope.TENANT) {
          if (!dto_tenant_id)
            throw new BadRequestException(
              "tenant_id is required when changing scope to TENANT."
            );
          const tenantExists = await this.prisma.tenants.findUnique({
            where: { id: dto_tenant_id },
          });
          if (!tenantExists)
            throw new NotFoundException(
              `Tenant with ID ${dto_tenant_id} not found.`
            );
          data.tenants = { connect: { id: dto_tenant_id } };
        } else if (scope === LineBotScope.SYSTEM) {
          data.tenants = { disconnect: true };
        }
        hasChanges = true;
      } else if (
        dto_tenant_id !== undefined &&
        existingBot.scope === LineBotScope.TENANT &&
        dto_tenant_id !== existingBot.tenant_id
      ) {
        const tenantExists = await this.prisma.tenants.findUnique({
          where: { id: dto_tenant_id },
        });
        if (!tenantExists)
          throw new NotFoundException(
            `Tenant with ID ${dto_tenant_id} not found.`
          );
        data.tenants = { connect: { id: dto_tenant_id } };
        hasChanges = true;
      }
    }

    if (webhook_url !== undefined && webhook_url !== existingBot.webhook_url) {
      data.webhook_url = webhook_url;
      hasChanges = true;
    }
    if (is_enabled !== undefined && is_enabled !== existingBot.is_enabled) {
      data.is_enabled = is_enabled;
      hasChanges = true;
    }

    if (bot_secret) {
      const encryptedNewSecret = this.encryptionService.encrypt(bot_secret);
      if (existingBot.bot_secret !== encryptedNewSecret) {
        data.bot_secret = encryptedNewSecret;
        hasChanges = true;
      }
    }

    if (updateLineBotDto.hasOwnProperty("bot_token")) {
      if (bot_token === null) {
        if (existingBot.bot_token !== null) {
          data.bot_token = null;
          data.token_last_updated_at = null;
          hasChanges = true;
        }
      } else if (bot_token) {
        const encryptedNewToken = this.encryptionService.encrypt(bot_token);
        if (existingBot.bot_token !== encryptedNewToken) {
          data.bot_token = encryptedNewToken;
          data.token_last_updated_at = new Date();
          hasChanges = true;
        }
      }
    }

    if (updateLineBotDto.hasOwnProperty("token_update_reminder_period_days")) {
      if (token_update_reminder_period_days === null) {
        if (existingBot.token_update_reminder_period_days !== null) {
          data.token_update_reminder_period_days = null;
          hasChanges = true;
        }
      } else if (
        token_update_reminder_period_days !==
        existingBot.token_update_reminder_period_days
      ) {
        data.token_update_reminder_period_days =
          token_update_reminder_period_days;
        hasChanges = true;
      }
    }

    if (!hasChanges) {
      this.logger.log(
        `LineBot update for ID: ${id} called with no new data to update.`
      );
      return this.transformBotResponse(existingBot) as LineBot;
    }

    const updatedLineBot = await this.prisma.line_bots.update({
      where: { id },
      data,
    });
    this.logger.log(`LineBot updated: ${updatedLineBot.id}`);
    return this.transformBotResponse(updatedLineBot) as LineBot;
  }

  async delete(id: string, userContext: UserContext): Promise<LineBot> {
    const existingBot = await this.prisma.line_bots.findUnique({
      where: { id },
    });
    if (!existingBot) {
      throw new NotFoundException(`LineBot ${id} not found.`);
    }
    if (
      userContext.role === "TENANT_ADMIN" &&
      existingBot.tenant_id !== userContext.tenantId
    ) {
      throw new ForbiddenException(
        "Tenant admin cannot delete bots of other tenants."
      );
    }

    const deletedBot = await this.prisma.line_bots.delete({ where: { id } });
    this.logger.log(`LineBot deleted: ${deletedBot.id}`);
    return this.transformBotResponse(deletedBot) as LineBot;
  }

  async findAll(
    params: { scope?: LineBotScope; tenantId?: string },
    userContext: UserContext
  ): Promise<LineBot[]> {
    const where: Prisma.line_botsWhereInput = {};

    if (userContext.role === "TENANT_ADMIN") {
      if (!userContext.tenantId) {
        this.logger.warn(
          "TENANT_ADMIN has no tenantId in context for findAll."
        );
        throw new ForbiddenException(
          "Tenant context is required for tenant admin."
        );
      }
      where.tenant_id = userContext.tenantId;
      where.scope = LineBotScope.TENANT;
    } else if (
      userContext.role === "SYSTEM_ADMIN" ||
      userContext.role === "SUPER_ADMIN"
    ) {
      if (params.scope) {
        where.scope = params.scope;
      }
      if (params.tenantId) {
        if (params.scope === LineBotScope.TENANT || !params.scope) {
          where.tenant_id = params.tenantId;
        } else if (params.scope === LineBotScope.SYSTEM && params.tenantId) {
          this.logger.warn(
            "Filtering SYSTEM scope bots by tenantId is not logical and will yield no results if tenantId is provided."
          );
          where.tenant_id = null;
        }
      }
    } else {
      this.logger.warn(
        `User with role ${userContext.role} attempted to findAll LineBots without permission.`
      );
      return [];
    }

    const bots = await this.prisma.line_bots.findMany({ where });
    return this.transformBotListResponse(bots);
  }

  async findOne(id: string): Promise<LineBot | null> {
    const bot = await this.prisma.line_bots.findUnique({ where: { id } });
    return this.transformBotResponse(bot);
  }

  async findBotsNeedingTokenReminder(
    daysThreshold: number = 7,
    userContext: UserContext
  ): Promise<Partial<LineBot>[]> {
    const where: Prisma.line_botsWhereInput = {
      is_enabled: true,
      bot_token: { not: null },
      token_last_updated_at: { not: null },
      token_update_reminder_period_days: { not: null },
    };

    if (userContext.role === TenantUserRole.TENANT_ADMIN) {
      if (!userContext.tenantId) {
        this.logger.warn(
          "TENANT_ADMIN has no tenantId for findBotsNeedingTokenReminder"
        );
        return [];
      }
      where.tenant_id = userContext.tenantId;
      where.scope = LineBotScope.TENANT;
    } else if (
      userContext.role === SystemUserRole.SYSTEM_ADMIN ||
      userContext.role === SystemUserRole.SUPER_ADMIN
    ) {
      // System admins can see all bots
    } else {
      this.logger.warn(
        `User with role ${userContext.role} attempted to findBotsNeedingTokenReminder.`
      );
      return [];
    }

    const bots = await this.prisma.line_bots.findMany({
      where,
      select: {
        id: true,
        name: true,
        scope: true,
        tenant_id: true,
        token_last_updated_at: true,
        token_update_reminder_period_days: true,
      },
    });

    const today = new Date();
    const reminderBots = bots.filter((bot) => {
      if (bot.token_last_updated_at && bot.token_update_reminder_period_days) {
        const reminderDate = new Date(bot.token_last_updated_at);
        reminderDate.setDate(
          reminderDate.getDate() +
            bot.token_update_reminder_period_days -
            daysThreshold
        );
        return reminderDate <= today;
      }
      return false;
    });

    return reminderBots;
  }

  async getDecryptedChannelSecret(botId: string): Promise<string | null> {
    const bot = await this.prisma.line_bots.findUnique({
      where: { id: botId },
      select: { bot_secret: true },
    });
    if (!bot || !bot.bot_secret) return null;
    return this.encryptionService.decrypt(bot.bot_secret);
  }

  async getDecryptedBotToken(botId: string): Promise<string | null> {
    const bot = await this.prisma.line_bots.findUnique({
      where: { id: botId },
      select: { bot_token: true },
    });
    if (!bot || !bot.bot_token) return null;
    return this.encryptionService.decrypt(bot.bot_token);
  }

  async handleWebhookEvents(
    botInstance: LineBot,
    events: WebhookEvent[]
  ): Promise<void> {
    const decryptedToken = botInstance.bot_token
      ? this.encryptionService.decrypt(botInstance.bot_token)
      : null;

    if (!decryptedToken) {
      this.logger.error(
        `Bot ${botInstance.id} has no decrypted token. Cannot process webhook events.`
      );
      return;
    }
    const client = new Client({ channelAccessToken: decryptedToken });

    for (const event of events) {
      try {
        await this.messageLogService.logIncomingEvent(botInstance, event);
        await this.processEvent(client, event, botInstance);
      } catch (error) {
        this.logger.error(
          `Error processing event type ${event.type} for bot ${botInstance.id}: ${error.message}`,
          error.stack
        );
      }
    }
  }

  private async processEvent(
    client: Client,
    event: WebhookEvent,
    bot: LineBot
  ): Promise<MessageAPIResponseBase | void> {
    this.logger.log(
      `Processing event type: ${event.type} for bot ${bot.id}, Source: ${JSON.stringify(event.source)}`
    );

    if (event.source.type === "group") {
      const groupId = event.source.groupId;
      const verification =
        await this.groupVerificationService.recordOrFindUnverifiedGroup(
          bot.id,
          groupId,
          bot.tenant_id
        );

      if (
        !verification.is_verified &&
        event.type !== "memberJoined" &&
        event.type !== "join"
      ) {
        this.logger.warn(
          `Bot ${bot.id} received event type '${event.type}' from unverified group ${groupId}. Ignoring.`
        );
        return;
      }

      if (
        event.type === "memberLeft" &&
        event.left.members.some(
          (member) =>
            member.userId === "BOT_USER_ID_NEEDS_TO_BE_FETCHED_OR_CONFIGURED"
        )
      ) {
        this.logger.log(
          `Bot ${bot.id} was removed from group ${groupId}. Resetting verification.`
        );
        await this.groupVerificationService.resetVerificationOnBotLeave(
          bot.id,
          groupId
        );
        return;
      }
    }

    switch (event.type) {
      case "message":
        return this.handleMessageEvent(client, event as MessageEvent, bot);
      case "follow":
        this.logger.log(`User ${event.source.userId} followed Bot ${bot.id}`);
        return client.replyMessage(event.replyToken, {
          type: "text",
          text: `Thanks for following ${bot.name}!`,
        });
      case "unfollow":
        this.logger.log(`User ${event.source.userId} unfollowed Bot ${bot.id}`);
        return;
      case "join":
      case "memberJoined":
        this.logger.log(
          `Bot ${bot.id} or member joined group. Source: ${JSON.stringify(event.source)}`
        );
        if (event.source.type === "group") {
          const groupId = event.source.groupId;
          const isBotJoined = (event as any).joined?.members.some(
            (m: any) =>
              m.userId === "BOT_USER_ID_NEEDS_TO_BE_FETCHED_OR_CONFIGURED"
          );
          if (isBotJoined || event.type === "join") {
            this.logger.log(
              `Bot ${bot.id} joined group ${groupId}. Sending verification guidance.`
            );
            await this.sendJoinVerificationMessageInternal(
              client,
              event.replyToken,
              groupId
            );
          }
        }
        return;
      case "leave":
      case "memberLeft":
        this.logger.log(
          `Bot ${bot.id} or member left group. Source: ${JSON.stringify(event.source)}`
        );
        return;
      case "postback":
        this.logger.log(
          `Postback event for Bot ${bot.id}: ${JSON.stringify(event.postback.data)}`
        );
        return;
      case "beacon":
        this.logger.log(
          `Beacon event for Bot ${bot.id}: Type ${event.beacon.type}, HWID ${event.beacon.hwid}`
        );
        return;
      default:
        this.logger.warn(
          `Unhandled event type: ${(event as any).type} for bot ${bot.id}`
        );
        return;
    }
  }

  private async handleMessageEvent(
    client: Client,
    event: MessageEvent,
    bot: LineBot
  ): Promise<MessageAPIResponseBase | void> {
    const message = event.message;
    this.logger.log(
      `Handling message event: Type ${message.type}, BotID: ${bot.id}, UserID: ${event.source.userId}, GroupID: ${event.source.type === "group" ? event.source.groupId : "N/A"}`
    );

    if (message.type === "text") {
      const textMessage = message as TextMessage;
      const replyText = `Bot ${bot.name} received: "${textMessage.text}"`;
      this.logger.log(`Replying with: "${replyText}"`);
      return client.replyMessage(event.replyToken, {
        type: "text",
        text: replyText,
      });
    }
    this.logger.log(`Received non-text message type: ${message.type}`);
    return client.replyMessage(event.replyToken, {
      type: "text",
      text: `Received a ${message.type} message. I can currently only process text. Bot: ${bot.name}`,
    });
  }

  private createFunctionListMessage(): TemplateMessage {
    return {
      type: "template",
      altText: "functions",
      template: {
        type: "buttons",
        title: "Functions",
        text: "Select a function",
        actions: [],
      },
    };
  }

  private createQuickReplyMessage(): TextMessage {
    return { type: "text", text: "Quick reply", quickReply: { items: [] } };
  }

  private createWelcomeFlexMessage(): FlexMessage {
    const bubble: FlexBubble = {
      type: "bubble",
      body: {
        type: "box",
        layout: "vertical",
        contents: [{ type: "text", text: "Welcome!" }],
      },
    };
    return { type: "flex", altText: "Welcome", contents: bubble };
  }

  async pushMessage(
    botId: string,
    to: string,
    messages: Message[]
  ): Promise<MessageAPIResponseBase> {
    const bot = await this.prisma.line_bots.findUnique({
      where: { id: botId },
    });
    if (!bot || !bot.is_enabled) {
      throw new NotFoundException(`LineBot ${botId} not found or is disabled.`);
    }

    const decryptedToken = bot.bot_token
      ? this.encryptionService.decrypt(bot.bot_token)
      : null;

    if (!decryptedToken) {
      throw new BadRequestException(
        `LineBot ${botId} does not have a configured access token.`
      );
    }

    const client = new Client({ channelAccessToken: decryptedToken });

    try {
      const response = await client.pushMessage(to, messages);
      this.logger.log(
        `Message pushed to ${to} via Bot ${botId}. Response: ${JSON.stringify(response)}`
      );

      let workspaceContextId: string | undefined | null = null;
      if (bot.scope === LineBotScope.TENANT && bot.tenant_id) {
        if (to.startsWith("C") || to.startsWith("R")) {
          const verification =
            await this.prisma.line_group_verifications.findFirst({
              where: { bot_id: botId, group_id: to, is_verified: true },
              select: { workspace_id: true },
            });
          workspaceContextId = verification?.workspace_id;
        }
      }
      await this.messageLogService.logOutgoingMessages(
        bot,
        to,
        messages,
        response
      );

      return response;
    } catch (error: any) {
      this.logger.error(
        `Failed to push message to ${to} via Bot ${botId}: ${error.message}`,
        error.stack
      );
      throw new BadRequestException(`Failed to push message: ${error.message}`);
    }
  }

  private async sendJoinVerificationMessageInternal(
    client: Client,
    replyToken: string,
    groupId: string
  ): Promise<void> {
    const verificationUrl = `https://YOUR_FRONTEND_URL/verify-group?groupId=${groupId}`;
    const message: TextMessage = {
      type: "text",
      text: `歡迎使用本服務！請管理員點擊以下連結完成群組驗證，以啟用完整功能： ${verificationUrl} (群組ID: ${groupId})`,
    };
    try {
      await client.replyMessage(replyToken, message);
      this.logger.log(`Sent verification guidance to group ${groupId}`);
    } catch (error) {
      this.logger.error(
        `Failed to send verification guidance to group ${groupId}: ${error}`
      );
    }
  }
}
