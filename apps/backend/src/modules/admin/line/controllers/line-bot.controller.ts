import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Param,
  Body,
  Query,
  Headers,
  BadRequestException,
  UnauthorizedException,
  Logger,
  Req,
  RawBodyRequest,
  UseGuards,
  NotFoundException,
  HttpCode,
  HttpStatus,
} from "@nestjs/common";
import { LineBotService } from "../services/line-bot.service";
import { CreateLineBotDto } from "../dto/create-line-bot.dto";
import { UpdateLineBotDto } from "../dto/update-line-bot.dto";
import {
  ApiTags,
  ApiBearerAuth,
  ApiOperation,
  ApiQuery,
  ApiResponse,
  ApiBody,
  ApiParam,
} from "@nestjs/swagger";
import { LineBotScope } from "../dto/create-line-bot.dto";
import {
  WebhookRequestBody,
  Message,
  FlexMessage,
  TemplateMessage,
  TextMessage,
} from "@line/bot-sdk";
import { Request } from "express";
import { Public } from "../../../../common/decorators/public.decorator";
import { LineSignatureGuard } from "../guards/line-signature.guard";
import { CheckPolicies } from "@/casl/decorators/check-policies.decorator";
import { AppAbility } from "@/types/models/casl.model";
import { Actions, Subjects } from "@horizai/permissions";
import { CurrentTenant } from "@/modules/admin/tenants/decorators/current-tenant.decorator";
import { JwtAuthGuard } from "@/modules/core/auth/guards/auth.guard";
import { RolesGuard } from "@/modules/core/auth/guards/roles.guard";
import { PoliciesGuard } from "@/casl/guards/permission.guard";
import { line_bots } from "@prisma/client"; // For return type hinting
import { Role as RoleEnum } from "@/common/enums/role.enum";
import { SystemLogService } from "@/common/services/system-log.service";

// Assume AuthenticatedUser interface is defined (e.g, in a global types file or imported)
interface AuthenticatedUser {
  id: string;
  role: RoleEnum;
  tenantId?: string | null;
  // any other properties from JWT payload
}

// DTO for Push Message API
class PushMessageDto {
  to: string; // LINE User ID or Group ID
  messages: Message[]; // Array of LINE message objects
}

@ApiTags("admin/line-bots")
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RolesGuard, PoliciesGuard)
@Controller("admin/line-bots")
export class LineBotController {
  private readonly logger = new Logger(LineBotController.name);
  constructor(
    private readonly lineBotService: LineBotService,
    private readonly systemLogService: SystemLogService
  ) {}

  @Get()
  @CheckPolicies((ability: AppAbility) =>
    ability.can(Actions.READ, Subjects.LINE_BOT)
  )
  @ApiOperation({ summary: "讀取所有 Line Bot" })
  @ApiQuery({ name: "scope", enum: LineBotScope, required: false })
  @ApiQuery({
    name: "tenantId",
    required: false,
    description: "系統管理員可選填以篩選特定租戶",
  })
  @ApiResponse({ status: 200, description: "成功取得 Line Bot 列表" })
  async findAll(
    @Req() req: Request,
    @Query("scope") scope?: LineBotScope,
    @Query("tenantId") queryTenantId?: string
  ) {
    const user = req.user as AuthenticatedUser;
    return this.lineBotService.findAll(
      { scope, tenantId: queryTenantId },
      { role: user.role, tenantId: user.tenantId }
    );
  }

  @Get(":id")
  @CheckPolicies((ability: AppAbility) =>
    ability.can(Actions.READ, Subjects.LINE_BOT)
  )
  @ApiOperation({ summary: "根據 ID 讀取 Line Bot" })
  @ApiResponse({ status: 200, description: "成功取得 Line Bot" })
  @ApiResponse({ status: 404, description: "找不到指定的 Line Bot" })
  async findOne(
    @Param("id") id: string
    // @CurrentTenant("id") tenantId?: string // REMOVED
  ) {
    const bot = await this.lineBotService.findOne(id);
    if (!bot) {
      throw new NotFoundException(`LineBot with ID ${id} not found`);
    }
    // Manual tenant check removed, PoliciesGuard should handle this.
    return bot;
  }

  @Post()
  @CheckPolicies((ability: AppAbility) =>
    ability.can(Actions.CREATE, Subjects.LINE_BOT)
  )
  @ApiOperation({ summary: "建立新 Line Bot" })
  @ApiResponse({ status: 201, description: "成功建立 Line Bot" })
  async create(@Body() dto: CreateLineBotDto, @Req() req: Request) {
    const user = req.user as AuthenticatedUser;
    const result = await this.lineBotService.create(dto, {
      role: user.role,
      tenantId: user.tenantId,
    });
    // 寫入操作日誌
    await this.systemLogService.logAudit({
      message: `建立 Line Bot: ${result.name}`,
      userId: user.id,
      tenantId: user.tenantId ?? undefined,
      action: "CREATE_LINE_BOT",
      targetResource: "LineBot",
      targetResourceId: result.id,
      status: "SUCCESS",
      ip: req.ip,
      path: req.path,
      method: req.method,
      details: dto,
    });
    return result;
  }

  @Put(":id")
  @CheckPolicies((ability: AppAbility) =>
    ability.can(Actions.UPDATE, Subjects.LINE_BOT)
  )
  @ApiOperation({ summary: "更新 Line Bot" })
  @ApiResponse({ status: 200, description: "成功更新 Line Bot" })
  @ApiResponse({ status: 404, description: "找不到指定的 Line Bot" })
  async update(
    @Param("id") id: string,
    @Body() dto: UpdateLineBotDto,
    @Req() req: Request
    // @CurrentTenant("id") tenantId?: string // REMOVED
  ) {
    const user = req.user as AuthenticatedUser;
    // const bot = await this.lineBotService.findOne(id); // REMOVED - PoliciesGuard handles access
    // if (!bot) throw new NotFoundException(); // REMOVED
    // if (bot.scope === "TENANT" && bot.tenant_id !== tenantId) // REMOVED
    //   throw new NotFoundException(); // REMOVED
    // dto.tenant_id = tenantId; // REMOVED - Service should handle tenant_id immutability or specific updates
    return this.lineBotService.update(id, dto, {
      role: user.role,
      tenantId: user.tenantId,
    });
  }

  @Delete(":id")
  @CheckPolicies((ability: AppAbility) =>
    ability.can(Actions.DELETE, Subjects.LINE_BOT)
  )
  @ApiOperation({ summary: "刪除 Line Bot" })
  @ApiResponse({ status: 200, description: "成功刪除 Line Bot" })
  @ApiResponse({ status: 404, description: "找不到指定的 Line Bot" })
  async delete(
    @Param("id") id: string,
    @Req() req: Request
    // @CurrentTenant("id") tenantId?: string // REMOVED
  ) {
    const user = req.user as AuthenticatedUser;
    // const bot = await this.lineBotService.findOne(id); // REMOVED
    // if (!bot) throw new NotFoundException(); // REMOVED
    // if (bot.scope === "TENANT" && bot.tenant_id !== tenantId) // REMOVED
    //   throw new NotFoundException(); // REMOVED
    return this.lineBotService.delete(id, {
      role: user.role,
      tenantId: user.tenantId,
    });
  }

  @Post(":botId/messages/push")
  @HttpCode(HttpStatus.OK)
  @CheckPolicies((ability: AppAbility) =>
    ability.can(Actions.UPDATE, Subjects.LINE_BOT)
  )
  @ApiOperation({ summary: "推播訊息" })
  @ApiParam({ name: "botId", description: "Line Bot 的 ID" })
  @ApiBody({ type: PushMessageDto, description: "推播訊息的目標與內容" })
  @ApiResponse({ status: 200, description: "成功推播訊息" })
  @ApiResponse({ status: 404, description: "找不到指定的 Line Bot" })
  async pushMessage(
    @Param("botId") botId: string,
    @Body() pushMessageDto: PushMessageDto,
    @Req() req: Request
    // @CurrentTenant("id") tenantId?: string // REMOVED
  ) {
    const user = req.user as AuthenticatedUser; // Get user context
    // const bot = await this.lineBotService.findOne(botId); // REMOVED - PoliciesGuard handles access
    // if (!bot) { // REMOVED
    //   throw new NotFoundException(`LineBot ${botId} not found`); // REMOVED
    // }
    // if (bot.scope === "TENANT" && bot.tenant_id !== user.tenantId) { // MODIFIED to use user.tenantId and then REMOVED
    //   throw new NotFoundException( // REMOVED
    //     `User cannot access LineBot ${botId}` // REMOVED
    //   );
    // }

    if (!pushMessageDto.messages || pushMessageDto.messages.length === 0) {
      throw new BadRequestException("No messages provided in payload");
    }

    return this.lineBotService.pushMessage(
      botId,
      pushMessageDto.to,
      pushMessageDto.messages
      // Consider passing user context if service.pushMessage needs it for further checks
      // { role: user.role, tenantId: user.tenantId }
    );
  }

  @Get("token-reminders")
  @CheckPolicies((ability: AppAbility) =>
    ability.can(Actions.READ, Subjects.LINE_BOT)
  )
  @ApiOperation({ summary: "取得需要憑證更新提醒的 Line Bot" })
  @ApiQuery({
    name: "daysThreshold",
    description: "提醒閾值（天，預設7天）",
    type: Number,
    required: false,
  })
  @ApiResponse({
    status: 200,
    description: "成功取得提醒列表",
  })
  async getTokenReminders(
    @Req() req: Request,
    @Query("daysThreshold") daysThreshold?: string
  ) {
    const user = req.user as AuthenticatedUser;
    const threshold = daysThreshold ? parseInt(daysThreshold, 10) : 7;
    if (isNaN(threshold) || threshold < 1) {
      throw new BadRequestException(
        "Invalid daysThreshold. Must be a positive integer."
      );
    }
    return this.lineBotService.findBotsNeedingTokenReminder(threshold, {
      role: user.role,
      tenantId: user.tenantId,
    });
  }

  /**
   * 公開路由 - 接收 LINE 平台的 webhook 事件
   * 路徑: /api/admin/line-bots/callback/:id
   */
  @Public()
  @Post("callback/:botId")
  @ApiOperation({ summary: "接收 LINE 平台的 Webhook 事件" })
  @ApiResponse({ status: 200, description: "成功處理 Webhook 事件" })
  async handlePublicWebhook(
    @Param("botId") botId: string,
    @Body() body: WebhookRequestBody
    // No @Req() user here as it's a public route
  ) {
    this.logger.log(`公開路由 - 收到 LINE Webhook 事件 (Bot ID: ${botId})`);

    // This service call likely doesn't need user context as it's triggered by LINE
    const bot = await this.lineBotService.findOne(botId);
    if (!bot) {
      this.logger.error(`找不到 ID 為 ${botId} 的 Line Bot`);
      return ""; // Return empty string or appropriate response for LINE
    }

    if (!bot.is_enabled) {
      this.logger.warn(`Line Bot ${botId} 已停用，不處理 Webhook 事件`);
      return "";
    }

    try {
      // handleWebhookEvents might internally check bot status or other configurations
      await this.lineBotService.handleWebhookEvents(bot, body.events);
      return "";
    } catch (error) {
      this.logger.error(`處理 Webhook 事件失敗: ${error.message}`, error.stack);
      // Consider returning a specific error response if LINE expects it
      return "";
    }
  }
}
