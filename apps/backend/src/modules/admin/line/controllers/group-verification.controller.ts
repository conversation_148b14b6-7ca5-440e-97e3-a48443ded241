import {
  Controller,
  Get,
  Post,
  Param,
  Query,
  Body,
  Logger,
  NotFoundException,
  BadRequestException,
} from "@nestjs/common";
import {
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiBearerAuth,
} from "@nestjs/swagger";
import { GroupVerificationService } from "../services/group-verification.service";
import { PrismaClient } from "@prisma/client";
import { CurrentUser } from "@/modules/core/auth/decorators/current-user.decorator";

// 定義驗證群組所需的DTO
class VerifyGroupDto {
  workspaceId: string}

@ApiTags("admin/line-bots")
@ApiBearerAuth()
@Controller("admin/line-bots")
export class GroupVerificationController {
  private readonly logger = new Logger(GroupVerificationController.name);

  constructor(
    private readonly groupVerificationService: GroupVerificationService
  ) {}

  @Get(":botId/group-verifications")
  @ApiOperation({ summary: "取得Line Bot群組驗證列表" })
  @ApiParam({ name: "botId", description: "Line Bot的ID" })
  @ApiQuery({
    name: "isVerified",
    required: false,
    type: Boolean,
    description: "是否已驗證（可選）",
  })
  @ApiQuery({
    name: "skip",
    required: false,
    type: Number,
    description: "跳過的記錄數（用於分頁）",
  })
  @ApiQuery({
    name: "take",
    required: false,
    type: Number,
    description: "取得的記錄數（用於分頁）",
  })
  @ApiResponse({ status: 200, description: "成功取得群組驗證列表" })
  async getGroupVerifications(
    @Param("botId") botId: string,
    @Query("isVerified") isVerified?: boolean,
    @Query("skip") skip?: number,
    @Query("take") take?: number
  ) {
    this.logger.log(
      `取得Line Bot ${botId} 的群組驗證列表, isVerified=${isVerified}, skip=${skip}, take=${take}`
    );

    try {
      const result = await this.groupVerificationService.listVerifications(
        botId,
        {
          isVerified:
            isVerified !== undefined ? isVerified === true : undefined,
          skip: skip !== undefined ? Number(skip) : undefined,
          take: take !== undefined ? Number(take) : undefined,
          includeWorkspace: true,
          includeUser: true,
        }
      );

      return result;
    } catch (error) {
      this.logger.error(`取得群組驗證列表失敗: ${error.message}`, error.stack);
      throw error;
    }
  }

  @Post(":botId/group-verifications/:groupId/verify")
  @ApiOperation({ summary: "驗證LINE群組" })
  @ApiParam({ name: "botId", description: "Line Bot的ID" })
  @ApiParam({ name: "groupId", description: "LINE群組的ID" })
  @ApiResponse({ status: 200, description: "成功驗證群組" })
  @ApiResponse({ status: 404, description: "找不到指定的群組驗證記錄" })
  async verifyGroup(
    @Param("botId") botId: string,
    @Param("groupId") groupId: string,
    @Body() verifyGroupDto: VerifyGroupDto,
    @CurrentUser("id") currentUserId: string
  ) {
    this.logger.log(
      `驗證群組: botId=${botId}, groupId=${groupId}, workspaceId=${verifyGroupDto.workspaceId}, currentUserId=${currentUserId}`
    );

    if (!verifyGroupDto.workspaceId) {
      throw new BadRequestException("必須提供工作區ID (workspaceId)");
    }

    if (!currentUserId) {
      throw new BadRequestException("無法識別當前用戶");
    }

    try {
      const result = await this.groupVerificationService.verifyGroup(
        botId,
        groupId,
        verifyGroupDto.workspaceId,
        currentUserId
      );

      return result;
    } catch (error) {
      this.logger.error(`驗證群組失敗: ${error.message}`, error.stack);
      throw error;
    }
  }

  @Post(":botId/group-verifications/:groupId/unverify")
  @ApiOperation({ summary: "取消LINE群組驗證" })
  @ApiParam({ name: "botId", description: "Line Bot的ID" })
  @ApiParam({ name: "groupId", description: "LINE群組的ID" })
  @ApiResponse({ status: 200, description: "成功取消群組驗證" })
  @ApiResponse({ status: 404, description: "找不到指定的群組驗證記錄" })
  async unverifyGroup(
    @Param("botId") botId: string,
    @Param("groupId") groupId: string
  ) {
    this.logger.log(`取消群組驗證: botId=${botId}, groupId=${groupId}`);

    try {
      const result = await this.groupVerificationService.unverifyGroup(
        botId,
        groupId
      );
      return result;
    } catch (error) {
      this.logger.error(`取消群組驗證失敗: ${error.message}`, error.stack);
      throw error;
    }
  }
}
