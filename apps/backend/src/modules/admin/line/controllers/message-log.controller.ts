import { Controller, Get, Param, Query, UseGuards } from "@nestjs/common";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiQuery,
  ApiParam,
  ApiBearerAuth,
} from "@nestjs/swagger";
import { MessageLogService } from "../services/message-log.service";
import { JwtAuthGuard } from "@/modules/core/auth/guards/auth.guard";
import { RolesGuard } from "@/modules/core/auth/guards/roles.guard";
import { PoliciesGuard } from "@/casl/guards/permission.guard";
import { CheckPolicies } from "@/casl/decorators/check-policies.decorator";
import { Actions, Subjects } from "@horizai/permissions";
import { AppAbility } from "@/types/models/casl.model";
import { CurrentTenant } from "@/modules/admin/tenants/decorators/current-tenant.decorator";

@ApiTags("admin/line-bots")
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RolesGuard, PoliciesGuard)
@Controller("admin/line-bots")
export class MessageLogController {
  constructor(private readonly messageLogService: MessageLogService) {}

  @Get(":botId/message-logs")
  @CheckPolicies((ability: AppAbility) =>
    ability.can(Actions.READ, Subjects.LINE_MESSAGE_LOG)
  )
  @ApiOperation({ summary: "取得 Line Bot 訊息記錄列表" })
  @ApiParam({ name: "botId", description: "Line Bot 的 ID" })
  @ApiQuery({ name: "skip", required: false, type: Number })
  @ApiQuery({ name: "take", required: false, type: Number })
  @ApiResponse({ status: 200, description: "成功取得訊息記錄列表" })
  async getMessageLogs(
    @Param("botId") botId: string,
    @Query("skip") skip?: number,
    @Query("take") take?: number,
    @CurrentTenant("id") tenantId?: string
  ) {
    return this.messageLogService.listLogs(botId, {
      skip: skip !== undefined ? Number(skip) : undefined,
      take: take !== undefined ? Number(take) : undefined,
      tenantId,
    });
  }
}
