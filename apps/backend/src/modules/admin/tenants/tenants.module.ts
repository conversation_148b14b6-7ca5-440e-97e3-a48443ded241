import { <PERSON>du<PERSON> } from "@nestjs/common";
import { TenantsService } from "./tenants.service";
import { TenantsController } from "./tenants.controller";
import { TenantUsersService } from "../tenant-users/tenant-users.service";
import { TenantInterceptor } from "./interceptors/tenant.interceptor";
import { PrismaModule } from "../../core/prisma/prisma.module";
import { APP_INTERCEPTOR } from "@nestjs/core";
import { ConfigModule } from "@nestjs/config";
import { CaslModule } from "../../../casl/casl.module";
import { CommonModule } from "../../../common/common.module";
import { MailModule } from "../../core/mail/mail.module";
import { UserManagementModule } from "../../core/user-management/user-management.module";

@Module({
  imports: [
    PrismaModule,
    ConfigModule,
    CaslModule,
    CommonModule,
    MailModule,
    UserManagementModule,
  ],
  controllers: [TenantsController],
  providers: [
    TenantsService,
    TenantUsersService,
    {
      provide: APP_INTERCEPTOR,
      useClass: TenantInterceptor,
    },
  ],
  exports: [TenantsService, TenantUsersService],
})
export class TenantsModule { }
