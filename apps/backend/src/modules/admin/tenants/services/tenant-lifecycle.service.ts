import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../../../core/prisma/prisma.service';
import { MailService } from '../../../core/mail/mail.service';

export enum TenantLifecycleStatus {
    ACTIVE = 'active',
    SUSPENDED = 'suspended',
    ARCHIVED = 'archived',
    PENDING_DELETION = 'pending_deletion',
}

export interface TenantLifecycleEvent {
    tenantId: string;
    fromStatus: string;
    toStatus: string;
    reason?: string;
    performedBy: string;
    performedAt: Date;
    metadata?: Record<string, any>;
}

@Injectable()
export class TenantLifecycleService {
    private readonly logger = new Logger(TenantLifecycleService.name);

    constructor(
        private readonly prisma: PrismaService,
        private readonly mailService: MailService
    ) { }

    /**
     * 暫停租戶
     */
    async suspendTenant(
        tenantId: string,
        reason: string,
        performedBy: string
    ): Promise<void> {
        const tenant = await this.prisma.tenants.findUnique({
            where: { id: tenantId },
            include: {
                tenant_users: {
                    where: { role: 'TENANT_ADMIN' },
                    select: { email: true, name: true },
                },
            },
        });

        if (!tenant) {
            throw new BadRequestException('租戶不存在');
        }

        if (tenant.status === TenantLifecycleStatus.SUSPENDED) {
            throw new BadRequestException('租戶已經被暫停');
        }

        await this.prisma.$transaction(async (tx) => {
            // 更新租戶狀態
            await tx.tenants.update({
                where: { id: tenantId },
                data: { status: TenantLifecycleStatus.SUSPENDED },
            });

            // 記錄生命週期事件
            await this.recordLifecycleEvent(tx, {
                tenantId,
                fromStatus: tenant.status,
                toStatus: TenantLifecycleStatus.SUSPENDED,
                reason,
                performedBy,
                performedAt: new Date(),
            });

            // 暫停所有租戶用戶
            await tx.tenant_users.updateMany({
                where: { tenantId },
                data: { status: 'SUSPENDED' },
            });
        });

        // 發送通知郵件給租戶管理員
        for (const admin of tenant.tenant_users) {
            await this.mailService.sendTenantSuspensionNotice(
                admin.email,
                tenant.name,
                reason
            );
        }

        this.logger.log(`租戶 ${tenantId} 已被暫停，原因：${reason}`);
    }

    /**
     * 重新啟用租戶
     */
    async reactivateTenant(
        tenantId: string,
        performedBy: string
    ): Promise<void> {
        const tenant = await this.prisma.tenants.findUnique({
            where: { id: tenantId },
            include: {
                tenant_users: {
                    where: { role: 'TENANT_ADMIN' },
                    select: { email: true, name: true },
                },
            },
        });

        if (!tenant) {
            throw new BadRequestException('租戶不存在');
        }

        if (tenant.status === TenantLifecycleStatus.ACTIVE) {
            throw new BadRequestException('租戶已經是啟用狀態');
        }

        await this.prisma.$transaction(async (tx) => {
            // 更新租戶狀態
            await tx.tenants.update({
                where: { id: tenantId },
                data: { status: TenantLifecycleStatus.ACTIVE },
            });

            // 記錄生命週期事件
            await this.recordLifecycleEvent(tx, {
                tenantId,
                fromStatus: tenant.status,
                toStatus: TenantLifecycleStatus.ACTIVE,
                reason: '重新啟用',
                performedBy,
                performedAt: new Date(),
            });

            // 重新啟用租戶用戶
            await tx.tenant_users.updateMany({
                where: {
                    tenantId,
                    status: 'SUSPENDED',
                },
                data: { status: 'ACTIVE' },
            });
        });

        // 發送重新啟用通知
        for (const admin of tenant.tenant_users) {
            await this.mailService.sendTenantReactivationNotice(
                admin.email,
                tenant.name
            );
        }

        this.logger.log(`租戶 ${tenantId} 已重新啟用`);
    }

    /**
     * 歸檔租戶（軟刪除）
     */
    async archiveTenant(
        tenantId: string,
        reason: string,
        performedBy: string
    ): Promise<void> {
        const tenant = await this.prisma.tenants.findUnique({
            where: { id: tenantId },
        });

        if (!tenant) {
            throw new BadRequestException('租戶不存在');
        }

        await this.prisma.$transaction(async (tx) => {
            // 更新租戶狀態
            await tx.tenants.update({
                where: { id: tenantId },
                data: {
                    status: TenantLifecycleStatus.ARCHIVED,
                    // 可以添加歸檔時間戳
                },
            });

            // 記錄生命週期事件
            await this.recordLifecycleEvent(tx, {
                tenantId,
                fromStatus: tenant.status,
                toStatus: TenantLifecycleStatus.ARCHIVED,
                reason,
                performedBy,
                performedAt: new Date(),
            });

            // 歸檔所有相關資料
            await this.archiveRelatedData(tx, tenantId);
        });

        this.logger.log(`租戶 ${tenantId} 已歸檔，原因：${reason}`);
    }

    /**
     * 獲取租戶生命週期歷史
     */
    async getTenantLifecycleHistory(tenantId: string): Promise<TenantLifecycleEvent[]> {
        const events = await this.prisma.tenant_lifecycle_events.findMany({
            where: { tenantId },
            orderBy: { createdAt: 'desc' },
        });

        return events.map(event => ({
            tenantId: event.tenantId,
            fromStatus: event.eventType, // 使用 eventType 作為狀態
            toStatus: event.eventType,
            reason: event.reason || undefined,
            performedBy: event.triggeredBy || '',
            performedAt: event.createdAt,
            metadata: event.metadata as Record<string, any>,
        }));
    }

    /**
     * 檢查租戶是否可以執行特定操作
     */
    async canPerformOperation(tenantId: string, operation: string): Promise<boolean> {
        const tenant = await this.prisma.tenants.findUnique({
            where: { id: tenantId },
            select: { status: true },
        });

        if (!tenant) {
            return false;
        }

        switch (operation) {
            case 'create_user':
            case 'create_project':
            case 'upload_file':
                return tenant.status === TenantLifecycleStatus.ACTIVE;

            case 'read_data':
                return [
                    TenantLifecycleStatus.ACTIVE,
                    TenantLifecycleStatus.SUSPENDED,
                ].includes(tenant.status as TenantLifecycleStatus);

            default:
                return tenant.status === TenantLifecycleStatus.ACTIVE;
        }
    }

    private async recordLifecycleEvent(
        tx: any,
        event: TenantLifecycleEvent
    ): Promise<void> {
        await tx.tenant_lifecycle_events.create({
            data: {
                tenantId: event.tenantId,
                eventType: event.toStatus, // 使用 toStatus 作為 eventType
                reason: event.reason,
                triggeredBy: event.performedBy,
                metadata: event.metadata || {},
            },
        });
    }

    private async archiveRelatedData(tx: any, tenantId: string): Promise<void> {
        // 歸檔租戶用戶
        await tx.tenant_users.updateMany({
            where: { tenantId },
            data: { status: 'ARCHIVED' },
        });

        // 歸檔工作區
        await tx.workspaces.updateMany({
            where: { tenantId },
            data: { status: 'ARCHIVED' },
        });

        // 歸檔專案
        await tx.projects.updateMany({
            where: { tenantId },
            data: { status: 'ARCHIVED' },
        });

        // 可以添加更多相關資料的歸檔邏輯
    }
} 