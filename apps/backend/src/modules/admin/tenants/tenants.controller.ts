import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  OnModuleInit,
  HttpCode,
  HttpStatus,
  Query,
  BadRequestException,
} from "@nestjs/common";
import { TenantsService, ExtendedTenant } from "./tenants.service";
import { CreateTenantDto } from "./dto/create-tenant.dto";
import { UpdateTenantDto } from "./dto/update-tenant.dto";
import { JwtAuthGuard } from "../../core/auth/guards/auth.guard";
import { Roles } from "../../core/auth/decorators/roles.decorator";
import { Role } from "../../../common/enums/role.enum";
import { PoliciesGuard } from "../../../casl/guards/permission.guard";
import { CheckPolicies } from "../../../casl/decorators/check-policies.decorator";
import { AppAbility } from "../../../types/models/casl.model";
import { Action } from "../../../common/enums/action.enum";
import {
  ApiTags,
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiParam,
} from "@nestjs/swagger";
import { ConfigService } from "@nestjs/config";
import { UpdateTenantStatusDto } from "./dto/update-tenant-status.dto";
import { CheckTenantUniquenessDto } from "./dto/check-tenant-uniqueness.dto";

@ApiTags("admin/tenants")
@ApiBearerAuth()
@Controller("admin/tenants")
@UseGuards(JwtAuthGuard, PoliciesGuard)
export class TenantsController implements OnModuleInit {
  private superAdminRoles: Role[];
  private adminRoles: Role[];

  constructor(
    private readonly tenantsService: TenantsService,
    private readonly configService: ConfigService
  ) {}

  onModuleInit() {
    // 從配置檔讀取超級管理員角色列表
    const superConfigRoles = this.configService.get<string>(
      "TENANT_SUPER_ADMIN_ROLES",
      ""
    );
    this.superAdminRoles = superConfigRoles
      ? superConfigRoles.split(",").map((role) => role.trim() as Role)
      : [Role.SUPER_ADMIN];

    // 從配置檔讀取一般管理員角色列表
    const adminConfigRoles = this.configService.get<string>(
      "TENANT_ADMIN_ROLES",
      ""
    );
    this.adminRoles = adminConfigRoles
      ? adminConfigRoles.split(",").map((role) => role.trim() as Role)
      : [Role.SUPER_ADMIN, Role.SYSTEM_ADMIN, Role.TENANT_ADMIN];
  }

  @Post()
  @Roles(Role.SUPER_ADMIN)
  @ApiOperation({
    summary: "建立新租戶",
    description:
      "建立新的租戶帳號，包含基本資訊與管理員設定。僅限超級管理員使用。",
  })
  @ApiResponse({
    status: 201,
    description: "租戶建立成功",
    schema: {
      example: {
        id: "e4c8e5d0-e4d6-4d8c-8b19-3f43e8a22a0d",
        name: "範例企業",
        domain: "example",
        plan: "professional",
        status: "active",
        adminName: "管理員",
        adminEmail: "<EMAIL>",
        createdAt: "2023-05-01T08:00:00.000Z",
      },
    },
  })
  @ApiResponse({ status: 401, description: "未授權 - 需要身份驗證" })
  @ApiResponse({ status: 403, description: "禁止 - 僅限超級管理員" })
  @ApiResponse({ status: 409, description: "衝突 - 租戶網域已存在" })
  create(@Body() createTenantDto: CreateTenantDto): Promise<ExtendedTenant> {
    return this.tenantsService.create(createTenantDto);
  }

  @Get()
  @Roles(Role.SUPER_ADMIN)
  @ApiOperation({
    summary: "取得所有租戶",
    description:
      "讀取系統中所有租戶的列表，包含租戶基本資訊、使用者數量等統計。僅限超級管理員使用。",
  })
  @ApiResponse({
    status: 200,
    description: "成功讀取租戶列表",
    schema: {
      type: "array",
      items: {
        example: {
          id: "e4c8e5d0-e4d6-4d8c-8b19-3f43e8a22a0d",
          name: "範例企業",
          domain: "example",
          status: "active",
          usersCount: 5,
          createdAt: "2023-05-01T08:00:00.000Z",
        },
      },
    },
  })
  @ApiResponse({ status: 401, description: "未授權 - 需要身份驗證" })
  @ApiResponse({ status: 403, description: "禁止 - 僅限超級管理員" })
  findAll(): Promise<ExtendedTenant[]> {
    return this.tenantsService.findAll();
  }

  @Get(":id")
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: "取得單一租戶",
    description:
      "根據租戶 ID 讀取特定租戶的詳細資訊。租戶管理員可查詢自己的租戶，超級管理員可查詢任意租戶。",
  })
  @ApiParam({ name: "id", description: "租戶 ID，UUID 格式" })
  @ApiResponse({
    status: 200,
    description: "成功讀取租戶資訊",
    schema: {
      example: {
        id: "e4c8e5d0-e4d6-4d8c-8b19-3f43e8a22a0d",
        name: "範例企業",
        domain: "example",
        plan: "professional",
        status: "active",
        adminName: "管理員",
        adminEmail: "<EMAIL>",
        usersCount: 5,
        projectsCount: 3,
        maxUsers: 10,
        maxProjects: 5,
        maxStorage: 10,
        createdAt: "2023-05-01T08:00:00.000Z",
        updatedAt: "2023-05-10T09:30:00.000Z",
      },
    },
  })
  @ApiResponse({ status: 401, description: "未授權 - 需要身份驗證" })
  @ApiResponse({ status: 404, description: "找不到 - 租戶不存在" })
  findOne(@Param("id") id: string): Promise<ExtendedTenant> {
    return this.tenantsService.findOne(id);
  }

  @Patch(":id")
  @Roles(Role.SUPER_ADMIN, Role.SYSTEM_ADMIN, Role.TENANT_ADMIN)
  @ApiOperation({
    summary: "更新租戶資訊",
    description:
      "更新租戶的基本資訊、方案、狀態等。超級管理員可更新任意租戶，租戶管理員只能更新自己的租戶。",
  })
  @ApiParam({ name: "id", description: "租戶 ID，UUID 格式" })
  @ApiResponse({
    status: 200,
    description: "租戶資訊更新成功",
    schema: {
      example: {
        id: "e4c8e5d0-e4d6-4d8c-8b19-3f43e8a22a0d",
        name: "範例企業（已更新）",
        status: "active",
        updatedAt: "2023-05-15T10:20:00.000Z",
      },
    },
  })
  @ApiResponse({ status: 401, description: "未授權 - 需要身份驗證" })
  @ApiResponse({ status: 403, description: "禁止 - 權限不足" })
  @ApiResponse({ status: 404, description: "找不到 - 租戶不存在" })
  update(
    @Param("id") id: string,
    @Body() payload: UpdateTenantDto
  ): Promise<ExtendedTenant> {
    return this.tenantsService.update(id, payload);
  }

  @Delete(":id")
  @Roles(Role.SUPER_ADMIN)
  @ApiOperation({
    summary: "刪除租戶",
    description:
      "刪除指定的租戶及其所有相關資料。此操作不可逆，僅限超級管理員使用。",
  })
  @ApiParam({ name: "id", description: "租戶 ID，UUID 格式" })
  @ApiResponse({ status: 200, description: "租戶刪除成功" })
  @ApiResponse({ status: 401, description: "未授權 - 需要身份驗證" })
  @ApiResponse({ status: 403, description: "禁止 - 僅限超級管理員" })
  @ApiResponse({ status: 404, description: "找不到 - 租戶不存在" })
  remove(@Param("id") id: string) {
    return this.tenantsService.remove(id);
  }

  @Patch(":id/status")
  @Roles(Role.SUPER_ADMIN, Role.SYSTEM_ADMIN, Role.TENANT_ADMIN)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: "更新租戶狀態",
    description:
      "根據傳入的 status 參數設置租戶狀態，值可為 active 或 inactive。",
  })
  @ApiParam({ name: "id", description: "租戶 ID，UUID 格式" })
  @ApiResponse({ status: 200, description: "租戶狀態更新成功" })
  @ApiResponse({ status: 400, description: "請求格式錯誤" })
  @ApiResponse({ status: 401, description: "未授權" })
  @ApiResponse({ status: 403, description: "禁止" })
  @ApiResponse({ status: 404, description: "找不到租戶" })
  updateStatus(
    @Param("id") id: string,
    @Body() dto: UpdateTenantStatusDto
  ): Promise<ExtendedTenant> {
    return this.tenantsService.updateStatus(id, dto.status);
  }

  /**
   * 公司唯一性檢查 API
   * @param dto 公司名稱、國家/地區、網域
   * @returns 唯一性檢查結果
   */
  @Post("check-uniqueness")
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: "公司唯一性檢查",
    description: "檢查公司名稱、國家/地區、Email 網域是否重複或相似",
  })
  @ApiResponse({
    status: 200,
    description: "檢查結果",
    schema: {
      example: {
        available: true,
        message: "可用",
        conflicts: [],
        suggestions: { companyName: [], domain: [] },
      },
    },
  })
  async checkTenantUniqueness(@Body() dto: CheckTenantUniquenessDto) {
    if (!dto.companyName) throw new BadRequestException("公司名稱為必填");
    return this.tenantsService.checkTenantUniqueness({
      companyName: dto.companyName,
      domain: dto.domain,
    });
  }
}
