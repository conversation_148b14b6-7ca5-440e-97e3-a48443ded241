import {
  Injectable,
  NotFoundException,
  ConflictException,
  BadRequestException,
  InternalServerErrorException,
  Logger,
  ForbiddenException,
} from "@nestjs/common";
import { PrismaService } from "../../core/prisma/prisma.service";
import { CreateTenantDto } from "./dto/create-tenant.dto";
import { UpdateTenantDto } from "./dto/update-tenant.dto";
import { PrismaClient } from "@prisma/client";
import { ConfigService } from "@nestjs/config";
import { Plan } from "../../../types/prisma";
import * as crypto from "crypto";
import * as bcrypt from "bcryptjs";
import { Prisma } from "@prisma/client";

/**
 * 擴展的租戶型別，包含額外的欄位資訊
 */
export interface ExtendedTenant {
  id: string;
  name: string;
  domain?: string | null;
  plan?: Plan | null;
  status?: string;
  adminName?: string | null;
  adminEmail?: string | null;
  companySize?: string;
  industry?: string | null;
  maxUsers?: number;
  maxProjects?: number;
  maxStorage?: number;
  departments?: string[];
  createdAt: Date;
  updatedAt: Date;
}

/**
 * 租戶服務類別
 * 負責處理租戶的建立、查詢、更新和刪除操作
 */
@Injectable()
export class TenantsService {
  private readonly logger = new Logger(TenantsService.name);

  constructor(
    private prisma: PrismaService,
    private configService: ConfigService
  ) { }

  /**
   * 公司唯一性檢查
   * @param params 包含公司名稱、國家、網域
   * @returns 衝突資訊與建議
   */
  async checkTenantUniqueness(params: {
    companyName: string;
    domain?: string;
  }): Promise<{
    available: boolean;
    message: string;
    conflicts?: string[];
    suggestions?: { companyName?: string[]; domain?: string[] };
  }> {
    const conflicts: string[] = [];
    const suggestions: { companyName?: string[]; domain?: string[] } = {};

    // 1. 檢查公司名稱重複
    const nameExists = await this.prisma.tenants.findFirst({
      where: {
        name: { equals: params.companyName, mode: "insensitive" },
      },
    });
    if (nameExists) {
      conflicts.push("companyName");
      suggestions.companyName = [`${params.companyName}-1`];
    }

    // 2. 檢查網域重複
    if (params.domain) {
      const domainDup = await this.prisma.tenants.findFirst({
        where: { domain: { equals: params.domain, mode: "insensitive" } },
      });
      if (domainDup) {
        conflicts.push("domain");
        suggestions.domain = [`${params.domain}-new`];
      }
    }

    // 3. 公司名稱相似度比對
    const similarTenants = await this.prisma.tenants.findMany({
      where: {
        name: { not: params.companyName },
      },
      select: { name: true },
    });
    const getLevenshtein = (a: string, b: string) => {
      const matrix = Array.from({ length: a.length + 1 }, () =>
        Array(b.length + 1).fill(0)
      );
      for (let i = 0; i <= a.length; i++) matrix[i][0] = i;
      for (let j = 0; j <= b.length; j++) matrix[0][j] = j;
      for (let i = 1; i <= a.length; i++) {
        for (let j = 1; j <= b.length; j++) {
          matrix[i][j] = Math.min(
            matrix[i - 1][j] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j - 1] +
            (a[i - 1].toLowerCase() === b[j - 1].toLowerCase() ? 0 : 1)
          );
        }
      }
      return matrix[a.length][b.length];
    };
    const similar = similarTenants.filter(
      (t) => getLevenshtein(t.name, params.companyName) <= 2
    );
    if (similar.length > 0) {
      conflicts.push("companyName-similar");
      suggestions.companyName = [
        ...(suggestions.companyName || []),
        ...similar.map((t) => t.name),
      ];
    }

    return {
      available: conflicts.length === 0,
      message: conflicts.length === 0 ? "可用" : "有衝突",
      conflicts,
      suggestions,
    };
  }

  /**
   * 建立新租戶
   * @param createTenantDto 租戶建立資料
   * @returns 新建立的租戶資料
   */
  async create(createTenantDto: CreateTenantDto): Promise<ExtendedTenant> {
    this.logger.log(`開始建立租戶: ${createTenantDto.name}`);
    this.logger.debug("租戶建立資料:", createTenantDto);

    try {
      // 檢查是否已存在相同的網域
      if (createTenantDto.domain) {
        const existingTenant = await this.prisma.tenants.findUnique({
          where: { domain: createTenantDto.domain },
        });

        if (existingTenant) {
          this.logger.warn(`網域已存在: ${createTenantDto.domain}`);
          throw new ConflictException(
            `網域 '${createTenantDto.domain}' 已被使用`
          );
        }
      }

      // 取得預設值配置
      const defaultStatus = this.configService.get(
        "TENANT_DEFAULT_STATUS",
        "active"
      );
      const defaultCompanySize = this.configService.get(
        "TENANT_DEFAULT_COMPANY_SIZE",
        "1-10"
      );
      const defaultMaxUsers = parseInt(
        this.configService.get("TENANT_DEFAULT_MAX_USERS", "5")
      );
      const defaultMaxProjects = parseInt(
        this.configService.get("TENANT_DEFAULT_MAX_PROJECTS", "5")
      );
      const defaultMaxStorage = parseInt(
        this.configService.get("TENANT_DEFAULT_MAX_STORAGE", "5")
      );

      // 建立租戶基本資料，確保所有欄位都有預設值
      const tenantData: any = {
        name: createTenantDto.name,
        domain: createTenantDto.domain,
        status: createTenantDto.status || defaultStatus,
        adminName: createTenantDto.adminName || "",
        adminEmail: createTenantDto.adminEmail || "",
        companySize: createTenantDto.companySize || defaultCompanySize,
        industry: createTenantDto.industry || "",
        maxUsers: createTenantDto.maxUsers || defaultMaxUsers,
        maxProjects: createTenantDto.maxProjects || defaultMaxProjects,
        maxStorage: createTenantDto.maxStorage || defaultMaxStorage,
        departments: createTenantDto.departments || [],
      };

      // 使用交易確保租戶與管理員一起建立成功
      const result = await this.prisma.$transaction(async (prisma) => {
        // 建立租戶
        const tenant = await prisma.tenants.create({
          data: tenantData,
          include: {
            _count: {
              select: {
                tenant_users: true,
              },
            },
          },
        });

        this.logger.debug(`建立租戶成功: ${tenant.id}`);

        let adminUser: any | null = null;

        // 如果提供了管理員資訊，建立管理員帳號
        if (createTenantDto.adminEmail) {
          try {
            // 檢查是否已存在相同的 email
            const existingUser = await prisma.tenant_users.findUnique({
              where: { email: createTenantDto.adminEmail },
            });

            if (existingUser) {
              this.logger.warn(
                `管理員信箱已存在: ${createTenantDto.adminEmail}`
              );
            } else {
              // 生成隨機密碼
              const tempPassword = this.generateTempPassword();
              const hashedPassword = await bcrypt.hash(tempPassword, 10);

              // 建立管理員使用者
              adminUser = await prisma.tenant_users.create({
                data: {
                  name: createTenantDto.adminName || "",
                  email: createTenantDto.adminEmail,
                  password: hashedPassword,
                  role: "TENANT_ADMIN",
                  tenantId: tenant.id,
                },
              });

              if (adminUser) {
                this.logger.debug(`建立管理員成功: ${adminUser.id}`);
              }
            }
          } catch (err) {
            this.logger.error(`無法建立管理員帳號: ${err.message}`);
            // 交易會自動回滾，所以這裡的錯誤會導致整個建立失敗
            throw err;
          }
        }

        return { tenant, adminUser };
      });

      this.logger.log(`租戶建立成功: ${result.tenant.id}`);

      // 取得預設方案
      const defaultPlanName = this.configService.get(
        "TENANT_DEFAULT_PLAN",
        "basic"
      );
      const defaultPlan: Plan = {
        id: "",
        name: defaultPlanName,
        description: "Default plan",
        price: 0,
        billingCycle: "MONTHLY",
        maxUsers: 1,
        maxProjects: 1,
        maxStorage: 0,
        isPopular: false,
        features: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        monthlyAiCreditsLimit: null,
      };

      // 建立擴展的租戶資料
      const extendedTenant: ExtendedTenant = {
        id: result.tenant.id,
        name: result.tenant.name,
        domain: result.tenant.domain || null,
        plan: (result.tenant as any).planRelation || defaultPlan,
        status: result.tenant.status || defaultStatus,
        adminName: result.adminUser?.name ?? null,
        adminEmail: result.adminUser?.email ?? null,
        companySize: result.tenant.companySize || defaultCompanySize,
        industry: result.tenant.industry || null,
        maxUsers: result.tenant.maxUsers || defaultMaxUsers,
        maxProjects: result.tenant.maxProjects || defaultMaxProjects,
        maxStorage: result.tenant.maxStorage || defaultMaxStorage,
        departments: result.tenant.departments || [],
        createdAt: result.tenant.createdAt,
        updatedAt: result.tenant.updatedAt,
      };

      return extendedTenant;
    } catch (error) {
      if (error instanceof ConflictException) {
        // 重複網域錯誤直接拋出
        throw error;
      }

      this.logger.error(`建立租戶失敗: ${error.message}`);
      throw new InternalServerErrorException("建立租戶時發生錯誤");
    }
  }

  /**
   * 查詢所有租戶
   * @param tenantId 可選，用於在特定租戶上下文中過濾
   * @returns 租戶列表
   */
  async findAll(tenantId?: string): Promise<ExtendedTenant[]> {
    const where: Prisma.tenantsWhereInput = {};
    if (tenantId) {
      where.id = tenantId;
    }

    this.logger.log("開始查詢所有租戶");

    try {
      // 查詢所有租戶資料，包含使用者計數，顯式指定需要的欄位
      const tenants = await this.prisma.tenants.findMany({
        where,
        select: {
          id: true,
          name: true,
          domain: true,
          status: true,
          adminName: true,
          adminEmail: true,
          companySize: true,
          industry: true,
          planId: true,
          maxUsers: true,
          maxProjects: true,
          maxStorage: true,
          departments: true,
          createdAt: true,
          updatedAt: true,
          _count: {
            select: {
              tenant_users: true,
            },
          },
        },
      });

      this.logger.debug(`找到 ${tenants.length} 個租戶`);

      if (tenants.length > 0) {
        this.logger.debug("租戶資料範例:", JSON.stringify(tenants[0], null, 2));
      }

      if (!tenants.length && tenantId) {
        throw new NotFoundException(`在指定的租戶上下文中找不到租戶`);
      }

      // 取得預設值配置
      const defaultPlanName = this.configService.get(
        "TENANT_DEFAULT_PLAN",
        "basic"
      );
      const defaultStatus = this.configService.get(
        "TENANT_DEFAULT_STATUS",
        "active"
      );
      const defaultCompanySize = this.configService.get(
        "TENANT_DEFAULT_COMPANY_SIZE",
        "1-10"
      );
      const defaultMaxUsers = parseInt(
        this.configService.get("TENANT_DEFAULT_MAX_USERS", "5")
      );
      const defaultMaxProjects = parseInt(
        this.configService.get("TENANT_DEFAULT_MAX_PROJECTS", "10")
      );
      const defaultMaxStorage = parseInt(
        this.configService.get("TENANT_DEFAULT_MAX_STORAGE", "10")
      );

      // 建立預設 Plan 對象
      const defaultPlan: Plan = {
        id: "",
        name: defaultPlanName,
        description: "Default plan",
        price: 0,
        billingCycle: "MONTHLY",
        maxUsers: defaultMaxUsers,
        maxProjects: defaultMaxProjects,
        maxStorage: defaultMaxStorage,
        isPopular: false,
        features: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        monthlyAiCreditsLimit: null,
      };

      // 對每個租戶查詢管理員資訊並建立擴展資料
      const result = await Promise.all(
        tenants.map(async (tenant) => {
          // 查詢租戶的管理員使用者
          const adminUsers = await this.prisma.tenant_users.findMany({
            where: {
              tenantId: tenant.id,
              role: "TENANT_ADMIN", // 用 enum 型別
            },
            select: {
              id: true,
              name: true,
              email: true,
            },
            take: 1,
          });

          // 查詢租戶關聯的計畫
          let plan: Plan | null = null;
          if (tenant.planId) {
            plan = await this.prisma.plans.findUnique({
              where: { id: tenant.planId },
            });
          }

          // 取得管理員資訊（如果存在）
          const admin = adminUsers.length > 0 ? adminUsers[0] : null;

          // 建立擴展的租戶資料
          return {
            id: tenant.id,
            name: tenant.name,
            domain: tenant.domain || "",
            plan: plan || defaultPlan,
            status: tenant.status || defaultStatus,
            adminName: admin?.name || "",
            adminEmail: admin?.email || "",
            companySize: tenant.companySize || defaultCompanySize,
            industry: tenant.industry || "",
            maxUsers: tenant.maxUsers || defaultMaxUsers,
            maxProjects: tenant.maxProjects || defaultMaxProjects,
            maxStorage: tenant.maxStorage || defaultMaxStorage,
            departments: tenant.departments || [],
            createdAt: tenant.createdAt,
            updatedAt: tenant.updatedAt,
            usersCount: tenant._count?.tenant_users || 0,
            projectsCount: 0, // 未來應以實際專案數替換
          } as ExtendedTenant;
        })
      );

      this.logger.log(`完成查詢所有租戶，共 ${result.length} 筆資料`);
      return result;
    } catch (error) {
      this.logger.error(`查詢租戶失敗: ${error.message}`);
      throw error;
    }
  }

  /**
   * 根據 ID 查詢單一租戶
   * @param id 租戶 ID
   * @param tenantId 可選，用於在特定租戶上下文中驗證
   * @returns 租戶資料
   */
  async findOne(id: string, tenantId?: string): Promise<ExtendedTenant> {
    const where: Prisma.tenantsWhereUniqueInput = { id };
    if (tenantId && id !== tenantId) {
      // 如果提供了 tenantId 但與查詢的 id 不匹配，這是一個無效的請求
      throw new ForbiddenException("您無權訪問此租戶的資源。");
    }

    this.logger.log(`開始查詢租戶: ${id}`);

    try {
      // 直接查詢租戶，避免使用事務和複雜關聯，顯式指定需要的欄位，避免查詢不存在的欄位
      const tenant = await this.prisma.tenants.findUnique({
        where,
        select: {
          id: true,
          name: true,
          domain: true,
          status: true,
          adminName: true,
          adminEmail: true,
          companySize: true,
          industry: true,
          planId: true,
          maxUsers: true,
          maxProjects: true,
          maxStorage: true,
          departments: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      if (!tenant) {
        throw new NotFoundException(`找不到 ID 為 ${id} 的租戶`);
      }

      // 單獨查詢管理員使用者
      const adminUsers = await this.prisma.tenant_users.findMany({
        where: {
          tenantId: id,
          role: "TENANT_ADMIN", // 用 enum 型別
        },
        select: {
          id: true,
          name: true,
          email: true,
        },
        take: 1,
      });

      // 單獨查詢關聯的計畫
      let plan: Plan | null = null;
      if (tenant.planId) {
        plan = await this.prisma.plans.findUnique({
          where: { id: tenant.planId },
        });
      }

      // 取得預設值配置
      const defaultPlanName = this.configService.get(
        "TENANT_DEFAULT_PLAN",
        "basic"
      );
      const defaultStatus = this.configService.get(
        "TENANT_DEFAULT_STATUS",
        "active"
      );
      const defaultCompanySize = this.configService.get(
        "TENANT_DEFAULT_COMPANY_SIZE",
        "1-10"
      );
      const defaultMaxUsers = parseInt(
        this.configService.get("TENANT_DEFAULT_MAX_USERS", "5")
      );
      const defaultMaxProjects = parseInt(
        this.configService.get("TENANT_DEFAULT_MAX_PROJECTS", "10")
      );
      const defaultMaxStorage = parseInt(
        this.configService.get("TENANT_DEFAULT_MAX_STORAGE", "10")
      );

      // 預設 Plan 對象
      const defaultPlan: Plan = {
        id: "",
        name: defaultPlanName,
        description: "Default plan",
        price: 0,
        billingCycle: "MONTHLY",
        maxUsers: defaultMaxUsers,
        maxProjects: defaultMaxProjects,
        maxStorage: defaultMaxStorage,
        isPopular: false,
        features: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        monthlyAiCreditsLimit: null,
      };

      // 管理員使用者
      const adminUser = adminUsers.length > 0 ? adminUsers[0] : null;

      // 建立擴展的租戶資料
      const extendedTenant: ExtendedTenant = {
        id: tenant.id,
        name: tenant.name,
        domain: tenant.domain || null,
        plan: plan || defaultPlan,
        status: tenant.status || defaultStatus,
        adminName: adminUser?.name ?? null,
        adminEmail: adminUser?.email ?? null,
        companySize: tenant.companySize || defaultCompanySize,
        industry: tenant.industry || null,
        maxUsers: tenant.maxUsers || defaultMaxUsers,
        maxProjects: tenant.maxProjects || defaultMaxProjects,
        maxStorage: tenant.maxStorage || defaultMaxStorage,
        departments: tenant.departments || [],
        createdAt: tenant.createdAt,
        updatedAt: tenant.updatedAt,
      };

      this.logger.log(`完成查詢租戶: ${id}`);
      return extendedTenant;
    } catch (error) {
      this.logger.error(`Failed to retrieve tenant with ID ${id}:`, error);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException("Could not retrieve tenant");
    }
  }

  /**
   * 更新租戶資料
   * @param id 租戶 ID
   * @param payload 更新的租戶資料 payload
   * @returns 更新後的租戶資料
   */
  async update(id: string, payload: UpdateTenantDto): Promise<ExtendedTenant> {
    this.logger.log(`開始更新租戶: ${id}`);
    this.logger.debug("傳入的 Payload DTO:", JSON.stringify(payload, null, 2));

    try {
      // 確認租戶是否存在 (findOne still returns ExtendedTenant)
      await this.findOne(id);

      const data: any = {};
      let planIdToConnect: string | undefined = undefined;

      // 從 payload 中取出要更新的欄位
      for (const key in payload) {
        if (
          Object.prototype.hasOwnProperty.call(payload, key) &&
          payload[key] !== undefined
        ) {
          if (key === "nextBillingDate") {
            data[key] = payload[key] ? new Date(payload[key]) : null;
          } else if (["maxUsers", "maxProjects", "maxStorage"].includes(key)) {
            data[key] = Number(payload[key]);
          } else if (
            ["plan", "lastBillingDate", "contractEndDate"].includes(key)
          ) {
            this.logger.debug(
              `跳過 ${key} 欄位 (${payload[key]})，此欄位不存在於資料庫結構中`
            );
            continue;
          } else if (key === "planId") {
            planIdToConnect = payload[key];
            continue;
          } else {
            data[key] = payload[key];
          }
        }
      }

      if (planIdToConnect !== undefined) {
        if (planIdToConnect) {
          data.planId = planIdToConnect;
        } else {
          data.planId = null;
        }
      }

      // 更新租戶，不立即包含 planRelation，因為我們會在後面重新查詢
      const updatedTenantPrisma = await this.prisma.tenants.update({
        where: { id },
        data,
        select: {
          id: true,
          name: true,
          domain: true,
          status: true,
          adminName: true,
          adminEmail: true,
          companySize: true,
          industry: true,
          planId: true,
          maxUsers: true,
          maxProjects: true,
          maxStorage: true,
          departments: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      // Admin user update logic
      let adminUser: any | null = null;
      if (payload.adminEmail) {
        const adminUsers = await this.prisma.tenant_users.findMany({
          where: {
            tenantId: id,
            role: "TENANT_ADMIN", // 用 enum 型別
          },
          select: {
            id: true,
            name: true,
            email: true,
          },
          take: 1,
        });

        if (adminUsers.length > 0) {
          adminUser = await this.prisma.tenant_users.update({
            where: { id: adminUsers[0].id },
            data: {
              name: payload.adminName,
              email: payload.adminEmail,
            },
          });
        } else {
          const existingUser = await this.prisma.tenant_users.findUnique({
            where: { email: payload.adminEmail },
          });
          if (!existingUser) {
            const tempPassword = this.generateTempPassword();
            const hashedPassword = await bcrypt.hash(tempPassword, 10);
            adminUser = await this.prisma.tenant_users.create({
              data: {
                name: payload.adminName || "",
                email: payload.adminEmail,
                password: hashedPassword,
                role: "TENANT_ADMIN", // 用 enum 型別
                tenantId: id,
              },
            });
          }
        }
      }

      // 重新查詢以讀取包含 planRelation 的完整 ExtendedTenant
      this.logger.log(
        `完成更新租戶: ${id}, 正在重新查詢以組合 ExtendedTenant...`
      );
      return this.findOne(id);
    } catch (error) {
      this.logger.error(`更新租戶失敗: ${error.message}`);
      throw error;
    }
  }

  /**
   * 刪除租戶及其所有相關資料（交易式）
   * @param id 租戶 ID
   */
  async remove(id: string) {
    this.logger.warn(`即將刪除租戶及所有相關資料: ${id}`);
    // 先確認租戶存在
    await this.findOne(id);
    try {
      await this.prisma.$transaction(async (prisma) => {
        // 1. 刪除租戶邀請
        await prisma.tenant_invitations.deleteMany({
          where: { tenantId: id },
        });
        // 2. 刪除租戶使用者
        await prisma.tenant_users.deleteMany({ where: { tenantId: id } });
        // 3. 刪除租戶下的專案
        await prisma.projects.deleteMany({ where: { tenantId: id } });
        // 4. 刪除租戶下的相簿
        await prisma.albums.deleteMany({ where: { tenantId: id } });
        // 5. 刪除租戶下的照片
        await prisma.photos.deleteMany({ where: { tenantId: id } });
        // 6. 刪除租戶下的工作空間
        await prisma.workspaces.deleteMany({ where: { tenantId: id } });
        // 7. 刪除租戶下的角色
        await prisma.roles.deleteMany({ where: { tenantId: id } });
        // 8. 刪除租戶下的訂閱
        await prisma.subscriptions.deleteMany({ where: { tenantId: id } });
        // 9. 刪除租戶下的訂單
        await prisma.orders.deleteMany({ where: { tenantId: id } });
        // 10. 刪除租戶下的點數購買紀錄
        await prisma.tenant_credit_purchases.deleteMany({
          where: { tenantId: id },
        });
        // 11. 刪除租戶下的 AI Bot
        await prisma.ai_bots.deleteMany({ where: { tenant_id: id } });
        // 12. 刪除租戶下的 LineBot
        await prisma.line_bots.deleteMany({ where: { tenant_id: id } });
        // 13. 刪除租戶下的 Line 群組驗證
        await prisma.line_group_verifications.deleteMany({
          where: { tenant_id: id },
        });
        // 14. 刪除租戶下的 Line 訊息紀錄
        await prisma.line_message_logs.deleteMany({ where: { tenant_id: id } });
        // 16. 最後刪除租戶本身
        await prisma.tenants.delete({ where: { id } });
      });
      this.logger.warn(`租戶 ${id} 及所有相關資料已成功刪除`);
      return { message: `租戶 ${id} 及所有相關資料已刪除` };
    } catch (error) {
      this.logger.error(`刪除租戶失敗: ${error.message}`);
      throw new BadRequestException("刪除租戶失敗: " + error.message);
    }
  }

  /**
   * 更新租戶狀態
   * @param id 租戶 ID
   * @param status 新的狀態 ('active' 或 'inactive')
   * @returns 更新後的擴展租戶資料
   */
  async updateStatus(
    id: string,
    status: "active" | "inactive"
  ): Promise<ExtendedTenant> {
    this.logger.log(`開始更新租戶狀態: ${id}, 新狀態: ${status}`);

    try {
      // 確認租戶存在
      await this.findOne(id);

      // 更新狀態
      await this.prisma.tenants.update({
        where: { id },
        data: { status },
      });

      this.logger.log(`租戶狀態更新成功: ${id}`);

      // 返回更新後的租戶資料
      return this.findOne(id);
    } catch (error) {
      this.logger.error(`更新租戶狀態失敗: ${error.message}`);
      throw error;
    }
  }

  /**
   * 搜尋租戶
   * @param query 搜尋關鍵字
   * @param limit 限制返回的租戶數量
   * @returns 符合條件的租戶列表
   */
  async searchTenants(query: string, limit: number = 10): Promise<ExtendedTenant[]> {
    this.logger.log(`搜尋租戶: ${query}`);

    try {
      const tenants = await this.prisma.tenants.findMany({
        where: {
          OR: [
            { name: { contains: query, mode: 'insensitive' } },
            { domain: { contains: query, mode: 'insensitive' } },
            { adminName: { contains: query, mode: 'insensitive' } },
            { adminEmail: { contains: query, mode: 'insensitive' } },
          ],
        },
        include: { plans: true },
        orderBy: { createdAt: 'desc' },
        take: limit,
      });

      return tenants.map(({ plans, companySize, maxUsers, maxProjects, maxStorage, ...rest }) => ({
        ...rest,
        plan: plans || null,
        companySize: companySize ?? undefined,
        maxUsers: maxUsers ?? undefined,
        maxProjects: maxProjects ?? undefined,
        maxStorage: maxStorage ?? undefined,
      }));
    } catch (error) {
      this.logger.error(`搜尋租戶失敗: ${error.message}`);
      throw new InternalServerErrorException('搜尋租戶失敗');
    }
  }

  /**
   * 產生臨時密碼
   * @returns 8位隨機密碼
   */
  private generateTempPassword(): string {
    const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let password = '';
    for (let i = 0; i < 8; i++) {
      password += charset.charAt(Math.floor(Math.random() * charset.length));
    }
    return password;
  }
}