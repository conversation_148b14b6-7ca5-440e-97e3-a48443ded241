import {
  IsString,
  IsOptional,
  Is<PERSON><PERSON>y,
  IsBoolean,
  IsEnum,
  ValidateIf,
} from "class-validator";
import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";

export enum RoleScope {
  SYSTEM = "SYSTEM",
  TENANT = "TENANT",
  WORKSPACE = "WORKSPACE",
}

export class CreateRoleDto {
  @ApiProperty({ description: "角色名稱 (系統識別碼)" })
  @IsString()
  name: string;

  @ApiProperty({ description: "角色顯示名稱" })
  @IsString()
  display_name: string;

  @ApiPropertyOptional({ description: "角色描述" })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: "角色使用區域",
    enum: RoleScope,
    example: RoleScope.TENANT,
  })
  @IsEnum(RoleScope)
  scope: RoleScope;

  @ApiPropertyOptional({
    description: "租戶 ID (當 scope 為 TENANT 或 WORKSPACE 時需要)",
  })
  @IsString()
  @ValidateIf(
    (o) => o.scope === RoleScope.TENANT || o.scope === RoleScope.WORKSPACE
  )
  @IsOptional()
  tenant_id?: string;

  @ApiPropertyOptional({
    description: "角色權限列表 (Permission IDs)",
    type: [String],
  })
  @IsArray()
  @IsOptional()
  permissions?: string[];

  @ApiPropertyOptional({ description: "是否為系統角色", default: false })
  @IsBoolean()
  @IsOptional()
  is_system_role?: boolean;

  @ApiPropertyOptional({ description: "父角色 ID (可選)" })
  @IsString()
  @IsOptional()
  parent_role_id?: string;
}

export class UpdateRoleDto {
  @ApiPropertyOptional({ description: "角色名稱 (系統識別碼)" })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiPropertyOptional({ description: "角色顯示名稱" })
  @IsString()
  @IsOptional()
  display_name?: string;

  @ApiPropertyOptional({ description: "角色描述" })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({
    description: "角色使用區域",
    enum: RoleScope,
  })
  @IsEnum(RoleScope)
  @IsOptional()
  scope?: RoleScope;

  @ApiPropertyOptional({
    description: "租戶 ID (當 scope 為 TENANT 或 WORKSPACE 時需要)",
  })
  @IsString()
  @IsOptional()
  @ValidateIf(
    (o) => o.scope === RoleScope.TENANT || o.scope === RoleScope.WORKSPACE
  )
  tenant_id?: string;

  @ApiPropertyOptional({
    description: "角色權限列表 (Permission IDs)",
    type: [String],
  })
  @IsArray()
  @IsOptional()
  permissions?: string[];

  @ApiPropertyOptional({ description: "是否為系統角色" })
  @IsBoolean()
  @IsOptional()
  is_system_role?: boolean;

  @ApiPropertyOptional({ description: "父角色 ID (可選)" })
  @IsString()
  @IsOptional()
  parent_role_id?: string;
}

export class UpdateRolePermissionsDto {
  @ApiProperty({ description: "角色權限列表 (Permission IDs)", type: [String] })
  @IsArray()
  permissions: string[];
}

export class RoleResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  name: string;

  @ApiProperty()
  displayName: string;

  @ApiPropertyOptional({ nullable: true })
  @IsOptional()
  description?: string | null;

  @ApiProperty()
  isSystem: boolean;

  @ApiProperty({ enum: RoleScope })
  scope: RoleScope;

  @ApiPropertyOptional({ description: "租戶 ID" })
  @IsString()
  @IsOptional()
  tenantId?: string | null;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;
}

export class RoleWithPermissionsResponseDto extends RoleResponseDto {
  @ApiProperty({ type: [String] })
  permissions: string[];
}

export class PermissionCategoryDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  name: string;

  @ApiProperty()
  description: string;

  @ApiPropertyOptional()
  icon?: string;

  @ApiPropertyOptional()
  sortOrder?: number;

  @ApiPropertyOptional()
  isActive?: boolean;
}

export class PermissionDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  name: string;

  @ApiProperty()
  description: string;

  @ApiProperty()
  categoryId: string;
}

export class RoleUserCountResponseDto {
  @ApiProperty()
  count: number;
}
