import {
  Injectable,
  NotFoundException,
  ConflictException,
  ForbiddenException,
} from "@nestjs/common";
import { PrismaService } from "../../core/prisma/prisma.service";
import {
  CreateRoleDto,
  UpdateRoleDto,
  RoleScope,
  PermissionDto,
  PermissionCategoryDto,
} from "./dto/role.dto";
import { PrismaClient } from "@prisma/client";

// 自定義 Permission 類型，因為 Prisma 客戶端不再導出
interface Permission {
  id: string;
  action: string;
  subject: string;
  conditions?: any;
  description?: string;
  categoryId?: string;
  createdAt: Date;
  updatedAt: Date;
}

@Injectable()
export class RolesService {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * 讀取所有角色，可依區域篩選
   */
  async findAll(scope?: RoleScope): Promise<any[]> {
    const where: any = {};
    if (scope) {
      where.scope = scope;
    }
    return this.prisma.roles.findMany({
      where,
      orderBy: {
        createdAt: "asc",
      },
    });
  }

  /**
   * 根據 ID 讀取角色詳情，包含權限
   */
  async findOne(id: string): Promise<any & { permissions: string[] }> {
    const role = await this.prisma.roles.findUnique({
      where: { id },
      include: {
        role_permissions: {
          select: {
            permissionId: true,
          },
        },
      },
    });

    if (!role) {
      throw new NotFoundException(`找不到 ID 為 ${id} 的角色`);
    }

    let permissionIds = role.role_permissions.map((rp) => rp.permissionId);

    // 特殊處理 SUPER_ADMIN 角色
    if (role.name === "SUPER_ADMIN") {
      // 獲取所有權限
      const allPermissions = await this.prisma.permissions.findMany({
        select: { id: true },
      });
      permissionIds = allPermissions.map((p) => p.id);
    }

    return {
      ...role,
      permissions: permissionIds,
    };
  }

  /**
   * 建立新角色
   */
  async create(createRoleDto: CreateRoleDto): Promise<any> {
    const {
      name,
      display_name,
      description,
      scope,
      tenant_id,
      permissions,
      is_system_role,
      parent_role_id,
    } = createRoleDto;

    const existingRole = await this.prisma.roles.findUnique({
      where: { name },
    });

    if (existingRole) {
      throw new ConflictException(`角色名稱 ${name} 已存在`);
    }

    if (
      (scope === RoleScope.TENANT || scope === RoleScope.WORKSPACE) &&
      !tenant_id
    ) {
      throw new ConflictException(
        `當 scope 為 TENANT 或 WORKSPACE 時，tenant_id 為必填`
      );
    }
    if (scope === RoleScope.SYSTEM && tenant_id) {
      throw new ConflictException(`當 scope 為 SYSTEM 時，tenant_id 必須為空`);
    }

    return this.prisma.$transaction(async (tx) => {
      const roleData: any = {
        name,
        displayName: display_name,
        description,
        scope,
        isSystem: is_system_role ?? false,
        tenant:
          (scope === RoleScope.TENANT || scope === RoleScope.WORKSPACE) &&
          tenant_id
            ? { connect: { id: tenant_id } }
            : undefined,
        parentRole: parent_role_id
          ? { connect: { id: parent_role_id } }
          : undefined,
      };

      const role = await tx.roles.create({
        data: roleData,
      });

      if (permissions && permissions.length > 0) {
        await this.createRolePermissionMappings(tx, role.id, permissions);
      }

      return role;
    });
  }

  /**
   * 更新角色
   */
  async update(id: string, updateRoleDto: UpdateRoleDto): Promise<any> {
    const {
      name,
      display_name,
      description,
      scope,
      tenant_id,
      permissions,
      is_system_role,
      parent_role_id,
    } = updateRoleDto;

    const existingRole = await this.prisma.roles.findUnique({
      where: { id },
    });

    if (!existingRole) {
      throw new NotFoundException(`找不到 ID 為 ${id} 的角色`);
    }

    if (name && name !== existingRole.name) {
      const roleWithSameName = await this.prisma.roles.findUnique({
        where: { name },
      });
      if (roleWithSameName && roleWithSameName.id !== id) {
        throw new ConflictException(`角色名稱 ${name} 已存在`);
      }
    }

    const currentScope = scope ?? existingRole.scope;
    const currentTenantId = tenant_id ?? existingRole.tenantId;

    if (
      (currentScope === RoleScope.TENANT ||
        currentScope === RoleScope.WORKSPACE) &&
      !currentTenantId
    ) {
      throw new ConflictException(
        `當 scope 為 TENANT 或 WORKSPACE 時，tenant_id 為必填`
      );
    }
    if (currentScope === RoleScope.SYSTEM && currentTenantId) {
      throw new ConflictException(`當 scope 為 SYSTEM 時，tenant_id 必須為空`);
    }

    return this.prisma.$transaction(async (tx) => {
      const updateData: any = {
        name: name ?? existingRole.name,
        displayName: display_name ?? existingRole.displayName,
        description: description ?? existingRole.description,
        scope: scope ?? existingRole.scope,
        isSystem: is_system_role ?? existingRole.isSystem,
        tenant:
          (scope === RoleScope.TENANT || scope === RoleScope.WORKSPACE) &&
          tenant_id
            ? { connect: { id: tenant_id } }
            : scope === RoleScope.SYSTEM
              ? { disconnect: true }
              : undefined,
        parentRole:
          parent_role_id !== undefined
            ? parent_role_id
              ? { connect: { id: parent_role_id } }
              : { disconnect: true }
            : undefined,
      };

      const updatedRole = await tx.roles.update({
        where: { id },
        data: updateData,
      });

      if (permissions) {
        await tx.role_permissions.deleteMany({
          where: { roleId: id },
        });
        if (permissions.length > 0) {
          await this.createRolePermissionMappings(tx, id, permissions);
        }
      }

      return updatedRole;
    });
  }

  /**
   * 刪除角色
   */
  async remove(id: string): Promise<{ success: boolean; message: string }> {
    const role = await this.prisma.roles.findUnique({
      where: { id },
      include: {
        system_user_roles: { select: { system_user_id: true } },
        tenant_user_roles: { select: { tenant_user_id: true } },
      },
    });

    if (!role) {
      throw new NotFoundException(`找不到 ID 為 ${id} 的角色`);
    }

    if (role.isSystem) {
      throw new ForbiddenException("無法刪除系統角色");
    }

    const totalUserCount =
      (role.system_user_roles?.length || 0) +
      (role.tenant_user_roles?.length || 0);
    if (totalUserCount > 0) {
      throw new ForbiddenException(
        `無法刪除角色，該角色仍有 ${totalUserCount} 個使用者在使用`
      );
    }

    await this.prisma.$transaction(async (tx) => {
      await tx.role_permissions.deleteMany({
        where: { roleId: id },
      });
      await tx.roles.delete({
        where: { id },
      });
    });

    return { success: true, message: "角色已成功刪除" };
  }

  /**
   * 更新角色權限
   */
  async updateRolePermissions(
    roleId: string,
    permissionIds: string[]
  ): Promise<{ success: boolean; message: string }> {
    const role = await this.prisma.roles.findUnique({
      where: { id: roleId },
    });

    if (!role) {
      throw new NotFoundException(`找不到 ID 為 ${roleId} 的角色`);
    }

    await this.prisma.$transaction(async (tx) => {
      await tx.role_permissions.deleteMany({
        where: { roleId: roleId },
      });

      if (permissionIds && permissionIds.length > 0) {
        await this.createRolePermissionMappings(tx, roleId, permissionIds);
      }
    });

    return { success: true, message: "角色權限已更新" };
  }

  /**
   * 讀取指定角色的使用者數量
   */
  async getUserCount(roleId: string): Promise<number> {
    const systemUserCount = await this.prisma.system_user_roles.count({
      where: { role_id: roleId },
    });

    const tenantUserCount = await this.prisma.tenant_user_roles.count({
      where: { role_id: roleId },
    });

    return systemUserCount + tenantUserCount;
  }

  /**
   * 格式化權限名稱供前端顯示
   */
  private formatPermissionName(permission: {
    action: string;
    subject: string;
  }): string {
    // 簡單的權限名稱格式化邏輯
    return `${permission.action}:${permission.subject}`;
  }

  /**
   * 建立角色權限映射
   */
  private async createRolePermissionMappings(
    tx: any,
    roleId: string,
    permissionIds: string[]
  ): Promise<void> {
    const existingPermissions = await tx.permissions.findMany({
      where: { id: { in: permissionIds } },
      select: { id: true },
    });

    const existingPermissionIds = new Set(existingPermissions.map((p) => p.id));
    const notFoundPermissions = permissionIds.filter(
      (pid) => !existingPermissionIds.has(pid)
    );

    if (notFoundPermissions.length > 0) {
      throw new NotFoundException(
        `以下權限 ID 不存在: ${notFoundPermissions.join(", ")}`
      );
    }

    const data = permissionIds.map((permissionId) => ({
      roleId: roleId,
      permissionId: permissionId,
    }));
    await tx.role_permissions.createMany({
      data: data,
    });
  }

  /**
   * 根據名稱和租戶 ID 查詢角色
   */
  async findByNameAndTenant(
    name: string,
    tenantId: string
  ): Promise<any | null> {
    return this.prisma.roles.findFirst({
      where: {
        name,
        tenantId: tenantId,
      },
    });
  }

  /**
   * 獲取所有權限
   */
  async getAllPermissions(): Promise<
    Array<{
      id: string;
      name: string;
      description: string;
      categoryId: string;
    }>
  > {
    const permissions = await this.prisma.$queryRaw<
      Array<{
        id: string;
        action: string;
        subject: string;
        description: string | null;
        categoryId: string | null;
      }>
    >`
      SELECT id, action, subject, description, "categoryId"
      FROM permissions 
      ORDER BY "createdAt" ASC
    `;

    // 獲取所有分類以建立名稱到 ID 的映射
    const categories = await this.prisma.$queryRaw<
      Array<{
        id: string;
        name: string;
      }>
    >`
      SELECT id, name 
      FROM permission_categories 
      WHERE "isActive" = true
    `;

    const categoryNameToIdMap = new Map(
      categories.map((cat) => [cat.name, cat.id])
    );

    return permissions.map((permission) => {
      let categoryId = permission.categoryId;

      // 如果權限沒有分類 ID，根據 subject 推斷
      if (!categoryId) {
        const categoryName = this.getPermissionCategoryIdSync(permission);
        categoryId =
          categoryNameToIdMap.get(categoryName) || categories[0]?.id || "";
      }

      return {
        id: permission.id,
        name: this.formatPermissionName(permission),
        description: permission.description || "",
        categoryId: categoryId,
      };
    });
  }

  /**
   * 根據權限決定其所屬類別 ID（同步版本）
   * 如果權限已有 categoryId 則直接返回，否則根據 subject 推斷並返回對應的分類名稱
   */
  private getPermissionCategoryIdSync(permission: any): string {
    // 如果權限已經有分類 ID，直接返回
    if (permission.categoryId) {
      return permission.categoryId;
    }

    // 否則根據 subject 推斷分類名稱（這些名稱對應資料庫中的分類名稱）
    const subject = permission.subject.toLowerCase();
    const action = permission.action.toLowerCase();

    if (
      subject.includes("user") ||
      subject.includes("role") ||
      subject.includes("permission")
    ) {
      return "user-management";
    }
    if (subject.includes("workspace")) {
      return "workspace-management";
    }
    if (subject.includes("project") || subject.includes("task")) {
      return "project-management";
    }
    if (
      subject.includes("billing") ||
      subject.includes("subscription") ||
      subject.includes("plan") ||
      subject.includes("order")
    ) {
      return "subscription-management";
    }
    if (subject.includes("report") || subject.includes("analytic")) {
      return "analytics";
    }
    if (
      subject.includes("ai") ||
      subject.includes("bot") ||
      subject.includes("model") ||
      subject.includes("key")
    ) {
      return "ai-management";
    }
    if (subject.includes("line")) {
      return "line-management";
    }
    if (subject.includes("tenant")) {
      return "tenant-management";
    }
    if (subject.includes("client")) {
      return "client-management";
    }
    if (subject.includes("member") || subject.includes("department")) {
      return "member-management";
    }
    if (subject.includes("form")) {
      return "form-management";
    }
    if (subject.includes("chat") || subject.includes("message")) {
      return "chat-management";
    }
    if (
      subject.includes("file") ||
      subject.includes("photo") ||
      subject.includes("document")
    ) {
      return "file-management";
    }
    return "system-settings";
  }

  /**
   * 獲取所有權限分類
   */
  async getAllPermissionCategories(): Promise<
    Array<{
      id: string;
      name: string;
      description: string;
      icon?: string;
      sortOrder?: number;
      isActive?: boolean;
    }>
  > {
    const categories = await this.prisma.$queryRaw<
      Array<{
        id: string;
        name: string;
        description: string;
        icon: string | null;
        sortOrder: number;
        isActive: boolean;
      }>
    >`
      SELECT id, name, description, icon, "sortOrder", "isActive"
      FROM permission_categories 
      WHERE "isActive" = true 
      ORDER BY "sortOrder" ASC
    `;

    return categories.map((category) => ({
      id: category.id,
      name: category.name,
      description: category.description || "",
      icon: category.icon || undefined,
      sortOrder: category.sortOrder,
      isActive: category.isActive,
    }));
  }
}
