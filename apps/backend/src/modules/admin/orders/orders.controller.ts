import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
} from "@nestjs/common";
import { OrdersService } from "./orders.service";
import { CreateOrderDto } from "./dto/create-order.dto";
import { UpdateOrderDto } from "./dto/update-order.dto";
import { UpdateOrderStatusDto } from "./dto/update-order-status.dto";
import { JwtAuthGuard } from "../../core/auth/guards/auth.guard";
import { RolesGuard } from "../../core/auth/guards/roles.guard";
import { Roles } from "../../core/auth/decorators/roles.decorator";
import { Role } from "../../../common/enums/role.enum";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
} from "@nestjs/swagger";

@ApiTags("admin/orders")
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles(Role.SUPER_ADMIN, Role.SYSTEM_ADMIN)
@Controller("admin/orders")
export class OrdersController {
  constructor(private readonly ordersService: OrdersService) {}

  @Post()
  @ApiOperation({ summary: "建立新訂單" })
  @ApiResponse({ status: 201, description: "訂單建立成功" })
  @ApiResponse({ status: 400, description: "請求資料格式錯誤" })
  create(@Body() createOrderDto: CreateOrderDto) {
    return this.ordersService.create(createOrderDto);
  }

  @Get()
  @ApiOperation({ summary: "取得訂單列表" })
  @ApiQuery({ name: "search", required: false, description: "搜尋關鍵字" })
  @ApiQuery({ name: "status", required: false, description: "訂單狀態" })
  @ApiQuery({ name: "startDate", required: false, description: "開始日期" })
  @ApiQuery({ name: "endDate", required: false, description: "結束日期" })
  @ApiResponse({ status: 200, description: "成功取得訂單列表" })
  findAll(
    @Query("search") search?: string,
    @Query("status") status?: string,
    @Query("startDate") startDate?: string,
    @Query("endDate") endDate?: string
  ) {
    return this.ordersService.findAll({ search, status, startDate, endDate });
  }

  @Get(":id")
  @ApiOperation({ summary: "取得單一訂單詳情" })
  @ApiParam({ name: "id", description: "訂單 ID" })
  @ApiResponse({ status: 200, description: "成功取得訂單詳情" })
  @ApiResponse({ status: 404, description: "訂單不存在" })
  findOne(@Param("id") id: string) {
    return this.ordersService.findOne(id);
  }

  @Patch(":id")
  @ApiOperation({ summary: "更新訂單資料" })
  @ApiParam({ name: "id", description: "訂單 ID" })
  @ApiResponse({ status: 200, description: "訂單更新成功" })
  @ApiResponse({ status: 404, description: "訂單不存在" })
  update(@Param("id") id: string, @Body() updateOrderDto: UpdateOrderDto) {
    return this.ordersService.update(id, updateOrderDto);
  }

  @Patch("status/:id")
  @ApiOperation({ summary: "更新訂單狀態" })
  @ApiParam({ name: "id", description: "訂單 ID" })
  @ApiResponse({ status: 200, description: "訂單狀態更新成功" })
  @ApiResponse({ status: 404, description: "訂單不存在" })
  updateStatus(
    @Param("id") id: string,
    @Body() updateStatusDto: UpdateOrderStatusDto
  ) {
    return this.ordersService.updateStatus(id, updateStatusDto.status);
  }

  @Delete(":id")
  @ApiOperation({ summary: "刪除訂單" })
  @ApiParam({ name: "id", description: "訂單 ID" })
  @ApiResponse({ status: 200, description: "訂單刪除成功" })
  @ApiResponse({ status: 404, description: "訂單不存在" })
  remove(@Param("id") id: string) {
    return this.ordersService.remove(id);
  }
}
