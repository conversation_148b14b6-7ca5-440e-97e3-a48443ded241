import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../../core/prisma/prisma.service';
import { CreateOrderDto } from './dto/create-order.dto';
import { UpdateOrderDto } from './dto/update-order.dto';
import { Order, OrderDetail, OrderHistoryItem, OrderStatus, PaymentStatus } from './interfaces/order.interface';
import { v4 as uuidv4 } from 'uuid';
import { PrismaClient } from "@prisma/client";

@Injectable()
export class OrdersService {
  constructor(private readonly prisma: PrismaService) {}

  async create(createOrderDto: CreateOrderDto): Promise<Order> {
    try {
      // 檢查租戶是否存在
      const tenant = await this.prisma.tenants.findUnique({
        where: { id: createOrderDto.tenantId },
      });

      if (!tenant) {
        throw new BadRequestException(`租戶不存在: ${createOrderDto.tenantId}`);
      }

      // 檢查方案是否存在
      const plan = await this.prisma.plans.findUnique({
        where: { id: createOrderDto.planId },
      });

      if (!plan) {
        throw new BadRequestException(`方案不存在: ${createOrderDto.planId}`);
      }

      // 計算結束日期（如果沒有提供）
      let endDate = createOrderDto.endDate;
      if (!endDate) {
        const startDate = new Date(createOrderDto.startDate);
        const calculatedEndDate = new Date(startDate);
        calculatedEndDate.setMonth(calculatedEndDate.getMonth() + createOrderDto.period);
        endDate = calculatedEndDate.toISOString().split('T')[0];
      }

      // 建立訂單，使用 Prisma 事務確保資料一致性
      const orderData = {
        id: uuidv4(),
        tenantId: createOrderDto.tenantId,
        planId: createOrderDto.planId,
        tenantName: createOrderDto.tenantName,
        planName: createOrderDto.planName,
        amount: createOrderDto.amount,
        period: createOrderDto.period,
        numberOfSubscribers: createOrderDto.numberOfSubscribers,
        startDate: new Date(createOrderDto.startDate),
        endDate: new Date(endDate),
        status: "pending",
        billingCycle: createOrderDto.billingCycle || 'monthly',
        remarks: createOrderDto.remarks,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // 使用原始 SQL 查詢插入訂單並返回結果
      const order = await this.prisma.$transaction(async (prisma) => {
        // 1. 建立訂單
        await prisma.$executeRaw`
          INSERT INTO "orders" (
            id, "tenantId", "planId", "tenantName", "planName", amount, period, 
            "numberOfSubscribers", "startDate", "endDate", status, "billingCycle",
            remarks, "createdAt", "updatedAt"
          ) VALUES (
            ${orderData.id}, ${orderData.tenantId}, ${orderData.planId}, 
            ${orderData.tenantName}, ${orderData.planName}, ${orderData.amount}, 
            ${orderData.period}, ${orderData.numberOfSubscribers}, 
            ${orderData.startDate}, ${orderData.endDate}, ${orderData.status}, 
            ${orderData.billingCycle}, ${orderData.remarks}, 
            ${orderData.createdAt}, ${orderData.updatedAt}
          )
        `;

        // 2. 建立訂單歷史記錄
        const historyId = uuidv4();
        await prisma.$executeRaw`
          INSERT INTO "order_histories" (
            id, type, status, description, "orderId", "createdAt"
          ) VALUES (
            ${historyId}, 'info', '訂單已建立', '訂單已成功建立，等待處理',
            ${orderData.id}, ${new Date()}
          )
        `;

        // 3. 建立付款記錄
        const paymentId = uuidv4();
        await prisma.$executeRaw`
          INSERT INTO "payments" (
            id, method, status, "orderId", "createdAt", "updatedAt"
          ) VALUES (
            ${paymentId}, '待定', ${"pending"},
            ${orderData.id}, ${new Date()}, ${new Date()}
          )
        `;

        // 4. 查詢並返回剛剛建立的訂單
        const orders = await prisma.$queryRaw<any[]>`
          SELECT * FROM "orders" WHERE id = ${orderData.id}
        `;
        
        return orders[0];
      });

      return this.mapToOrderResponse(order);
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('建立訂單失敗: ' + error.message);
    }
  }

  async findAll(params: {
    search?: string;
    status?: string;
    startDate?: string;
    endDate?: string;
  }): Promise<Order[]> {
    try {
      const { search, status, startDate, endDate } = params;
      
      // 構建查詢參數
      const queryParams: any[] = [];
      let sql = 'SELECT * FROM "orders"';
      const conditions: string[] = [];
      
      // 搜尋條件
      if (search) {
        queryParams.push(`%${search}%`);
        conditions.push(`(id::text ILIKE ? OR "tenantName" ILIKE ? OR "planName" ILIKE ?)`);
      }
      
      // 狀態條件
      if (status && Object.values(OrderStatus).includes(status as OrderStatus)) {
        queryParams.push(status);
        conditions.push(`status = ?`);
      }
      
      // 日期條件
      if (startDate) {
        queryParams.push(new Date(startDate));
        conditions.push(`"createdAt" >= ?`);
      }
      
      if (endDate) {
        queryParams.push(new Date(endDate));
        conditions.push(`"createdAt" <= ?`);
      }
      
      // 添加 WHERE 子句
      if (conditions.length > 0) {
        sql += ' WHERE ' + conditions.join(' AND ');
      }
      
      // 添加排序
      sql += ' ORDER BY "createdAt" DESC';
      
      // 執行查詢
      const orders = await this.prisma.$queryRawUnsafe<any[]>(sql, ...queryParams);
      
      return orders.map(order => this.mapToOrderResponse(order));
    } catch (error) {
      throw new BadRequestException('查詢訂單失敗: ' + error.message);
    }
  }

  async findOne(id: string): Promise<OrderDetail> {
    try {
      // 使用原始 SQL 查詢讀取訂單詳情
      const orders = await this.prisma.$queryRaw<any[]>`
        SELECT o.*, 
          p.method AS "paymentMethod", 
          p.status AS "paymentStatus",
          t.id AS "tenant_id", 
          t.name AS "tenant_name",
          t."contactName" AS "tenant_contactName",
          t."contactEmail" AS "tenant_contactEmail",
          pl.id AS "plan_id",
          pl.name AS "plan_name",
          pl.description AS "plan_description",
          pl.price AS "plan_price",
          pl."billingCycle" AS "plan_billingCycle",
          pl."maxUsers" AS "plan_usersLimit",
          pl."maxProjects" AS "plan_workspacesLimit"
        FROM "orders" o
        LEFT JOIN "payments" p ON p."orderId" = o.id
        LEFT JOIN "tenants" t ON t.id = o."tenantId"
        LEFT JOIN "plans" pl ON pl.id = o."planId"
        WHERE o.id = ${id}
      `;
      
      if (!orders || orders.length === 0) {
        throw new NotFoundException(`訂單不存在: ${id}`);
      }
      
      const order = orders[0];
      
      // 讀取訂單歷史記錄
      const orderHistory = await this.prisma.$queryRaw<any[]>`
        SELECT * FROM "order_histories"
        WHERE "orderId" = ${id}
        ORDER BY "createdAt" DESC
      `;
      
      // 合併所有數據
      const combinedOrder = {
        ...order,
        orderHistory,
        tenant: {
          id: order.tenant_id,
          name: order.tenant_name,
          contactName: order.tenant_contactName || '未指定',
          contactEmail: order.tenant_contactEmail || '未指定'
        },
        plan: {
          id: order.plan_id,
          name: order.plan_name,
          description: order.plan_description,
          price: order.plan_price,
          billingCycle: order.plan_billingCycle,
          usersLimit: order.plan_usersLimit,
          workspacesLimit: order.plan_workspacesLimit
        }
      };
      
      return this.mapToOrderDetailResponse(combinedOrder);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException('查詢訂單詳情失敗: ' + error.message);
    }
  }

  async update(id: string, updateOrderDto: UpdateOrderDto): Promise<Order> {
    // 檢查訂單是否存在
    const existingOrder = await this.findOne(id);
    
    if (!existingOrder) {
      throw new NotFoundException(`訂單不存在: ${id}`);
    }

    try {
      // 準備更新資料
      const updateData: any = {
        updatedAt: new Date()
      };

      // 記錄變更內容
      const changes: string[] = [];

      // 基本資訊
      if (updateOrderDto.tenantName && updateOrderDto.tenantName !== existingOrder.tenantName) {
        updateData.tenantName = updateOrderDto.tenantName;
        changes.push(`租戶名稱從「${existingOrder.tenantName}」變更為「${updateOrderDto.tenantName}」`);
      }

      if (updateOrderDto.planName && updateOrderDto.planName !== existingOrder.planName) {
        updateData.planName = updateOrderDto.planName;
        changes.push(`方案名稱從「${existingOrder.planName}」變更為「${updateOrderDto.planName}」`);
      }

      if (updateOrderDto.startDate && updateOrderDto.startDate !== this.formatDate(existingOrder.startDate)) {
        updateData.startDate = new Date(updateOrderDto.startDate);
        changes.push(`開始日期從「${this.formatDate(existingOrder.startDate)}」變更為「${updateOrderDto.startDate}」`);
      }

      if (updateOrderDto.endDate && updateOrderDto.endDate !== this.formatDate(existingOrder.endDate)) {
        updateData.endDate = new Date(updateOrderDto.endDate);
        changes.push(`結束日期從「${this.formatDate(existingOrder.endDate)}」變更為「${updateOrderDto.endDate}」`);
      }

      if (updateOrderDto.period && updateOrderDto.period !== existingOrder.period) {
        updateData.period = updateOrderDto.period;
        changes.push(`訂閱期間從「${existingOrder.period}個月」變更為「${updateOrderDto.period}個月」`);
      }

      if (updateOrderDto.numberOfSubscribers && updateOrderDto.numberOfSubscribers !== existingOrder.numberOfSubscribers) {
        updateData.numberOfSubscribers = updateOrderDto.numberOfSubscribers;
        changes.push(`訂閱人數從「${existingOrder.numberOfSubscribers}人」變更為「${updateOrderDto.numberOfSubscribers}人」`);
      }

      if (updateOrderDto.amount && updateOrderDto.amount !== existingOrder.amount) {
        updateData.amount = updateOrderDto.amount;
        changes.push(`訂單金額從「${existingOrder.amount}元」變更為「${updateOrderDto.amount}元」`);
      }

      // 付款資訊
      if (updateOrderDto.paymentMethod && updateOrderDto.paymentMethod !== existingOrder.paymentMethod) {
        updateData.paymentMethod = updateOrderDto.paymentMethod;
        changes.push(`付款方式從「${existingOrder.paymentMethod || '未指定'}」變更為「${updateOrderDto.paymentMethod}」`);
      }

      if (updateOrderDto.paymentStatus && updateOrderDto.paymentStatus !== existingOrder.paymentStatus) {
        updateData.paymentStatus = updateOrderDto.paymentStatus;
        const statusMap: Record<string, string> = {
          'pending': '待付款',
          'paid': '已付款',
          'failed': '付款失敗'
        };
        const oldStatus = statusMap[existingOrder.paymentStatus] || existingOrder.paymentStatus;
        const newStatus = statusMap[updateOrderDto.paymentStatus] || updateOrderDto.paymentStatus;
        changes.push(`付款狀態從「${oldStatus}」變更為「${newStatus}」`);
      }

      // 聯絡人資訊
      if (updateOrderDto.contactName && updateOrderDto.contactName !== existingOrder.contactName) {
        updateData.contactName = updateOrderDto.contactName;
        changes.push(`聯絡人從「${existingOrder.contactName || '未指定'}」變更為「${updateOrderDto.contactName}」`);
      }

      if (updateOrderDto.contactEmail && updateOrderDto.contactEmail !== existingOrder.contactEmail) {
        updateData.contactEmail = updateOrderDto.contactEmail;
        changes.push(`聯絡信箱從「${existingOrder.contactEmail || '未指定'}」變更為「${updateOrderDto.contactEmail}」`);
      }

      // 狀態和備註
      if (updateOrderDto.status && updateOrderDto.status !== existingOrder.status) {
        updateData.status = updateOrderDto.status;
        changes.push(`訂單狀態從「${this.getStatusLabel(existingOrder.status)}」變更為「${this.getStatusLabel(updateOrderDto.status)}」`);
      }

      if (updateOrderDto.remarks !== undefined && updateOrderDto.remarks !== existingOrder.remarks) {
        updateData.remarks = updateOrderDto.remarks;
        if (!updateOrderDto.remarks && existingOrder.remarks) {
          changes.push(`備註已清空`);
        } else if (!existingOrder.remarks) {
          changes.push(`備註已新增`);
        } else {
          changes.push(`備註已更新`);
        }
      }

      if (updateOrderDto.billingCycle && updateOrderDto.billingCycle !== existingOrder.billingCycle) {
        updateData.billingCycle = updateOrderDto.billingCycle;
        const cycleMap: Record<string, string> = {
          'monthly': '月付',
          'yearly': '年付'
        };
        const oldCycle = cycleMap[existingOrder.billingCycle] || existingOrder.billingCycle;
        const newCycle = cycleMap[updateOrderDto.billingCycle] || updateOrderDto.billingCycle;
        changes.push(`計費週期從「${oldCycle}」變更為「${newCycle}」`);
      }

      // 使用 Prisma Client API 更新訂單
      const setValues: string[] = [];
      const queryParams: any[] = [];
      
      Object.entries(updateData).forEach(([key, value]) => {
        setValues.push(`"${key}" = $${queryParams.length + 1}`);
        queryParams.push(value);
      });

      const setClause = setValues.join(', ');
      queryParams.push(id);

      const updatedOrder = await this.prisma.$queryRawUnsafe<any[]>(
        `UPDATE "orders" SET ${setClause} WHERE id = $${queryParams.length} RETURNING *`,
        ...queryParams
      );

      // 如果有任何變更，添加歷史記錄
      if (changes.length > 0) {
        const historyId = uuidv4();
        const historyType = updateOrderDto.status ? this.getHistoryTypeByStatus(updateOrderDto.status) : 'info';
        const historyStatus = updateOrderDto.status ? this.getStatusMessage(updateOrderDto.status) : '訂單已更新';
        const historyDescription = changes.join('；');
        const operatorName = updateOrderDto.updatedBy || '系統';
        const now = new Date();

        await this.prisma.$executeRaw`
          INSERT INTO "order_histories" (
            id, type, status, description, by, "orderId", "createdAt"
          ) VALUES (
            ${historyId},
            ${historyType},
            ${historyStatus},
            ${'變更內容: ' + historyDescription},
            ${operatorName},
            ${id},
            ${now}
          )
        `;
      }

      if (!updatedOrder?.[0]) {
        throw new NotFoundException(`找不到要更新的訂單: ${id}`);
      }

      return this.mapToOrderResponse(updatedOrder[0]);
    } catch (error) {
      throw new BadRequestException('更新訂單失敗: ' + error.message);
    }
  }

  async updateStatus(id: string, status: OrderStatus, updatedBy: string = '系統'): Promise<Order> {
    // 檢查訂單是否存在
    const existingOrder = await this.findOne(id);
    
    if (!existingOrder) {
      throw new NotFoundException(`訂單不存在: ${id}`);
    }

    try {
      // 更新訂單狀態
      await this.prisma.$executeRaw`
        UPDATE "orders"
        SET status = ${status}, "updatedAt" = ${new Date()}
        WHERE id = ${id}
      `;
      
      // 生成變更描述
      const statusChangeDescription = `狀態從「${this.getStatusLabel(existingOrder.status)}」變更為「${this.getStatusLabel(status)}」`;
      
      // 建立訂單歷史記錄
      const historyId = uuidv4();
      await this.prisma.$executeRaw`
        INSERT INTO "order_histories" (
          id, type, status, description, by, "orderId", "createdAt"
        ) VALUES (
          ${historyId}, 
          ${this.getHistoryTypeByStatus(status)}, 
          ${this.getStatusMessage(status)}, 
          ${'變更內容: ' + statusChangeDescription}, 
          ${updatedBy}, 
          ${id}, 
          ${new Date()}
        )
      `;
      
      // 查詢更新後的訂單
      const orders = await this.prisma.$queryRaw<any[]>`
        SELECT * FROM "orders" WHERE id = ${id}
      `;
      
      if (!orders[0]) {
        throw new NotFoundException(`找不到要更新的訂單: ${id}`);
      }
      
      return this.mapToOrderResponse(orders[0]);
    } catch (error) {
      throw new BadRequestException('更新訂單狀態失敗: ' + error.message);
    }
  }

  async remove(id: string): Promise<{ success: boolean; message: string }> {
    // 檢查訂單是否存在
    const existingOrder = await this.findOne(id);
    
    if (!existingOrder) {
      throw new NotFoundException(`訂單不存在: ${id}`);
    }

    try {
      await this.prisma.$transaction(async (prisma) => {
        // 刪除訂單歷史記錄
        await prisma.$executeRaw`
          DELETE FROM "order_histories"
          WHERE "orderId" = ${id}
        `;
        
        // 刪除付款記錄
        await prisma.$executeRaw`
          DELETE FROM "payments"
          WHERE "orderId" = ${id}
        `;
        
        // 刪除訂單
        await prisma.$executeRaw`
          DELETE FROM "orders"
          WHERE id = ${id}
        `;
      });
      
      return { success: true, message: '訂單已成功刪除' };
    } catch (error) {
      throw new BadRequestException('刪除訂單失敗: ' + error.message);
    }
  }

  // 輔助方法
  private getHistoryTypeByStatus(status: OrderStatus): 'info' | 'success' | 'warning' | 'danger' {
    switch (status) {
      case OrderStatus.COMPLETED:
        return 'success';
      case OrderStatus.CANCELLED:
        return 'danger';
      default:
        return 'info';
    }
  }

  private getStatusMessage(status: OrderStatus): string {
    switch (status) {
      case OrderStatus.COMPLETED:
        return '訂單已完成';
      case OrderStatus.CANCELLED:
        return '訂單已取消';
      case OrderStatus.PENDING:
        return '訂單待處理';
      default:
        return '訂單狀態已更新';
    }
  }

  private getStatusLabel(status: OrderStatus): string {
    switch (status) {
      case OrderStatus.COMPLETED:
        return '已完成';
      case OrderStatus.CANCELLED:
        return '已取消';
      case OrderStatus.PENDING:
        return '待處理';
      default:
        return status;
    }
  }

  private mapToOrderResponse(order: any): Order {
    return {
      id: order.id,
      tenantName: order.tenantName,
      planName: order.planName,
      amount: order.amount,
      period: order.period,
      numberOfSubscribers: order.numberOfSubscribers,
      startDate: order.startDate.toISOString ? order.startDate.toISOString() : order.startDate,
      endDate: order.endDate.toISOString ? order.endDate.toISOString() : order.endDate,
      status: order.status,
      billingCycle: order.billingCycle || 'monthly',
      createdAt: order.createdAt.toISOString ? order.createdAt.toISOString() : order.createdAt,
      updatedAt: order.updatedAt.toISOString ? order.updatedAt.toISOString() : order.updatedAt,
      remarks: order.remarks,
    };
  }

  private mapToOrderDetailResponse(order: any): OrderDetail {
    const orderHistory: OrderHistoryItem[] = (order.orderHistory || []).map(history => {
      // 處理顯示日期，確保以正確的格式顯示
      const dateDisplay = this.formatDateTime(history.createdAt);

      return {
        type: history.type,
        date: dateDisplay,
        status: history.status,
        description: history.description,
        by: history.by,
      };
    });

    return {
      id: order.id,
      tenantName: order.tenantName,
      planName: order.planName,
      amount: order.amount,
      period: order.period,
      numberOfSubscribers: order.numberOfSubscribers,
      startDate: order.startDate.toISOString ? order.startDate.toISOString() : order.startDate,
      endDate: order.endDate.toISOString ? order.endDate.toISOString() : order.endDate,
      status: order.status,
      billingCycle: order.billingCycle || 'monthly',
      createdAt: order.createdAt.toISOString ? order.createdAt.toISOString() : order.createdAt,
      updatedAt: order.updatedAt.toISOString ? order.updatedAt.toISOString() : order.updatedAt,
      remarks: order.remarks,
      // 付款相關信息
      paymentMethod: order.paymentMethod || '未指定',
      paymentStatus: order.paymentStatus || 'pending',
      // 聯絡人相關信息
      contactName: order.contactName || '未指定',
      contactEmail: order.contactEmail || '未指定',
      // 租戶詳細信息
      tenant: order.tenant ? {
        id: order.tenant.id,
        name: order.tenant.name,
        contactName: order.tenant.contactName || '未指定',
        contactEmail: order.tenant.contactEmail || '未指定',
      } : {
        id: order.id,
        name: order.tenantName,
        contactName: order.contactName || '未指定',
        contactEmail: order.contactEmail || '未指定',
      },
      // 方案詳細信息
      plan: order.plan ? {
        name: order.plan.name,
        description: order.plan.description,
        price: order.plan.price,
        billingCycle: order.plan.billingCycle || 'monthly',
        usersLimit: order.plan.usersLimit || order.numberOfSubscribers,
        workspacesLimit: order.plan.workspacesLimit || 1,
      } : {
        name: order.planName,
        description: '未提供方案詳細描述',
        price: order.amount,
        billingCycle: 'monthly',
        usersLimit: order.numberOfSubscribers,
        workspacesLimit: 1,
      },
      // 訂單歷史記錄
      orderHistory,
    };
  }

  private formatDate(dateString: string): string {
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return '無效日期';
      return date.toISOString().split('T')[0]; // 格式化為 YYYY-MM-DD
    } catch (e) {
      return '無效日期';
    }
  }

  private formatDateTime(dateString: string | Date): string {
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return '無效日期';
      return date.toISOString(); // 保留完整的 ISO 格式用於前端顯示
    } catch (e) {
      return '無效日期';
    }
  }
}
