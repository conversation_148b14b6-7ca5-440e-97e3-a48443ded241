export enum OrderStatus {
  PENDING = 'pending',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
}

export enum PaymentStatus {
  PENDING = 'pending',
  PAID = 'paid',
  FAILED = 'failed',
}

export interface Order {
  id: string;
  tenantName: string;
  planName: string;
  amount: number;
  period: number;
  numberOfSubscribers: number;
  startDate: string;
  endDate: string;
  status: OrderStatus;
  billingCycle: string;
  createdAt: string;
  updatedAt: string;
  remarks?: string;
}

export interface OrderDetail extends Order {
  paymentMethod: string;
  paymentStatus: string;
  contactName: string;
  contactEmail: string;
  tenant: {
    id: string;
    name: string;
    contactName: string;
    contactEmail: string;
  };
  plan: {
    name: string;
    description: string;
    price: number;
    billingCycle: string;
    usersLimit: number;
    workspacesLimit: number;
  };
  orderHistory: OrderHistoryItem[];
}

export interface OrderHistoryItem {
  type: 'info' | 'success' | 'warning' | 'danger';
  date: string;
  status: string;
  description: string;
  by: string | null;
} 