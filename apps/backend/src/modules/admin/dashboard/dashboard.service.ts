import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../core/prisma/prisma.service';
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';
import { DashboardStats, Activity } from '@auth/shared/types';

export interface MonthlyRevenue {
  month: string;
  value: number;
}

export interface ActivityLog {
  date: string;
  users: number;
  tenants: number;
  total: number;
  subscriptions: number; // 新增屬性
}

@Injectable()
export class DashboardService {
  private readonly logger = new Logger(DashboardService.name);
  
  constructor(private readonly prisma: PrismaService) {}

  // 使用 private getter 來取得擴展的 Prisma 服務
  private get extendedPrisma(): any {
    return this.prisma as any;
  }

  async getStats() {
    try {
      // 使用 Promise.allSettled 來確保即使某一個查詢失敗，其他查詢仍會繼續執行
      const results = await Promise.allSettled([
        this.prisma.tenants.count(),
        this.getTotalUsersCount(),
        this.getSubscriptionsCount(),
        this.getPlansCount()
      ]);

      // 讀取每個結果，如果某個查詢失敗則設為 0
      const [tenantsCount, usersCount, subscriptionsCount, plansCount] = results.map(
        result => (result.status === 'fulfilled' ? result.value : 0)
      );

      return {
        tenantsCount,
        usersCount,
        ordersCount: subscriptionsCount,
        plansCount
      };
    } catch (error) {
      this.logger.error(`讀取統計資料失敗: ${error.message}`, error.stack);
      // 返回預設值而不是拋出錯誤，確保前端仍能顯示某些資料
      return {
        tenantsCount: 0,
        usersCount: 0,
        ordersCount: 0,
        plansCount: 0
      };
    }
  }

  // 安全地取得訂閱數量
  private async getSubscriptionsCount(): Promise<number> {
    try {
      return await this.extendedPrisma.subscription.count({
        where: {
          status: { in: ['ACTIVE', 'PENDING'] }
        }
      });
    } catch (error) {
      if (error instanceof PrismaClientKnownRequestError && error.message.includes('does not exist in the current database')) {
        this.logger.warn('subscriptions 資料表不存在，返回預設值 0');
        return 0;
      }
      throw error;
    }
  }

  // 安全地取得方案數量
  private async getPlansCount(): Promise<number> {
    try {
      return await this.prisma.plans.count();
    } catch (error) {
      if (error instanceof PrismaClientKnownRequestError && error.message.includes('does not exist in the current database')) {
        this.logger.warn('plans 資料表不存在，返回預設值 0');
        return 0;
      }
      throw error;
    }
  }

  // 取得總用戶數量（系統用戶 + 租戶用戶）
  private async getTotalUsersCount(): Promise<number> {
    try {
      const [systemUsersCount, tenantUsersCount] = await Promise.all([
        this.prisma.system_users.count(),
        this.prisma.tenant_users.count()
      ]);
      return systemUsersCount + tenantUsersCount;
    } catch (error) {
      this.logger.error(`取得總用戶數量失敗: ${error.message}`, error.stack);
      return 0;
    }
  }

  // 取得新用戶數量（系統用戶 + 租戶用戶）
  private async getNewUsersCount(fromDate: Date): Promise<number> {
    try {
      const [newSystemUsers, newTenantUsers] = await Promise.all([
        this.prisma.system_users.count({
          where: { createdAt: { gte: fromDate } }
        }),
        this.prisma.tenant_users.count({
          where: { createdAt: { gte: fromDate } }
        })
      ]);
      return newSystemUsers + newTenantUsers;
    } catch (error) {
      this.logger.error(`取得新用戶數量失敗: ${error.message}`, error.stack);
      return 0;
    }
  }

  // 取得活躍用戶數量（系統用戶 + 租戶用戶）
  private async getActiveUsersCount(fromDate: Date): Promise<number> {
    try {
      const [activeSystemUsers, activeTenantUsers] = await Promise.all([
        this.prisma.system_users.count({
          where: { updatedAt: { gte: fromDate } }
        }),
        this.prisma.tenant_users.count({
          where: { updatedAt: { gte: fromDate } }
        })
      ]);
      return activeSystemUsers + activeTenantUsers;
    } catch (error) {
      this.logger.error(`取得活躍用戶數量失敗: ${error.message}`, error.stack);
      return 0;
    }
  }

  async getRecentTenants() {
    try {
      return await this.prisma.tenants.findMany({
        take: 5,
        orderBy: {
          createdAt: 'desc'
        },
        select: {
          id: true,
          name: true,
          status: true,
          createdAt: true
        }
      });
    } catch (error) {
      this.logger.error(`讀取最近租戶失敗: ${error.message}`, error.stack);
      return []; // 返回空陣列而不是拋出錯誤
    }
  }

  async getRecentOrders() {
    try {
      return await this.getRecentSubscriptions();
    } catch (error) {
      this.logger.error(`讀取最近訂單失敗: ${error.message}`, error.stack);
      return []; // 返回空陣列而不是拋出錯誤
    }
  }

  // 安全地取得最近訂閱
  private async getRecentSubscriptions() {
    try {
      return await this.extendedPrisma.subscription.findMany({
        take: 5,
        orderBy: {
          createdAt: 'desc'
        },
        include: {
          tenants: {
            select: {
              name: true
            }
          }
        }
      });
    } catch (error) {
      if (error instanceof PrismaClientKnownRequestError && error.message.includes('does not exist in the current database')) {
        this.logger.warn('subscriptions 資料表不存在，返回空陣列');
        return [];
      }
      throw error;
    }
  }

  async getActiveUsers() {
    try {
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const [activeSystemUsers, activeTenantUsers] = await Promise.all([
        this.prisma.system_users.count({
          where: {
            updatedAt: {
              gte: thirtyDaysAgo
            }
          }
        }),
        this.prisma.tenant_users.count({
          where: {
            updatedAt: {
              gte: thirtyDaysAgo
            }
          }
        })
      ]);

      const activeUsers = activeSystemUsers + activeTenantUsers;
      const totalUsers = await this.getTotalUsersCount();

      return {
        activeUsers,
        totalUsers,
        percentage: totalUsers > 0 ? Math.round((activeUsers / totalUsers) * 100) : 0
      };
    } catch (error) {
      this.logger.error(`讀取活躍使用者統計失敗: ${error.message}`, error.stack);
      return {
        activeUsers: 0,
        totalUsers: 0,
        percentage: 0
      }; // 返回預設值而不是拋出錯誤
    }
  }

  async getRevenue(): Promise<{ totalRevenue: number; currentMonthRevenue: number; monthlyRevenue: DashboardStats[] }> {
    try {
      const sixMonthsAgo = new Date();
      sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
      sixMonthsAgo.setDate(1);
      sixMonthsAgo.setHours(0, 0, 0, 0);

      const monthlyRevenue: DashboardStats[] = [];
      for (let i = 0; i < 6; i++) {
        const month = new Date();
        month.setMonth(month.getMonth() - (5 - i));
        monthlyRevenue.push({
          totalUsers: 0,
          activeUsers: 0,
          totalTenants: 0,
          tenantCount: 0,
          activeWorkspaces: 0,
          totalProjects: 0,
          systemHealth: 0,
          monthlyRevenue: 0,
          storageUsed: 0,
          apiCalls: 0
        });
      }

      const totalRevenue = monthlyRevenue.reduce((sum, item) => sum + item.monthlyRevenue, 0);
      const currentMonthRevenue = monthlyRevenue[monthlyRevenue.length - 1].monthlyRevenue;

      return { totalRevenue, currentMonthRevenue, monthlyRevenue };
    } catch (error) {
      this.logger.error(`讀取收入統計失敗: ${error.message}`, error.stack);
      return { totalRevenue: 0, currentMonthRevenue: 0, monthlyRevenue: [] };
    }
  }

  async getActivity(): Promise<{ summary: { newTenants: number; newUsers: number; newSubscriptions: number; activeUsers: number; }; activityLogs: Activity[] }> {
    try {
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const activityResults = await Promise.allSettled([
        this.prisma.tenants.count({
          where: { createdAt: { gte: thirtyDaysAgo } }
        }),
        this.getNewUsersCount(thirtyDaysAgo),
        this.getNewSubscriptionsCount(thirtyDaysAgo),
        this.getActiveUsersCount(thirtyDaysAgo)
      ]);

      const [newTenants, newUsers, newSubscriptions, activeUsers] = activityResults.map(
        result => (result.status === 'fulfilled' ? result.value : 0)
      );

      const activityLogs: Activity[] = [];
      for (let i = 0; i < 14; i++) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        activityLogs.push({
          type: 'system',
          message: `Activity log for ${date.toISOString().split('T')[0]}`,
          timestamp: date.toISOString()
        });
      }

      return { summary: { newTenants, newUsers, newSubscriptions, activeUsers }, activityLogs };
    } catch (error) {
      this.logger.error(`讀取活動資料失敗: ${error.message}`, error.stack);
      return { summary: { newTenants: 0, newUsers: 0, newSubscriptions: 0, activeUsers: 0 }, activityLogs: [] };
    }
  }

  // 安全地讀取新訂閱數量
  private async getNewSubscriptionsCount(fromDate: Date): Promise<number> {
    try {
      return await this.extendedPrisma.subscription.count({
        where: {
          createdAt: {
            gte: fromDate
          }
        }
      });
    } catch (error) {
      if (error instanceof PrismaClientKnownRequestError && error.message.includes('does not exist in the current database')) {
        this.logger.warn('subscriptions 資料表不存在，返回預設值 0');
        return 0;
      }
      throw error;
    }
  }

  // 安全地讀取訂閱活動資料
  private async getSubscriptionActivityData(activityLogs: ActivityLog[]): Promise<void> {
    try {
      const dailySubscriptionsData = await this.extendedPrisma.subscription.groupBy({
        by: ['createdAt'],
        _count: {
          id: true
        },
        where: {
          createdAt: {
            gte: new Date(new Date().setDate(new Date().getDate() - 14))
          }
        }
      });

      for (const item of dailySubscriptionsData) {
        const date = new Date(item.createdAt);
        const dateString = date.toISOString().split('T')[0];
        const logIndex = activityLogs.findIndex(log => log.date === dateString);
        
        if (logIndex !== -1) {
          activityLogs[logIndex].subscriptions = item._count.id;
          activityLogs[logIndex].total += item._count.id;
        }
      }
    } catch (error) {
      if (error instanceof PrismaClientKnownRequestError && error.message.includes('does not exist in the current database')) {
        this.logger.warn('subscriptions 資料表不存在，訂閱活動資料設為 0');
        // 表格不存在時，不做任何操作，保留預設值 0
        return;
      }
      throw error;
    }
  }
}