import {
  Injectable,
  BadRequestException,
  NotFoundException,
} from "@nestjs/common";
import { PrismaService } from "@/modules/core/prisma/prisma.service";
import { EncryptionService } from "@/modules/core/encryption/encryption.service";
import { AiProviderFactory } from "../core/providers/factory";
import {
  AiMessage,
  AiExecuteOptions,
} from "../core/providers/base/base.provider";
import {
  CreateBotDto,
  UpdateBotDto,
  OptimizePromptDto,
  ExecuteBotDto,
} from "./dto/bot.dto";
import { PrismaClient } from "@prisma/client";
import * as fs from "fs";
import * as path from "path";
import * as crypto from "crypto";
import { v4 as uuidv4 } from "uuid";
import {
  AiBotScope,
  AiBotProviderType,
  AiBotResponseFormat,
  Prisma,
} from "@prisma/client";

@Injectable()
export class AiBotsService {
  private readonly defaultPromptOptimizationPath = path.join(
    __dirname,
    "../prompts/prompt-optimization.md"
  );

  constructor(
    private readonly prisma: PrismaService,
    private readonly encryptionService: EncryptionService,
    private readonly providerFactory: AiProviderFactory
  ) {}

  /**
   * 讀取所有 Bot
   */
  async findAll(scope?: AiBotScope, tenantId?: string, workspaceId?: string) {
    const where: any = {};

    if (scope) {
      where.scope = scope;
    }

    if (tenantId) {
      where.tenant_id = tenantId;
    }

    if (workspaceId) {
      where.workspace_id = workspaceId;
    }

    return this.prisma.ai_bots.findMany({
      where,
      include: {
        ai_keys: true,
        ai_models: true,
      },
      orderBy: { created_at: "desc" },
    });
  }

  /**
   * 根據 ID 讀取 Bot
   */
  async findOne(id: string) {
    const bot = await this.prisma.ai_bots.findUnique({
      where: { id },
      include: {
        ai_keys: true,
        ai_models: true,
      },
    });

    if (!bot) {
      throw new NotFoundException(`Bot with ID ${id} not found`);
    }

    return bot;
  }

  /**
   * 建立新 Bot
   */
  async create(createBotDto: CreateBotDto, userId: string) {
    const now = new Date();
    const data = {
      id: uuidv4(),
      name: createBotDto.name,
      description: createBotDto.description,
      scope: createBotDto.scope,
      provider_type: createBotDto.provider_type,
      model_id: createBotDto.model_id,
      key_id: createBotDto.key_id,
      provider_config_override: createBotDto.provider_config_override,
      system_prompt: createBotDto.system_prompt,
      temperature: createBotDto.temperature,
      max_tokens: createBotDto.max_tokens,
      response_format: createBotDto.response_format || AiBotResponseFormat.TEXT,
      is_enabled: createBotDto.is_enabled ?? true,
      is_template: createBotDto.is_template ?? false,
      scene: createBotDto.scene,
      tenant_id: createBotDto.tenant_id,
      workspace_id: createBotDto.workspace_id,
      created_by: userId,
      updated_by: userId,
      created_at: now,
      updated_at: now,
    };

    return this.prisma.ai_bots.create({
      data,
      include: {
        ai_keys: true,
        ai_models: true,
      },
    });
  }

  /**
   * 更新 Bot
   */
  async update(id: string, updateBotDto: UpdateBotDto, userId: string) {
    const existingBot = await this.findOne(id);

    const data = {
      ...updateBotDto,
      updated_by: userId,
    };

    return this.prisma.ai_bots.update({
      where: { id },
      data,
      include: {
        ai_keys: true,
        ai_models: true,
      },
    });
  }

  /**
   * 刪除 Bot
   */
  async delete(id: string) {
    const existingBot = await this.findOne(id);

    return this.prisma.ai_bots.delete({
      where: { id },
    });
  }

  /**
   * 測試 Bot
   */
  async testBot(
    botId: string,
    message: string,
    prompt?: string,
    temperature?: number,
    systemPrompt?: string
  ) {
    const bot = await this.prisma.ai_bots.findUnique({
      where: { id: botId },
      include: {
        ai_models: true,
        ai_keys: true,
      },
    });

    if (!bot) {
      throw new NotFoundException(`Bot with ID ${botId} not found`);
    }

    if (!bot.ai_keys) {
      throw new Error("Bot does not have an associated API key");
    }

    // 這裡應該實作實際的 AI 提供者調用邏輯
    // 目前返回模擬回應
    return {
      success: true,
      response: `Test response from ${bot.name}: ${message}`,
      model: bot.ai_models?.model_name,
      temperature: temperature || bot.temperature,
      systemPrompt: systemPrompt || bot.system_prompt,
    };
  }

  /**
   * 優化提示詞
   */
  async optimizePrompt(dto: OptimizePromptDto) {
    // 這裡應該實作提示詞優化邏輯
    // 目前返回模擬回應
    return {
      success: true,
      originalPrompt: dto.prompt,
      optimizedPrompt: `Optimized: ${dto.prompt}`,
      suggestions: ["Make it more specific", "Add context", "Clarify the goal"],
    };
  }

  /**
   * 執行 Bot
   */
  async execute(id: string, executeBotDto: ExecuteBotDto) {
    const bot = await this.findOne(id);

    if (!bot.is_enabled) {
      throw new Error("Bot is disabled");
    }

    // 這裡應該實作實際的 Bot 執行邏輯
    // 目前返回模擬回應
    return {
      success: true,
      botId: id,
      input: executeBotDto.messages || "No input provided",
      output: `Response from ${bot.name}`,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * 更新 Bot 啟用狀態
   */
  async updateStatus(id: string, isEnabled: boolean) {
    const existingBot = await this.findOne(id);

    return this.prisma.ai_bots.update({
      where: { id },
      data: { is_enabled: isEnabled },
    });
  }

  private calculateCost(
    inputTokens: number,
    outputTokens: number,
    inputPrice: number,
    outputPrice: number
  ): number {
    const inputCost = (inputTokens / 1000) * inputPrice;
    const outputCost = (outputTokens / 1000) * outputPrice;
    return inputCost + outputCost;
  }

  /**
   * 查找默認 Bot
   */
  async findDefaultBot(tenantId: string, scope: AiBotScope) {
    const bot = await this.prisma.ai_bots.findFirst({
      where: {
        tenant_id: tenantId,
        scope: scope,
        is_enabled: true,
      },
      include: {
        ai_models: true,
        ai_keys: true,
      },
      orderBy: {
        created_at: "asc", // 使用最早創建的 Bot 作為默認
      },
    });

    return bot;
  }

  /**
   * 執行 Bot 並返回結果
   */
  async executeBot(
    botId: string,
    messages: AiMessage[],
    options: AiExecuteOptions
  ) {
    const bot = await this.findOne(botId);

    if (!bot.is_enabled) {
      throw new Error("Bot is disabled");
    }

    if (!bot.ai_keys) {
      throw new Error("Bot does not have an associated API key");
    }

    // 解密 API 密鑰
    const decryptedApiKey = this.encryptionService.decrypt(bot.ai_keys.api_key);

    // 創建 AI 提供者
    const provider = this.providerFactory.createProvider(
      bot.provider_type,
      decryptedApiKey,
      bot.ai_keys.api_url || undefined
    );

    // 執行 AI 請求
    const result = await provider.execute(messages, {
      model: bot.ai_models?.model_name || options.model,
      temperature: bot.temperature || options.temperature,
      maxTokens: bot.max_tokens || options.maxTokens,
      responseFormat: bot.response_format || options.responseFormat,
    });

    return result;
  }

  private getProviderTypeFromKeyProvider(provider: string): AiBotProviderType {
    switch (provider.toLowerCase()) {
      case "openai":
        return AiBotProviderType.OPENAI;
      case "anthropic":
        return AiBotProviderType.CLAUDE;
      case "google-gemini":
        return AiBotProviderType.GEMINI;
      case "openai-compatible":
        return AiBotProviderType.OPENAI_COMPATIBLE;
      default:
        return AiBotProviderType.OPENAI; // 預設使用 OpenAI
    }
  }
}
