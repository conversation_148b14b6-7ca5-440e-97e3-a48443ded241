import { Test, TestingModule } from "@nestjs/testing";
import {
  INestApplication,
  HttpStatus,
  NotFoundException,
} from "@nestjs/common";
import request from "supertest";
import { AiBotsController } from "../ai-bots.controller";
import { AiBotsService } from "../ai-bots.service";
import { JwtAuthGuard } from "../../../../core/auth/guards/auth.guard";
import { PrismaClient } from "@prisma/client";
import {
  CreateBotDto,
  UpdateBotDto,
  TestBotDto,
  OptimizePromptDto,
  ExecuteBotDto,
} from "../dto/bot.dto";
import {
  AiBotScope,
  AiBotProviderType,
  AiBotResponseFormat,
} from "@prisma/client";

// Mock PrismaClient
const prismaMock = {
  ai_bots: {
    create: jest.fn(),
    findMany: jest.fn(),
    findUnique: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  },
  ai_models: {
    findUnique: jest.fn(),
  },
  ai_keys: {
    findUnique: jest.fn(),
  },
};

// Mock AiBotsService
const aiBotsServiceMock = {
  create: jest.fn(),
  findAll: jest.fn(),
  findOne: jest.fn(),
  update: jest.fn(),
  delete: jest.fn(),
  testBot: jest.fn(),
  optimizePrompt: jest.fn(),
  execute: jest.fn(),
  updateStatus: jest.fn(),
};

describe("AiBotsController (Integration)", () => {
  let app: INestApplication;
  let aiBotsService: AiBotsService;

  const mockUser = {
    id: "mockUserId",
    email: "<EMAIL>",
    name: "Test User",
  };

  const mockBot = {
    id: "mockBotId",
    name: "Test Bot",
    description: "A test bot",
    scope: AiBotScope.WORKSPACE,
    provider_type: AiBotProviderType.OPENAI,
    model_id: "mockModelId",
    key_id: "mockKeyId",
    provider_config_override: {},
    system_prompt: "You are a helpful AI assistant.",
    temperature: 0.7,
    max_tokens: 1000,
    response_format: AiBotResponseFormat.TEXT,
    is_enabled: true,
    is_template: false,
    scene: null,
    tenant_id: "mockTenantId",
    workspace_id: "mockWorkspaceId",
    created_at: new Date("2025-06-10T09:31:30.196Z").toISOString(),
    updated_at: new Date("2025-06-10T09:31:30.196Z").toISOString(),
    created_by: "mockUserId",
    updated_by: null,
  };

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      controllers: [AiBotsController],
      providers: [
        {
          provide: AiBotsService,
          useValue: aiBotsServiceMock,
        },
        {
          provide: PrismaClient,
          useValue: prismaMock,
        },
      ],
    })
      .overrideGuard(JwtAuthGuard) // Mock JwtAuthGuard to allow requests without actual JWT
      .useValue({
        canActivate: (context) => {
          const req = context.switchToHttp().getRequest();
          req.user = mockUser; // Attach mock user to request
          return true;
        },
      })
      .compile();

    app = moduleFixture.createNestApplication();
    await app.init();
    aiBotsService = moduleFixture.get<AiBotsService>(AiBotsService);
  });

  afterAll(async () => {
    await app.close();
  });

  beforeEach(() => {
    jest.clearAllMocks(); // Clear mocks before each test
  });

  it("should be defined", () => {
    expect(aiBotsService).toBeDefined();
    expect(app).toBeDefined();
  });

  describe("GET /admin/ai/bots", () => {
    it("should return an array of bots", async () => {
      aiBotsServiceMock.findAll.mockResolvedValue([mockBot]);

      const response = await request(app.getHttpServer())
        .get("/admin/ai/bots")
        .expect(HttpStatus.OK);

      expect(response.body).toEqual([mockBot]);
      expect(aiBotsServiceMock.findAll).toHaveBeenCalledWith(
        undefined,
        undefined,
        undefined
      );
    });

    it("should return bots filtered by scope", async () => {
      aiBotsServiceMock.findAll.mockResolvedValue([mockBot]);

      const response = await request(app.getHttpServer())
        .get("/admin/ai/bots?scope=WORKSPACE")
        .expect(HttpStatus.OK);

      expect(response.body).toEqual([mockBot]);
      expect(aiBotsServiceMock.findAll).toHaveBeenCalledWith(
        AiBotScope.WORKSPACE,
        undefined,
        undefined
      );
    });
  });

  describe("GET /admin/ai/bots/:id", () => {
    it("should return a single bot by ID", async () => {
      aiBotsServiceMock.findOne.mockResolvedValue(mockBot);

      const response = await request(app.getHttpServer())
        .get(`/admin/ai/bots/${mockBot.id}`)
        .expect(HttpStatus.OK);

      expect(response.body).toEqual(mockBot);
      expect(aiBotsServiceMock.findOne).toHaveBeenCalledWith(mockBot.id);
    });

    it("should return 404 if bot not found", async () => {
      aiBotsServiceMock.findOne.mockRejectedValue(
        new NotFoundException("Bot not found")
      );

      await request(app.getHttpServer())
        .get(`/admin/ai/bots/nonExistentId`)
        .expect(HttpStatus.NOT_FOUND);
    });
  });

  describe("POST /admin/ai/bots", () => {
    it("should create a new bot", async () => {
      const createDto: CreateBotDto = {
        name: "New Bot",
        scope: AiBotScope.TENANT_TEMPLATE,
        provider_type: AiBotProviderType.OPENAI,
        model_id: "newModelId",
        key_id: "newKeyId",
      };
      aiBotsServiceMock.create.mockResolvedValue({ ...mockBot, ...createDto });

      const response = await request(app.getHttpServer())
        .post("/admin/ai/bots")
        .send(createDto)
        .expect(HttpStatus.CREATED);

      expect(response.body).toEqual({ ...mockBot, ...createDto });
      expect(aiBotsServiceMock.create).toHaveBeenCalledWith(
        createDto,
        mockUser.id
      );
    });
  });

  describe("PUT /admin/ai/bots/:id", () => {
    it("should update an existing bot", async () => {
      const updateDto: UpdateBotDto = {
        name: "Updated Bot Name",
        temperature: 0.8,
      };
      aiBotsServiceMock.update.mockResolvedValue({ ...mockBot, ...updateDto });

      const response = await request(app.getHttpServer())
        .put(`/admin/ai/bots/${mockBot.id}`)
        .send(updateDto)
        .expect(HttpStatus.OK);

      expect(response.body).toEqual({ ...mockBot, ...updateDto });
      expect(aiBotsServiceMock.update).toHaveBeenCalledWith(
        mockBot.id,
        updateDto,
        mockUser.id
      );
    });
  });

  describe("DELETE /admin/ai/bots/:id", () => {
    it("should delete a bot", async () => {
      aiBotsServiceMock.delete.mockResolvedValue(mockBot);

      const response = await request(app.getHttpServer())
        .delete(`/admin/ai/bots/${mockBot.id}`)
        .expect(HttpStatus.OK);

      expect(response.body).toEqual(mockBot);
      expect(aiBotsServiceMock.delete).toHaveBeenCalledWith(mockBot.id);
    });
  });

  describe("POST /admin/ai/bots/test", () => {
    it("should test bot settings", async () => {
      const testDto: TestBotDto = {
        botId: mockBot.id,
        message: "Hello, bot!",
      };
      const testResponse = { reply: "Bot says: Hello!" };
      aiBotsServiceMock.testBot.mockResolvedValue(testResponse);

      const response = await request(app.getHttpServer())
        .post("/admin/ai/bots/test")
        .send(testDto)
        .expect(HttpStatus.CREATED);

      expect(response.body).toEqual(testResponse);
      expect(aiBotsServiceMock.testBot).toHaveBeenCalledWith(
        testDto.botId,
        testDto.message,
        undefined, // prompt
        undefined // temperature
      );
    });
  });

  describe("POST /admin/ai/bots/optimize-prompt", () => {
    it("should optimize prompt", async () => {
      const optimizeDto: OptimizePromptDto = {
        id: mockBot.id,
        provider: "openai",
        apiKey: "mockApiKey",
        prompt: "Old prompt",
      };
      const optimizedPromptResponse = {
        optimizedPrompt: "New optimized prompt",
      };
      aiBotsServiceMock.optimizePrompt.mockResolvedValue(
        optimizedPromptResponse
      );

      const response = await request(app.getHttpServer())
        .post("/admin/ai/bots/optimize-prompt")
        .send(optimizeDto)
        .expect(HttpStatus.CREATED);

      expect(response.body).toEqual(optimizedPromptResponse);
      expect(aiBotsServiceMock.optimizePrompt).toHaveBeenCalledWith(
        optimizeDto
      );
    });
  });

  describe("POST /admin/ai/bots/:id/chat", () => {
    it("should chat with the specified bot", async () => {
      const message = "Hello, bot via chat!";
      const chatResponse = { reply: "Bot chat reply!" };
      aiBotsServiceMock.testBot.mockResolvedValue(chatResponse);

      const response = await request(app.getHttpServer())
        .post(`/admin/ai/bots/${mockBot.id}/chat`)
        .send({ message })
        .expect(HttpStatus.CREATED);

      expect(response.body).toEqual(chatResponse);
      expect(aiBotsServiceMock.testBot).toHaveBeenCalledWith(
        mockBot.id,
        message,
        undefined, // prompt
        undefined, // temperature
        undefined // systemPrompt
      );
    });

    it("should handle chat failure", async () => {
      aiBotsServiceMock.testBot.mockRejectedValue(new Error("Bot chat failed"));

      const response = await request(app.getHttpServer())
        .post(`/admin/ai/bots/${mockBot.id}/chat`)
        .send({ message: "Hello" })
        .expect(HttpStatus.BAD_REQUEST);

      expect(response.body.message).toEqual("對話失敗");
      expect(response.body.error).toEqual("Bot chat failed");
    });
  });

  describe("POST /admin/ai/bots/:id/execute", () => {
    it("should execute the bot", async () => {
      const executeDto: ExecuteBotDto = {
        messages: [{ role: "user", content: "Execute this command." }],
      };
      const executeResponse = { result: "Command executed." };
      aiBotsServiceMock.execute.mockResolvedValue(executeResponse);

      const response = await request(app.getHttpServer())
        .post(`/admin/ai/bots/${mockBot.id}/execute`)
        .send(executeDto)
        .expect(HttpStatus.CREATED);

      expect(response.body).toEqual(executeResponse);
      expect(aiBotsServiceMock.execute).toHaveBeenCalledWith(
        mockBot.id,
        executeDto
      );
    });
  });

  describe("PUT /admin/ai/bots/:id/status", () => {
    it("should update bot status", async () => {
      aiBotsServiceMock.updateStatus.mockResolvedValue({
        ...mockBot,
        is_enabled: false,
      });

      const response = await request(app.getHttpServer())
        .put(`/admin/ai/bots/${mockBot.id}/status`)
        .send({ isEnabled: false })
        .expect(HttpStatus.OK);

      expect(response.body).toEqual({ ...mockBot, is_enabled: false });
      expect(aiBotsServiceMock.updateStatus).toHaveBeenCalledWith(
        mockBot.id,
        false
      );
    });
  });
});
