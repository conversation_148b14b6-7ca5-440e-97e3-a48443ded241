import {
  Controller,
  Post,
  Body,
  HttpException,
  HttpStatus,
  Get,
  Put,
  Delete,
  Param,
  Query,
  Request,
  UseGuards,
  Logger,
} from "@nestjs/common";
import { ApiTags, ApiOperation, ApiBearerAuth } from "@nestjs/swagger";
import { AiBotsService } from "./ai-bots.service";
import {
  CreateBotDto,
  UpdateBotDto,
  TestBotDto,
  OptimizePromptDto,
  ExecuteBotDto,
} from "./dto/bot.dto";
import { JwtAuthGuard } from "../../../core/auth/guards/auth.guard";
import { CurrentUser } from "../../../core/auth/decorators/current-user.decorator";
import { PrismaClient, AiBotScope } from "@prisma/client";

// 定義 User 類型
interface User {
  id: string;
  email: string;
  name?: string;
}

@ApiTags("admin/ai-bots")
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller("admin/ai/bots")
export class AiBotsController {
  private readonly logger = new Logger(AiBotsController.name);

  constructor(private readonly aiBotsService: AiBotsService) {}

  @Get()
  @ApiOperation({ summary: "讀取所有 Bot" })
  async findAll(
    @Query("scope") scope?: AiBotScope,
    @Query("tenantId") tenantId?: string,
    @Query("workspaceId") workspaceId?: string
  ) {
    return this.aiBotsService.findAll(scope, tenantId, workspaceId);
  }

  @Get(":id")
  @ApiOperation({ summary: "根據 ID 讀取 Bot" })
  async findOne(@Param("id") id: string) {
    return this.aiBotsService.findOne(id);
  }

  @Post()
  @ApiOperation({ summary: "建立新 Bot" })
  async create(@Body() createBotDto: CreateBotDto, @CurrentUser() user: User) {
    return this.aiBotsService.create(createBotDto, user.id);
  }

  @Put(":id")
  @ApiOperation({ summary: "更新 Bot" })
  async update(
    @Param("id") id: string,
    @Body() updateBotDto: UpdateBotDto,
    @CurrentUser() user: User
  ) {
    return this.aiBotsService.update(id, updateBotDto, user.id);
  }

  @Delete(":id")
  @ApiOperation({ summary: "刪除 Bot" })
  async remove(@Param("id") id: string) {
    return this.aiBotsService.delete(id);
  }

  @Post("test")
  @ApiOperation({ summary: "測試 Bot 設定" })
  async testBot(@Body() dto: TestBotDto) {
    return await this.aiBotsService.testBot(
      dto.botId,
      dto.message,
      dto.prompt,
      dto.temperature
    );
  }

  @Post("optimize-prompt")
  @ApiOperation({ summary: "優化提示詞" })
  async optimizePrompt(@Body() dto: OptimizePromptDto) {
    return await this.aiBotsService.optimizePrompt(dto);
  }

  @Post(":id/chat")
  @ApiOperation({ summary: "使用指定 Bot 進行對話" })
  async chat(
    @Param("id") id: string,
    @Body()
    body: {
      message: string;
      temperature?: number;
      prompt?: string;
      systemPrompt?: string;
    }
  ) {
    this.logger.log(`使用 Bot ${id} 進行對話，訊息: ${body.message}`);
    try {
      const response = await this.aiBotsService.testBot(
        id,
        body.message,
        body.prompt,
        body.temperature,
        body.systemPrompt
      );
      return response;
    } catch (error) {
      this.logger.error(`Bot ${id} 對話失敗: ${error.message}`, error.stack);
      throw new HttpException(
        {
          statusCode: HttpStatus.BAD_REQUEST,
          message: "對話失敗",
          error: error.message,
          timestamp: new Date().toISOString(),
          details: JSON.stringify(body, null, 2),
        },
        HttpStatus.BAD_REQUEST
      );
    }
  }

  @Post(":id/execute")
  @ApiOperation({ summary: "執行 Bot" })
  async execute(@Param("id") id: string, @Body() executeBotDto: ExecuteBotDto) {
    return this.aiBotsService.execute(id, executeBotDto);
  }

  @Put(":id/status")
  @ApiOperation({ summary: "更新 Bot 啟用狀態" })
  async updateStatus(
    @Param("id") id: string,
    @Body("isEnabled") isEnabled: boolean
  ) {
    return this.aiBotsService.updateStatus(id, isEnabled);
  }
}
