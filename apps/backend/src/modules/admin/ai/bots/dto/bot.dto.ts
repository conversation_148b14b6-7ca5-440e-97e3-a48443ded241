import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>al,
  Is<PERSON>umber,
  Is<PERSON>num,
  Min,
  <PERSON>,
  IsBoolean,
  IsJSON,
  IsNotEmpty,
  IsArray,
  ValidateNested,
  IsObject,
  IsUUID,
} from "class-validator";
import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Type } from "class-transformer";
import {
  AiBotProviderType,
  AiBotResponseFormat,
  AiBotScope,
} from "@prisma/client";
import { AiMessage } from "@/modules/admin/ai/core/providers/base/base.provider";

export class CreateBotDto {
  @ApiProperty({ description: "Bot 名稱" })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiPropertyOptional({ description: "Bot 描述" })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({ description: "Bot 範圍", enum: AiBotScope })
  @IsEnum(AiBotScope)
  scope: AiBotScope;

  @ApiProperty({ description: "AI Provider 類型", enum: AiBotProviderType })
  @IsEnum(AiBotProviderType)
  provider_type: AiBotProviderType;

  @ApiProperty({ description: "AI Model ID" })
  @IsString()
  @IsNotEmpty()
  model_id: string;

  @ApiProperty({ description: "API Key ID" })
  @IsString()
  @IsNotEmpty()
  key_id: string;

  @ApiPropertyOptional({ description: "Provider 配置覆蓋" })
  @IsObject()
  @IsOptional()
  provider_config_override?: Record<string, any>;

  @ApiPropertyOptional({ description: "系統提示詞" })
  @IsString()
  @IsOptional()
  system_prompt?: string;

  @ApiPropertyOptional({ description: "溫度", minimum: 0, maximum: 1 })
  @IsNumber()
  @Min(0)
  @Max(1)
  @IsOptional()
  @Type(() => Number)
  temperature?: number;

  @ApiPropertyOptional({ description: "最大輸出 Token 數" })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  max_tokens?: number;

  @ApiPropertyOptional({ description: "回應格式", enum: AiBotResponseFormat })
  @IsEnum(AiBotResponseFormat)
  @IsOptional()
  response_format?: AiBotResponseFormat;

  @ApiPropertyOptional({ description: "是否啟用" })
  @IsBoolean()
  @IsOptional()
  is_enabled?: boolean;

  @ApiPropertyOptional({ description: "是否為範本" })
  @IsBoolean()
  @IsOptional()
  is_template?: boolean;

  @ApiPropertyOptional({ description: "場景" })
  @IsString()
  @IsOptional()
  scene?: string;

  @ApiPropertyOptional({ description: "租戶 ID" })
  @IsString()
  @IsOptional()
  tenant_id?: string;

  @ApiPropertyOptional({ description: "工作區 ID" })
  @IsString()
  @IsOptional()
  workspace_id?: string;
}

export class UpdateBotDto {
  @ApiPropertyOptional({ description: "Bot 名稱" })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiPropertyOptional({ description: "Bot 描述" })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({ description: "AI Model ID" })
  @IsString()
  @IsOptional()
  model_id?: string;

  @ApiPropertyOptional({ description: "API Key ID" })
  @IsString()
  @IsOptional()
  key_id?: string;

  @ApiPropertyOptional({ description: "Provider 配置覆蓋" })
  @IsObject()
  @IsOptional()
  provider_config_override?: Record<string, any>;

  @ApiPropertyOptional({ description: "系統提示詞" })
  @IsString()
  @IsOptional()
  system_prompt?: string;

  @ApiPropertyOptional({ description: "溫度", minimum: 0, maximum: 1 })
  @IsNumber()
  @Min(0)
  @Max(1)
  @IsOptional()
  @Type(() => Number)
  temperature?: number;

  @ApiPropertyOptional({ description: "最大輸出 Token 數" })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  max_tokens?: number;

  @ApiPropertyOptional({ description: "回應格式", enum: AiBotResponseFormat })
  @IsEnum(AiBotResponseFormat)
  @IsOptional()
  response_format?: AiBotResponseFormat;

  @ApiPropertyOptional({ description: "是否啟用" })
  @IsBoolean()
  @IsOptional()
  is_enabled?: boolean;

  @ApiPropertyOptional({ description: "是否為範本" })
  @IsBoolean()
  @IsOptional()
  is_template?: boolean;

  @ApiPropertyOptional({ description: "場景" })
  @IsString()
  @IsOptional()
  scene?: string;

  @ApiPropertyOptional({ description: "租戶 ID" })
  @IsString()
  @IsOptional()
  tenant_id?: string;

  @ApiPropertyOptional({ description: "工作區 ID" })
  @IsString()
  @IsOptional()
  workspace_id?: string;
}

export class TestBotDto {
  @ApiProperty({ description: "Bot ID" })
  @IsUUID()
  @IsNotEmpty()
  botId: string;

  @ApiProperty({ description: "測試訊息" })
  @IsString()
  message: string;

  @ApiProperty({ description: "覆寫提示詞" })
  @IsString()
  @IsOptional()
  prompt?: string;

  @ApiProperty({ description: "覆寫溫度" })
  @IsNumber()
  @Min(0)
  @Max(2)
  @IsOptional()
  temperature?: number;
}

export class OptimizePromptDto {
  @ApiProperty({ description: "Bot ID" })
  @IsString()
  @IsNotEmpty()
  id: string;

  @ApiProperty({
    description: "AI Provider",
    enum: ["openai", "claude", "openai-compatible"],
  })
  @IsEnum(["openai", "claude", "openai-compatible"])
  provider: string;

  @ApiProperty({ description: "API 金鑰" })
  @IsString()
  apiKey: string;

  @ApiProperty({ description: "API 端點 (僅 OpenAI Compatible 需要)" })
  @IsString()
  @IsOptional()
  apiUrl?: string;

  @ApiProperty({ description: "要優化的提示詞" })
  @IsString()
  prompt: string;

  @ApiProperty({ description: "場景描述" })
  @IsString()
  @IsOptional()
  scene?: string;

  @ApiProperty({ description: "優化需求" })
  @IsString()
  @IsOptional()
  requirement?: string;
}

export class ExecuteBotDto {
  @IsArray()
  messages: AiMessage[];

  @IsNumber()
  @IsOptional()
  temperature?: number;
}
