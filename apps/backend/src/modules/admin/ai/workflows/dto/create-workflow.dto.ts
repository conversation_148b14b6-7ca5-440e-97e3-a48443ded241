import {
  IsString,
  IsOptional,
  IsBoolean,
  IsObject,
  IsEnum,
} from "class-validator";
import { WorkflowVisibility, WorkflowStatus } from "@prisma/client";

export class CreateWorkflowDto {
  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsString()
  version?: string = "1.0.0";

  @IsOptional()
  @IsBoolean()
  is_published?: boolean = false;

  @IsOptional()
  @IsBoolean()
  is_template?: boolean = false;

  @IsOptional()
  @IsObject()
  config?: any;

  @IsOptional()
  @IsObject()
  input_schema?: any;

  @IsOptional()
  @IsObject()
  output_schema?: any;

  @IsOptional()
  @IsEnum(WorkflowVisibility)
  visibility?: WorkflowVisibility = WorkflowVisibility.PRIVATE;

  @IsOptional()
  @IsString()
  tenant_id?: string;

  @IsOptional()
  @IsString()
  workspace_id?: string;

  @IsOptional()
  @IsEnum(WorkflowStatus)
  status?: WorkflowStatus = WorkflowStatus.DRAFT;
}
