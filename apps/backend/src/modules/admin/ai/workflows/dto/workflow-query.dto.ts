import { IsOptional, IsEnum, IsBoolean, IsString } from "class-validator";
import { WorkflowStatus, WorkflowVisibility } from "@prisma/client";

export class WorkflowQueryDto {
  @IsOptional()
  @IsString()
  tenantId?: string;

  @IsOptional()
  @IsString()
  workspaceId?: string;

  @IsOptional()
  @IsEnum(WorkflowStatus)
  status?: WorkflowStatus;

  @IsOptional()
  @IsEnum(WorkflowVisibility)
  visibility?: WorkflowVisibility;

  @IsOptional()
  @IsBoolean()
  isPublished?: boolean;
}
