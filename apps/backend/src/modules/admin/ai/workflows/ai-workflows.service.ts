import {
  Injectable,
  NotFoundException,
  ForbiddenException,
} from "@nestjs/common";
import { PrismaService } from "@/modules/core/prisma/prisma.service";
import {
  CreateWorkflowDto,
  UpdateWorkflowDto,
  CreateWorkflowNodeDto,
  UpdateWorkflowNodeDto,
  CreateNodeConnectionDto,
} from "./dto";
import {
  WorkflowStatus,
  WorkflowVisibility,
  WorkflowNodeType,
} from "@prisma/client";

@Injectable()
export class AiWorkflowsService {
  constructor(private prisma: PrismaService) {}

  // 工作流程 CRUD 操作
  async create(data: CreateWorkflowDto, userId: string) {
    return this.prisma.ai_workflows.create({
      data: {
        ...data,
        created_by: userId,
        updated_by: userId,
      },
      include: {
        nodes: {
          include: {
            input_connections: true,
            output_connections: true,
          },
        },
      },
    });
  }

  async findAll(filters?: {
    tenantId?: string;
    workspaceId?: string;
    status?: WorkflowStatus;
    visibility?: WorkflowVisibility;
    isPublished?: boolean;
  }) {
    const where: any = {};

    if (filters?.tenantId) where.tenant_id = filters.tenantId;
    if (filters?.workspaceId) where.workspace_id = filters.workspaceId;
    if (filters?.status) where.status = filters.status;
    if (filters?.visibility) where.visibility = filters.visibility;
    if (filters?.isPublished !== undefined)
      where.is_published = filters.isPublished;

    return this.prisma.ai_workflows.findMany({
      where,
      include: {
        nodes: {
          include: {
            input_connections: true,
            output_connections: true,
          },
        },
      },
      orderBy: {
        updated_at: "desc",
      },
    });
  }

  async findOne(id: string) {
    const workflow = await this.prisma.ai_workflows.findUnique({
      where: { id },
      include: {
        nodes: {
          include: {
            input_connections: {
              include: {
                source_node: true,
              },
            },
            output_connections: {
              include: {
                target_node: true,
              },
            },
          },
        },
        executions: {
          orderBy: {
            started_at: "desc",
          },
          take: 10, // 最近 10 次執行記錄
        },
      },
    });

    if (!workflow) {
      throw new NotFoundException(`Workflow with ID ${id} not found`);
    }

    return workflow;
  }

  async update(id: string, data: UpdateWorkflowDto, userId: string) {
    // 檢查工作流程是否存在
    const existing = await this.prisma.ai_workflows.findUnique({
      where: { id },
    });

    if (!existing) {
      throw new NotFoundException(`Workflow with ID ${id} not found`);
    }

    // 檢查權限（只有創建者可以編輯）
    if (existing.created_by !== userId) {
      throw new ForbiddenException("You can only edit workflows you created");
    }

    return this.prisma.ai_workflows.update({
      where: { id },
      data: {
        ...data,
        updated_by: userId,
      },
      include: {
        nodes: {
          include: {
            input_connections: true,
            output_connections: true,
          },
        },
      },
    });
  }

  async remove(id: string, userId: string) {
    // 檢查工作流程是否存在
    const existing = await this.prisma.ai_workflows.findUnique({
      where: { id },
    });

    if (!existing) {
      throw new NotFoundException(`Workflow with ID ${id} not found`);
    }

    // 檢查權限（只有創建者可以刪除）
    if (existing.created_by !== userId) {
      throw new ForbiddenException("You can only delete workflows you created");
    }

    return this.prisma.ai_workflows.delete({
      where: { id },
    });
  }

  // 工作流程節點管理
  async createNode(
    workflowId: string,
    data: CreateWorkflowNodeDto,
    userId: string
  ) {
    // 檢查工作流程權限
    await this.checkWorkflowPermission(workflowId, userId);

    return this.prisma.workflow_nodes.create({
      data: {
        ...data,
        workflow_id: workflowId,
      },
      include: {
        input_connections: true,
        output_connections: true,
      },
    });
  }

  async updateNode(
    nodeId: string,
    data: UpdateWorkflowNodeDto,
    userId: string
  ) {
    const node = await this.prisma.workflow_nodes.findUnique({
      where: { id: nodeId },
      include: { workflow: true },
    });

    if (!node) {
      throw new NotFoundException(`Node with ID ${nodeId} not found`);
    }

    // 檢查工作流程權限
    await this.checkWorkflowPermission(node.workflow_id, userId);

    return this.prisma.workflow_nodes.update({
      where: { id: nodeId },
      data,
      include: {
        input_connections: true,
        output_connections: true,
      },
    });
  }

  async removeNode(nodeId: string, userId: string) {
    const node = await this.prisma.workflow_nodes.findUnique({
      where: { id: nodeId },
      include: { workflow: true },
    });

    if (!node) {
      throw new NotFoundException(`Node with ID ${nodeId} not found`);
    }

    // 檢查工作流程權限
    await this.checkWorkflowPermission(node.workflow_id, userId);

    return this.prisma.workflow_nodes.delete({
      where: { id: nodeId },
    });
  }

  // 節點連接管理
  async createConnection(data: CreateNodeConnectionDto, userId: string) {
    // 檢查源節點和目標節點是否屬於同一個工作流程
    const [sourceNode, targetNode] = await Promise.all([
      this.prisma.workflow_nodes.findUnique({
        where: { id: data.source_node_id },
        include: { workflow: true },
      }),
      this.prisma.workflow_nodes.findUnique({
        where: { id: data.target_node_id },
        include: { workflow: true },
      }),
    ]);

    if (!sourceNode || !targetNode) {
      throw new NotFoundException("Source or target node not found");
    }

    if (sourceNode.workflow_id !== targetNode.workflow_id) {
      throw new ForbiddenException(
        "Cannot connect nodes from different workflows"
      );
    }

    // 檢查工作流程權限
    await this.checkWorkflowPermission(sourceNode.workflow_id, userId);

    return this.prisma.node_connections.create({
      data,
      include: {
        source_node: true,
        target_node: true,
      },
    });
  }

  async removeConnection(connectionId: string, userId: string) {
    const connection = await this.prisma.node_connections.findUnique({
      where: { id: connectionId },
      include: {
        source_node: {
          include: { workflow: true },
        },
      },
    });

    if (!connection) {
      throw new NotFoundException(
        `Connection with ID ${connectionId} not found`
      );
    }

    // 檢查工作流程權限
    await this.checkWorkflowPermission(
      connection.source_node.workflow_id,
      userId
    );

    return this.prisma.node_connections.delete({
      where: { id: connectionId },
    });
  }

  // 工作流程發布管理
  async publish(id: string, userId: string) {
    await this.checkWorkflowPermission(id, userId);

    return this.prisma.ai_workflows.update({
      where: { id },
      data: {
        is_published: true,
        status: WorkflowStatus.PUBLISHED,
        published_at: new Date(),
        updated_by: userId,
      },
    });
  }

  async unpublish(id: string, userId: string) {
    await this.checkWorkflowPermission(id, userId);

    return this.prisma.ai_workflows.update({
      where: { id },
      data: {
        is_published: false,
        status: WorkflowStatus.DRAFT,
        published_at: null,
        updated_by: userId,
      },
    });
  }

  // 複製工作流程
  async duplicate(id: string, userId: string) {
    const originalWorkflow = await this.findOne(id);

    // 檢查是否有讀取權限（公開或者是創建者）
    if (
      originalWorkflow.visibility === WorkflowVisibility.PRIVATE &&
      originalWorkflow.created_by !== userId
    ) {
      throw new ForbiddenException("Cannot duplicate private workflow");
    }

    return this.prisma.$transaction(async (tx) => {
      // 創建新工作流程
      const newWorkflow = await tx.ai_workflows.create({
        data: {
          name: `${originalWorkflow.name} (Copy)`,
          description: originalWorkflow.description,
          version: "1.0.0",
          is_published: false,
          is_template: false,
          config: originalWorkflow.config as any,
          input_schema: originalWorkflow.input_schema as any,
          output_schema: originalWorkflow.output_schema as any,
          visibility: WorkflowVisibility.PRIVATE,
          status: WorkflowStatus.DRAFT,
          created_by: userId,
          updated_by: userId,
          tenant_id: originalWorkflow.tenant_id,
          workspace_id: originalWorkflow.workspace_id,
        },
      });

      // 複製節點
      const nodeIdMapping: Record<string, string> = {};
      for (const node of originalWorkflow.nodes) {
        const newNode = await tx.workflow_nodes.create({
          data: {
            workflow_id: newWorkflow.id,
            name: node.name,
            description: node.description,
            node_type: node.node_type,
            position_x: node.position_x,
            position_y: node.position_y,
            config: node.config as any,
            execution_order: node.execution_order,
            is_enabled: node.is_enabled,
          },
        });
        nodeIdMapping[node.id] = newNode.id;
      }

      // 複製連接
      for (const node of originalWorkflow.nodes) {
        for (const connection of node.output_connections) {
          await tx.node_connections.create({
            data: {
              source_node_id: nodeIdMapping[connection.source_node_id],
              target_node_id: nodeIdMapping[connection.target_node_id],
              source_port: connection.source_port,
              target_port: connection.target_port,
              config: connection.config as any,
              is_enabled: connection.is_enabled,
            },
          });
        }
      }

      return newWorkflow;
    });
  }

  // 私有方法：檢查工作流程權限
  private async checkWorkflowPermission(workflowId: string, userId: string) {
    const workflow = await this.prisma.ai_workflows.findUnique({
      where: { id: workflowId },
    });

    if (!workflow) {
      throw new NotFoundException(`Workflow with ID ${workflowId} not found`);
    }

    if (workflow.created_by !== userId) {
      throw new ForbiddenException("You can only modify workflows you created");
    }

    return workflow;
  }

  // 獲取可用的節點類型
  getAvailableNodeTypes() {
    return Object.values(WorkflowNodeType).map((type) => ({
      type,
      name: this.getNodeTypeName(type),
      description: this.getNodeTypeDescription(type),
      category: this.getNodeTypeCategory(type),
    }));
  }

  private getNodeTypeName(type: WorkflowNodeType): string {
    const names = {
      [WorkflowNodeType.INPUT]: "Input",
      [WorkflowNodeType.OUTPUT]: "Output",
      [WorkflowNodeType.AI_BOT]: "AI Bot",
      [WorkflowNodeType.AI_ANALYSIS]: "AI Analysis",
      [WorkflowNodeType.PROMPT_TEMPLATE]: "Prompt Template",
      [WorkflowNodeType.DATA_TRANSFORM]: "Data Transform",
      [WorkflowNodeType.DATA_FILTER]: "Data Filter",
      [WorkflowNodeType.DATA_MERGE]: "Data Merge",
      [WorkflowNodeType.CONDITION]: "Condition",
      [WorkflowNodeType.LOOP]: "Loop",
      [WorkflowNodeType.PARALLEL]: "Parallel",
      [WorkflowNodeType.API_CALL]: "API Call",
      [WorkflowNodeType.DATABASE]: "Database",
      [WorkflowNodeType.FILE_OPERATION]: "File Operation",
      [WorkflowNodeType.NOTIFICATION]: "Notification",
      [WorkflowNodeType.EMAIL]: "Email",
      [WorkflowNodeType.WEBHOOK]: "Webhook",
    };
    return names[type] || type;
  }

  private getNodeTypeDescription(type: WorkflowNodeType): string {
    const descriptions = {
      [WorkflowNodeType.INPUT]: "Define workflow input parameters",
      [WorkflowNodeType.OUTPUT]: "Define workflow output",
      [WorkflowNodeType.AI_BOT]: "Execute AI bot with custom configuration",
      [WorkflowNodeType.AI_ANALYSIS]:
        "Perform AI analysis (project, photo, workflow)",
      [WorkflowNodeType.PROMPT_TEMPLATE]: "Apply prompt template",
      [WorkflowNodeType.DATA_TRANSFORM]: "Transform data structure",
      [WorkflowNodeType.DATA_FILTER]: "Filter data based on conditions",
      [WorkflowNodeType.DATA_MERGE]: "Merge multiple data sources",
      [WorkflowNodeType.CONDITION]: "Conditional branching",
      [WorkflowNodeType.LOOP]: "Repeat operations",
      [WorkflowNodeType.PARALLEL]: "Execute operations in parallel",
      [WorkflowNodeType.API_CALL]: "Call external API",
      [WorkflowNodeType.DATABASE]: "Database operations",
      [WorkflowNodeType.FILE_OPERATION]: "File system operations",
      [WorkflowNodeType.NOTIFICATION]: "Send notifications",
      [WorkflowNodeType.EMAIL]: "Send email",
      [WorkflowNodeType.WEBHOOK]: "Send webhook",
    };
    return descriptions[type] || "No description available";
  }

  private getNodeTypeCategory(type: WorkflowNodeType): string {
    const categories = {
      [WorkflowNodeType.INPUT]: "IO",
      [WorkflowNodeType.OUTPUT]: "IO",
      [WorkflowNodeType.AI_BOT]: "AI",
      [WorkflowNodeType.AI_ANALYSIS]: "AI",
      [WorkflowNodeType.PROMPT_TEMPLATE]: "AI",
      [WorkflowNodeType.DATA_TRANSFORM]: "Data",
      [WorkflowNodeType.DATA_FILTER]: "Data",
      [WorkflowNodeType.DATA_MERGE]: "Data",
      [WorkflowNodeType.CONDITION]: "Control",
      [WorkflowNodeType.LOOP]: "Control",
      [WorkflowNodeType.PARALLEL]: "Control",
      [WorkflowNodeType.API_CALL]: "Integration",
      [WorkflowNodeType.DATABASE]: "Integration",
      [WorkflowNodeType.FILE_OPERATION]: "Integration",
      [WorkflowNodeType.NOTIFICATION]: "Communication",
      [WorkflowNodeType.EMAIL]: "Communication",
      [WorkflowNodeType.WEBHOOK]: "Communication",
    };
    return categories[type] || "Other";
  }
}
