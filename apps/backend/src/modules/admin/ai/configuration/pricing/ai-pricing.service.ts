import { HttpException, HttpStatus, Injectable, Logger } from "@nestjs/common";
import { PrismaService } from "@/modules/core/prisma/prisma.service";
import { PrismaClient } from "@prisma/client";
import { Decimal } from "@prisma/client/runtime/library";

export class PricingInformationMissingError extends HttpException {
  constructor(message = "模型缺少定價資訊 (輸入和輸出價格皆無)") {
    super(message, HttpStatus.INTERNAL_SERVER_ERROR);
    this.name = "PricingInformationMissingError";
  }
}

@Injectable()
export class AiPricingService {
  private readonly logger = new Logger(AiPricingService.name);

  constructor(private readonly prisma: PrismaService) {}

  /**
   * Calculates the estimated cost of an AI model usage based on input and output tokens.
   * @param modelName The name of the AI model.
   * @param provider The provider of the AI model.
   * @param inputTokens The number of input tokens.
   * @param outputTokens The number of output tokens.
   * @returns The estimated cost as a Decimal.
   * @throws Error if the model is not found or pricing information is missing.
   */
  async calculateCost(
    modelName: string,
    provider: string,
    inputTokens: number,
    outputTokens: number
  ): Promise<Decimal> {
    this.logger.log(
      `Calculating cost for model: ${provider}/${modelName}, input: ${inputTokens}, output: ${outputTokens}`
    );
    const model = await this.prisma.ai_models.findUnique({
      where: { provider_model_name: { provider, model_name: modelName } },
    });

    if (!model) {
      this.logger.error(`Model not found: ${provider}/${modelName}`);
      throw new Error(`AI 模型 ${provider}/${modelName} 未找到`);
    }

    if (!model.input_price_per_1k_tokens || !model.output_price_per_1k_tokens) {
      this.logger.error(
        `Pricing information missing for model ${model.id}. Input price: ${model.input_price_per_1k_tokens}, Output price: ${model.output_price_per_1k_tokens}`
      );
      throw new Error(
        `AI 模型 ${provider}/${modelName} 缺少 input_price_per_1k_tokens 或 output_price_per_1k_tokens`
      );
    }

    const inputCost = new Decimal(model.input_price_per_1k_tokens)
      .mul(inputTokens)
      .div(1000);
    const outputCost = new Decimal(model.output_price_per_1k_tokens)
      .mul(outputTokens)
      .div(1000);
    const totalCost = inputCost.add(outputCost);
    this.logger.log(`Calculated cost for ${model.id}: ${totalCost}`);
    return totalCost;
  }

  /**
   * Calculates the potential cost for a given model and total number of tokens.
   * This function is useful for scenarios where:
   * 1. The exact split between input and output tokens is not yet known (e.g, early-stage estimations).
   * 2. The AI model has a single effective price tier, or input price is the primary reference.
   * 3. UI needs to display a potential cost before detailed token counts are available.
   *
   * The pricing logic prioritizes `input_price_per_1k_tokens`. If it's unavailable,
   * it falls back to `output_price_per_1k_tokens`. If both are unavailable,
   * it throws a `PricingInformationMissingError`.
   *
   * @param modelId The ID of the AI model.
   * @param totalTokens The total number of tokens (can be input, output, or combined).
   * @returns The estimated potential cost as a Decimal.
   * @throws {PricingInformationMissingError} If both input and output pricing information is missing for the model.
   * @throws {Error} If the model is not found (via Prisma's findUniqueOrThrow).
   */
  async calculatePotentialCostForTotalTokens(
    modelId: string,
    totalTokens: number
  ): Promise<Decimal> {
    this.logger.log(
      `Calculating potential cost for modelId: ${modelId}, totalTokens: ${totalTokens}`
    );

    if (totalTokens < 0) {
      this.logger.warn(
        `totalTokens is negative (${totalTokens}) for modelId ${modelId}. Calculation will proceed with the absolute value implicitly if Decimal handles it, or it might lead to negative cost.`
      );
    }

    const model = await this.prisma.ai_models.findUniqueOrThrow({
      where: { id: modelId },
    });

    let priceToUse: Decimal | null = null;
    let priceType = "";

    if (model.input_price_per_1k_tokens) {
      priceToUse = model.input_price_per_1k_tokens;
      priceType = "input";
    } else if (model.output_price_per_1k_tokens) {
      priceToUse = model.output_price_per_1k_tokens;
      priceType = "output";
      this.logger.warn(
        `Input price missing for model ${modelId}. Using output price for potential cost calculation.`
      );
    }

    if (!priceToUse) {
      this.logger.error(
        `Both input and output pricing information are missing for model ${modelId}. Cannot calculate potential cost.`
      );
      throw new PricingInformationMissingError(
        `AI 模型 ${model.provider}/${model.model_name} (ID: ${modelId}) 缺少必要的定價資訊。`
      );
    }

    this.logger.log(
      `Using ${priceType} price (${priceToUse}) for model ${modelId} for potential cost calculation.`
    );

    const totalTokensDecimal = new Decimal(totalTokens);
    const potentialCost = priceToUse.mul(totalTokensDecimal).div(1000);

    this.logger.log(
      `Calculated potential cost for model ${modelId} with ${totalTokens} tokens: ${potentialCost} (using ${priceType} price)`
    );
    return potentialCost;
  }
}
