import { Injectable, Logger } from "@nestjs/common";
import { HttpService } from "@nestjs/axios";
import * as cheerio from "cheerio";
import * as puppeteer from "puppeteer";
import { firstValueFrom } from "rxjs";
import { map } from "rxjs/operators";

export interface ModelPriceInfo {
  provider: string;
  modelName: string;
  displayName?: string;
  inputPricePer1kTokens: number;
  outputPricePer1kTokens: number;
  contextWindowTokens?: number;
  currency?: string;
}

@Injectable()
export class AiPriceCrawlerService {
  private readonly logger = new Logger(AiPriceCrawlerService.name);

  constructor(private readonly httpService: HttpService) {}

  /**
   * 從多個來源擷取模型價格資訊
   */
  async fetchAllModelPrices(): Promise<ModelPriceInfo[]> {
    try {
      // 主要依賴 llmpricecheck.com
      this.logger.log("開始從 llmpricecheck.com 爬取價格...");
      const llmPriceCheckResults = await this.fetchFromLlmPriceCheck();
      this.logger.log(
        `從 llmpricecheck.com 成功爬取 ${llmPriceCheckResults.length} 筆價格資訊。`
      );

      // 其他來源作為備用或未來擴展
      const otherSourcesResults = await Promise.allSettled([
        this.fetchFromOpenAI(), // 目前返回空陣列
        this.fetchFromAnthropicDocs(), // 目前返回空陣列
        this.fetchFromGoogleAI(), // 目前返回空陣列
      ]);

      let allPrices: ModelPriceInfo[] = [...llmPriceCheckResults];

      otherSourcesResults.forEach((result) => {
        if (result.status === "fulfilled" && result.value.length > 0) {
          allPrices.push(...result.value);
          this.logger.log(
            `從其他來源成功爬取 ${result.value.length} 筆價格資訊。`
          );
        } else if (result.status === "rejected") {
          this.logger.error(`爬取其他來源時發生錯誤: ${result.reason}`);
        }
      });

      this.logger.log(`總共合併 ${allPrices.length} 筆價格資訊 (去重前)。`);
      const deduplicated = this.deduplicateModelPrices(allPrices);
      this.logger.log(`去重後剩餘 ${deduplicated.length} 筆價格資訊。`);
      return deduplicated;
    } catch (error) {
      this.logger.error(
        `擷取所有模型價格時發生嚴重錯誤: ${error.message}`,
        error.stack
      );
      // 即使主爬蟲失敗，也嘗試回傳空陣列，避免服務完全中斷
      return [];
    }
  }

  /**
   * 從 LLM Price Check 網站擷取價格資訊
   */
  async fetchFromLlmPriceCheck(): Promise<ModelPriceInfo[]> {
    let browser: puppeteer.Browser | null = null;
    this.logger.log(
      "Attempting to launch Puppeteer browser for llmpricecheck.com"
    );
    try {
      browser = await puppeteer.launch({
        headless: true,
        args: [
          "--no-sandbox",
          "--disable-setuid-sandbox",
          "--disable-dev-shm-usage",
          "--disable-gpu", // 有時有幫助
        ],
        timeout: 60000,
        dumpio: true,
      });
      this.logger.log("Puppeteer browser launched. Creating new page.");
      const page = await browser.newPage();

      // Event listeners
      page.on("framenavigated", (frame) =>
        this.logger.debug(`EVENT framenavigated: ${frame.url()}`)
      );
      page.on("domcontentloaded", () =>
        this.logger.debug("EVENT DOMContentLoaded")
      );
      page.on("load", () => this.logger.debug("EVENT load"));
      page.on("close", () => this.logger.debug("EVENT close"));
      page.on("error", (err) => this.logger.error("EVENT error:", err.message));
      page.on("pageerror", (pageErr) =>
        this.logger.error("EVENT pageerror:", pageErr.message)
      );

      await page.setUserAgent(
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
      );
      this.logger.log("Navigating to https://llmpricecheck.com/");
      this.logger.log(`Current page URL before goto: ${page.url()}`);

      await page.goto("https://llmpricecheck.com/", {
        waitUntil: "domcontentloaded",
        timeout: 90000,
      });
      this.logger.log(`Current page URL after goto: ${page.url()}`);

      // try {
      //   await page.screenshot({ path: 'debug_llmpricecheck_before_wait.png', fullPage: true });
      //   this.logger.log("Screenshot taken: debug_llmpricecheck_before_wait.png");
      // } catch (screenshotError) {
      //   this.logger.error("Failed to take screenshot before waitForSelector:", screenshotError.message);
      // }

      this.logger.log(
        "Page DOMContentLoaded. Waiting for selector 'table' with increased timeout."
      );
      try {
        await page.waitForSelector("table", { timeout: 60000 }); // Changed selector to 'table'
      } catch (e) {
        this.logger.error(
          `Timeout or error waiting for table. Current URL: ${page.url()}. Error: ${e.message}`
        );
        try {
          const htmlContent = await page.content();
          this.logger.debug(
            `Page HTML (first 1000 chars on error): ${htmlContent.substring(0, 1000)}`
          );
        } catch (dumpError) {
          this.logger.error(
            `Failed to dump page content on error. Current URL: ${page.url()}`,
            dumpError.message
          );
        }
        throw e; // 重新拋出錯誤
      }

      this.logger.log("Selector 'table' found. Getting page content.");
      const content = await page.content();
      const $ = cheerio.load(content);
      const models: ModelPriceInfo[] = [];

      this.logger.log("Parsing table 'table tbody tr'"); // Cheerio selector also needs update if it was specific
      $("table tbody tr").each((rowIndex, row) => {
        // Changed selector to 'table tbody tr'
        const cells = $(row).find("td");
        if (cells.length < 6) {
          this.logger.warn(
            `Row ${rowIndex + 1}: Expected at least 6 cells, found ${cells.length}. Skipping row.`
          );
          return;
        }

        // 提取原始文本
        const rawModelName = $(cells[0]).text().trim();
        const rawProvider = $(cells[1]).text().trim();
        // Column 2 is 'Creator', we skip it for now or use it to refine provider if needed
        const rawContextWindow = $(cells[3]).text().trim();
        const rawInputPrice = $(cells[4]).text().trim();
        const rawOutputPrice = $(cells[5]).text().trim();

        // 1. 正規化 Provider
        let providerKey: string;
        const providerLower = rawProvider.toLowerCase();
        if (providerLower.includes("openai")) providerKey = "openai";
        else if (
          providerLower.includes("anthropic") ||
          providerLower.includes("claude")
        )
          providerKey = "anthropic";
        else if (
          providerLower.includes("google") ||
          providerLower.includes("gemini")
        )
          providerKey = "google-gemini"; // 預設 google 相關的都先對應到 google-gemini
        else if (providerLower.includes("mistral"))
          providerKey = "mistral"; // 假設我們系統要支援 mistral
        else if (providerLower.includes("cohere"))
          providerKey = "cohere"; // 假設我們系統要支援 cohere
        // ...可以加入更多提供商的判斷
        else {
          this.logger.warn(
            `Row ${rowIndex + 1}: Unknown provider '${rawProvider}'. Skipping.`
          );
          providerKey = providerLower; // 作為後備，但可能不符合系統定義
          // return; // 如果嚴格要求 provider 必須是已知的，則取消註解此行
        }

        // 2. Model Name - llmpricecheck 上的通常是易讀名稱，我們用作 displayName
        // modelName (API identifier) 可能需要後續手動校準或從其他來源補充
        const displayName = rawModelName;
        const modelName = rawModelName; // 暫時用 displayName 填充，理想情況下應有 API 識別碼

        // 3. 解析價格 (USD)
        const inputPrice = parseFloat(rawInputPrice.replace(/[^\d.-]/g, ""));
        const outputPrice = parseFloat(rawOutputPrice.replace(/[^\d.-]/g, ""));

        if (isNaN(inputPrice) || isNaN(outputPrice)) {
          this.logger.warn(
            `Row ${rowIndex + 1}: Failed to parse prices for model ${displayName}. Input: '${rawInputPrice}', Output: '${rawOutputPrice}'. Skipping.`
          );
          return;
        }

        // 4. 解析 Context Window
        let contextWindowTokens: number | undefined;
        const contextCleaned = rawContextWindow.replace(/,/g, "").toUpperCase(); // 移除逗號並轉大寫
        if (contextCleaned.endsWith("K")) {
          contextWindowTokens = parseInt(contextCleaned.slice(0, -1)) * 1000;
        } else if (/^\d+$/.test(contextCleaned)) {
          // 檢查是否為純數字
          contextWindowTokens = parseInt(contextCleaned);
        }
        if (contextWindowTokens && isNaN(contextWindowTokens)) {
          this.logger.warn(
            `Row ${rowIndex + 1}: Failed to parse context window for model ${displayName}: '${rawContextWindow}'. Setting to undefined.`
          );
          contextWindowTokens = undefined;
        }

        models.push({
          provider: providerKey,
          modelName: modelName,
          displayName: displayName,
          inputPricePer1kTokens: inputPrice,
          outputPricePer1kTokens: outputPrice,
          contextWindowTokens: contextWindowTokens,
          currency: "USD",
        });
      });

      this.logger.log(
        `Successfully parsed ${models.length} models from llmpricecheck.com.`
      );
      return models;
    } catch (error) {
      this.logger.error(
        `Error fetching prices from LLM Price Check: ${error.message}`,
        error.stack
      );
      return [];
    } finally {
      if (browser) {
        this.logger.log("Closing Puppeteer browser.");
        try {
          await browser.close();
        } catch (closeError) {
          this.logger.error("Error closing browser: ", closeError);
        }
      }
    }
  }

  /**
   * 從 OpenAI 官方網站擷取價格資訊
   */
  async fetchFromOpenAI(): Promise<ModelPriceInfo[]> {
    this.logger.warn(
      "fetchFromOpenAI 尚未實現爬蟲邏輯，返回空陣列。建議從 llmpricecheck.com 獲取或手動維護。"
    );
    // 實際的爬蟲邏輯會非常依賴 OpenAI 官網的 HTML 結構，且容易失效。
    // 例如:
    // const response = await firstValueFrom(this.httpService.get("https://openai.com/pricing").pipe(map(res => res.data)));
    // const $ = cheerio.load(response);
    // ... 解析邏輯 ...
    return Promise.resolve([]);
  }

  /**
   * 從 Anthropic 文檔擷取價格資訊
   */
  async fetchFromAnthropicDocs(): Promise<ModelPriceInfo[]> {
    this.logger.warn(
      "fetchFromAnthropicDocs 尚未實現爬蟲邏輯，返回空陣列。建議從 llmpricecheck.com 獲取或手動維護。"
    );
    // 實際的爬蟲邏輯會非常依賴 Anthropic 官網的 HTML 結構，且容易失效。
    return Promise.resolve([]);
  }

  /**
   * 從 Google AI 網站擷取價格資訊
   */
  async fetchFromGoogleAI(): Promise<ModelPriceInfo[]> {
    this.logger.warn(
      "fetchFromGoogleAI 尚未實現爬蟲邏輯，返回空陣列。建議從 llmpricecheck.com 獲取或手動維護。"
    );
    // 實際的爬蟲邏輯會非常依賴 Google AI 官網的 HTML 結構，且容易失效。
    return Promise.resolve([]);
  }

  /**
   * 去除重複的模型價格資訊，優先保留更完整的資訊
   */
  private deduplicateModelPrices(prices: ModelPriceInfo[]): ModelPriceInfo[] {
    const modelMap = new Map<string, ModelPriceInfo>();
    prices.forEach((price) => {
      // 標準化 key，將 provider 和 modelName 都轉成小寫並去除多餘空格來比較
      const providerKey = price.provider.toLowerCase().trim();
      const modelNameKey = price.modelName.toLowerCase().trim();
      const key = `${providerKey}:${modelNameKey}`;

      const existing = modelMap.get(key);
      if (!existing || this.isMoreComplete(price, existing)) {
        modelMap.set(key, {
          ...price,
          // 確保 provider 和 modelName 使用標準化後的值存儲 (如果需要)
          // provider: providerKey,
          // modelName: modelNameKey,
        });
      }
    });
    return Array.from(modelMap.values());
  }

  /**
   * 判斷一個價格資訊是否比另一個更完整
   */
  private isMoreComplete(a: ModelPriceInfo, b: ModelPriceInfo): boolean {
    // 優先選擇有 contextWindowTokens 的
    if (a.contextWindowTokens && !b.contextWindowTokens) return true;
    if (!a.contextWindowTokens && b.contextWindowTokens) return false;

    // 比較非空欄位數
    const score = (p: ModelPriceInfo) =>
      Object.values(p).filter((v) => v !== undefined && v !== null && v !== "")
        .length;
    return score(a) > score(b);
  }
}
