import {
  Injectable,
  NotFoundException,
  ConflictException,
  BadRequestException,
  Logger,
} from "@nestjs/common";
import { PrismaService } from "@/modules/core/prisma/prisma.service";
import { CreateModelDto, UpdateModelDto } from "./dto/ai-model.dto";
import { PrismaClient, Prisma, AiBotProviderType } from "@prisma/client";

// 定義 AiModel 類型
type AiModel = {
  id: string;
  provider: string;
  model_name: string;
  display_name: string;
  input_price_per_1k_tokens: Prisma.Decimal;
  output_price_per_1k_tokens: Prisma.Decimal;
  currency: string;
  context_window_tokens: number | null;
  notes: string | null;
  is_enabled: boolean;
  price_last_updated_at: Date | null;
  created_at: Date;
  updated_at: Date;
};
import { AiKeysService } from "../keys/ai-keys.service";
import { AiProviderFactory } from "../../core/providers/factory";
import axios from "axios";
import * as cheerio from "cheerio";
import * as crypto from "crypto";
import {
  AiPriceCrawlerService,
  ModelPriceInfo,
} from "../pricing/ai-price-crawler.service";

// 定義結果型別
export interface ProviderResult {
  provider: string;
  created: number;
  existed: number;
  failed?: boolean;
  error?: string;
}

// 定義錯誤型別
export interface PriceError {
  provider: string;
  modelName: string;
  error: string;
}

@Injectable()
export class AiModelsService {
  private readonly logger = new Logger(AiModelsService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly aiKeysService: AiKeysService,
    private readonly providerFactory: AiProviderFactory,
    private readonly priceCrawlerService: AiPriceCrawlerService
  ) {}

  /**
   * 列出所有 AI 模型，可篩選 provider 和 status
   */
  async findAll(): Promise<AiModel[]> {
    return this.prisma.ai_models.findMany({
      orderBy: [{ provider: "asc" }, { display_name: "asc" }],
    });
  }

  /**
   * 根據 ID 讀取單一 AI 模型
   */
  async findOne(id: string): Promise<AiModel> {
    const model = await this.prisma.ai_models.findUnique({ where: { id } });
    if (!model) throw new NotFoundException(`找不到 ID 為 ${id} 的 AI 模型`);
    return model;
  }

  /**
   * 建立新的 AI 模型
   */
  async create(data: CreateModelDto): Promise<AiModel> {
    try {
      // 只保留 DB 欄位，駝峰轉 snake_case
      const dbData: any = {
        provider: data.provider,
        model_name: data.modelName,
        display_name: data.displayName,
        input_price_per_1k_tokens: data.inputPricePer1kTokens,
        output_price_per_1k_tokens: data.outputPricePer1kTokens,
        currency: data.currency,
        context_window_tokens: data.contextWindowTokens,
        notes: data.notes,
        is_enabled: false, // 預設為停用狀態，需管理員手動啟用
      };
      return await this.prisma.ai_models.create({
        data: dbData,
      });
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        // 處理唯一索引衝突
        if (error.code === "P2002") {
          throw new ConflictException(`此 Provider 下已存在相同的模型名稱`);
        }
      }
      throw error;
    }
  }

  /**
   * 批量建立模型（用於從 API 導入的模型）
   */
  async bulkCreate(
    provider: string,
    models: string[]
  ): Promise<{ created: number; existed: number }> {
    let created = 0;
    let existed = 0;

    // 取得現有模型列表
    const existingModels = await this.prisma.ai_models.findMany({
      where: { provider },
      select: { model_name: true },
    });
    const existingModelNames = new Set(existingModels.map((m) => m.model_name));

    // 過濾出新模型
    const newModels = models.filter(
      (modelName) => !existingModelNames.has(modelName)
    );

    if (newModels.length > 0) {
      // 批量寫入新模型
      const now = new Date();
      await this.prisma.ai_models.createMany({
        data: newModels.map((modelName) => ({
          id: crypto.randomUUID(),
          provider,
          model_name: modelName,
          display_name: modelName,
          input_price_per_1k_tokens: 0,
          output_price_per_1k_tokens: 0,
          currency: "USD",
          is_enabled: false, // 預設為停用狀態，需管理員手動啟用
          price_last_updated_at: now,
          created_at: now,
          updated_at: now,
        })),
        skipDuplicates: true,
      });
      created = newModels.length;
    }

    existed = models.length - created;

    return { created, existed };
  }

  /**
   * 更新指定 ID 的 AI 模型
   */
  async update(id: string, data: UpdateModelDto): Promise<AiModel> {
    // 首先檢查模型是否存在
    await this.findOne(id);

    // 將 DTO 的 camelCase 欄位轉成 snake_case
    const dbData: any = {};
    if (data.displayName !== undefined) dbData.display_name = data.displayName;
    if (data.isEnabled !== undefined) dbData.is_enabled = data.isEnabled;
    if (data.inputPricePer1kTokens !== undefined)
      dbData.input_price_per_1k_tokens = data.inputPricePer1kTokens;
    if (data.outputPricePer1kTokens !== undefined)
      dbData.output_price_per_1k_tokens = data.outputPricePer1kTokens;
    if (data.currency !== undefined) dbData.currency = data.currency;
    if (data.contextWindowTokens !== undefined)
      dbData.context_window_tokens = data.contextWindowTokens;
    if (data.notes !== undefined) dbData.notes = data.notes;
    return this.prisma.ai_models.update({
      where: { id },
      data: dbData,
    });
  }

  /**
   * 刪除指定 ID 的 AI 模型
   */
  async remove(id: string): Promise<AiModel> {
    // 首先檢查模型是否存在
    await this.findOne(id);
    // 檢查是否有 Bot 使用此模型
    const bots = await this.prisma.ai_bots.findMany({
      where: { model_id: id },
    });
    if (bots.length > 0) {
      const botNames = bots.map((bot) => bot.name).join(", ");
      throw new ConflictException(
        `此模型仍被 AI Bot 使用: [${botNames}]，請先移除相關 Bot 後再刪除。`
      );
    }

    return this.prisma.ai_models.delete({
      where: { id },
    });
  }

  /**
   * 更新 AI 模型的啟用狀態
   */
  async updateStatus(id: string, isEnabled: boolean): Promise<AiModel> {
    return this.prisma.ai_models.update({
      where: { id },
      data: { is_enabled: isEnabled },
    });
  }

  /**
   * 從 AI 提供商獲取模型列表並保存到資料庫
   * POST /api/admin/ai/models/fetchList
   */
  async fetchFromProviders(): Promise<{
    results: Array<{
      provider: string;
      created: number;
      existed: number;
      failed?: boolean;
      error?: string;
    }>;
  }> {
    // 獲取所有啟用的 API 金鑰
    const keys = await this.prisma.ai_keys.findMany({
      where: { is_enabled: true },
    });

    if (keys.length === 0) {
      throw new BadRequestException(
        "沒有啟用的 API 金鑰可用，請先新增並啟用至少一個 API 金鑰"
      );
    }

    type ResultItem = {
      provider: string;
      created: number;
      existed: number;
      failed?: boolean;
      error?: string;
    };

    const results: ResultItem[] = [];
    const providerTypes = {
      openai: "OPENAI" as AiBotProviderType,
      anthropic: "CLAUDE" as AiBotProviderType,
      "google-gemini": "GEMINI" as AiBotProviderType,
      "openai-compatible": "OPENAI_COMPATIBLE" as AiBotProviderType,
    };

    // 用於追蹤已處理的提供商，避免重複處理
    const processedProviders = new Set<string>();

    // 依序處理每個金鑰
    for (const key of keys) {
      // 如果此提供商已處理，則跳過
      if (processedProviders.has(key.provider)) {
        continue;
      }

      try {
        // 使用 API Key（假設已經是解密的）
        const decryptedKey = key.api_key;

        // 對照 provider 與 AiBotProviderType
        const providerType =
          providerTypes[key.provider as keyof typeof providerTypes];
        if (!providerType) {
          results.push({
            provider: key.provider,
            created: 0,
            existed: 0,
            failed: true,
            error: `不支援的提供商: ${key.provider}`,
          });
          continue;
        }

        // 建立提供商實例
        const provider = this.providerFactory.createProvider(
          providerType,
          decryptedKey,
          key.api_url || undefined
        );

        // 獲取可用模型列表
        const models = await provider.getAvailableModels();

        // 將模型保存到資料庫
        const result = await this.bulkCreate(key.provider, models);

        results.push({
          provider: key.provider,
          created: result.created,
          existed: result.existed,
        });

        // 標記此提供商已處理
        processedProviders.add(key.provider);
      } catch (error) {
        this.logger.error(
          `從 ${key.provider} 獲取模型失敗: ${error.message}`,
          error.stack
        );
        results.push({
          provider: key.provider,
          created: 0,
          existed: 0,
          failed: true,
          error: `獲取模型失敗: ${error.message}`,
        });
      }
    }

    // 同步完成後，立即同步價格和 token 信息
    try {
      await this.syncModelPricing();
    } catch (error) {
      this.logger.warn("模型同步後自動同步價格失敗：", error.message);
    }

    return { results };
  }

  /**
   * 從供應商網站擷取模型列表並更新資料庫
   */
  async fetchModelsFromProviders(): Promise<{ results: ProviderResult[] }> {
    try {
      const results: ProviderResult[] = [];

      // 從 OpenAI 獲取模型列表
      const openaiResult = await this.fetchAndSyncOpenAIModels();
      results.push(openaiResult);

      // 從 Anthropic 獲取模型列表
      const anthropicResult = await this.fetchAndSyncAnthropicModels();
      results.push(anthropicResult);

      // 從 Google 獲取模型列表
      const googleResult = await this.fetchAndSyncGoogleModels();
      results.push(googleResult);

      // 從其他供應商獲取模型列表
      // ...

      return { results };
    } catch (error) {
      this.logger.error(`從供應商擷取模型時發生錯誤: ${error.message}`);
      throw error;
    }
  }

  /**
   * 同步模型價格資訊
   */
  async syncModelPricing(): Promise<{
    success: boolean;
    updated: number;
    skipped: number;
    notFound: { provider: string; modelName: string }[];
    errors?: PriceError[];
  }> {
    try {
      // 先取得資料庫中所有已存在的模型
      const existingModels = await this.prisma.ai_models.findMany({
        select: {
          id: true,
          provider: true,
          model_name: true,
        },
      });

      // 建立快速查詢映射表
      const modelMap = new Map<string, string>(); // key: provider:model_name, value: id
      existingModels.forEach((model) => {
        const key = `${model.provider}:${model.model_name}`;
        modelMap.set(key, model.id);
      });

      // 使用爬蟲服務獲取所有模型的價格資訊
      const modelPrices = await this.priceCrawlerService.fetchAllModelPrices();

      // 更新資料庫中的模型價格
      let updatedCount = 0;
      let skippedCount = 0;
      const notFoundModels: { provider: string; modelName: string }[] = [];
      const errors: PriceError[] = [];

      for (const priceInfo of modelPrices) {
        try {
          // 產生查詢鍵
          const lookupKey = `${priceInfo.provider}:${priceInfo.modelName}`;
          const existingModelId = modelMap.get(lookupKey);

          if (existingModelId) {
            // 檢查價格是否有變動，避免不必要的更新
            const existingModel = await this.prisma.ai_models.findUnique({
              where: { id: existingModelId },
              select: {
                input_price_per_1k_tokens: true,
                output_price_per_1k_tokens: true,
                context_window_tokens: true,
              },
            });

            const priceChanged =
              Number(existingModel?.input_price_per_1k_tokens) !==
                priceInfo.inputPricePer1kTokens ||
              Number(existingModel?.output_price_per_1k_tokens) !==
                priceInfo.outputPricePer1kTokens ||
              (priceInfo.contextWindowTokens &&
                existingModel?.context_window_tokens !==
                  priceInfo.contextWindowTokens);

            if (priceChanged) {
              // 更新現有模型的價格資訊
              await this.prisma.ai_models.update({
                where: { id: existingModelId },
                data: {
                  input_price_per_1k_tokens: priceInfo.inputPricePer1kTokens,
                  output_price_per_1k_tokens: priceInfo.outputPricePer1kTokens,
                  ...(priceInfo.contextWindowTokens && {
                    context_window_tokens: priceInfo.contextWindowTokens,
                  }),
                  price_last_updated_at: new Date(),
                },
              });
              updatedCount++;
              this.logger.log(
                `更新模型 ${priceInfo.provider}:${priceInfo.modelName} 的價格資訊成功`
              );
            } else {
              skippedCount++;
              this.logger.debug(
                `模型 ${priceInfo.provider}:${priceInfo.modelName} 價格無變動，已跳過`
              );
            }
          } else {
            // 記錄不存在的模型
            notFoundModels.push({
              provider: priceInfo.provider,
              modelName: priceInfo.modelName,
            });
            this.logger.warn(
              `模型 ${priceInfo.provider}:${priceInfo.modelName} 在系統中不存在，已跳過更新`
            );
          }
        } catch (error) {
          this.logger.error(
            `更新模型 ${priceInfo.provider}:${priceInfo.modelName} 的價格時發生錯誤: ${error.message}`
          );
          errors.push({
            provider: priceInfo.provider,
            modelName: priceInfo.modelName,
            error: error.message,
          });
        }
      }

      return {
        success: true,
        updated: updatedCount,
        skipped: skippedCount,
        notFound: notFoundModels,
        ...(errors.length > 0 && { errors }),
      };
    } catch (error) {
      this.logger.error(`同步模型價格時發生錯誤: ${error.message}`);
      return {
        success: false,
        updated: 0,
        skipped: 0,
        notFound: [],
        errors: [
          { provider: "general", modelName: "all", error: error.message },
        ],
      };
    }
  }

  /**
   * 從 OpenAI API 擷取模型列表並同步到資料庫
   */
  private async fetchAndSyncOpenAIModels(): Promise<ProviderResult> {
    try {
      // 這裡應該實現實際從 OpenAI API 獲取模型列表的邏輯
      // 示例實現
      const openaiModels = [
        { modelName: "gpt-4o", displayName: "GPT-4o" },
        { modelName: "gpt-4-turbo", displayName: "GPT-4 Turbo" },
        { modelName: "gpt-3.5-turbo", displayName: "GPT-3.5 Turbo" },
        // ...其他模型
      ];

      let created = 0;
      let existed = 0;

      for (const model of openaiModels) {
        const existingModel = await this.prisma.ai_models.findFirst({
          where: {
            provider: "openai",
            model_name: model.modelName,
          },
        });

        if (!existingModel) {
          const now = new Date();
          await this.prisma.ai_models.create({
            data: {
              id: crypto.randomUUID(),
              provider: "openai",
              model_name: model.modelName,
              display_name: model.displayName,
              input_price_per_1k_tokens: 0, // 初始值，將通過 syncModelPricing 更新
              output_price_per_1k_tokens: 0, // 初始值，將通過 syncModelPricing 更新
              is_enabled: false, // 預設為停用
              currency: "USD",
              price_last_updated_at: now,
              created_at: now,
              updated_at: now,
            },
          });
          created++;
        } else {
          existed++;
        }
      }

      return {
        provider: "openai",
        created,
        existed,
      };
    } catch (error) {
      this.logger.error(`從 OpenAI 擷取模型時發生錯誤: ${error.message}`);
      return {
        provider: "openai",
        created: 0,
        existed: 0,
        failed: true,
        error: error.message,
      };
    }
  }

  /**
   * 從 Anthropic API 擷取模型列表並同步到資料庫
   */
  private async fetchAndSyncAnthropicModels(): Promise<ProviderResult> {
    try {
      // 這裡應該實現實際從 Anthropic API 獲取模型列表的邏輯
      // 示例實現
      const anthropicModels = [
        { modelName: "claude-3-opus", displayName: "Claude 3 Opus" },
        { modelName: "claude-3-sonnet", displayName: "Claude 3 Sonnet" },
        { modelName: "claude-3-haiku", displayName: "Claude 3 Haiku" },
        // ...其他模型
      ];

      let created = 0;
      let existed = 0;

      for (const model of anthropicModels) {
        const existingModel = await this.prisma.ai_models.findFirst({
          where: {
            provider: "anthropic",
            model_name: model.modelName,
          },
        });

        if (!existingModel) {
          const now = new Date();
          await this.prisma.ai_models.create({
            data: {
              id: crypto.randomUUID(),
              provider: "anthropic",
              model_name: model.modelName,
              display_name: model.displayName,
              input_price_per_1k_tokens: 0, // 初始值，將通過 syncModelPricing 更新
              output_price_per_1k_tokens: 0, // 初始值，將通過 syncModelPricing 更新
              is_enabled: false, // 預設為停用
              currency: "USD",
              price_last_updated_at: now,
              created_at: now,
              updated_at: now,
            },
          });
          created++;
        } else {
          existed++;
        }
      }

      return {
        provider: "anthropic",
        created,
        existed,
      };
    } catch (error) {
      this.logger.error(`從 Anthropic 擷取模型時發生錯誤: ${error.message}`);
      return {
        provider: "anthropic",
        created: 0,
        existed: 0,
        failed: true,
        error: error.message,
      };
    }
  }

  /**
   * 從 Google AI API 擷取模型列表並同步到資料庫
   */
  private async fetchAndSyncGoogleModels(): Promise<ProviderResult> {
    try {
      // 這裡應該實現實際從 Google AI API 獲取模型列表的邏輯
      // 示例實現
      const googleModels = [
        { modelName: "gemini-1.5-pro", displayName: "Gemini 1.5 Pro" },
        { modelName: "gemini-1.5-flash", displayName: "Gemini 1.5 Flash" },
        // ...其他模型
      ];

      let created = 0;
      let existed = 0;

      for (const model of googleModels) {
        const existingModel = await this.prisma.ai_models.findFirst({
          where: {
            provider: "google",
            model_name: model.modelName,
          },
        });

        if (!existingModel) {
          const now = new Date();
          await this.prisma.ai_models.create({
            data: {
              id: crypto.randomUUID(),
              provider: "google",
              model_name: model.modelName,
              display_name: model.displayName,
              input_price_per_1k_tokens: 0, // 初始值，將通過 syncModelPricing 更新
              output_price_per_1k_tokens: 0, // 初始值，將通過 syncModelPricing 更新
              is_enabled: false, // 預設為停用
              currency: "USD",
              price_last_updated_at: now,
              created_at: now,
              updated_at: now,
            },
          });
          created++;
        } else {
          existed++;
        }
      }

      return {
        provider: "google",
        created,
        existed,
      };
    } catch (error) {
      this.logger.error(`從 Google AI 擷取模型時發生錯誤: ${error.message}`);
      return {
        provider: "google",
        created: 0,
        existed: 0,
        failed: true,
        error: error.message,
      };
    }
  }
}
