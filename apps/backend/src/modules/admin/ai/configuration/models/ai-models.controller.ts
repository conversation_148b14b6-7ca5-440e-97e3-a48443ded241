import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  UseGuards,
  Logger,
} from "@nestjs/common";
import { ApiTags, ApiOperation, ApiBearerAuth } from "@nestjs/swagger";
import { JwtAuthGuard } from "@modules/core/auth/guards/auth.guard";
import { AiModelsService } from "./ai-models.service";
import { CreateModelDto, UpdateModelDto } from "./dto/ai-model.dto";

// CASL 相關的導入
import { PoliciesGuard } from "@/casl/guards/permission.guard";
import { CheckPolicies } from "@/casl/decorators/check-policies.decorator";
import { Actions, Subjects } from "@horizai/permissions";
import { AppAbility } from "@/types/models/casl.model";
// import { Action } from "@/common/enums/action.enum";
// 為配合權限掃描靜態分析，請於 @CheckPolicies 及 ability.can 內直接使用字串，如 'read', 'manage' 等。

@ApiTags("admin/ai/models")
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, PoliciesGuard)
@Controller("admin/ai/models")
export class AiModelsController {
  private readonly logger = new Logger(AiModelsController.name);

  constructor(private readonly aiModelsService: AiModelsService) {}

  @Get()
  @CheckPolicies((ability: AppAbility) =>
    ability.can(Actions.READ, Subjects.AI_MODEL)
  )
  @ApiOperation({ summary: "讀取所有 AI 模型" })
  async findAll() {
    return this.aiModelsService.findAll();
  }

  @Get(":id")
  @CheckPolicies((ability: AppAbility) =>
    ability.can(Actions.READ, Subjects.AI_MODEL)
  )
  @ApiOperation({ summary: "根據 ID 讀取 AI 模型" })
  async findOne(@Param("id") id: string) {
    return this.aiModelsService.findOne(id);
  }

  @Post()
  @CheckPolicies((ability: AppAbility) =>
    ability.can(Actions.CREATE, Subjects.AI_MODEL)
  )
  @ApiOperation({ summary: "建立新 AI 模型" })
  async create(@Body() createModelDto: CreateModelDto) {
    return this.aiModelsService.create(createModelDto);
  }

  @Put(":id")
  @CheckPolicies((ability: AppAbility) =>
    ability.can(Actions.UPDATE, Subjects.AI_MODEL)
  )
  @ApiOperation({ summary: "更新 AI 模型" })
  async update(
    @Param("id") id: string,
    @Body() updateModelDto: UpdateModelDto
  ) {
    return this.aiModelsService.update(id, updateModelDto);
  }

  @Delete(":id")
  @CheckPolicies((ability: AppAbility) =>
    ability.can(Actions.DELETE, Subjects.AI_MODEL)
  )
  @ApiOperation({ summary: "刪除 AI 模型" })
  async remove(@Param("id") id: string) {
    return this.aiModelsService.remove(id);
  }

  @Put(":id/status")
  @CheckPolicies((ability: AppAbility) =>
    ability.can(Actions.UPDATE, Subjects.AI_MODEL)
  )
  @ApiOperation({ summary: "更新 AI 模型啟用狀態" })
  async updateStatus(
    @Param("id") id: string,
    @Body("isEnabled") isEnabled: boolean
  ) {
    return this.aiModelsService.updateStatus(id, isEnabled);
  }

  @Post("fetch-from-providers")
  @CheckPolicies((ability: AppAbility) =>
    ability.can(Actions.MANAGE, Subjects.AI_MODEL)
  )
  @ApiOperation({ summary: "從 AI 提供商獲取模型列表並保存到資料庫" })
  async fetchFromProviders() {
    this.logger.log("開始從供應商擷取模型列表");
    return this.aiModelsService.fetchFromProviders();
  }

  @Post("sync-pricing")
  @CheckPolicies((ability: AppAbility) =>
    ability.can(Actions.MANAGE, Subjects.AI_MODEL)
  )
  @ApiOperation({
    summary: "從 AI 提供商官方網站獲取模型價格和 Token 容量並更新資料庫",
  })
  async syncPricing() {
    this.logger.log("開始同步模型價格資訊");
    return this.aiModelsService.syncModelPricing();
  }
}
