import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsBoolean, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator'
import { Type } from 'class-transformer'

export class CreateModelDto {
  @ApiProperty({ description: 'AI Provider' })
  @IsString()
  @IsNotEmpty()
  provider: string

  @ApiProperty({ description: 'API 使用的模型名稱' })
  @IsString()
  @IsNotEmpty()
  modelName: string

  @ApiProperty({ description: '顯示名稱' })
  @IsString()
  @IsNotEmpty()
  displayName: string

  @ApiPropertyOptional({ description: '是否啟用' })
  @IsBoolean()
  @IsOptional()
  isEnabled?: boolean

  @ApiPropertyOptional({ description: '輸入 tokens 每 1k 的價格' })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  inputPricePer1kTokens?: number

  @ApiPropertyOptional({ description: '輸出 tokens 每 1k 的價格' })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  outputPricePer1kTokens?: number

  @ApiPropertyOptional({ description: '貨幣' })
  @IsString()
  @IsOptional()
  currency?: string

  @ApiPropertyOptional({ description: 'Context Window 大小 (tokens)' })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  contextWindowTokens?: number

  @ApiPropertyOptional({ description: '備註' })
  @IsString()
  @IsOptional()
  notes?: string
}

export class UpdateModelDto {
  @ApiPropertyOptional({ description: '顯示名稱' })
  @IsString()
  @IsOptional()
  displayName?: string

  @ApiPropertyOptional({ description: '是否啟用' })
  @IsBoolean()
  @IsOptional()
  isEnabled?: boolean

  @ApiPropertyOptional({ description: '輸入 tokens 每 1k 的價格' })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  inputPricePer1kTokens?: number

  @ApiPropertyOptional({ description: '輸出 tokens 每 1k 的價格' })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  outputPricePer1kTokens?: number

  @ApiPropertyOptional({ description: '貨幣' })
  @IsString()
  @IsOptional()
  currency?: string

  @ApiPropertyOptional({ description: 'Context Window 大小 (tokens)' })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  contextWindowTokens?: number

  @ApiPropertyOptional({ description: '備註' })
  @IsString()
  @IsOptional()
  notes?: string
}

export class ModelResponseDto {
  @ApiProperty({ description: 'Model ID' })
  id: string

  @ApiProperty({ description: 'AI Provider' })
  provider: string

  @ApiProperty({ description: 'API 使用的模型名稱' })
  modelName: string

  @ApiProperty({ description: '顯示名稱' })
  displayName: string

  @ApiProperty({ description: '是否啟用' })
  isEnabled: boolean

  @ApiProperty({ description: '輸入 tokens 每 1k 的價格' })
  inputPricePer1kTokens: number

  @ApiProperty({ description: '輸出 tokens 每 1k 的價格' })
  outputPricePer1kTokens: number

  @ApiProperty({ description: '貨幣' })
  currency: string

  @ApiProperty({ description: '價格最後更新時間' })
  priceLastUpdatedAt: Date

  @ApiPropertyOptional({ description: 'Context Window 大小 (tokens)' })
  contextWindowTokens?: number

  @ApiPropertyOptional({ description: '備註' })
  notes?: string

  @ApiProperty({ description: '建立時間' })
  createdAt: Date

  @ApiProperty({ description: '更新時間' })
  updatedAt: Date
}

export class FetchPricingDto {
  @ApiProperty({ description: '價格來源網址' })
  @IsString()
  @IsNotEmpty()
  url: string;

  @ApiProperty({ description: '模型名稱 (API 用的 model name，如 gpt-4-turbo)' })
  @IsString()
  @IsNotEmpty()
  modelName: string;
}

export class FetchPricingResponseDto {
  @ApiProperty({ description: '輸入價格，每 1k tokens (USD)' })
  inputPricePer1kTokens: number;

  @ApiProperty({ description: '輸出價格，每 1k tokens (USD)' })
  outputPricePer1kTokens: number;
} 