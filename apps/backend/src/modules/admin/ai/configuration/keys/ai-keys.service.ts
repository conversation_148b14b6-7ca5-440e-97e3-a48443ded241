import {
  Injectable,
  NotFoundException,
  ConflictException,
  Logger,
} from "@nestjs/common";
import { PrismaService } from "@/modules/core/prisma/prisma.service";
import { EncryptionService } from "@/modules/core/encryption/encryption.service";
import { Create<PERSON>eyDto, UpdateKeyDto } from "./dto/ai-keys.dto";
import { PrismaClient, Prisma } from "@prisma/client";
import { AiProviderFactory } from "../../core/providers/factory";
import * as crypto from "crypto";

// 定義 AiKey 類型
type AiKey = {
  id: string;
  name: string;
  api_key: string;
  provider: string;
  api_url: string | null;
  is_enabled: boolean;
  created_at: Date;
  updated_at: Date;
};

@Injectable()
export class AiKeysService {
  private readonly logger = new Logger(AiKeysService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly encryptionService: EncryptionService,
    private readonly providerFactory: AiProviderFactory
  ) {}

  async findAll(): Promise<AiKey[]> {
    const keys = await this.prisma.ai_keys.findMany({
      orderBy: { updated_at: "desc" },
    });

    return keys.map((key) => ({
      ...key,
      api_key: "********", // 隱藏實際的 API Key
    }));
  }

  async findOne(id: string): Promise<AiKey> {
    const key = await this.prisma.ai_keys.findUnique({
      where: { id },
    });

    if (!key) {
      throw new NotFoundException(`找不到 ID 為 ${id} 的 AI 金鑰`);
    }

    return {
      ...key,
      api_key: "********", // 隱藏實際的 API Key
    };
  }

  async create(data: CreateKeyDto): Promise<AiKey> {
    // 加密 API Key
    const encryptedKey = this.encryptionService.encrypt(data.apiKey);
    const now = new Date();
    const key = await this.prisma.ai_keys.create({
      data: {
        id: crypto.randomUUID(),
        name: data.name,
        provider: data.provider,
        api_key: encryptedKey,
        api_url: data.apiUrl,
        is_enabled: data.isEnabled !== undefined ? data.isEnabled : true,
        created_at: now,
        updated_at: now,
      },
    });
    return {
      ...key,
      api_key: "********", // 隱藏實際的 API Key
    };
  }

  async update(id: string, data: UpdateKeyDto): Promise<AiKey> {
    await this.findOne(id);
    // 只保留 DB 欄位
    let updateData: any = {};
    if (data.name !== undefined) updateData.name = data.name;
    if (data.apiKey)
      updateData.api_key = this.encryptionService.encrypt(data.apiKey);
    if (data.apiUrl !== undefined) updateData.api_url = data.apiUrl;
    if (data.isEnabled !== undefined) updateData.is_enabled = data.isEnabled;
    const key = await this.prisma.ai_keys.update({
      where: { id },
      data: updateData,
    });
    return {
      ...key,
      api_key: "********", // 隱藏實際的 API Key
    };
  }

  async remove(id: string): Promise<void> {
    const key = await this.prisma.ai_keys.findUnique({
      where: { id },
    });

    if (!key) {
      throw new NotFoundException(`AI Key with ID "${id}" not found`);
    }

    // 檢查是否有 AiBot 正在使用此金鑰
    const botsUsingKeyCount = await this.prisma.ai_bots.count({
      where: { key_id: id },
    });

    if (botsUsingKeyCount > 0) {
      throw new ConflictException(
        `Cannot delete AI Key with ID "${id}" because it is currently in use by ${botsUsingKeyCount} AI Bot(s). Please reassign or delete these bots first.`
      );
    }

    // 如果沒有 Bot 使用，則執行刪除
    await this.prisma.ai_keys.delete({
      where: { id },
    });
  }

  async testKey(id: string): Promise<boolean> {
    try {
      const key = await this.prisma.ai_keys.findUnique({
        where: { id },
      });

      if (!key) {
        return false;
      }

      // 解密 API Key
      const apiKey = this.encryptionService.decrypt(key.api_key);

      // 根據供應商執行不同的測試邏輯
      switch (key.provider) {
        case "openai":
        case "openai-compatible":
          return apiKey.startsWith("sk-");
        case "anthropic":
          return apiKey.startsWith("sk-ant-");
        case "google-gemini":
          return apiKey.length > 0;
        default:
          return false;
      }
    } catch (error) {
      this.logger.error(`測試 API Key 失敗: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * 用明文驗證金鑰（不查資料庫，不寫入資料庫）
   * 實際向 AI 提供商發送 API 請求來驗證金鑰
   */
  async testKeyPlaintext(
    provider: string,
    apiKey: string,
    apiUrl?: string
  ): Promise<boolean> {
    try {
      // 首先檢查格式
      const formatValid = this.validateKeyFormat(provider, apiKey);
      if (!formatValid) {
        this.logger.warn(
          `金鑰格式無效: provider=${provider}, key=${apiKey.substring(0, 10)}...`
        );
        return false;
      }

      // 檢查是否為測試金鑰
      if (apiKey.includes("sk-test-")) {
        this.logger.warn(`檢測到測試金鑰: ${apiKey.substring(0, 15)}...`);
        return false;
      }

      // 實際向 AI 提供商驗證
      switch (provider) {
        case "openai":
          return await this.validateOpenAIKey(apiKey, apiUrl);
        case "openai-compatible":
          return await this.validateOpenAICompatibleKey(apiKey, apiUrl);
        case "anthropic":
          return await this.validateAnthropicKey(apiKey);
        case "google-gemini":
          return await this.validateGoogleGeminiKey(apiKey);
        default:
          this.logger.warn(`不支援的提供商: ${provider}`);
          return false;
      }
    } catch (error) {
      this.logger.error(`明文驗證 API Key 失敗: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * 驗證金鑰格式
   */
  private validateKeyFormat(provider: string, apiKey: string): boolean {
    switch (provider) {
      case "openai":
        return apiKey.startsWith("sk-") && apiKey.length > 10;
      case "openai-compatible":
        // OpenAI 相容服務的金鑰格式更靈活
        // 可能是 sk- 開頭的 OpenAI 格式，或其他自定義格式
        return apiKey.length > 5 && !apiKey.includes(" "); // 基本檢查：長度 > 5 且無空格
      case "anthropic":
        return apiKey.startsWith("sk-ant-") && apiKey.length > 15;
      case "google-gemini":
        return apiKey.length > 10;
      default:
        return false;
    }
  }

  /**
   * 驗證 OpenAI API 金鑰
   */
  private async validateOpenAIKey(
    apiKey: string,
    apiUrl?: string
  ): Promise<boolean> {
    try {
      const baseURL = apiUrl || "https://api.openai.com/v1";
      const response = await fetch(`${baseURL}/models`, {
        method: "GET",
        headers: {
          Authorization: `Bearer ${apiKey}`,
          "Content-Type": "application/json",
        },
        // 設定 10 秒超時
        signal: AbortSignal.timeout(10000),
      });

      return response.ok;
    } catch (error) {
      this.logger.error(`OpenAI 金鑰驗證失敗: ${error.message}`);
      return false;
    }
  }

  /**
   * 驗證 OpenAI 相容 API 金鑰
   */
  private async validateOpenAICompatibleKey(
    apiKey: string,
    apiUrl?: string
  ): Promise<boolean> {
    try {
      // OpenAI 相容服務必須提供 API URL
      if (!apiUrl) {
        this.logger.warn("OpenAI 相容服務需要提供 API URL");
        return false;
      }

      // 確保 API URL 格式正確
      let baseURL: string;
      try {
        const url = new URL(apiUrl);
        baseURL = apiUrl.endsWith("/") ? apiUrl.slice(0, -1) : apiUrl;
        // 如果沒有版本路徑，加上 /v1
        if (!baseURL.includes("/v1")) {
          baseURL = `${baseURL}/v1`;
        }
      } catch (urlError) {
        this.logger.warn(`無效的 API URL: ${apiUrl}`);
        return false;
      }

      this.logger.debug(`嘗試驗證 OpenAI 相容金鑰，API URL: ${baseURL}`);

      const response = await fetch(`${baseURL}/models`, {
        method: "GET",
        headers: {
          Authorization: `Bearer ${apiKey}`,
          "Content-Type": "application/json",
        },
        // 設定 15 秒超時 (相容服務可能較慢)
        signal: AbortSignal.timeout(15000),
      });

      this.logger.debug(
        `OpenAI 相容金鑰驗證回應: status=${response.status}, url=${baseURL}/models`
      );

      return response.ok;
    } catch (error) {
      if (error.name === "TimeoutError") {
        this.logger.error(`OpenAI 相容金鑰驗證超時: ${apiUrl}`);
      } else {
        this.logger.error(
          `OpenAI 相容金鑰驗證失敗: ${error.message}`,
          error.stack
        );
      }
      return false;
    }
  }

  /**
   * 驗證 Anthropic API 金鑰
   */
  private async validateAnthropicKey(apiKey: string): Promise<boolean> {
    try {
      // 使用 Anthropic 的簡單 API 來驗證金鑰
      const response = await fetch("https://api.anthropic.com/v1/messages", {
        method: "POST",
        headers: {
          "x-api-key": apiKey,
          "anthropic-version": "2023-06-01",
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          model: "claude-3-haiku-20240307",
          max_tokens: 1,
          messages: [{ role: "user", content: "test" }],
        }),
        signal: AbortSignal.timeout(10000),
      });

      // 即使是錯誤回應，只要不是 401 (未授權) 就表示金鑰有效
      return response.status !== 401;
    } catch (error) {
      this.logger.error(`Anthropic 金鑰驗證失敗: ${error.message}`);
      return false;
    }
  }

  /**
   * 驗證 Google Gemini API 金鑰
   */
  private async validateGoogleGeminiKey(apiKey: string): Promise<boolean> {
    try {
      // 使用 Google Gemini API 來驗證金鑰
      const response = await fetch(
        `https://generativelanguage.googleapis.com/v1beta/models?key=${apiKey}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
          signal: AbortSignal.timeout(10000),
        }
      );

      return response.ok;
    } catch (error) {
      this.logger.error(`Google Gemini 金鑰驗證失敗: ${error.message}`);
      return false;
    }
  }

  // 取得解密後的 API Key (僅供內部使用)
  async getDecryptedKey(id: string): Promise<string> {
    const key = await this.prisma.ai_keys.findUnique({
      where: { id },
    });

    if (!key) {
      throw new NotFoundException(`找不到 ID 為 ${id} 的 AI 金鑰`);
    }

    return this.encryptionService.decrypt(key.api_key);
  }
}
