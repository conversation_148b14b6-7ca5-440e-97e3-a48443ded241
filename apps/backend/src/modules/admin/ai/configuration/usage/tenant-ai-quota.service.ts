import {
  Injectable,
  Logger,
  HttpException,
  HttpStatus,
  BadRequestException,
  NotFoundException,
} from "@nestjs/common";
import { PrismaService } from "@/modules/core/prisma/prisma.service";
import { PrismaClient } from "@prisma/client";
import { Decimal } from "@prisma/client/runtime/library";
import * as crypto from "crypto";

export class InsufficientCreditsException extends HttpException {
  constructor(message = "AI 信用點數不足") {
    super(message, HttpStatus.FORBIDDEN); // 403 Forbidden or 402 Payment Required might be suitable
    this.name = "InsufficientCreditsException";
  }
}

@Injectable()
export class TenantAiQuotaService {
  private readonly logger = new Logger(TenantAiQuotaService.name);

  constructor(private readonly prisma: PrismaService) {}

  /**
   * Checks if the tenant has enough AI credits and deducts them.
   * This operation should be part of a transaction if other dependent operations exist.
   * @param tenantId The ID of the tenant.
   * @param costToDeduct The amount of credits to deduct.
   * @returns True if credits were sufficient and deducted, otherwise throws InsufficientCreditsException.
   * @throws InsufficientCreditsException if credits are not enough.
   * @throws Error if tenant or plan information is missing.
   */
  async checkAndDeductCredits(
    tenantId: string,
    costToDeduct: Decimal
  ): Promise<boolean> {
    if (costToDeduct.isNegative() || costToDeduct.isZero()) {
      this.logger.log(
        `Deduction amount is zero or negative (${costToDeduct}) for tenant ${tenantId}, no action taken.`
      );
      return true; // No cost, so it's fine
    }

    const tenant = await this.prisma.tenants.findUnique({
      where: { id: tenantId },
      select: { currentAiCredits: true, id: true },
    });

    if (!tenant) {
      this.logger.error(`Tenant with ID ${tenantId} not found.`);
      throw new NotFoundException(`找不到租戶 ${tenantId}`);
    }

    const currentCredits = tenant.currentAiCredits ?? new Decimal(0);

    if (currentCredits.lessThan(costToDeduct)) {
      this.logger.warn(
        `Tenant ${tenantId} has insufficient AI credits. Current: ${currentCredits}, Required: ${costToDeduct}`
      );
      throw new InsufficientCreditsException(
        `您的 AI 信用點數不足。目前剩餘: ${currentCredits}, 本次需要: ${costToDeduct}`
      );
    }

    // Deduct credits
    const updatedTenant = await this.prisma.tenants.update({
      where: { id: tenantId },
      data: {
        currentAiCredits: currentCredits.sub(costToDeduct),
      },
    });

    this.logger.log(
      `Deducted ${costToDeduct} AI credits from tenant ${tenantId}. New balance: ${updatedTenant.currentAiCredits}`
    );
    return true;
  }

  /**
   * Resets or adds monthly AI credits for a tenant based on their plan.
   * Typically called by a scheduled job.
   * @param tenantId The ID of the tenant.
   * @returns True if credits were reset/added successfully.
   */
  async resetMonthlyCredits(tenantId: string): Promise<boolean> {
    const tenant = await this.prisma.tenants.findUnique({
      where: { id: tenantId },
      include: { plans: true },
    });

    if (!tenant) {
      this.logger.error(
        `Tenant ${tenantId} not found for monthly credit reset.`
      );
      throw new NotFoundException(`找不到租戶 ${tenantId}`);
    }

    if (!tenant.plans) {
      this.logger.warn(
        `Tenant ${tenantId} does not have an associated plan. Cannot reset monthly credits.`
      );
      throw new BadRequestException(
        `租戶 ${tenantId} 未綁定方案，無法重設信用點數`
      );
    }

    const planMonthlyCredits =
      tenant.plans.monthlyAiCreditsLimit ?? new Decimal(0);

    await this.prisma.tenants.update({
      where: { id: tenantId },
      data: { currentAiCredits: planMonthlyCredits },
    });

    this.logger.log(
      `重設租戶 ${tenantId} 的 AI 信用點數為 ${planMonthlyCredits}（方案：${tenant.plans.name}）`
    );
    // TODO: 可考慮寫入 SystemLog 或自訂日誌表
    return true;
  }

  /**
   * Adds purchased AI credits to a tenant's balance.
   * @param tenantId The ID of the tenant.
   * @param creditsToAdd The amount of credits to add.
   * @param purchaseDetails Optional details about the purchase for logging or record keeping.
   */
  async addPurchasedCredits(
    tenantId: string,
    creditsToAdd: Decimal,
    purchaseDetails?: {
      amount?: Decimal;
      pricePaid: Decimal;
      currency: string;
      paymentId?: string;
      notes?: string;
      purchasedAt?: Date;
    }
  ): Promise<any> {
    if (creditsToAdd.isNegative() || creditsToAdd.isZero()) {
      this.logger.warn(
        `嘗試為租戶 ${tenantId} 增加 0 或負數點數 (${creditsToAdd})。`
      );
      throw new BadRequestException("購買點數必須大於 0");
    }

    // 交易式操作，確保點數與購買記錄同時寫入
    return await this.prisma.$transaction(async (prisma) => {
      const tenant = await prisma.tenants.findUnique({
        where: { id: tenantId },
      });
      if (!tenant) {
        this.logger.error(
          `Tenant ${tenantId} not found when adding purchased credits.`
        );
        throw new NotFoundException(`找不到租戶 ${tenantId}`);
      }
      // 若有購買記錄，檢查必要欄位
      let creditPurchase: { id: string } | undefined = undefined;
      if (purchaseDetails) {
        if (!purchaseDetails.pricePaid || !purchaseDetails.currency) {
          throw new BadRequestException(
            "缺少購買記錄必要欄位（pricePaid, currency）"
          );
        }
        creditPurchase = await prisma.tenant_credit_purchases.create({
          data: {
            id: crypto.randomUUID(),
            tenantId: tenantId,
            amount: creditsToAdd,
            pricePaid: purchaseDetails.pricePaid,
            currency: purchaseDetails.currency,
            paymentId: purchaseDetails.paymentId ?? null,
            notes: purchaseDetails.notes ?? null,
            purchasedAt: purchaseDetails.purchasedAt ?? new Date(),
          },
          select: { id: true },
        });
      }
      const updatedTenant = await prisma.tenants.update({
        where: { id: tenantId },
        data: {
          currentAiCredits: {
            increment: creditsToAdd,
          },
        },
      });
      this.logger.log(
        `租戶 ${tenantId} 購買點數：+${creditsToAdd}，新餘額：${updatedTenant.currentAiCredits}` +
          (creditPurchase ? `，已建立購買記錄（ID: ${creditPurchase.id}）` : "")
      );
      return updatedTenant;
    });
  }

  /**
   * Retrieves the current AI credit balance for a tenant.
   * @param tenantId The ID of the tenant.
   * @returns The current AI credit balance.
   */
  async getCurrentCredits(tenantId: string): Promise<Decimal> {
    const tenant = await this.prisma.tenants.findUnique({
      where: { id: tenantId },
      select: { currentAiCredits: true },
    });

    if (!tenant) {
      this.logger.error(
        `Tenant with ID ${tenantId} not found when fetching credits.`
      );
      throw new NotFoundException(`找不到租戶 ${tenantId}`);
    }
    this.logger.debug(
      `查詢租戶 ${tenantId} 的 AI 信用點數：${tenant.currentAiCredits ?? 0}`
    );
    return tenant.currentAiCredits ?? new Decimal(0);
  }
}
