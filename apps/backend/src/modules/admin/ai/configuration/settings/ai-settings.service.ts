import { Injectable, NotFoundException, Logger } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { PrismaService } from "@/modules/core/prisma/prisma.service";
import { EncryptionService } from "@/modules/core/encryption/encryption.service";
import {
  UpdateGlobalSettingsDto,
  UpdateFeatureConfigDto,
} from "./dto/ai-settings.dto";
import { PrismaClient } from "@prisma/client";
import * as crypto from "crypto";
// 新增引入其他 AI 服務用於配置管理
import { AiModelsService } from "../models/ai-models.service";
import { AiKeysService } from "../keys/ai-keys.service";
import { AiBotsService } from "../../bots/ai-bots.service";
import { AiUsageService } from "../usage/ai-usage.service";
import { AiPricingService } from "../pricing/ai-pricing.service";

// 功能清單
export const AI_FEATURES = [
  "promptOptimization",
  "writingAssistant",
  "imageGeneration",
  "projectAssistant",
  "documentAnalysis",
  "chatAssistant",
] as const;

// 功能與顯示名稱的對應
export const FEATURE_DISPLAY_NAMES: Record<string, string> = {
  promptOptimization: "AI 提示詞優化",
  writingAssistant: "AI 寫作助手",
  imageGeneration: "AI 圖像生成",
  projectAssistant: "AI 專案助手",
  documentAnalysis: "AI 文件分析",
  chatAssistant: "AI 對話助手",
};

// 配置管理相關類型定義
export interface ConfigurationTemplate {
  id: string;
  name: string;
  description?: string;
  environment: "development" | "staging" | "production" | "all";
  version: string;
  config: Record<string, any>;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface ConfigurationVersion {
  id: string;
  templateId: string;
  version: string;
  changes: Record<string, any>;
  changeDescription?: string;
  createdBy?: string;
  createdAt: Date;
}

export interface EnvironmentConfig {
  environment: string;
  globalSettings: Record<string, any>;
  providerConfigs: Record<string, any>;
  featureConfigs: Record<string, any>;
  securitySettings: Record<string, any>;
}

export interface ConfigValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  suggestions: string[];
}

@Injectable()
export class AiSettingsService {
  private readonly logger = new Logger(AiSettingsService.name);
  private readonly settingName = "ai_settings";
  private readonly settingType = "AI";

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
    private readonly encryptionService: EncryptionService,
    // 注入其他 AI 服務用於配置管理
    private readonly aiModelsService: AiModelsService,
    private readonly aiKeysService: AiKeysService,
    private readonly aiBotsService: AiBotsService,
    private readonly aiUsageService: AiUsageService,
    private readonly aiPricingService: AiPricingService
  ) {}

  // Helper to convert BigInt fields to numbers for JSON serialization
  private serializeGlobalSettings(settings: any) {
    return {
      ...settings,
      global_monthly_quota_tokens:
        settings.global_monthly_quota_tokens != null
          ? Number(settings.global_monthly_quota_tokens)
          : null,
      global_monthly_quota_calls:
        settings.global_monthly_quota_calls != null
          ? Number(settings.global_monthly_quota_calls)
          : null,
    };
  }

  async getGlobalSettings() {
    let settings = await this.prisma.ai_global_settings.findFirst();
    if (!settings) {
      const now = new Date();
      const created = await this.prisma.ai_global_settings.create({
        data: {
          id: crypto.randomUUID(),
          is_ai_globally_enabled: true,
          created_at: now,
          updated_at: now,
        },
      });
      return this.serializeGlobalSettings(created);
    }
    return this.serializeGlobalSettings(settings);
  }

  async updateGlobalSettings(updateDto: UpdateGlobalSettingsDto) {
    let settings = await this.prisma.ai_global_settings.findFirst();

    // 將 DTO 中的欄位名稱轉換為 Prisma schema 中的欄位名稱
    const dataToUpdate: any = {
      is_ai_globally_enabled: updateDto.isAiGloballyEnabled,
    };

    if (updateDto.globalMonthlyQuotaTokens !== undefined) {
      dataToUpdate.global_monthly_quota_tokens =
        updateDto.globalMonthlyQuotaTokens;
    }

    if (updateDto.globalMonthlyQuotaCalls !== undefined) {
      dataToUpdate.global_monthly_quota_calls =
        updateDto.globalMonthlyQuotaCalls;
    }

    if (!settings) {
      // 如果找不到設定，則建立新的設定
      // 確保所有必要的欄位都有預設值或從 DTO 傳入
      const createData = {
        ...dataToUpdate,
        // 如果有其他必要欄位且 DTO 中沒有，請在此處提供預設值
        // 例如：some_other_required_field: defaultValue
      };
      const created = await this.prisma.ai_global_settings.create({
        data: createData,
      });
      return this.serializeGlobalSettings(created);
    }

    // 如果找到設定，則更新現有的設定
    const updated = await this.prisma.ai_global_settings.update({
      where: { id: settings.id },
      data: dataToUpdate,
    });
    return this.serializeGlobalSettings(updated);
  }

  async getAllFeatureConfigs() {
    // 首先獲取所有現有的功能配置
    const existingConfigs = await this.prisma.ai_feature_configs.findMany({});

    // 獲取所有系統級機器人，按創建時間降序排列（最新的優先）
    const systemBots = await this.prisma.ai_bots.findMany({
      where: {
        scope: "SYSTEM",
        scene: {
          not: null,
        },
      },
      select: {
        id: true,
        name: true,
        scene: true,
        is_enabled: true,
        created_at: true,
      },
      orderBy: {
        created_at: "desc",
      },
    });

    this.logger.log(
      `Found ${systemBots.length} system bots:`,
      systemBots.map((bot) => ({
        id: bot.id,
        name: bot.name,
        scene: bot.scene,
      }))
    );

    // 按場景分組，每個場景只保留最新的機器人
    const botsByScene = new Map<string, (typeof systemBots)[0]>();
    for (const bot of systemBots) {
      if (bot.scene && AI_FEATURES.includes(bot.scene as any)) {
        if (!botsByScene.has(bot.scene)) {
          botsByScene.set(bot.scene, bot);
        }
      }
    }

    this.logger.log(
      `Unique bots by scene:`,
      Array.from(botsByScene.entries()).map(([scene, bot]) => ({
        scene,
        botId: bot.id,
        botName: bot.name,
      }))
    );

    // 處理每個場景的功能配置
    const configsToCreate: Array<{
      feature_key: string;
      is_enabled: boolean;
      bot_id: string;
    }> = [];

    const configsToUpdate: Array<{
      id: string;
      bot_id: string;
      is_enabled: boolean;
    }> = [];

    for (const [scene, bot] of botsByScene) {
      const existingConfig = existingConfigs.find(
        (config) => config.feature_key === scene
      );

      if (!existingConfig) {
        // 創建新的功能配置
        configsToCreate.push({
          feature_key: scene,
          is_enabled: bot.is_enabled,
          bot_id: bot.id,
        });
        this.logger.log(
          `Will create new config for feature: ${scene} with bot: ${bot.id}`
        );
      } else if (existingConfig.bot_id !== bot.id) {
        // 更新現有的功能配置，關聯到對應的機器人
        configsToUpdate.push({
          id: existingConfig.id,
          bot_id: bot.id,
          is_enabled: bot.is_enabled,
        });
        this.logger.log(
          `Will update existing config for feature: ${scene} to use bot: ${bot.id}`
        );
      }
    }

    // 批量創建缺失的功能配置
    if (configsToCreate.length > 0) {
      this.logger.log(`Creating ${configsToCreate.length} new feature configs`);
      const now = new Date();
      const dataWithIds = configsToCreate.map((config) => ({
        id: crypto.randomUUID(),
        ...config,
        created_at: now,
        updated_at: now,
      }));
      await this.prisma.ai_feature_configs.createMany({
        data: dataWithIds,
        skipDuplicates: true,
      });
    }

    // 批量更新現有的功能配置
    if (configsToUpdate.length > 0) {
      this.logger.log(
        `Updating ${configsToUpdate.length} existing feature configs`
      );
      for (const config of configsToUpdate) {
        await this.prisma.ai_feature_configs.update({
          where: { id: config.id },
          data: {
            bot_id: config.bot_id,
            is_enabled: config.is_enabled,
          },
        });
      }
    }

    // 重新獲取所有功能配置（包括新創建和更新的）
    return this.prisma.ai_feature_configs.findMany({});
  }

  async getFeatureConfig(key: string) {
    const config = await this.prisma.ai_feature_configs.findUnique({
      where: { feature_key: key },
    });

    if (!config) {
      throw new NotFoundException(`Feature config with key ${key} not found`);
    }

    return config;
  }

  async updateFeatureConfig(key: string, updateDto: UpdateFeatureConfigDto) {
    const config = await this.prisma.ai_feature_configs.findUnique({
      where: { feature_key: key },
    });

    if (!config) {
      const now = new Date();
      return this.prisma.ai_feature_configs.create({
        data: {
          id: crypto.randomUUID(),
          feature_key: key,
          is_enabled: updateDto.isEnabled,
          bot_id: updateDto.botId || null,
          created_at: now,
          updated_at: now,
        },
      });
    }

    return this.prisma.ai_feature_configs.update({
      where: { feature_key: key },
      data: {
        is_enabled: updateDto.isEnabled,
        bot_id: updateDto.botId,
      },
    });
  }

  /**
   * 同步功能定義到資料庫
   * 從前端傳入的功能定義陣列創建或更新 AiFeatureConfig 記錄
   */
  async syncFeatureDefinitions(
    features: Array<{
      key: string;
      name: string;
      description?: string;
      systemBot?: boolean;
    }>
  ) {
    this.logger.log(`開始同步 ${features.length} 個功能定義到資料庫`);

    const results = {
      created: 0,
      updated: 0,
      skipped: 0,
      errors: [] as string[],
    };

    for (const feature of features) {
      try {
        // 檢查是否已存在
        const existing = await this.prisma.ai_feature_configs.findUnique({
          where: { feature_key: feature.key },
        });

        if (existing) {
          // 如果已存在，跳過（AiFeatureConfig 只存儲配置，不存儲名稱和描述）
          results.skipped++;
          this.logger.debug(`功能 ${feature.key} 已存在，跳過`);
        } else {
          // 創建新的功能配置記錄
          const now = new Date();
          await this.prisma.ai_feature_configs.create({
            data: {
              id: crypto.randomUUID(),
              feature_key: feature.key,
              is_enabled: false, // 預設為停用
              bot_id: null,
              created_at: now,
              updated_at: now,
            },
          });
          results.created++;
          this.logger.debug(`創建新功能配置: ${feature.key}`);
        }
      } catch (error) {
        const errorMessage = `處理功能 ${feature.key} 時發生錯誤: ${error.message}`;
        this.logger.error(errorMessage, error.stack);
        results.errors.push(errorMessage);
      }
    }

    this.logger.log(
      `同步完成 - 創建: ${results.created}, 更新: ${results.updated}, 跳過: ${results.skipped}, 錯誤: ${results.errors.length}`
    );

    return results;
  }

  /**
   * 獲取完整的 AI 配置概覽
   * 整合所有 AI 服務的狀態信息
   */
  async getConfigurationOverview(): Promise<{
    globalSettings: any;
    providerStatus: any[];
    modelStatus: any[];
    botStatus: any[];
    usageStats: any;
    healthCheck: any;
  }> {
    try {
      this.logger.log("獲取 AI 配置概覽...");

      const [globalSettings, keys, models, bots] = await Promise.all([
        this.getGlobalSettings(),
        this.aiKeysService.findAll(),
        this.aiModelsService.findAll(),
        this.aiBotsService.findAll(),
      ]);

      // 彙整供應商狀態
      const providerStatus = this.aggregateProviderStatus(keys, models, bots);

      // 彙整模型狀態
      const modelStatus = this.aggregateModelStatus(models, bots);

      // 彙整機器人狀態
      const botStatus = this.aggregateBotStatus(bots);

      // 獲取使用統計
      const usageStats = await this.getUsageStatistics();

      // 系統健康檢查
      const healthCheck = await this.performHealthCheck();

      return {
        globalSettings,
        providerStatus,
        modelStatus,
        botStatus,
        usageStats,
        healthCheck,
      };
    } catch (error) {
      this.logger.error("獲取 AI 配置概覽失敗", error);
      throw new Error("無法獲取 AI 配置概覽");
    }
  }

  /**
   * 測試 AI 配置連接
   */
  async testProviderConnection(
    provider: string,
    keyId?: string
  ): Promise<{
    success: boolean;
    message: string;
    responseTime?: number;
    details?: any;
  }> {
    try {
      const startTime = Date.now();

      // 如果指定了 keyId，測試特定金鑰
      if (keyId) {
        const result = await this.aiKeysService.testKey(keyId);
        return {
          success: result,
          message: result ? "連接測試成功" : "連接測試失敗",
          responseTime: Date.now() - startTime,
        };
      }

      // 否則測試供應商的所有可用金鑰
      const keys = await this.prisma.ai_keys.findMany({
        where: { provider, is_enabled: true },
      });

      if (keys.length === 0) {
        return {
          success: false,
          message: `供應商 ${provider} 沒有可用的 API 金鑰`,
        };
      }

      const testResults = await Promise.all(
        keys.map((key) => this.aiKeysService.testKey(key.id))
      );

      const successCount = testResults.filter((result) => result).length;

      return {
        success: successCount > 0,
        message: `${successCount}/${keys.length} 個金鑰連接成功`,
        responseTime: Date.now() - startTime,
        details: {
          totalKeys: keys.length,
          successfulKeys: successCount,
          failedKeys: keys.length - successCount,
        },
      };
    } catch (error) {
      this.logger.error("測試供應商連接失敗", error);
      return {
        success: false,
        message: error.message || "連接測試失敗",
      };
    }
  }

  /**
   * 獲取使用統計
   */
  private async getUsageStatistics() {
    try {
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(endDate.getDate() - 30); // 過去30天

      const logs = await this.prisma.ai_usage_logs.findMany({
        where: {
          request_timestamp: {
            gte: startDate,
            lte: endDate,
          },
        },
      });

      const totalCalls = logs.length;
      const successfulCalls = logs.filter((log) => log.is_success).length;
      const totalTokens = logs.reduce(
        (sum, log) => sum + log.input_tokens + log.output_tokens,
        0
      );
      const totalCost = logs.reduce(
        (sum, log) => sum + Number(log.estimated_cost),
        0
      );

      return {
        period: "過去30天",
        totalCalls,
        successfulCalls,
        failureRate:
          totalCalls > 0 ? (totalCalls - successfulCalls) / totalCalls : 0,
        totalTokens,
        totalCost,
        averageCostPerCall: totalCalls > 0 ? totalCost / totalCalls : 0,
      };
    } catch (error) {
      this.logger.error("獲取使用統計失敗", error);
      return {
        period: "過去30天",
        totalCalls: 0,
        successfulCalls: 0,
        failureRate: 0,
        totalTokens: 0,
        totalCost: 0,
        averageCostPerCall: 0,
      };
    }
  }

  /**
   * 彙整供應商狀態
   */
  private aggregateProviderStatus(keys: any[], models: any[], bots: any[]) {
    const providers = new Map();

    // 從金鑰中統計供應商信息
    keys.forEach((key) => {
      if (!providers.has(key.provider)) {
        providers.set(key.provider, {
          name: key.provider,
          status: "inactive",
          keysCount: 0,
          enabledKeysCount: 0,
          modelsCount: 0,
          enabledModelsCount: 0,
          botsCount: 0,
          enabledBotsCount: 0,
          lastActivity: null,
        });
      }

      const provider = providers.get(key.provider);
      provider.keysCount++;
      if (key.is_enabled) {
        provider.enabledKeysCount++;
        provider.status = "active";
      }
    });

    // 從模型中統計
    models.forEach((model) => {
      if (providers.has(model.provider)) {
        const provider = providers.get(model.provider);
        provider.modelsCount++;
        if (model.is_enabled) {
          provider.enabledModelsCount++;
        }
      }
    });

    // 從機器人中統計
    bots.forEach((bot) => {
      if (bot.ai_keys && providers.has(bot.ai_keys.provider)) {
        const provider = providers.get(bot.ai_keys.provider);
        provider.botsCount++;
        if (bot.is_enabled) {
          provider.enabledBotsCount++;
        }
      }
    });

    return Array.from(providers.values());
  }

  /**
   * 彙整模型狀態
   */
  private aggregateModelStatus(models: any[], bots: any[]) {
    return models.map((model) => {
      const modelBots = bots.filter((bot) => bot.model_id === model.id);
      return {
        id: model.id,
        name: model.model_name,
        displayName: model.display_name,
        provider: model.provider,
        isEnabled: model.is_enabled,
        botsUsingCount: modelBots.length,
        enabledBotsCount: modelBots.filter((bot) => bot.is_enabled).length,
        inputPrice: model.input_price_per_1k_tokens,
        outputPrice: model.output_price_per_1k_tokens,
        currency: model.currency,
        contextWindow: model.context_window_tokens,
        lastUpdated: model.updated_at,
      };
    });
  }

  /**
   * 彙整機器人狀態
   */
  private aggregateBotStatus(bots: any[]) {
    return bots.map((bot) => ({
      id: bot.id,
      name: bot.name,
      description: bot.description,
      scope: bot.scope,
      provider: bot.ai_keys?.provider,
      model: bot.ai_models?.model_name,
      isEnabled: bot.is_enabled,
      scene: bot.scene,
      createdAt: bot.created_at,
      lastUpdated: bot.updated_at,
    }));
  }

  /**
   * 執行系統健康檢查
   */
  private async performHealthCheck() {
    try {
      const checks = {
        database: true, // 如果能執行到這裡說明資料庫連接正常
        aiKeys: false,
        aiModels: false,
        aiFeatures: false,
      };

      // 檢查 AI 金鑰
      const enabledKeys = await this.prisma.ai_keys.count({
        where: { is_enabled: true },
      });
      checks.aiKeys = enabledKeys > 0;

      // 檢查 AI 模型
      const enabledModels = await this.prisma.ai_models.count({
        where: { is_enabled: true },
      });
      checks.aiModels = enabledModels > 0;

      // 檢查 AI 功能配置
      const enabledFeatures = await this.prisma.ai_feature_configs.count({
        where: { is_enabled: true },
      });
      checks.aiFeatures = enabledFeatures > 0;

      const allHealthy = Object.values(checks).every((check) => check);

      return {
        status: allHealthy ? "healthy" : "warning",
        checks,
        timestamp: new Date(),
        summary: {
          enabledKeys,
          enabledModels,
          enabledFeatures,
        },
      };
    } catch (error) {
      this.logger.error("健康檢查失敗", error);
      return {
        status: "error",
        checks: {
          database: false,
          aiKeys: false,
          aiModels: false,
          aiFeatures: false,
        },
        timestamp: new Date(),
        error: error.message,
      };
    }
  }

  /**
   * 同步和更新 AI 系統配置
   */
  async syncSystemConfiguration(): Promise<{
    success: boolean;
    message: string;
    details: any;
  }> {
    try {
      this.logger.log("開始同步 AI 系統配置...");

      // 1. 同步模型信息
      const modelSyncResult = await this.aiModelsService.fetchFromProviders();

      // 2. 同步功能配置
      await this.getAllFeatureConfigs();

      // 3. 更新價格信息
      const priceSyncResult = await this.aiModelsService.syncModelPricing();

      return {
        success: true,
        message: "AI 系統配置同步完成",
        details: {
          models: modelSyncResult,
          pricing: priceSyncResult,
          timestamp: new Date(),
        },
      };
    } catch (error) {
      this.logger.error("同步 AI 系統配置失敗", error);
      return {
        success: false,
        message: "同步 AI 系統配置失敗",
        details: {
          error: error.message,
          timestamp: new Date(),
        },
      };
    }
  }

  // ==================== 配置管理擴展功能 ====================

  /**
   * 獲取環境特定配置
   */
  async getEnvironmentConfig(environment: string): Promise<EnvironmentConfig> {
    try {
      this.logger.log(`獲取 ${environment} 環境配置...`);

      const [globalSettings, keys, models, featureConfigs] = await Promise.all([
        this.getGlobalSettings(),
        this.aiKeysService.findAll(),
        this.aiModelsService.findAll(),
        this.getAllFeatureConfigs(),
      ]);

      // 過濾環境特定的配置 (目前所有金鑰和模型都適用於所有環境)
      const envKeys = keys;
      const envModels = models;

      return {
        environment,
        globalSettings,
        providerConfigs: this.groupConfigsByProvider(envKeys),
        featureConfigs: this.processFeatureConfigs(featureConfigs),
        securitySettings: await this.getSecuritySettings(environment),
      };
    } catch (error) {
      this.logger.error(`獲取 ${environment} 環境配置失敗`, error);
      throw new Error(`無法獲取 ${environment} 環境配置: ${error.message}`);
    }
  }

  /**
   * 更新環境特定配置
   */
  async updateEnvironmentConfig(
    environment: string,
    config: Partial<EnvironmentConfig>
  ): Promise<{ success: boolean; message: string; details?: any }> {
    try {
      this.logger.log(`更新 ${environment} 環境配置...`);

      const updateResults: Array<{
        type: string;
        key?: string;
        result: any;
      }> = [];

      // 更新全域設定
      if (config.globalSettings) {
        const globalResult = await this.updateGlobalSettings(
          config.globalSettings as any
        );
        updateResults.push({ type: "global", result: globalResult });
      }

      // 更新功能配置
      if (config.featureConfigs) {
        for (const [featureKey, featureConfig] of Object.entries(
          config.featureConfigs
        )) {
          const featureResult = await this.updateFeatureConfig(
            featureKey,
            featureConfig as any
          );
          updateResults.push({
            type: "feature",
            key: featureKey,
            result: featureResult,
          });
        }
      }

      // 記錄配置變更
      await this.logConfigurationChange(environment, config, "update");

      return {
        success: true,
        message: `${environment} 環境配置更新成功`,
        details: updateResults,
      };
    } catch (error) {
      this.logger.error(`更新 ${environment} 環境配置失敗`, error);
      return {
        success: false,
        message: `更新 ${environment} 環境配置失敗: ${error.message}`,
      };
    }
  }

  /**
   * 驗證配置有效性
   */
  async validateConfiguration(
    config: Partial<EnvironmentConfig>
  ): Promise<ConfigValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];
    const suggestions: string[] = [];

    try {
      // 驗證全域設定
      if (config.globalSettings) {
        const globalValidation = await this.validateGlobalSettings(
          config.globalSettings
        );
        errors.push(...globalValidation.errors);
        warnings.push(...globalValidation.warnings);
        suggestions.push(...globalValidation.suggestions);
      }

      // 驗證供應商配置
      if (config.providerConfigs) {
        const providerValidation = await this.validateProviderConfigs(
          config.providerConfigs
        );
        errors.push(...providerValidation.errors);
        warnings.push(...providerValidation.warnings);
        suggestions.push(...providerValidation.suggestions);
      }

      // 驗證功能配置
      if (config.featureConfigs) {
        const featureValidation = await this.validateFeatureConfigs(
          config.featureConfigs
        );
        errors.push(...featureValidation.errors);
        warnings.push(...featureValidation.warnings);
        suggestions.push(...featureValidation.suggestions);
      }

      return {
        isValid: errors.length === 0,
        errors,
        warnings,
        suggestions,
      };
    } catch (error) {
      this.logger.error("配置驗證失敗", error);
      return {
        isValid: false,
        errors: [`配置驗證過程中發生錯誤: ${error.message}`],
        warnings,
        suggestions,
      };
    }
  }

  /**
   * 創建配置模板
   */
  async createConfigurationTemplate(
    name: string,
    description: string,
    environment: string,
    config: Record<string, any>
  ): Promise<ConfigurationTemplate> {
    try {
      const template: ConfigurationTemplate = {
        id: crypto.randomUUID(),
        name,
        description,
        environment: environment as any,
        version: "1.0.0",
        config,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // 這裡可以將模板保存到數據庫
      // 目前先返回模板對象
      this.logger.log(`創建配置模板: ${name} (${environment})`);

      return template;
    } catch (error) {
      this.logger.error("創建配置模板失敗", error);
      throw new Error(`創建配置模板失敗: ${error.message}`);
    }
  }

  /**
   * 應用配置模板
   */
  async applyConfigurationTemplate(
    templateId: string,
    environment: string
  ): Promise<{ success: boolean; message: string; details?: any }> {
    try {
      this.logger.log(`應用配置模板 ${templateId} 到 ${environment} 環境...`);

      // 這裡需要從數據庫獲取模板
      // 目前先模擬實現
      const template = await this.getConfigurationTemplate(templateId);

      if (!template) {
        throw new Error(`找不到配置模板: ${templateId}`);
      }

      // 應用模板配置
      const result = await this.updateEnvironmentConfig(
        environment,
        template.config
      );

      // 記錄模板應用
      await this.logConfigurationChange(
        environment,
        template.config,
        "template_apply",
        templateId
      );

      return {
        success: true,
        message: `配置模板 ${template.name} 已成功應用到 ${environment} 環境`,
        details: result.details,
      };
    } catch (error) {
      this.logger.error("應用配置模板失敗", error);
      return {
        success: false,
        message: `應用配置模板失敗: ${error.message}`,
      };
    }
  }

  /**
   * 獲取配置變更歷史
   */
  async getConfigurationHistory(
    environment?: string,
    limit: number = 50
  ): Promise<ConfigurationVersion[]> {
    try {
      // 這裡需要從數據庫查詢配置變更歷史
      // 目前先返回空數組
      this.logger.log(
        `獲取配置變更歷史 (環境: ${environment || "全部"}, 限制: ${limit})`
      );

      return [];
    } catch (error) {
      this.logger.error("獲取配置變更歷史失敗", error);
      throw new Error(`獲取配置變更歷史失敗: ${error.message}`);
    }
  }

  /**
   * 回滾配置到指定版本
   */
  async rollbackConfiguration(
    environment: string,
    versionId: string
  ): Promise<{ success: boolean; message: string; details?: any }> {
    try {
      this.logger.log(`回滾 ${environment} 環境配置到版本 ${versionId}...`);

      // 這裡需要從數據庫獲取指定版本的配置
      // 然後應用該配置

      // 記錄回滾操作
      await this.logConfigurationChange(environment, {}, "rollback", versionId);

      return {
        success: true,
        message: `${environment} 環境配置已成功回滾到版本 ${versionId}`,
      };
    } catch (error) {
      this.logger.error("配置回滾失敗", error);
      return {
        success: false,
        message: `配置回滾失敗: ${error.message}`,
      };
    }
  }

  // ==================== 私有輔助方法 ====================

  /**
   * 按供應商分組配置
   */
  private groupConfigsByProvider(keys: any[]): Record<string, any> {
    const grouped: Record<string, any> = {};

    keys.forEach((key) => {
      if (!grouped[key.provider]) {
        grouped[key.provider] = {
          keys: [],
          defaultKeyId: null,
        };
      }

      grouped[key.provider].keys.push({
        id: key.id,
        name: key.name,
        isEnabled: key.is_enabled,
        isDefault: key.is_default || false,
      });

      if (key.is_default) {
        grouped[key.provider].defaultKeyId = key.id;
      }
    });

    return grouped;
  }

  /**
   * 處理功能配置
   */
  private processFeatureConfigs(configs: any[]): Record<string, any> {
    const processed: Record<string, any> = {};

    configs.forEach((config) => {
      processed[config.feature_key] = {
        isEnabled: config.is_enabled,
        botId: config.bot_id,
        lastUpdated: config.updated_at,
      };
    });

    return processed;
  }

  /**
   * 獲取安全設定
   */
  private async getSecuritySettings(
    environment: string
  ): Promise<Record<string, any>> {
    return {
      encryptionEnabled: true,
      keyRotationInterval: this.configService.get(
        "AI_KEY_ROTATION_INTERVAL",
        "30d"
      ),
      accessLogging:
        this.configService.get("AI_ACCESS_LOGGING", "true") === "true",
      rateLimiting: {
        enabled: this.configService.get("AI_RATE_LIMITING", "true") === "true",
        requestsPerMinute: parseInt(
          this.configService.get("AI_RATE_LIMIT_RPM", "60")
        ),
        requestsPerHour: parseInt(
          this.configService.get("AI_RATE_LIMIT_RPH", "1000")
        ),
      },
    };
  }

  /**
   * 驗證全域設定
   */
  private async validateGlobalSettings(
    settings: Record<string, any>
  ): Promise<ConfigValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];
    const suggestions: string[] = [];

    if (
      settings.globalMonthlyQuotaTokens &&
      settings.globalMonthlyQuotaTokens < 0
    ) {
      errors.push("全域月度 Token 配額不能為負數");
    }

    if (
      settings.globalMonthlyQuotaCalls &&
      settings.globalMonthlyQuotaCalls < 0
    ) {
      errors.push("全域月度 API 呼叫配額不能為負數");
    }

    if (
      !settings.globalMonthlyQuotaTokens &&
      !settings.globalMonthlyQuotaCalls
    ) {
      warnings.push("建議設定全域配額以控制使用量");
    }

    return { isValid: errors.length === 0, errors, warnings, suggestions };
  }

  /**
   * 驗證供應商配置
   */
  private async validateProviderConfigs(
    configs: Record<string, any>
  ): Promise<ConfigValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];
    const suggestions: string[] = [];

    for (const [provider, config] of Object.entries(configs)) {
      if (!config.keys || config.keys.length === 0) {
        warnings.push(`供應商 ${provider} 沒有配置 API 金鑰`);
      }

      const enabledKeys = config.keys.filter((key: any) => key.isEnabled);
      if (enabledKeys.length === 0) {
        warnings.push(`供應商 ${provider} 沒有啟用的 API 金鑰`);
      }

      if (!config.defaultKeyId && enabledKeys.length > 1) {
        suggestions.push(`建議為供應商 ${provider} 設定預設 API 金鑰`);
      }
    }

    return { isValid: errors.length === 0, errors, warnings, suggestions };
  }

  /**
   * 驗證功能配置
   */
  private async validateFeatureConfigs(
    configs: Record<string, any>
  ): Promise<ConfigValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];
    const suggestions: string[] = [];

    for (const [feature, config] of Object.entries(configs)) {
      if (config.isEnabled && !config.botId) {
        errors.push(`功能 ${feature} 已啟用但未配置機器人`);
      }

      if (!config.isEnabled && config.botId) {
        warnings.push(`功能 ${feature} 已配置機器人但未啟用`);
      }
    }

    const enabledFeatures = Object.values(configs).filter(
      (config: any) => config.isEnabled
    );
    if (enabledFeatures.length === 0) {
      warnings.push("沒有啟用任何 AI 功能");
    }

    return { isValid: errors.length === 0, errors, warnings, suggestions };
  }

  /**
   * 記錄配置變更
   */
  private async logConfigurationChange(
    environment: string,
    config: Record<string, any>,
    action: string,
    reference?: string
  ): Promise<void> {
    try {
      const logEntry = {
        id: crypto.randomUUID(),
        environment,
        action,
        config,
        reference,
        timestamp: new Date(),
      };

      this.logger.log("配置變更記錄", logEntry);

      // 這裡可以將變更記錄保存到數據庫
      // await this.prisma.ai_configuration_logs.create({ data: logEntry });
    } catch (error) {
      this.logger.error("記錄配置變更失敗", error);
    }
  }

  /**
   * 獲取配置模板
   */
  private async getConfigurationTemplate(
    templateId: string
  ): Promise<ConfigurationTemplate | null> {
    try {
      // 這裡需要從數據庫查詢模板
      // 目前先返回 null
      this.logger.log(`查詢配置模板: ${templateId}`);
      return null;
    } catch (error) {
      this.logger.error("獲取配置模板失敗", error);
      return null;
    }
  }
}
