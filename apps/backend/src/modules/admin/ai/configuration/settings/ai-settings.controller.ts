import {
  Controller,
  Get,
  Put,
  Body,
  Param,
  UseGuards,
  Post,
  Query,
} from "@nestjs/common";
import {
  ApiTags,
  ApiOperation,
  ApiBearerAuth,
  ApiQuery,
} from "@nestjs/swagger";
import { JwtAuthGuard } from "@/modules/core/auth/guards/auth.guard";
import { AiSettingsService } from "./ai-settings.service";
import {
  UpdateGlobalSettingsDto,
  UpdateFeatureConfigDto,
} from "./dto/ai-settings.dto";

@ApiTags("admin/ai-settings")
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller("admin/ai/settings")
export class AiSettingsController {
  constructor(private readonly aiSettingsService: AiSettingsService) {}

  @Get("global")
  @ApiOperation({ summary: "取得全域設定" })
  async getGlobalSettings() {
    return this.aiSettingsService.getGlobalSettings();
  }

  @Put("global")
  @ApiOperation({ summary: "更新全域設定" })
  async updateGlobalSettings(@Body() updateDto: UpdateGlobalSettingsDto) {
    return this.aiSettingsService.updateGlobalSettings(updateDto);
  }

  @Get("features")
  @ApiOperation({ summary: "取得所有功能配置" })
  async getAllFeatureConfigs() {
    return this.aiSettingsService.getAllFeatureConfigs();
  }

  @Get("features/:key")
  @ApiOperation({ summary: "取得特定功能配置" })
  async getFeatureConfig(@Param("key") key: string) {
    return this.aiSettingsService.getFeatureConfig(key);
  }

  @Put("features/:key")
  @ApiOperation({ summary: "更新功能配置" })
  async updateFeatureConfig(
    @Param("key") key: string,
    @Body() updateDto: UpdateFeatureConfigDto
  ) {
    return this.aiSettingsService.updateFeatureConfig(key, updateDto);
  }

  @Post("sync-feature-definitions")
  @ApiOperation({ summary: "同步功能定義到資料庫" })
  async syncFeatureDefinitions(
    @Body()
    features: Array<{
      key: string;
      name: string;
      description?: string;
      systemBot?: boolean;
    }>
  ) {
    return this.aiSettingsService.syncFeatureDefinitions(features);
  }

  @Get("overview")
  @ApiOperation({ summary: "取得完整的 AI 配置概覽" })
  async getConfigurationOverview() {
    return this.aiSettingsService.getConfigurationOverview();
  }

  @Post("test-connection/:provider")
  @ApiOperation({ summary: "測試指定供應商的連接" })
  @ApiQuery({
    name: "keyId",
    required: false,
    description: "指定要測試的金鑰 ID",
  })
  async testProviderConnection(
    @Param("provider") provider: string,
    @Query("keyId") keyId?: string
  ) {
    return this.aiSettingsService.testProviderConnection(provider, keyId);
  }

  @Post("sync")
  @ApiOperation({ summary: "同步和更新 AI 系統配置" })
  async syncSystemConfiguration() {
    return this.aiSettingsService.syncSystemConfiguration();
  }
}
