import { Injectable, Logger } from "@nestjs/common";
import { PrismaService } from "@/modules/core/prisma/prisma.service";
import { EncryptionService } from "@/modules/core/encryption/encryption.service";
import { AiProviderFactory } from "../core/providers/factory";
import { AiBotsService } from "../bots/ai-bots.service";

// 定義通用的訊息介面
export interface BusinessAiMessage {
  role: "system" | "user" | "assistant";
  content: string;
}

// 定義執行選項介面
export interface BusinessAiExecuteOptions {
  model?: string;
  temperature?: number;
  maxTokens?: number;
  responseFormat?: "TEXT" | "JSON_OBJECT";
}

// 定義視覺分析選項介面
export interface BusinessVisionAnalysisOptions {
  imageUrl: string;
  prompt: string;
  model?: string;
  temperature?: number;
  maxTokens?: number;
  responseFormat?: "TEXT" | "JSON_OBJECT";
  detail?: "low" | "high" | "auto";
}

export interface ProjectAnalysisRequest {
  projectId: string;
  tenantId: string;
  analysisType: "status" | "risk" | "performance" | "optimization";
  includeSubProjects?: boolean;
  includeTasks?: boolean;
  includeProgress?: boolean;
  confidence?: number;
}

export interface ProjectAnalysisResult {
  projectId: string;
  analysisType: string;
  insights: {
    summary: string;
    recommendations: string[];
    riskFactors: string[];
    optimizationSuggestions: string[];
  };
  confidence: number;
  generatedAt: Date;
}

export interface PhotoAnalysisRequest {
  photoUrl: string;
  projectId: string;
  tenantId: string;
  analysisType: "progress" | "quality" | "safety" | "equipment";
  context?: string;
  confidence?: number;
}

export interface PhotoAnalysisResult {
  photoUrl: string;
  projectId: string;
  analysisType: string;
  findings: {
    description: string;
    progressPercentage?: number;
    qualityScore?: number;
    safetyIssues: string[];
    equipmentDetected: string[];
    recommendations: string[];
  };
  confidence: number;
  generatedAt: Date;
}

export interface WorkflowOptimizationRequest {
  tenantId: string;
  projectId?: string;
  timeRange: {
    startDate: Date;
    endDate: Date;
  };
  includeMetrics: string[];
  confidence?: number;
}

export interface WorkflowOptimizationResult {
  tenantId: string;
  projectId?: string;
  analysis: {
    bottlenecks: Array<{
      area: string;
      description: string;
      impact: "low" | "medium" | "high";
      suggestions: string[];
    }>;
    efficiencyMetrics: {
      taskCompletionRate: number;
      averageTaskDuration: number;
      resourceUtilization: number;
    };
    recommendations: Array<{
      priority: "low" | "medium" | "high";
      category: string;
      description: string;
      expectedImpact: string;
    }>;
  };
  confidence: number;
  generatedAt: Date;
}

@Injectable()
export class AiBusinessIntegrationService {
  private readonly logger = new Logger(AiBusinessIntegrationService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly aiProviderFactory: AiProviderFactory,
    private readonly aiBotsService: AiBotsService,
    private readonly encryptionService: EncryptionService
  ) {}

  /**
   * 分析項目狀態、風險和性能
   */
  async analyzeProject(
    request: ProjectAnalysisRequest
  ): Promise<ProjectAnalysisResult> {
    this.logger.log(
      `Starting project analysis for project ${request.projectId}`
    );

    try {
      // 獲取項目數據
      const projectData = await this.getProjectData(request);

      // 構建分析提示
      const analysisPrompt = this.buildProjectAnalysisPrompt(
        projectData,
        request.analysisType
      );

      // 執行 AI 分析
      const aiResponse = await this.executeAiAnalysis(
        analysisPrompt,
        request.tenantId
      );

      // 解析結果
      const result = this.parseProjectAnalysisResult(aiResponse, request);

      // 保存分析結果
      await this.saveAnalysisResult(result);

      return result;
    } catch (error) {
      this.logger.error(
        `Project analysis failed: ${error.message}`,
        error.stack
      );
      throw error;
    }
  }

  /**
   * 分析照片中的項目進度
   */
  async analyzePhoto(
    request: PhotoAnalysisRequest
  ): Promise<PhotoAnalysisResult> {
    this.logger.log(`Starting photo analysis for project ${request.projectId}`);

    try {
      // 獲取項目上下文
      const projectContext = await this.getProjectContext(
        request.projectId,
        request.tenantId
      );

      // 構建照片分析提示
      const analysisPrompt = this.buildPhotoAnalysisPrompt(
        request,
        projectContext
      );

      // 執行 AI 視覺分析
      const aiResponse = await this.executeVisionAnalysis(
        request.photoUrl,
        analysisPrompt,
        request.tenantId
      );

      // 解析結果
      const result = this.parsePhotoAnalysisResult(aiResponse, request);

      // 保存分析結果 (包含額外資訊)
      const processingStartTime = Date.now();
      await this.savePhotoAnalysisResult(
        result,
        "system", // TODO: 從請求中獲取實際用戶 ID
        "gpt-4-vision-preview", // TODO: 從 AI bot 配置中獲取實際模型
        Date.now() - processingStartTime
      );

      return result;
    } catch (error) {
      this.logger.error(`Photo analysis failed: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 分析工作流程並提供優化建議
   */
  async optimizeWorkflow(
    request: WorkflowOptimizationRequest
  ): Promise<WorkflowOptimizationResult> {
    this.logger.log(
      `Starting workflow optimization analysis for tenant ${request.tenantId}`
    );

    try {
      // 獲取工作流程數據
      const workflowData = await this.getWorkflowData(request);

      // 構建優化分析提示
      const analysisPrompt = this.buildWorkflowOptimizationPrompt(workflowData);

      // 執行 AI 分析
      const aiResponse = await this.executeAiAnalysis(
        analysisPrompt,
        request.tenantId
      );

      // 解析結果
      const result = this.parseWorkflowOptimizationResult(aiResponse, request);

      // 保存優化建議
      await this.saveWorkflowOptimizationResult(result);

      return result;
    } catch (error) {
      this.logger.error(
        `Workflow optimization failed: ${error.message}`,
        error.stack
      );
      throw error;
    }
  }

  /**
   * 獲取項目數據
   */
  private async getProjectData(request: ProjectAnalysisRequest) {
    const project = await this.prisma.projects.findUnique({
      where: { id: request.projectId, tenantId: request.tenantId },
    });

    if (!project) {
      throw new Error(`Project ${request.projectId} not found`);
    }

    // 獲取相關的任務和進度數據
    const [tasks, progressEntries, milestones] = await Promise.all([
      request.includeTasks
        ? this.prisma.tasks.findMany({
            where: { projectId: request.projectId, tenantId: request.tenantId },
          })
        : [],
      request.includeProgress
        ? this.prisma.progress_entries.findMany({
            where: { projectId: request.projectId, tenantId: request.tenantId },
          })
        : [],
      this.prisma.project_milestones.findMany({
        where: { projectId: request.projectId, tenantId: request.tenantId },
      }),
    ]);

    return {
      ...project,
      tasks,
      progressEntries,
      milestones,
    };
  }

  /**
   * 獲取項目上下文
   */
  private async getProjectContext(projectId: string, tenantId: string) {
    return await this.prisma.projects.findUnique({
      where: { id: projectId, tenantId },
      select: {
        name: true,
        description: true,
        priority: true,
        startDate: true,
        endDate: true,
      },
    });
  }

  /**
   * 獲取工作流程數據
   */
  private async getWorkflowData(request: WorkflowOptimizationRequest) {
    const where = {
      tenantId: request.tenantId,
      ...(request.projectId && { projectId: request.projectId }),
      createdAt: {
        gte: request.timeRange.startDate,
        lte: request.timeRange.endDate,
      },
    };

    const [tasks, progressEntries, projects] = await Promise.all([
      this.prisma.tasks.findMany({ where }),
      this.prisma.progress_entries.findMany({ where }),
      this.prisma.projects.findMany({
        where: {
          tenantId: request.tenantId,
          ...(request.projectId && { id: request.projectId }),
        },
      }),
    ]);

    return { tasks, progressEntries, projects };
  }

  /**
   * 構建項目分析提示
   */
  private buildProjectAnalysisPrompt(
    projectData: any,
    analysisType: string
  ): string {
    const basePrompt = `
作為一個專業的項目管理分析師，請分析以下項目數據並提供詳細的${analysisType}分析：

項目信息：
- 名稱：${projectData.name}
- 描述：${projectData.description || "無描述"}
- 優先級：${projectData.priority}
- 開始日期：${projectData.startDate || "未設定"}
- 結束日期：${projectData.endDate || "未設定"}
- 預算：${projectData.budget || "未設定"}

任務統計：
- 總任務數：${projectData.tasks?.length || 0}
- 已完成任務：${projectData.tasks?.filter((t: any) => t.status === "done").length || 0}
- 進行中任務：${projectData.tasks?.filter((t: any) => t.status === "in-progress").length || 0}

里程碑統計：
- 總里程碑數：${projectData.milestones?.length || 0}
- 已完成里程碑：${projectData.milestones?.filter((m: any) => m.status === "COMPLETED").length || 0}

請提供：
1. 項目現狀摘要
2. 具體的改進建議（至少3個）
3. 潛在風險因素（至少2個）
4. 優化建議（至少2個）

請以JSON格式回應，包含summary、recommendations、riskFactors、optimizationSuggestions字段。
    `;

    return basePrompt;
  }

  /**
   * 構建照片分析提示
   */
  private buildPhotoAnalysisPrompt(
    request: PhotoAnalysisRequest,
    projectContext: any
  ): string {
    return `
作為一個專業的建築/工程項目分析師，請分析這張項目照片：

項目背景：
- 項目名稱：${projectContext?.name || "未知"}
- 項目描述：${projectContext?.description || "無描述"}
- 分析類型：${request.analysisType}
- 額外上下文：${request.context || "無"}

請根據照片內容提供：
1. 詳細的描述
2. 如果是進度分析，請估算完成百分比
3. 如果是質量分析，請給出質量評分（1-10）
4. 識別任何安全問題
5. 檢測到的設備或材料
6. 具體的改進建議

請以JSON格式回應。
    `;
  }

  /**
   * 構建工作流程優化提示
   */
  private buildWorkflowOptimizationPrompt(workflowData: any): string {
    const tasksCount = workflowData.tasks.length;
    const completedTasks = workflowData.tasks.filter(
      (t: any) => t.status === "done"
    ).length;
    const completionRate =
      tasksCount > 0 ? (completedTasks / tasksCount) * 100 : 0;

    return `
作為一個專業的工作流程優化專家，請分析以下工作流程數據：

統計數據：
- 總任務數：${tasksCount}
- 已完成任務：${completedTasks}
- 完成率：${completionRate.toFixed(2)}%
- 進度條目數：${workflowData.progressEntries.length}
- 項目數：${workflowData.projects.length}

請分析並提供：
1. 識別的瓶頸（包含區域、描述、影響程度、建議）
2. 效率指標（任務完成率、平均任務持續時間、資源利用率）
3. 優化建議（包含優先級、類別、描述、預期影響）

請以JSON格式回應，包含bottlenecks、efficiencyMetrics、recommendations字段。
    `;
  }

  /**
   * 執行 AI 分析
   */
  private async executeAiAnalysis(
    prompt: string,
    tenantId: string
  ): Promise<string> {
    try {
      // 獲取租戶的 AI Bot 配置
      const aiBot = await this.aiBotsService.findDefaultBot(
        tenantId,
        "WORKSPACE"
      );

      if (!aiBot) {
        throw new Error("No AI bot configured for this tenant");
      }

      // 構建標準化的訊息格式
      const messages: BusinessAiMessage[] = [
        {
          role: "system",
          content:
            "你是一個專業的項目管理和業務分析專家，請提供詳細、準確的分析結果。",
        },
        {
          role: "user",
          content: prompt,
        },
      ];

      // 構建執行選項
      const model = aiBot.ai_models?.model_name || "gpt-3.5-turbo";
      const options: BusinessAiExecuteOptions = {
        model,
        temperature: aiBot.temperature ?? 0.3,
        maxTokens: aiBot.max_tokens ?? 2000,
        responseFormat:
          aiBot.response_format === "JSON_OBJECT" ? "JSON_OBJECT" : "TEXT",
      };

      // 使用 aiBotsService 執行分析
      const result = await this.aiBotsService.executeBot(
        aiBot.id,
        messages,
        options as any // 臨時使用 any 直到型別相容
      );

      return result.content;
    } catch (error) {
      this.logger.error(
        `AI analysis execution failed: ${error.message}`,
        error.stack
      );
      throw error;
    }
  }

  /**
   * 執行視覺分析
   */
  private async executeVisionAnalysis(
    photoUrl: string,
    prompt: string,
    tenantId: string
  ): Promise<string> {
    try {
      // 獲取租戶的 AI Bot 配置
      const aiBot = await this.aiBotsService.findDefaultBot(
        tenantId,
        "WORKSPACE"
      );

      if (!aiBot) {
        throw new Error("No AI bot configured for this tenant");
      }

      // 檢查是否為 OpenAI 提供者（目前只有 OpenAI 支援視覺分析）
      if (aiBot.provider_type !== "OPENAI") {
        throw new Error(
          "Vision analysis is currently only supported with OpenAI provider"
        );
      }

      // 解密 API 密鑰
      const decryptedApiKey = this.encryptionService.decrypt(
        aiBot.ai_keys.api_key
      );

      // 創建 AI 提供者
      const provider = this.aiProviderFactory.createProvider(
        aiBot.provider_type,
        decryptedApiKey,
        aiBot.ai_keys.api_url || undefined
      );

      // 檢查提供者是否支援視覺分析
      if (!provider.executeVisionAnalysis) {
        throw new Error(
          "Vision analysis not supported by the configured AI provider"
        );
      }

      // 構建視覺分析選項
      const model = aiBot.ai_models?.model_name || "gpt-4-vision-preview";
      const visionOptions: BusinessVisionAnalysisOptions = {
        imageUrl: photoUrl,
        prompt: prompt,
        model,
        temperature: aiBot.temperature ?? 0.3,
        maxTokens: aiBot.max_tokens ?? 2000,
        responseFormat:
          aiBot.response_format === "JSON_OBJECT" ? "JSON_OBJECT" : "TEXT",
        detail: "high",
      };

      // 執行視覺分析
      const result = await provider.executeVisionAnalysis(visionOptions as any);

      return result.content;
    } catch (error) {
      this.logger.error(
        `Vision analysis failed: ${error.message}`,
        error.stack
      );
      // 返回模擬結果作為後備
      return JSON.stringify({
        description: "視覺分析暫時不可用，請稍後再試",
        progressPercentage: 0,
        qualityScore: 0,
        safetyIssues: [],
        equipmentDetected: [],
        recommendations: ["請檢查 AI 服務配置或聯繫管理員"],
      });
    }
  }

  /**
   * 解析項目分析結果
   */
  private parseProjectAnalysisResult(
    aiResponse: string,
    request: ProjectAnalysisRequest
  ): ProjectAnalysisResult {
    try {
      const parsed = JSON.parse(aiResponse);
      return {
        projectId: request.projectId,
        analysisType: request.analysisType,
        insights: {
          summary: parsed.summary || "",
          recommendations: parsed.recommendations || [],
          riskFactors: parsed.riskFactors || [],
          optimizationSuggestions: parsed.optimizationSuggestions || [],
        },
        confidence: request.confidence || 0.85,
        generatedAt: new Date(),
      };
    } catch (error) {
      this.logger.error(`Failed to parse AI response: ${error.message}`);
      throw new Error("Invalid AI response format");
    }
  }

  /**
   * 解析照片分析結果
   */
  private parsePhotoAnalysisResult(
    aiResponse: string,
    request: PhotoAnalysisRequest
  ): PhotoAnalysisResult {
    try {
      const parsed = JSON.parse(aiResponse);
      return {
        photoUrl: request.photoUrl,
        projectId: request.projectId,
        analysisType: request.analysisType,
        findings: {
          description: parsed.description || "",
          progressPercentage: parsed.progressPercentage,
          qualityScore: parsed.qualityScore,
          safetyIssues: parsed.safetyIssues || [],
          equipmentDetected: parsed.equipmentDetected || [],
          recommendations: parsed.recommendations || [],
        },
        confidence: request.confidence || 0.8,
        generatedAt: new Date(),
      };
    } catch (error) {
      this.logger.error(
        `Failed to parse photo analysis response: ${error.message}`
      );
      throw new Error("Invalid photo analysis response format");
    }
  }

  /**
   * 解析工作流程優化結果
   */
  private parseWorkflowOptimizationResult(
    aiResponse: string,
    request: WorkflowOptimizationRequest
  ): WorkflowOptimizationResult {
    try {
      const parsed = JSON.parse(aiResponse);
      return {
        tenantId: request.tenantId,
        projectId: request.projectId,
        analysis: {
          bottlenecks: parsed.bottlenecks || [],
          efficiencyMetrics: parsed.efficiencyMetrics || {
            taskCompletionRate: 0,
            averageTaskDuration: 0,
            resourceUtilization: 0,
          },
          recommendations: parsed.recommendations || [],
        },
        confidence: request.confidence || 0.85,
        generatedAt: new Date(),
      };
    } catch (error) {
      this.logger.error(
        `Failed to parse workflow optimization response: ${error.message}`
      );
      throw new Error("Invalid workflow optimization response format");
    }
  }

  /**
   * 保存分析結果
   */
  private async saveAnalysisResult(
    result: ProjectAnalysisResult
  ): Promise<void> {
    // 這裡可以將結果保存到數據庫
    this.logger.log(`Analysis result saved for project ${result.projectId}`);
  }

  /**
   * 保存照片分析結果
   */
  private async savePhotoAnalysisResult(
    result: PhotoAnalysisResult,
    userId?: string,
    aiModelUsed?: string,
    processingTimeMs?: number
  ): Promise<void> {
    try {
      const analysisId = `ai_photo_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;

      // 獲取項目的 tenant_id
      const project = await this.prisma.projects.findUnique({
        where: { id: result.projectId },
        select: { tenantId: true },
      });

      if (!project) {
        throw new Error(`Project ${result.projectId} not found`);
      }

      await this.prisma.ai_photo_analysis_results.create({
        data: {
          id: analysisId,
          photo_url: result.photoUrl,
          project_id: result.projectId,
          tenant_id: project.tenantId,
          user_id: userId || "system",
          analysis_type: result.analysisType,
          confidence: result.confidence,
          description: result.findings.description,
          progress_percentage: result.findings.progressPercentage,
          quality_score: result.findings.qualityScore,
          safety_issues: result.findings.safetyIssues,
          equipment_detected: result.findings.equipmentDetected,
          recommendations: result.findings.recommendations,
          ai_model_used: aiModelUsed,
          processing_time_ms: processingTimeMs,
        },
      });

      this.logger.log(
        `Photo analysis result saved to database with ID: ${analysisId}`
      );
    } catch (error) {
      this.logger.error(
        `Failed to save photo analysis result: ${error.message}`,
        error.stack
      );
      // 不拋出錯誤，以免影響主要的分析流程
    }
  }

  /**
   * 保存工作流程優化結果
   */
  private async saveWorkflowOptimizationResult(
    result: WorkflowOptimizationResult
  ): Promise<void> {
    // 這裡可以將結果保存到數據庫
    this.logger.log(
      `Workflow optimization result saved for tenant ${result.tenantId}`
    );
  }
}
