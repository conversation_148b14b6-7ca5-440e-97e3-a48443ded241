import Anthropic from "@anthropic-ai/sdk";
import {
  BaseAiProvider,
  AiMessage,
  AiExecuteOptions,
  AiResponse,
} from "../base/base.provider";
import { Logger } from "@nestjs/common";
import { AiErrorMapper } from "../../exceptions/ai-service.exceptions";

export class <PERSON><PERSON><PERSON>ider extends BaseAiProvider {
  private anthropic: Anthropic;
  private readonly providerName = "anthropic";

  constructor(apiKey: string, apiUrl?: string) {
    super(api<PERSON>ey, new Logger(ClaudeProvider.name), apiUrl);
    this.anthropic = new Anthropic({ apiKey });
  }

  private convertToClaudeMessage(
    message: AiMessage
  ): Anthropic.Messages.MessageParam {
    const role = message.role as "user" | "assistant";

    // 處理字串內容
    if (typeof message.content === "string") {
      return {
        role,
        content: message.content,
      };
    }

    // 處理陣列內容（多模態訊息）
    if (Array.isArray(message.content)) {
      const contentBlocks = message.content.map((item) => {
        if (item.type === "text") {
          return {
            type: "text" as const,
            text: item.text || "",
          };
        } else if (item.type === "image_url" && item.image_url) {
          return {
            type: "image" as const,
            source: {
              type: "base64" as const,
              media_type: "image/jpeg" as const, // 預設類型
              data: item.image_url.url.split(",")[1] || item.image_url.url, // 移除 data:image/jpeg;base64, 前綴
            },
          };
        }
        return {
          type: "text" as const,
          text: "",
        };
      });

      return {
        role,
        content: contentBlocks,
      };
    }

    // 後備處理
    return {
      role,
      content: String(message.content),
    };
  }

  private separateSystemPrompt(messages: AiMessage[]): {
    system: string;
    userMessages: Anthropic.Messages.MessageParam[];
  } {
    let system = "";
    const userMessages: Anthropic.Messages.MessageParam[] = [];

    for (const message of messages) {
      if (message.role === "system") {
        system +=
          (typeof message.content === "string"
            ? message.content
            : JSON.stringify(message.content)) + "\n";
      } else {
        userMessages.push(this.convertToClaudeMessage(message));
      }
    }

    return { system: system.trim(), userMessages };
  }

  private extractResponseContent(response: Anthropic.Messages.Message): string {
    if (!response.content || response.content.length === 0) {
      return "";
    }

    // Claude API 返回的是 content blocks 數組
    return response.content
      .map((block) => {
        if (block.type === "text") {
          return block.text;
        }
        return "";
      })
      .join("");
  }

  async _execute(
    messages: AiMessage[],
    options: AiExecuteOptions
  ): Promise<AiResponse> {
    const { model, temperature, maxTokens } = options;

    const { system, userMessages } = this.separateSystemPrompt(messages);

    try {
      const response = await this.anthropic.messages.create({
        model,
        system,
        messages: userMessages,
        temperature,
        max_tokens: maxTokens || 2000, // 提供默認值
      });

      const responseContent = this.extractResponseContent(response);
      if (!responseContent) {
        throw new Error("無效的 Claude API 回應格式");
      }

      return {
        content: responseContent,
        usage: {
          inputTokens: response.usage.input_tokens,
          outputTokens: response.usage.output_tokens,
        },
      };
    } catch (error) {
      throw AiErrorMapper.mapError(error, this.providerName, model);
    }
  }

  async getAvailableModels(): Promise<string[]> {
    try {
      this.logger.log("Fetching available models from Anthropic API");

      // 使用 Anthropic SDK 的 client 獲取模型列表
      const response = await this.anthropic.models.list();

      // 從回應中提取模型 ID
      const modelIds = response.data.map((model) => model.id);

      this.logger.log(
        `Successfully fetched ${modelIds.length} models from Anthropic API`
      );
      return modelIds;
    } catch (error) {
      this.logger.error(
        `Failed to fetch Anthropic models: ${error.message}`,
        error.stack
      );

      // 返回空陣列，讓呼叫端知道獲取模型失敗
      throw new Error(`無法獲取 Claude 模型列表：${error.message}`);
    }
  }
}
