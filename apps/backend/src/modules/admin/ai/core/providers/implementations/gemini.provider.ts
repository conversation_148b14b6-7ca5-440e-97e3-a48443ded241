import { GoogleGenerativeAI } from "@google/generative-ai";
import {
  BaseAiProvider,
  AiMessage,
  AiExecuteOptions,
  AiResponse,
  VisionAnalysisOptions,
} from "../base/base.provider";
import { Logger } from "@nestjs/common";
import { AiErrorMapper } from "../../exceptions/ai-service.exceptions";

export class GeminiProvider extends BaseAiProvider {
  private client: GoogleGenerativeAI;
  private readonly providerName = "google-gemini";

  constructor(apiKey: string, apiUrl?: string) {
    super(apiKey, new Logger(GeminiProvider.name), apiUrl);
    this.client = new GoogleGenerativeAI(apiKey);
  }

  private extractTextContent(content: AiMessage["content"]): string {
    if (typeof content === "string") {
      return content;
    }

    if (Array.isArray(content)) {
      return content
        .filter((item) => item.type === "text" && item.text)
        .map((item) => item.text)
        .join(" ");
    }

    return String(content);
  }

  /**
   * 將 OpenAI 格式的角色轉換為 Gemini 格式
   */
  private convertToGeminiRole(role: string): "user" | "model" {
    switch (role) {
      case "user":
        return "user";
      case "assistant":
      case "system":
        return "model";
      default:
        return "user";
    }
  }

  /**
   * 將圖片 URL 轉換為 Gemini 支援的格式
   */
  private async convertImageToGeminiFormat(imageUrl: string) {
    try {
      // 如果是 data URL，直接處理
      if (imageUrl.startsWith("data:")) {
        const [mimeType, base64Data] = imageUrl.split(",");
        const mimeTypePart = mimeType.split(":")[1].split(";")[0];

        return {
          inlineData: {
            data: base64Data,
            mimeType: mimeTypePart,
          },
        };
      }

      // 如果是 HTTP URL，需要下載並轉換
      const response = await fetch(imageUrl);
      if (!response.ok) {
        throw new Error(`Failed to fetch image: ${response.statusText}`);
      }

      const arrayBuffer = await response.arrayBuffer();
      const base64Data = Buffer.from(arrayBuffer).toString("base64");
      const contentType = response.headers.get("content-type") || "image/jpeg";

      return {
        inlineData: {
          data: base64Data,
          mimeType: contentType,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to process image: ${error.message}`);
      throw new Error(`Failed to process image: ${error.message}`);
    }
  }

  async _execute(
    messages: AiMessage[],
    options: AiExecuteOptions
  ): Promise<AiResponse> {
    const { model, temperature, maxTokens } = options;

    try {
      const geminiModel = this.client.getGenerativeModel({ model });

      // 處理 system prompt
      let systemPrompt = "";
      const conversationMessages = messages.filter((msg) => {
        if (msg.role === "system") {
          systemPrompt += this.extractTextContent(msg.content) + "\n";
          return false;
        }
        return true;
      });

      // 構建 Gemini 格式的對話歷史
      const geminiContents = await Promise.all(
        conversationMessages.map(async (msg) => {
          const parts: any[] = [];

          if (typeof msg.content === "string") {
            parts.push({ text: msg.content });
          } else if (Array.isArray(msg.content)) {
            for (const item of msg.content) {
              if (item.type === "text" && item.text) {
                parts.push({ text: item.text });
              } else if (item.type === "image_url" && item.image_url?.url) {
                const imageData = await this.convertImageToGeminiFormat(
                  item.image_url.url
                );
                parts.push(imageData);
              }
            }
          }

          return {
            role: this.convertToGeminiRole(msg.role),
            parts,
          };
        })
      );

      // 如果有 system prompt，將其添加到第一個用戶消息前
      if (systemPrompt && geminiContents.length > 0) {
        const firstUserIndex = geminiContents.findIndex(
          (content) => content.role === "user"
        );
        if (
          firstUserIndex !== -1 &&
          geminiContents[firstUserIndex].parts.length > 0
        ) {
          // 找到第一個文字部分
          const firstTextPart = geminiContents[firstUserIndex].parts.find(
            (part) => part.text
          );
          if (firstTextPart) {
            firstTextPart.text =
              systemPrompt.trim() + "\n\n" + firstTextPart.text;
          } else {
            // 如果沒有文字部分，在開頭添加
            geminiContents[firstUserIndex].parts.unshift({
              text: systemPrompt.trim(),
            });
          }
        }
      }

      // 確保對話以用戶消息結尾（Gemini 要求）
      if (
        geminiContents.length === 0 ||
        geminiContents[geminiContents.length - 1].role !== "user"
      ) {
        throw new Error("Conversation must end with a user message for Gemini");
      }

      const result = await geminiModel.generateContent({
        contents: geminiContents,
        generationConfig: {
          temperature,
          maxOutputTokens: maxTokens,
        },
      });

      const response = result.response;
      const responseContent = response.text();

      if (!responseContent) {
        throw new Error("無效的 Gemini API 回應格式");
      }

      // Gemini API 不直接在單次對話返回 token 數，需要單獨計算
      const modelInfo = await geminiModel.countTokens({
        contents: geminiContents,
      });
      const responseInfo = await geminiModel.countTokens(responseContent);

      return {
        content: responseContent,
        usage: {
          inputTokens: modelInfo.totalTokens,
          outputTokens: responseInfo.totalTokens,
        },
      };
    } catch (error) {
      throw AiErrorMapper.mapError(error, this.providerName, model);
    }
  }

  async executeVisionAnalysis(
    options: VisionAnalysisOptions
  ): Promise<AiResponse> {
    try {
      const model = this.client.getGenerativeModel({ model: options.model });

      // 處理圖片
      const imageData = await this.convertImageToGeminiFormat(options.imageUrl);

      const result = await model.generateContent({
        contents: [
          {
            role: "user",
            parts: [{ text: options.prompt }, imageData],
          },
        ],
        generationConfig: {
          temperature: options.temperature || 0.7,
          maxOutputTokens: options.maxTokens || 1024,
        },
      });

      const response = await result.response;
      const responseText = response.text();

      return {
        content: responseText,
        usage: {
          // Vision 分析的 token 計算，圖片通常消耗更多 tokens
          inputTokens: Math.ceil(options.prompt.length / 3) + 1000, // 圖片估算為 1000 tokens
          outputTokens: Math.ceil(responseText.length / 3),
        },
      };
    } catch (error) {
      this.logger.error(
        `Gemini Vision API error: ${error.message}`,
        error.stack
      );
      throw new Error(`Gemini Vision API error: ${error.message}`);
    }
  }

  async getAvailableModels(): Promise<string[]> {
    try {
      this.logger.log("Fetching available models from Google Gemini API");

      const baseUrl =
        this.apiUrl || "https://generativelanguage.googleapis.com";
      const url = `${baseUrl}/v1beta/models?key=${this.apiKey}`;

      const response = await fetch(url, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error(
          `Failed to fetch models: ${response.status} ${response.statusText}`
        );
      }

      const data = await response.json();

      if (data && data.models && Array.isArray(data.models)) {
        const modelIds = data.models
          .filter((model) => {
            // 只返回支援 generateContent 的 Gemini 模型
            return (
              model.name &&
              model.name.includes("gemini") &&
              model.supportedGenerationMethods &&
              model.supportedGenerationMethods.includes("generateContent")
            );
          })
          .map((model) => {
            // 移除模型名稱中的 'models/' 前綴
            const fullName = model.name;
            return fullName.startsWith("models/")
              ? fullName.substring(7)
              : fullName;
          });

        this.logger.log(
          `Successfully fetched ${modelIds.length} models from Gemini API`
        );
        return modelIds;
      }

      this.logger.warn("No models found in Gemini API response");
      return [];
    } catch (error) {
      this.logger.error(
        `Failed to fetch Gemini models: ${error.message}`,
        error.stack
      );
      throw new Error(`Failed to fetch Gemini models: ${error.message}`);
    }
  }
}
