import { OpenAI } from "openai";
import {
  BaseAiProvider,
  AiMessage,
  AiExecuteOptions,
  AiResponse,
} from "../base/base.provider";
import { AiErrorMapper } from "../../exceptions/ai-service.exceptions";
import { Logger } from "@nestjs/common";

export class OpenAiCompatibleProvider extends BaseAiProvider {
  private openai: OpenAI;
  private readonly providerName = "openai-compatible";

  constructor(apiKey: string, apiUrl: string) {
    if (!apiUrl) {
      throw new Error(
        "OpenAI 相容提供者需要一個 API URL。請在金鑰設定中提供。"
      );
    }
    super(apiKey, new Logger(OpenAiCompatibleProvider.name), apiUrl);
    this.openai = new OpenAI({ apiKey, baseURL: apiUrl });
  }

  private convertToOpenAIMessage(
    message: AiMessage
  ): OpenAI.Chat.Completions.ChatCompletionMessageParam {
    const role = message.role as "system" | "user" | "assistant";

    // 處理字串內容
    if (typeof message.content === "string") {
      return {
        role,
        content: message.content,
      };
    }

    // 處理陣列內容（多模態訊息）
    if (Array.isArray(message.content)) {
      // 系統訊息和助手訊息不支援陣列內容，轉換為字串
      if (message.role === "system" || message.role === "assistant") {
        const textContent = message.content
          .filter((item) => item.type === "text" && item.text)
          .map((item) => item.text)
          .join(" ");
        return {
          role: message.role as "system" | "assistant",
          content: textContent || "",
        };
      }

      // 只有用戶訊息支援陣列內容（多模態）
      const contentParts: OpenAI.Chat.Completions.ChatCompletionContentPart[] =
        message.content.map((item) => {
          if (item.type === "text") {
            return {
              type: "text",
              text: item.text || "",
            };
          } else if (item.type === "image_url" && item.image_url) {
            return {
              type: "image_url",
              image_url: {
                url: item.image_url.url,
                detail: item.image_url.detail || "auto",
              },
            };
          }
          return {
            type: "text",
            text: "",
          };
        });

      return {
        role: "user",
        content: contentParts,
      };
    }

    // 後備處理
    return {
      role,
      content: String(message.content),
    };
  }

  async _execute(
    messages: AiMessage[],
    options: AiExecuteOptions
  ): Promise<AiResponse> {
    const { model, temperature, maxTokens, responseFormat } = options;
    try {
      const response = await this.openai.chat.completions.create({
        model,
        messages: messages.map((msg) => this.convertToOpenAIMessage(msg)),
        temperature,
        max_tokens: maxTokens,
        response_format:
          responseFormat === "JSON_OBJECT"
            ? { type: "json_object" }
            : undefined,
      });

      const responseContent = response.choices[0]?.message?.content;
      if (!responseContent) {
        throw new Error("無效的 OpenAI 相容 API 回應格式");
      }

      return {
        content: responseContent,
        usage: {
          inputTokens: response.usage?.prompt_tokens ?? 0,
          outputTokens: response.usage?.completion_tokens ?? 0,
        },
      };
    } catch (error) {
      throw AiErrorMapper.mapError(error, this.providerName, model);
    }
  }

  async getAvailableModels(): Promise<string[]> {
    try {
      this.logger.log(
        `Fetching available models from OpenAI Compatible API at ${this.apiUrl}`
      );

      const models = await this.openai.models.list();
      const modelIds = models.data.map((model) => model.id);

      this.logger.log(
        `Successfully fetched ${modelIds.length} models from OpenAI Compatible API`
      );
      return modelIds;
    } catch (error) {
      this.logger.error(
        `Failed to fetch OpenAI Compatible models: ${error.message}`,
        error.stack
      );
      throw new Error(
        `Failed to fetch OpenAI Compatible models: ${error.message}`
      );
    }
  }
}
