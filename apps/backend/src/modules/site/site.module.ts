import { Modu<PERSON> } from '@nestjs/common';
import { SettingsModule } from '../admin/settings/settings.module';
import { PrismaModule } from '../core/prisma/prisma.module';
import { HealthModule } from './health/health.module';
import { SiteController } from './site.controller';
import { SiteService } from './site.service';

@Module({
  imports: [PrismaModule, SettingsModule, HealthModule],
  controllers: [SiteController],
  providers: [SiteService],
})
export class SiteModule {}
