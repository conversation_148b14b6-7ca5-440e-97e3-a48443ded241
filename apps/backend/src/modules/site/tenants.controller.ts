import {
  Controller,
  Get,
  Post,
  Query,
  Param,
  UseGuards,
  Body,
} from "@nestjs/common";
import {
  ApiTags,
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
} from "@nestjs/swagger";
import { JwtAuthGuard } from "../core/auth/guards/auth.guard";
import { CurrentUser } from "../core/auth/decorators/current-user.decorator";
import { Public } from "../../common/decorators/public.decorator";
import { TenantsService } from "../admin/tenants/tenants.service";
import { TenantInvitationsService } from "../core/tenant-invitations/tenant-invitations.service";

@ApiTags("tenants")
@Controller("tenants")
export class TenantsController {
  constructor(
    private readonly tenantsService: TenantsService,
    private readonly tenantInvitationsService: TenantInvitationsService
  ) {}

  /**
   * 搜尋租戶 - 供使用者搜尋現有公司
   */
  @Get("search")
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: "搜尋租戶" })
  @ApiResponse({ status: 200, description: "搜尋成功" })
  async searchTenants(
    @Query("query") query: string,
    @Query("limit") limit?: number
  ) {
    return this.tenantsService.searchTenants(query, limit || 10);
  }

  /**
   * 申請加入租戶
   */
  @Post(":tenantId/join-requests")
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: "申請加入租戶" })
  @ApiResponse({ status: 201, description: "申請提交成功" })
  @ApiResponse({ status: 400, description: "申請失敗" })
  async requestJoinTenant(
    @Param("tenantId") tenantId: string,
    @CurrentUser() user: any
  ) {
    return this.tenantInvitationsService.createJoinRequest(tenantId, user.id);
  }
}
