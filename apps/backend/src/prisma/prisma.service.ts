import { Injectable, OnModuleInit, OnModuleD<PERSON>roy, Lo<PERSON> } from '@nestjs/common';
import { PrismaClient } from "@prisma/client";

// 擴充 PrismaClient 型別定義
interface ExtendedPrismaClient extends PrismaClient {
  // 確保所有 schema 中定義的模型都有對應的型別
  subscription: any;
  tenantInvitation: any;
}

@Injectable()
export class PrismaService extends PrismaClient implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(PrismaService.name);

  constructor() {
    super({
      log: ['query', 'info', 'warn', 'error'],
    });
  }

  async onModuleInit() {
    try {
      await this.$connect();
      this.logger.log('Successfully connected to the database');
    } catch (error) {
      this.logger.error(`Failed to connect to the database: ${error.message}`, error.stack);
      throw error;
    }
  }

  async onModuleDestroy() {
    await this.$disconnect();
    this.logger.log('Disconnected from the database');
  }
}

// 擴充 PrismaService 型別，確保所有服務都能正確識別新增的模型
export interface ExtendedPrismaService extends PrismaService {
  subscription: any;
  tenantInvitation: any;
}