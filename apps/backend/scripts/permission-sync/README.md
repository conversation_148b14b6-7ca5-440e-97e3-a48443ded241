# Permission Sync CLI

This folder contains the CLI tool for keeping the `Permission` table in sync with
code definitions. Use `pnpm` scripts to execute.

## Commands

```bash
pnpm db:scan-perms           # scan permissions only
pnpm db:sync-perms --dry-run # preview changes
pnpm db:sync-perms           # sync database
pnpm db:sync-perms --force   # sync and overwrite descriptions
```

Run `pnpm db:sync-perms --dry-run` in CI pipelines. The command generates
`apps/backend/reports/permission-sync-summary.json`. If `created`, `updated`, or
`deprecated` counts are non‑zero, the CI job should fail.
