import { PermissionScanner } from "./scanner";
import { getScanConfig } from "./config";

describe("PermissionScanner", () => {
  it("掃描空路徑時應回傳空結果", async () => {
    // 使用自訂 config，清空後端及前端路徑，並關閉快取
    const scanner = new PermissionScanner({
      backendPaths: [],
      cacheEnabled: false,
      frontendPaths: [],
      cachePath: "",
      ttl: 0,
      inference: { scopes: {}, categories: {} }
    });
    const result = await scanner.scan(false);
    expect(result.permissions).toHaveLength(0);
    expect(result.stats.scannedFiles).toBe(0);
    expect(result.errors).toHaveLength(0);
  });
});
