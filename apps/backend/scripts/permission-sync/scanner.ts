import {
  Project,
  SourceFile,
  SyntaxKind,
  CallExpression,
  Node,
} from "ts-morph";
import * as path from "path";
import * as fs from "fs";
import * as crypto from "crypto";
import { 
  PERMISSION_DEFINITIONS, 
  getPermissionName, 
  getPermissionDescription,
  isPermissionDefined 
} from "../../../../packages/permissions/dist";
import { PermissionDefinition, ScanResult, ScanConfig } from "./types";
import { getScanConfig, DEFAULT_CONFIG } from "./config";
import { Actions, Subjects } from '@horizai/permissions';

/**
 * 權限掃描器
 * 負責掃描程式碼中的權限定義
 */
export class PermissionScanner {
  private permissions = new Map<string, PermissionDefinition>();
  private project: Project;
  private config: ScanConfig;
  /** 用於控制警告輸出的方法 */
  private logWarn: (...args: any[]) => void;

  // 建立權限常數映射表，用於解析實際值
  private actionConstantMap: Record<string, string> = {};
  private subjectConstantMap: Record<string, string> = {};

  constructor(config?: Partial<ScanConfig>) {
    this.config = getScanConfig(config);
    // 初始化警告輸出控制
    this.logWarn = (...args: any[]) => {
      if (!(this.config as any).suppressWarnings) {
        console.warn(...args);
      }
    };

    this.project = new Project({
      tsConfigFilePath: path.join(__dirname, "../../tsconfig.json"),
    });

    // 初始化常數映射表
    this.initializeConstantMaps();
  }

  /**
   * 初始化權限常數映射表
   */
  private initializeConstantMaps(): void {
    // 建立 Actions 映射
    for (const [key, value] of Object.entries(Actions)) {
      this.actionConstantMap[key] = value;
    }
    
    // 建立 Subjects 映射
    for (const [key, value] of Object.entries(Subjects)) {
      this.subjectConstantMap[key] = value;
    }
    
    console.log(`    Initialized constant maps: ${Object.keys(this.actionConstantMap).length} actions, ${Object.keys(this.subjectConstantMap).length} subjects`);
  }

  /**
   * 執行完整掃描
   */
  async scan(useCache = true): Promise<ScanResult> {
    // 嘗試使用快取
    if (useCache && this.config.cacheEnabled) {
      const cacheFile = this.getCacheFilePath();
      if (this.isCacheValid()) {
        console.log("♻️ 從快取載入掃描結果...");
        return this.loadFromCache();
      }
    }
    console.log("🔍 開始掃描權限定義...");

    // 清除舊有結果
    this.permissions.clear();
    const errors: string[] = [];
    let stats = {
      totalFiles: 0,
      scannedFiles: 0,
      filesWithPermissions: 0,
      totalPermissions: 0,
    };

    try {
      // scan backend only if paths provided
      if (this.config.backendPaths && this.config.backendPaths.length > 0) {
        const backendStats = await this.scanBackend();
        stats.totalFiles += backendStats.totalFiles;
        stats.scannedFiles += backendStats.scannedFiles;
        stats.filesWithPermissions += backendStats.filesWithPermissions;
        stats.totalPermissions += backendStats.totalPermissions;
      }

      // scan frontend only if paths provided
      if (this.config.frontendPaths && this.config.frontendPaths.length > 0) {
        const frontendStats = await this.scanFrontend();
        stats.totalFiles += frontendStats.totalFiles;
        stats.scannedFiles += frontendStats.scannedFiles;
        stats.filesWithPermissions += frontendStats.filesWithPermissions;
      }

      // scan seed only if paths provided
      if (this.config.seedPaths && this.config.seedPaths.length > 0) {
        const seedStats = await this.scanSeed();
        stats.totalFiles += seedStats.totalFiles;
        stats.scannedFiles += seedStats.scannedFiles;
        stats.filesWithPermissions += seedStats.filesWithPermissions;
      }
    } catch (error) {
      const errorMsg = `掃描過程發生錯誤: ${error.message}`;
      console.error(`❌ ${errorMsg}`);
      errors.push(errorMsg);
    }

    const result: ScanResult = {
      permissions: Array.from(this.permissions.values()),
      stats,
      errors,
    };

    console.log(`✅ 掃描完成，發現 ${result.permissions.length} 個權限定義`);
    // 警告硬編碼使用：若非從常數引用掃描，建議使用 @horizai/permissions 常數
    result.permissions.forEach((perm) => {
      if (
        perm.filePath &&
        perm.lineNumber &&
        !perm.description?.includes("權限常數引用") &&
        !perm.description?.includes("常數引用")
      ) {
        console.warn(
          `⚠️ 硬編碼權限字串: ${perm.action}:${perm.subject} at ${perm.filePath}:${perm.lineNumber}，建議使用 @horizai/permissions 常數`
        );
      }
    });
    // 儲存快取結果
    if (this.config.cacheEnabled) {
      this.saveToCache(result);
    }
    return result;
  }

  /**
   * 掃描後端程式碼
   */
  private async scanBackend() {
    console.log("  📂 掃描後端程式碼...");

    const sourceFiles = this.project.getSourceFiles(this.config.backendPaths);
    console.log(`    找到 ${sourceFiles.length} 個檔案`);

    let scannedFiles = 0;
    let filesWithPermissions = 0;
    let totalPermissions = 0;

    for (const sourceFile of sourceFiles) {
      scannedFiles++;
      const filePermissions = this.scanSourceFile(sourceFile);

      if (filePermissions > 0) {
        filesWithPermissions++;
        totalPermissions += filePermissions;
      }
    }

    console.log(
      `    後端統計: 掃描 ${scannedFiles} 個檔案，${filesWithPermissions} 個檔案包含權限，總計 ${totalPermissions} 個權限`
    );

    return {
      totalFiles: sourceFiles.length,
      scannedFiles,
      filesWithPermissions,
      totalPermissions,
    };
  }

  /**
   * 掃描前端程式碼
   */
  private async scanFrontend() {
    console.log("  📂 掃描前端程式碼...");

    let scannedFiles = 0;
    let filesWithPermissions = 0;
    let totalFiles = 0;

    try {
      // 直接掃描前端目錄
      const frontendDir = path.resolve(__dirname, "../../../frontend/src");
      if (fs.existsSync(frontendDir)) {
        const files = await this.scanDirectoryRecursive(frontendDir, [
          ".vue",
          ".ts",
          ".js",
        ]);
        totalFiles = files.length;

        for (const file of files) {
          scannedFiles++;
          const content = fs.readFileSync(file, "utf-8");
          const filePermissions = this.scanFrontendFile(content, file);

          if (filePermissions > 0) {
            filesWithPermissions++;
            console.log(
              `    🎯 發現權限使用: ${path.relative(frontendDir, file)} (${filePermissions} 個權限)`
            );
          }
        }
      } else {
        this.logWarn(`    ⚠️  前端目錄不存在: ${frontendDir}`);
      }
    } catch (error) {
      this.logWarn(`    ⚠️  前端掃描警告: ${error.message}`);
    }

    console.log(
      `    前端統計: 掃描 ${scannedFiles} 個檔案，${filesWithPermissions} 個檔案包含權限使用`
    );

    return {
      totalFiles,
      scannedFiles,
      filesWithPermissions,
      totalPermissions: 0,
    };
  }

  /**
   * 掃描 Seed 檔案
   */
  private async scanSeed() {
    console.log("  📂 掃描 Seed 檔案...");

    let scannedFiles = 0;
    let filesWithPermissions = 0;

    for (const seedPath of this.config.seedPaths) {
      const fullPath = path.resolve(__dirname, "../..", seedPath);

      if (fs.existsSync(fullPath)) {
        scannedFiles++;
        const content = fs.readFileSync(fullPath, "utf-8");
        const filePermissions = this.scanSeedFile(content, fullPath);

        if (filePermissions > 0) {
          filesWithPermissions++;
        }
      }
    }

    console.log(
      `    Seed 統計: 掃描 ${scannedFiles} 個檔案，${filesWithPermissions} 個檔案包含權限定義`
    );

    return {
      totalFiles: this.config.seedPaths.length,
      scannedFiles,
      filesWithPermissions,
      totalPermissions: 0,
    };
  }

  /**
   * 掃描單一原始檔案
   */
  private scanSourceFile(sourceFile: SourceFile): number {
    let count = 0;
    const filePath = sourceFile.getFilePath();
    console.log(`    📄 掃描檔案: ${path.relative(process.cwd(), filePath)}`);

    try {
      // 掃描 @CheckPolicies 裝飾器
      count += this.scanCheckPoliciesDecorator(sourceFile, filePath);

      // 掃描 can() 方法呼叫
      count += this.scanCanMethodCalls(sourceFile, filePath);

      // 掃描權限常數定義
      count += this.scanPermissionConstants(sourceFile, filePath);
    } catch (error) {
      this.logWarn(`    ⚠️  掃描檔案 ${filePath} 時發生錯誤: ${error.message}`);
    }

    return count;
  }

  /**
   * 掃描 @CheckPolicies 裝飾器
   */
  private scanCheckPoliciesDecorator(
    sourceFile: SourceFile,
    filePath: string
  ): number {
    const decorators = sourceFile.getDescendantsOfKind(SyntaxKind.Decorator);
    let count = 0;

    for (const decorator of decorators) {
      try {
        const decoratorName = decorator.getName();
        if (decoratorName === "CheckPolicies") {
          const callExpression = decorator.getCallExpression();
          if (callExpression) {
            const args = callExpression.getArguments();

            if (args.length > 0) {
              const firstArg = args[0];
              
              // 檢查是否為 Arrow Function
              if (Node.isArrowFunction(firstArg)) {
                // 掃描 Arrow Function 內的 ability.can() 調用
                const arrowFunctionBody = firstArg.getBody();
                
                if (Node.isCallExpression(arrowFunctionBody)) {
                  // 直接是一個 CallExpression (如: ability.can(...))
                  const permission = this.extractPermissionFromCanCall(arrowFunctionBody, sourceFile);
                  if (permission) {
                    this.addPermission({
                      action: permission.action,
                      subject: permission.subject,
                      description: `從 @CheckPolicies Arrow Function 掃描`,
                      isSystemDefined: true,
                      filePath,
                      lineNumber: decorator.getStartLineNumber(),
                    });
                    count++;
                    console.log(
                      `      Found @CheckPolicies Arrow Function: ${permission.action}:${permission.subject} at ${filePath}:${decorator.getStartLineNumber()}`
                    );
                  }
                } else if (Node.isBlock(arrowFunctionBody)) {
                  // Arrow Function 有 block body，掃描其中的 return statement 或直接的 can() 調用
                  const blockStatements = arrowFunctionBody.getStatements();
                  for (const statement of blockStatements) {
                    if (Node.isReturnStatement(statement)) {
                      const returnExpression = statement.getExpression();
                      if (returnExpression && Node.isCallExpression(returnExpression)) {
                        const permission = this.extractPermissionFromCanCall(returnExpression, sourceFile);
                        if (permission) {
                          this.addPermission({
                            action: permission.action,
                            subject: permission.subject,
                            description: `從 @CheckPolicies Arrow Function Return 掃描`,
                            isSystemDefined: true,
                            filePath,
                            lineNumber: decorator.getStartLineNumber(),
                          });
                          count++;
                          console.log(
                            `      Found @CheckPolicies Arrow Function Return: ${permission.action}:${permission.subject} at ${filePath}:${decorator.getStartLineNumber()}`
                          );
                        }
                      }
                    }
                  }
                }
              } else {
                // 舊版格式：直接字串參數
                let actionNode = firstArg;
                let subjectNode = args.length > 1 ? args[1] : undefined;

                if (Node.isArrowFunction(actionNode)) {
                  const body = actionNode.getBody();
                  if (Node.isNewExpression(body)) {
                    const newArgs = body.getArguments();
                    if (newArgs.length >= 2) {
                      actionNode = newArgs[0];
                      subjectNode = newArgs[1];
                    } else if (newArgs.length === 1) {
                      actionNode = newArgs[0];
                      subjectNode = undefined;
                    }
                  }
                }

                const actionValue = this.extractStringValue(
                  actionNode,
                  sourceFile
                );
                let subjectValue: string;
                if (subjectNode) {
                  const extractedSub = this.extractStringValue(
                    subjectNode,
                    sourceFile
                  );
                  if (extractedSub) {
                    subjectValue = extractedSub;
                  } else {
                    this.logWarn(
                      `    ⚠️  無法從 @CheckPolicies 的 subjectNode 提取字串值: ${subjectNode.getText()} in ${filePath}:${subjectNode.getStartLineNumber()}. 使用 'unknown' 作為 subject。`
                    );
                    subjectValue = "unknown";
                  }
                } else {
                  subjectValue = "all"; // Default if no subjectNode was derived
                }

                if (actionValue) {
                  this.addPermission({
                    action: actionValue,
                    subject: subjectValue, // Now guaranteed to be a string
                    description: `從 @CheckPolicies 裝飾器掃描`,
                    isSystemDefined: true,
                    filePath,
                    lineNumber: decorator.getStartLineNumber(),
                  });
                  count++;
                  console.log(
                    `      Found @CheckPolicies: ${actionValue}, ${subjectValue} at ${filePath}:${decorator.getStartLineNumber()}`
                  );
                } else {
                  this.logWarn(
                    `    ⚠️  無法從 @CheckPolicies 提取 action: ${decorator.getText()} in ${filePath}:${decorator.getStartLineNumber()}`
                  );
                }
              }
            }
          }
        }
      } catch (e) {
        this.logWarn(
          `    ⚠️  處理 @CheckPolicies 裝飾器時發生錯誤: ${decorator.getText()} in ${filePath}:${decorator.getStartLineNumber()}`,
          e
        );
      }
    }

    return count;
  }

  /**
   * 從 ability.can() 調用中提取權限信息
   */
  private extractPermissionFromCanCall(
    callExpression: any, 
    sourceFile: SourceFile
  ): { action: string; subject: string } | null {
    try {
      const expression = callExpression.getExpression();
      
      if (Node.isPropertyAccessExpression(expression)) {
        const methodName = expression.getName();
        
        if (methodName === "can") {
          const args = callExpression.getArguments();
          if (args.length >= 2) {
            const actionArg = args[0];
            const subjectArg = args[1];

            // 檢查是否為 PropertyAccessExpression (Actions.READ, Subjects.DASHBOARD)
            if (
              Node.isPropertyAccessExpression(actionArg) &&
              Node.isPropertyAccessExpression(subjectArg)
            ) {
              const actionObject = actionArg.getExpression();
              const subjectObject = subjectArg.getExpression();

              if (
                Node.isIdentifier(actionObject) &&
                Node.isIdentifier(subjectObject)
              ) {
                const actionObjectName = actionObject.getText();
                const subjectObjectName = subjectObject.getText();

                if (actionObjectName === "Actions" && subjectObjectName === "Subjects") {
                  const actionName = actionArg.getName();
                  const subjectName = subjectArg.getName();

                  // 解析常數的實際值
                  const actualActionValue = this.actionConstantMap[actionName] || actionName.toLowerCase();
                  const actualSubjectValue = this.subjectConstantMap[subjectName] || subjectName;

                  return {
                    action: actualActionValue,
                    subject: actualSubjectValue
                  };
                }
              }
            }
          }
        }
      }
    } catch (e) {
      this.logWarn(
        `    ⚠️  提取 can() 調用權限時發生錯誤: ${callExpression.getText()} in ${sourceFile.getFilePath()}`,
        e
      );
    }
    
    return null;
  }

  /**
   * 掃描 can() 方法呼叫
   */
  private scanCanMethodCalls(sourceFile: SourceFile, filePath: string): number {
    const callExpressions = sourceFile.getDescendantsOfKind(
      SyntaxKind.CallExpression
    );
    let count = 0;

    // 檢查是否有從 @horizai/permissions 引入的常數
    const importDecls = sourceFile.getImportDeclarations();
    const permModules = importDecls.filter((imp) =>
      imp.getModuleSpecifierValue().includes("permissions")
    );
    const importedNames = new Set<string>();
    for (const imp of permModules) {
      imp.getNamedImports().forEach((named) => {
        importedNames.add(named.getName());
      });
    }

    for (const call of callExpressions) {
      try {
        const expression = call.getExpression();

        if (Node.isPropertyAccessExpression(expression)) {
          const propAccess = expression;
          const methodName = propAccess.getName();

          if (methodName === "can") {
            const args = call.getArguments();
            if (args.length >= 2) {
              // 檢查是否為常數引用 (Actions.XXX, Subjects.XXX 形式)
              const actionArg = args[0];
              const subjectArg = args[1];

              // 如果是 PropertyAccessExpression 且來自權限常數，則跳過 (由 scanPermissionConstants 處理)
              if (
                Node.isPropertyAccessExpression(actionArg) &&
                Node.isPropertyAccessExpression(subjectArg)
              ) {
                const actionObject = actionArg.getExpression();
                const subjectObject = subjectArg.getExpression();

                if (
                  Node.isIdentifier(actionObject) &&
                  Node.isIdentifier(subjectObject)
                ) {
                  const actionObjectName = actionObject.getText();
                  const subjectObjectName = subjectObject.getText();

                  // 如果是 Actions/Subjects 常數引用，跳過處理
                  if (
                    (actionObjectName === "Actions" ||
                      importedNames.has(actionObjectName)) &&
                    (subjectObjectName === "Subjects" ||
                      importedNames.has(subjectObjectName))
                  ) {
                    continue; // 跳過，由 scanPermissionConstants 處理
                  }
                }
              } else {
                // 只處理字串字面量的情況
                const actionValue = this.extractStringValue(
                  args[0],
                  sourceFile
                );
                const subjectValue = this.extractStringValue(
                  args[1],
                  sourceFile
                );

                if (actionValue && subjectValue) {
                  this.addPermission({
                    action: actionValue,
                    subject: subjectValue,
                    description: `從 can() 方法呼叫掃描 (字串字面量)`,
                    isSystemDefined: true,
                    filePath,
                    lineNumber: call.getStartLineNumber(),
                  });
                  count++;
                  console.log(
                    `      Found can(): ${actionValue}, ${subjectValue} at ${filePath}:${call.getStartLineNumber()}`
                  );
                }
              }
            }
          }
        }
      } catch (e) {
        this.logWarn(
          `    ⚠️  處理 can() 方法呼叫時發生錯誤: ${call.getText()} in ${filePath}:${call.getStartLineNumber()}`,
          e
        );
      }
    }

    return count;
  }

  /**
   * 掃描權限常數定義
   */
  private scanPermissionConstants(
    sourceFile: SourceFile,
    filePath: string
  ): number {
    let count = 0;
    // 偵測從 @horizai/permissions 引入的常數
    const importDecls = sourceFile.getImportDeclarations();
    const permModules = importDecls.filter((imp) =>
      imp.getModuleSpecifierValue().includes("permissions")
    );
    const importedNames = new Set<string>();
    for (const imp of permModules) {
      imp.getNamedImports().forEach((named) => {
        importedNames.add(named.getName());
      });
    }
    if (importedNames.size === 0) {
      return 0;
    }

    // 搜尋 ElementAccessExpression 形式的常數引用，例如 SYSTEM_PERMISSIONS['manage:user']
    const elementAccesses = sourceFile.getDescendantsOfKind(
      SyntaxKind.ElementAccessExpression
    );
    for (const access of elementAccesses) {
      const expr = access.getExpression();
      if (Node.isIdentifier(expr) && importedNames.has(expr.getText())) {
        const arg = access.getArgumentExpression();
        const permId = this.extractStringValue(arg, sourceFile);
        if (permId && permId.includes(":")) {
          const [action, subject] = permId.split(":");
          this.addPermission({
            action,
            subject,
            description: `從 ${expr.getText()} 常數引用掃描`,
            isSystemDefined: true,
            filePath,
            lineNumber: access.getStartLineNumber(),
          });
          count++;
          console.log(
            `      Found constant: ${expr.getText()}['${permId}'] at ${filePath}:${access.getStartLineNumber()}`
          );
        }
      }
    }

    // 搜尋 PropertyAccessExpression 形式的常數引用，例如 Actions.READ, Subjects.DASHBOARD
    // 主要用於 ability.can(Actions.READ, Subjects.DASHBOARD) 這種情況
    const callExpressions = sourceFile.getDescendantsOfKind(
      SyntaxKind.CallExpression
    );
    for (const call of callExpressions) {
      try {
        // 檢查是否為 can() 方法調用
        const expression = call.getExpression();
        if (Node.isPropertyAccessExpression(expression)) {
          const methodName = expression.getName();
          if (methodName === "can") {
            const args = call.getArguments();
            if (args.length >= 2) {
              // 檢查第一個參數 (action) 是否為 PropertyAccessExpression
              const actionArg = args[0];
              const subjectArg = args[1];

              if (
                Node.isPropertyAccessExpression(actionArg) &&
                Node.isPropertyAccessExpression(subjectArg)
              ) {
                const actionObject = actionArg.getExpression();
                const subjectObject = subjectArg.getExpression();

                // 檢查是否為 Actions.XXX 和 Subjects.XXX 的形式
                if (
                  Node.isIdentifier(actionObject) &&
                  Node.isIdentifier(subjectObject)
                ) {
                  const actionObjectName = actionObject.getText();
                  const subjectObjectName = subjectObject.getText();

                  if (
                    (actionObjectName === "Actions" ||
                      importedNames.has(actionObjectName)) &&
                    (subjectObjectName === "Subjects" ||
                      importedNames.has(subjectObjectName))
                  ) {
                    const actionName = actionArg.getName();
                    const subjectName = subjectArg.getName();

                    // 解析常數的實際值
                    const actualActionValue = this.actionConstantMap[actionName] || actionName.toLowerCase();
                    const actualSubjectValue = this.subjectConstantMap[subjectName] || subjectName;

                    this.addPermission({
                      action: actualActionValue,
                      subject: actualSubjectValue,
                      description: `從權限常數引用掃描`,
                      isSystemDefined: true,
                      filePath,
                      lineNumber: call.getStartLineNumber(),
                    });
                    count++;
                    console.log(
                      `      Found permission constant: ${actionObjectName}.${actionName} -> ${actualActionValue}, ${subjectObjectName}.${subjectName} -> ${actualSubjectValue} at ${filePath}:${call.getStartLineNumber()}`
                    );
                  }
                }
              }
            }
          }
        }
      } catch (e) {
        this.logWarn(
          `    ⚠️  處理權限常數引用時發生錯誤: ${call.getText()} in ${filePath}:${call.getStartLineNumber()}`,
          e
        );
      }
    }

    return count;
  }

  /**
   * 掃描前端檔案
   */
  private scanFrontendFile(content: string, filePath: string): number {
    let count = 0;

    const canPatterns = [
      /can\s*\(\s*['"`](\w+)['"`]\s*,\s*['"`](\w+)['"`]\s*\)/g,
      /checkPermission\s*\(\s*['"`](\w+)['"`]\s*,\s*['"`](\w+)['"`]\s*\)/g,
      /\$can\s*\(\s*['"`](\w+)['"`]\s*,\s*['"`](\w+)['"`]\s*\)/g,
    ];

    for (const pattern of canPatterns) {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        const lineNumber = content.substring(0, match.index).split("\n").length;
        const action = match[1];
        const subject = match[2];

        this.addPermission({
          action,
          subject,
          description: `從前端檔案 ${path.basename(filePath)} 掃描`,
          isSystemDefined: false,
          filePath,
          lineNumber,
        });
        count++;
        console.log(
          `      Found Frontend Permission: ${action}, ${subject} at ${filePath}:${lineNumber}`
        );
      }
    }

    const vueTemplatePattern =
      /v-if\s*=\s*['"`]can\s*\(\s*['"`](\w+)['"`]\s*,\s*['"`](\w+)['"`]\s*\)['"`]/g;
    let vueMatch;
    while ((vueMatch = vueTemplatePattern.exec(content)) !== null) {
      const lineNumber = content
        .substring(0, vueMatch.index)
        .split("\n").length;
      const action = vueMatch[1];
      const subject = vueMatch[2];

      this.addPermission({
        action,
        subject,
        description: `從前端 Vue 模板 ${path.basename(filePath)} 掃描`,
        isSystemDefined: false,
        filePath,
        lineNumber,
      });
      count++;
      console.log(
        `      Found Frontend Permission: ${action}, ${subject} at ${filePath}:${lineNumber}`
      );
    }

    return count;
  }

  /**
   * 掃描 Seed 檔案
   */
  private scanSeedFile(content: string, filePath: string): number {
    let count = 0;

    const regex =
      /\{\s*action:\s*['"`](\w+)['"`]\s*,\s*subject:\s*['"`](\w+)['"`].*?\}/g;
    let match;
    while ((match = regex.exec(content)) !== null) {
      const lineNumber = content.substring(0, match.index).split("\n").length;
      this.addPermission({
        action: match[1],
        subject: match[2],
        description: "從 Seed 檔案掃描",
        isSystemDefined: false,
        filePath,
        lineNumber,
      });
      count++;
      console.log(
        `      Found Seed Permission: ${match[1]}, ${match[2]} at ${filePath}:${lineNumber}`
      );
    }

    return count;
  }

  /**
   * 提取字串值
   */
  private extractStringValue(
    node: Node | undefined,
    sourceFile: SourceFile
  ): string | undefined {
    if (!node) {
      return undefined;
    }

    if (Node.isStringLiteral(node)) {
      return node.getLiteralValue();
    } else if (Node.isIdentifier(node)) {
      const definitions = node.getDefinitions();
      if (definitions.length > 0) {
        const defNode = definitions[0].getDeclarationNode();
        if (defNode && Node.isVariableDeclaration(defNode)) {
          const initializer = defNode.getInitializer();
          if (initializer && Node.isStringLiteral(initializer)) {
            return initializer.getLiteralValue();
          }
        }
      }
      this.logWarn(
        `    ⚠️  無法完全解析 Identifier "${node.getText()}" in ${sourceFile.getFilePath()}:${node.getStartLineNumber()}. 使用其文本作為值。`
      );
      return node.getText();
    } else if (
      Node.isPropertyAccessExpression(node) ||
      Node.isElementAccessExpression(node)
    ) {
      const text = node.getText();
      this.logWarn(
        `    ⚠️  Property/Element Access Expression "${text}" in ${sourceFile.getFilePath()}:${node.getStartLineNumber()} is not fully resolved to a static string. Using its text representation.`
      );
      return text;
    } else if (Node.isTemplateExpression(node)) {
      if (node.getTemplateSpans().length === 0) {
        // For a simple template string like `some_string` (no expressions)
        return node.getHead().getLiteralText(); // Corrected method
      } else {
        const text = node.getText();
        this.logWarn(
          `    ⚠️  Template expression "${text}" in ${sourceFile.getFilePath()}:${node.getStartLineNumber()} is dynamic. Using its text representation.`
        );
        return text;
      }
    }

    // Add a check for NoSubstitutionTemplateLiteral if it's a distinct possibility and handled differently
    if (Node.isNoSubstitutionTemplateLiteral(node)) {
      return node.getLiteralValue();
    }

    this.logWarn(
      `    ⚠️  無法從節點提取字串值 (類型: ${node.getKindName()}): ${node.getText()} in ${sourceFile.getFilePath()}:${node.getStartLineNumber()}`
    );
    return undefined;
  }

  /**
   * 添加權限到集合
   */
  private addPermission(permission: PermissionDefinition) {
    const key = `${permission.action}:${permission.subject}`;

    const existing = this.permissions.get(key);
    if (existing && existing.description && !permission.description) {
      return;
    }

    // 首先檢查是否在預定義權限中存在
    if (isPermissionDefined(permission.action, permission.subject)) {
      const predefinedPermission = PERMISSION_DEFINITIONS[key];
      
      // 使用預定義的中文名稱和描述
      permission.name = predefinedPermission.name;
      permission.description = predefinedPermission.description;
      permission.zone = predefinedPermission.zone;
      permission.category = predefinedPermission.category;
      permission.scope = predefinedPermission.scope;
      permission.isSystemDefined = predefinedPermission.isSystemDefined;
      
      console.log(`✅ 已對應權限中文定義: ${key} -> ${permission.name}`);
    } else {
      // 對於未預定義的權限，進行推論
      console.log(`⚠️  未找到預定義權限，使用推論邏輯: ${key}`);
      
      // 生成預設中文名稱
      permission.name = this.generateChineseName(permission.action, permission.subject);
      
      // 推論權限區域（admin 或 workspace）
      if (!permission.zone) {
        permission.zone = this.inferZone(permission.subject);
      }

      // 推論 scope
      if (!permission.scope) {
        const scopeConfig = this.config.inference!.scopes;
        for (const [scopeKey, keywords] of Object.entries(scopeConfig)) {
          if (
            keywords.some((kw: string) =>
              permission.subject.toLowerCase().includes(kw)
            )
          ) {
            permission.scope = scopeKey.toUpperCase() as any;
            break;
          }
        }
        if (!permission.scope) {
          permission.scope = "GLOBAL";
        }
      }

      // 推論 category
      if (!permission.category) {
        permission.category = this.inferCategory(permission.subject, permission.zone);
      }
    }

    this.permissions.set(key, permission);
  }

  /**
   * 推論權限區域 (admin 或 workspace)
   */
  private inferZone(subject: string): 'admin' | 'workspace' {
    const subjectLower = subject.toLowerCase();
    
    // admin 區域關鍵字
    const adminKeywords = [
      'system', 'admin', 'tenant', 'plan', 'order', 'global', 'setting',
      'log', 'audit', 'systemuser', 'aikey', 'aimodel', 'aiglobalsetting',
      'permission', 'role', 'loginlog', 'systemlog'
    ];
    
    // workspace 區域關鍵字  
    const workspaceKeywords = [
      'workspace', 'project', 'client', 'form', 'workspacemember',
      'aibot', 'aiusagelog'
    ];
    
    if (adminKeywords.some(keyword => subjectLower.includes(keyword))) {
      return 'admin';
    }
    
    if (workspaceKeywords.some(keyword => subjectLower.includes(keyword))) {
      return 'workspace';
    }
    
    // 預設為 admin
    return 'admin';
  }

  /**
   * 推論權限類別
   */
  private inferCategory(subject: string, zone: 'admin' | 'workspace'): string {
    const subjectLower = subject.toLowerCase();
    
    if (zone === 'admin') {
      if (['system', 'dashboard', 'adminpanel'].some(kw => subjectLower.includes(kw))) {
        return 'system_management';
      }
      if (['tenant'].some(kw => subjectLower.includes(kw))) {
        return 'tenant_management';
      }
      if (['user', 'systemuser', 'tenantuser'].some(kw => subjectLower.includes(kw))) {
        return 'user_management';
      }
      if (['role', 'permission'].some(kw => subjectLower.includes(kw))) {
        return 'role_management';
      }
      if (['ai', 'bot'].some(kw => subjectLower.includes(kw))) {
        return 'ai_management';
      }
      if (['plan', 'order'].some(kw => subjectLower.includes(kw))) {
        return 'subscription_management';
      }
      if (['setting', 'config'].some(kw => subjectLower.includes(kw))) {
        return 'system_config';
      }
      // 將 LOG 相關權限歸類到 log_management
      if (['log', 'loginlog', 'systemlog', 'messagelog'].some(kw => subjectLower.includes(kw))) {
        return 'log_management';
      }
      // audit 相關權限歸類到 audit_management
      if (['audit'].some(kw => subjectLower.includes(kw))) {
        return 'audit_management';
      }
    } else {
      if (['workspace'].some(kw => subjectLower.includes(kw))) {
        return 'workspace_settings';
      }
      if (['project'].some(kw => subjectLower.includes(kw))) {
        return 'project_management';
      }
      if (['member', 'workspacemember'].some(kw => subjectLower.includes(kw))) {
        return 'member_management';
      }
      if (['client', 'form'].some(kw => subjectLower.includes(kw))) {
        return 'resource_management';
      }
      if (['ai', 'bot'].some(kw => subjectLower.includes(kw))) {
        return 'ai_features';
      }
      return 'collaboration';
    }
    
    return 'other';
  }

  /**
   * 生成預設中文名稱
   */
  private generateChineseName(action: string, subject: string): string {
    const actionMap: Record<string, string> = {
      'manage': '管理',
      'create': '建立',
      'read': '查看', 
      'update': '修改',
      'delete': '刪除',
      'invite': '邀請',
      'remove': '移除',
      'execute': '執行',
      'share': '分享',
      'access': '存取'
    };
    
    const subjectMap: Record<string, string> = {
      'user': '用戶',
      'tenant': '租戶',
      'workspace': '工作區',
      'project': '專案',
      'client': '客戶',
      'form': '表單',
      'role': '角色',
      'permission': '權限',
      'dashboard': '儀表板',
      'system': '系統',
      'aibot': 'AI 助理',
      'aimodel': 'AI 模型',
      'aikey': 'AI 金鑰',
      'plan': '訂閱方案',
      'order': '訂單',
      'log': '日誌'
    };
    
    const actionChinese = actionMap[action.toLowerCase()] || action;
    const subjectChinese = subjectMap[subject.toLowerCase()] || subject;
    
    return `${actionChinese}${subjectChinese}`;
  }

  /**
   * 排序權限列表：先依 category 反向排序，再依 subject, action 排序
   */
  private sortPermissions(
    permissions: PermissionDefinition[]
  ): PermissionDefinition[] {
    return permissions.sort((a, b) => {
      const categoryA = a.category || "other";
      const categoryB = b.category || "other";
      if (categoryA !== categoryB) {
        return categoryB.localeCompare(categoryA);
      }
      if (a.subject !== b.subject) {
        return a.subject.localeCompare(b.subject);
      }
      return a.action.localeCompare(b.action);
    });
  }

  /**
   * 簡易的 glob 檔案搜尋 - 實作基本的檔案搜尋功能
   */
  private async globFiles(pattern: string): Promise<string[]> {
    const files: string[] = [];

    try {
      if (path.isAbsolute(pattern) && fs.existsSync(pattern)) {
        const stats = fs.statSync(pattern);
        if (stats.isFile()) {
          return [pattern];
        } else if (stats.isDirectory()) {
          const entries = fs.readdirSync(pattern);
          for (const entry of entries) {
            const fullPath = path.join(pattern, entry);
            const entryStats = fs.statSync(fullPath);

            if (
              entryStats.isFile() &&
              (entry.endsWith(".ts") || entry.endsWith(".vue"))
            ) {
              files.push(fullPath);
            } else if (entryStats.isDirectory() && !this.shouldExclude(entry)) {
              const subFiles = await this.globFiles(fullPath);
              files.push(...subFiles);
            }
          }
        }
        return files;
      }

      const baseDir = path.dirname(pattern);
      const fileName = path.basename(pattern);
      const fullBaseDir = path.resolve(__dirname, "../..", baseDir);

      if (!fs.existsSync(fullBaseDir)) {
        return [];
      }

      await this.scanDirectory(fullBaseDir, fileName, files);
    } catch (error) {
      this.logWarn(`⚠️  搜尋檔案 ${pattern} 時發生錯誤: ${error.message}`);
    }

    return files;
  }

  /**
   * 掃描目錄中的檔案
   */
  private async scanDirectory(
    dir: string,
    pattern: string,
    files: string[]
  ): Promise<void> {
    try {
      const entries = fs.readdirSync(dir, { withFileTypes: true });

      for (const entry of entries) {
        const fullPath = path.join(dir, entry.name);

        if (entry.isFile()) {
          if (this.matchesPattern(entry.name, pattern)) {
            files.push(fullPath);
          }
        } else if (entry.isDirectory() && !this.shouldExclude(entry.name)) {
          await this.scanDirectory(fullPath, pattern, files);
        }
      }
    } catch (error) {}
  }

  /**
   * 檢查檔案名稱是否符合模式
   */
  private matchesPattern(fileName: string, pattern: string): boolean {
    if (pattern === "*") return true;
    if (pattern.includes("*")) {
      const regex = new RegExp(pattern.replace(/\*/g, ".*"));
      return regex.test(fileName);
    }
    return fileName.includes(pattern.replace("*", ""));
  }

  /**
   * 遞歸掃描目錄中指定副檔名的檔案
   */
  private async scanDirectoryRecursive(
    dir: string,
    extensions: string[]
  ): Promise<string[]> {
    const files: string[] = [];

    try {
      const entries = fs.readdirSync(dir, { withFileTypes: true });

      for (const entry of entries) {
        const fullPath = path.join(dir, entry.name);

        if (entry.isFile()) {
          const ext = path.extname(entry.name);
          if (extensions.includes(ext)) {
            files.push(fullPath);
          }
        } else if (entry.isDirectory() && !this.shouldExclude(entry.name)) {
          const subFiles = await this.scanDirectoryRecursive(
            fullPath,
            extensions
          );
          files.push(...subFiles);
        }
      }
    } catch (error) {
      this.logWarn(`⚠️  掃描目錄 ${dir} 時發生錯誤: ${error.message}`);
    }

    return files;
  }

  /**
   * 檢查是否應該排除此目錄/檔案
   */
  private shouldExclude(name: string): boolean {
    return this.config.excludePatterns.some((pattern) =>
      name.includes(pattern)
    );
  }

  /**
   * 取得快取檔案路徑
   */
  private getCacheFilePath(): string {
    return path.resolve(__dirname, "../..", this.config.cachePath);
  }

  /**
   * 檢查快取是否有效
   */
  private isCacheValid(): boolean {
    const cacheFile = this.getCacheFilePath();
    if (!fs.existsSync(cacheFile)) {
      return false;
    }
    try {
      const stats = fs.statSync(cacheFile);
      const mtime = stats.mtime.getTime();
      const now = Date.now();
      if (this.config.ttl && now - mtime < this.config.ttl) {
        return true;
      }
    } catch (error) {
      this.logWarn(`⚠️ 檢查快取有效性時發生錯誤: ${error.message}`);
    }
    return false;
  }

  /**
   * 從快取載入掃描結果
   */
  private loadFromCache(): ScanResult {
    const cacheFile = this.getCacheFilePath();
    try {
      const content = fs.readFileSync(cacheFile, "utf-8");
      const parsed = JSON.parse(content) as ScanResult;
      return parsed;
    } catch (error) {
      this.logWarn(`⚠️ 載入快取時發生錯誤: ${error.message}`);
      return {
        permissions: [],
        stats: {
          totalFiles: 0,
          scannedFiles: 0,
          filesWithPermissions: 0,
          totalPermissions: 0,
        },
        errors: [error.message],
      };
    }
  }

  /**
   * 將掃描結果儲存至快取
   */
  private saveToCache(result: ScanResult): void {
    const cacheFile = this.getCacheFilePath();
    try {
      const dir = path.dirname(cacheFile);
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
      fs.writeFileSync(cacheFile, JSON.stringify(result));
      console.log(`💾 已將掃描結果儲存至快取: ${cacheFile}`);
    } catch (error) {
      this.logWarn(`⚠️ 儲存快取時發生錯誤: ${error.message}`);
    }
  }
}