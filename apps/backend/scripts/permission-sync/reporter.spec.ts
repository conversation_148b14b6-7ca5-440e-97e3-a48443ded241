import { PermissionReporter } from "./reporter";
import { ScanResult, SyncResult, PermissionDefinition, PermissionSyncReport } from "./types";

describe("PermissionReporter 核心邏輯", () => {
  const permissions: PermissionDefinition[] = [
    { action: "read", subject: "User", scope: "SYSTEM", category: "user" },
    { action: "write", subject: "User", scope: "SYSTEM", category: "user" },
    { action: "delete", subject: "Post", scope: "TENANT", category: "content" }
  ];

  it("generateSummary 應正確統計權限", () => {
    const reporter = new PermissionReporter("tempDir");
    // @ts-ignore Access private method for testing
    const summary = (reporter as any).generateSummary(permissions);
    expect(summary.total).toBe(3);
    expect(summary.byScope).toEqual({ SYSTEM: 2, TENANT: 1 });
    expect(summary.byCategory).toEqual({ user: 2, content: 1 });
    expect(summary.bySubject).toEqual({ User: 2, Post: 1 });
  });

  it("sortPermissions 應依 category, subject, action 排序", () => {
    const reporter = new PermissionReporter();
    const shuffled = [...permissions].sort(() => Math.random() - 0.5);
    // @ts-ignore Access private method for testing
    const sorted = (reporter as any).sortPermissions(shuffled);
    // sorted[0] 和 sorted[1] 應為 category user, subject User, action read, write
    expect(sorted[0].action).toBe("read");
    expect(sorted[1].action).toBe("write");
    // sorted[2] 應為 delete Post
    expect(sorted[2].subject).toBe("Post");
    expect(sorted[2].action).toBe("delete");
  });
});
