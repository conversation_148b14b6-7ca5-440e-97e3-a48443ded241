#!/usr/bin/env ts-node
/**
 * HorizAI SaaS 資料庫遷移工具
 * 
 * 這個工具封裝了 Prisma 遷移功能，並提供更多符合項目需求的功能：
 * - 支援多環境配置
 * - 檢查資料庫當前狀態
 * - 建立新的遷移檔案
 * - 執行遷移並可選擇執行種子資料
 * - 提供回滾功能
 * - 與權限同步機制整合
 * - 資料庫匯出與匯入功能
 * 
 * 使用方式：
 *   npx ts-node scripts/migrate.ts [指令] [選項]
 * 
 * 指令：
 *   status        顯示目前資料庫遷移狀態
 *   create        建立新的遷移檔案
 *   apply         套用遷移
 *   reset         重置資料庫並套用所有遷移
 *   rollback      回滾最近的遷移
 *   sync-perms    僅同步權限定義
 *   export        匯出資料庫到檔案
 *   import        從檔案匯入資料庫
 * 
 * 選項：
 *   --env         指定環境 (dev/staging/prod)，預設為 dev
 *   --name        遷移名稱 (create 指令必須)
 *   --seed        套用遷移後執行種子資料建立
 *   --full-data   建立完整的展示資料 (僅適用於 --seed)
 *   --dry-run     權限同步預覽模式 (僅適用於 sync-perms)
 *   --force       強制執行操作
 *   --skip-perms  跳過權限同步
 *   --output      匯出檔案路徑 (適用於 export)
 *   --file        匯入檔案路徑 (適用於 import)
 *   --format      匯出/匯入檔案格式 (custom|sql|dump，預設為 dump)
 *   --help        顯示說明
 */

import { spawn, spawnSync } from 'child_process';
import * as path from 'path';
import * as fs from 'fs';
import { randomUUID } from 'crypto';
import * as dotenv from 'dotenv';

// 載入環境變數
dotenv.config();

// 檢查是否使用 Prisma Accelerate
function isPrismaAccelerate() {
  return false;
}

// 命令列參數解析
const args = process.argv.slice(2);
const command = args[0]?.toLowerCase();
const options: Record<string, any> = {};

// 解析選項
for (let i = 1; i < args.length; i++) {
  const arg = args[i];
  if (arg.startsWith('--')) {
    const optName = arg.slice(2);
    const hasValue = i + 1 < args.length && !args[i + 1].startsWith('--');
    options[optName] = hasValue ? args[++i] : true;
  }
}

// 設定環境
const env = options.env || 'dev';
const envFiles = [
  `.env.${env}`,
  `.env.${env}.local`,
  '.env',
  '.env.local',
];

// 找到第一個存在的環境檔案
const envFile = envFiles.find(file => fs.existsSync(path.resolve(process.cwd(), file)));
if (envFile) {
  console.log(`🌿 使用環境設定檔: ${envFile}`);
  dotenv.config({ path: path.resolve(process.cwd(), envFile) });
}

// 檢查 DATABASE_URL 是否存在
if (!process.env.DATABASE_URL) {
  console.error('❌ 錯誤: 找不到 DATABASE_URL 環境變數');
  console.error('請確保在環境變數或 .env 檔案中設定 DATABASE_URL');
  process.exit(1);
}

// 顯示說明
if (command === 'help' || options.help || !command) {
  console.log(`
HorizAI SaaS 資料庫遷移工具

使用方式:
  npx ts-node scripts/migrate.ts [指令] [選項]

指令:
  status        顯示目前資料庫遷移狀態
  create        建立新的遷移檔案
  apply         套用遷移
  reset         重置資料庫並套用所有遷移
  rollback      回滾最近的遷移
  sync-perms    僅同步權限定義
  export        匯出資料庫到檔案
  import        從檔案匯入資料庫

選項:
  --env         指定環境 (dev/staging/prod)，預設為 dev
  --name        遷移名稱 (create 指令必須)
  --seed        套用遷移後執行種子資料建立
  --full-data   建立完整的展示資料 (僅適用於 --seed)
  --dry-run     權限同步預覽模式 (僅適用於 sync-perms)
  --force       強制執行操作
  --skip-perms  跳過權限同步
  --output      匯出檔案路徑 (適用於 export)
  --file        匯入檔案路徑 (適用於 import)
  --format      匯出/匯入檔案格式 (custom|sql|dump，預設為 dump)
  --help        顯示說明

範例:
  npx ts-node scripts/migrate.ts status
  npx ts-node scripts/migrate.ts create --name add_user_fields
  npx ts-node scripts/migrate.ts apply --seed
  npx ts-node scripts/migrate.ts reset --seed --full-data
  npx ts-node scripts/migrate.ts sync-perms --dry-run
  npx ts-node scripts/migrate.ts export --output=./backup.dump
  npx ts-node scripts/migrate.ts import --file=./backup.dump
  `);
  process.exit(0);
}

// 執行 shell 命令的輔助函數
function execCommand(command: string, args: string[] = [], env: NodeJS.ProcessEnv = process.env): Promise<void> {
  return new Promise((resolve, reject) => {
    console.log(`🚀 執行: ${command} ${args.join(' ')}`);
    
    const childProcess = spawn(command, args, {
      stdio: 'inherit',
      shell: true,
      env: { ...process.env, ...env }
    });

    childProcess.on('close', (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`命令執行失敗，退出碼: ${code}`));
      }
    });
  });
}

// 執行同步命令並取得輸出
function execSyncCommand(command: string, args: string[] = [], env: NodeJS.ProcessEnv = process.env): string {
  console.log(`🚀 執行: ${command} ${args.join(' ')}`);
  
  const result = spawnSync(command, args, {
    stdio: ['inherit', 'pipe', 'inherit'],
    shell: true,
    env: { ...process.env, ...env }
  });

  if (result.status !== 0) {
    throw new Error(`命令執行失敗，退出碼: ${result.status}`);
  }

  return result.stdout?.toString() || '';
}

// 驗證 Prisma 架構
async function validateSchema(): Promise<boolean> {
  try {
    await execCommand('npx', ['prisma', 'validate']);
    return true;
  } catch (error) {
    console.error('❌ Prisma 架構驗證失敗:', error);
    return false;
  }
}

// 同步權限
async function syncPermissions(options: { dryRun?: boolean, force?: boolean } = {}): Promise<void> {
  try {
    const args = ['ts-node', 'prisma/templates/permission-sync.ts'];
    
    if (options.dryRun) {
      args.push('--dry-run');
    }
    
    if (options.force) {
      args.push('--force');
    }
    
    await execCommand('npx', args);
    console.log('✅ 權限同步完成');
  } catch (error) {
    console.error('❌ 權限同步失敗:', error);
    throw error;
  }
}

// 執行種子資料建立
async function seedDatabase(fullData: boolean = false): Promise<void> {
  try {
    console.log(`🌱 執行資料庫種子資料建立 ${fullData ? '(完整展示資料)' : ''}`);
    
    const env: NodeJS.ProcessEnv = {};
    if (fullData) {
      env.SEED_FULL_DATA = 'true';
    }
    
    await execCommand('npx', ['prisma', 'db', 'seed'], env);
    console.log('✅ 種子資料建立完成');
  } catch (error) {
    console.error('❌ 種子資料建立失敗:', error);
    throw error;
  }
}

// 檢查資料庫狀態
async function checkDatabaseStatus(): Promise<void> {
  try {
    console.log('📊 檢查資料庫遷移狀態...');
    
    // 使用 Prisma Migrate 檢查狀態
    const output = execSyncCommand('npx', ['prisma', 'migrate', 'status']);
    
    // 分析輸出結果
    if (output.includes('Database schema is up to date')) {
      console.log('✅ 資料庫結構已是最新');
    } else if (output.includes('The following migration have not been applied')) {
      console.log('⚠️ 有待套用的遷移');
    }
    
    console.log('');
    console.log('詳細遷移狀態:');
    console.log(output);
  } catch (error) {
    console.error('❌ 檢查資料庫狀態失敗:', error);
    throw error;
  }
}

// 建立新遷移
async function createMigration(name: string): Promise<void> {
  if (!name) {
    console.error('❌ 錯誤: 建立遷移時必須指定名稱 (--name)');
    process.exit(1);
  }
  
  // 驗證遷移名稱
  if (!/^[a-z0-9_]+$/.test(name)) {
    console.error('❌ 錯誤: 遷移名稱必須是小寫字母、數字和底線的組合');
    process.exit(1);
  }
  
  try {
    // 先驗證 schema
    const isValid = await validateSchema();
    if (!isValid) {
      console.error('❌ 無法建立遷移: 架構驗證失敗');
      process.exit(1);
    }
    
    console.log(`🔨 建立新遷移: ${name}`);
    await execCommand('npx', ['prisma', 'migrate', 'dev', '--name', name, '--create-only']);
    console.log('✅ 遷移建立完成');
    
    // 顯示遷移檔案位置
    console.log(`
📁 遷移檔案已建立，您現在可以編輯遷移檔案
   請檢查 prisma/migrations/ 目錄下最新的遷移資料夾
    `);
  } catch (error) {
    console.error('❌ 建立遷移失敗:', error);
    throw error;
  }
}

// 套用遷移
async function applyMigration(options: { seed?: boolean, fullData?: boolean, skipPerms?: boolean, force?: boolean } = {}): Promise<void> {
  try {
    console.log('🔄 套用資料庫遷移...');
    
    // 先驗證 schema
    const isValid = await validateSchema();
    if (!isValid) {
      console.error('❌ 無法套用遷移: 架構驗證失敗');
      process.exit(1);
    }
    
    // 套用遷移
    await execCommand('npx', ['prisma', 'migrate', 'dev', '--skip-seed']);
    console.log('✅ 遷移套用完成');
    
    // 同步權限 (除非明確跳過)
    if (!options.skipPerms) {
      console.log('🔄 同步權限...');
      await syncPermissions({ force: options.force });
    }
    
    // 如果需要，執行種子資料
    if (options.seed) {
      await seedDatabase(options.fullData);
    }
  } catch (error) {
    console.error('❌ 套用遷移失敗:', error);
    throw error;
  }
}

// 重置資料庫
async function resetDatabase(options: { seed?: boolean, fullData?: boolean, skipPerms?: boolean, force?: boolean } = {}): Promise<void> {
  if (!options.force) {
    console.log('⚠️ 警告: 重置會刪除資料庫中的所有資料');
    console.log('如果您確定要繼續，請加上 --force 選項');
    process.exit(1);
  }
  
  try {
    console.log('🔄 重置資料庫...');
    
    // 設定環境變數
    const env: NodeJS.ProcessEnv = {};
    if (options.fullData) {
      env.SEED_FULL_DATA = 'true';
    }
    
    // 重置資料庫 (包含套用遷移和種子資料)
    if (options.seed) {
      // 如果需要種子資料，使用標準重置流程
      await execCommand('npx', ['prisma', 'migrate', 'reset', '--force'], env);
      console.log('✅ 資料庫重置並初始化完成');
    } else {
      // 如果不需要種子資料，先重置再應用遷移但跳過種子
      await execCommand('npx', ['prisma', 'migrate', 'reset', '--force', '--skip-seed']);
      console.log('✅ 資料庫重置完成');
      
      // 同步權限 (除非明確跳過)
      if (!options.skipPerms) {
        console.log('🔄 同步權限...');
        await syncPermissions({ force: true });
      }
    }
  } catch (error) {
    console.error('❌ 資料庫重置失敗:', error);
    throw error;
  }
}

// 回滾最近的遷移
async function rollbackMigration(options: { force?: boolean } = {}): Promise<void> {
  if (!options.force) {
    console.log('⚠️ 警告: 回滾會刪除最近一次遷移所做的變更');
    console.log('如果您確定要繼續，請加上 --force 選項');
    process.exit(1);
  }
  
  try {
    console.log('🔄 回滾最近的遷移...');
    
    // 讀取遷移歷史
    const status = execSyncCommand('npx', ['prisma', 'migrate', 'status']);
    
    // 如果沒有遷移可回滾
    if (status.includes('Database schema is up to date') && !status.includes('Migration ID')) {
      console.log('❓ 沒有可回滾的遷移');
      return;
    }
    
    // 執行回滾 (Prisma 沒有直接回滾指令，需要使用 reset 到特定版本)
    // 1. 讀取當前遷移列表
    const prismaDir = path.resolve(process.cwd(), 'prisma');
    const migrationsDir = path.resolve(prismaDir, 'migrations');
    const migrations = fs.readdirSync(migrationsDir)
      .filter(dir => fs.statSync(path.join(migrationsDir, dir)).isDirectory() && !dir.startsWith('.'))
      .sort();
    
    if (migrations.length <= 1) {
      console.log('❓ 沒有足夠的遷移可回滾');
      return;
    }
    
    // 2. 取得倒數第二個遷移 (回滾到這個版本)
    const targetMigration = migrations[migrations.length - 2];
    
    // 3. 執行回滾到指定版本
    await execCommand('npx', [
      'prisma', 'migrate', 'resolve', 
      '--applied', targetMigration
    ]);
    
    await execCommand('npx', ['prisma', 'db', 'push', '--force-reset']);
    
    console.log(`✅ 已回滾到遷移: ${targetMigration}`);
    
    // 同步權限
    console.log('🔄 同步權限...');
    await syncPermissions({ force: true });
  } catch (error) {
    console.error('❌ 回滾遷移失敗:', error);
    throw error;
  }
}

// 匯出資料庫
async function exportDatabase(options: { output?: string, format?: string } = {}): Promise<void> {
  const format = options.format || 'dump';
  const defaultFileName = `horizai_backup_${new Date().toISOString().replace(/[:]/g, '_')}.${format === 'sql' ? 'sql' : 'dump'}`;
  const outputPath = options.output || path.resolve(process.cwd(), 'backup', defaultFileName);
  
  // 確保備份目錄存在
  const backupDir = path.dirname(outputPath);
  if (!fs.existsSync(backupDir)) {
    fs.mkdirSync(backupDir, { recursive: true });
  }
  
  try {
    console.log('📤 開始匯出資料庫...');
    
    // 從 DATABASE_URL 解析資料庫資訊
    const url = new URL(process.env.DATABASE_URL!);
    const dbName = url.pathname.slice(1);
    const username = url.username;
    const password = url.password;
    const host = url.hostname;
    const port = url.port || '5432';
    
    // 設定環境變數供 pg_dump 使用
    const env: NodeJS.ProcessEnv = {
      PGUSER: username,
      PGPASSWORD: password,
      PGHOST: host,
      PGPORT: port,
      PGDATABASE: dbName,
    };
    
    // 根據選定的格式匯出資料庫
    if (format === 'custom' || format === 'dump') {
      // 使用自定義格式，最適合用於備份和還原
      await execCommand('pg_dump', [
        '-F', 'c',  // 自定義格式
        '-b',       // 包含大型物件
        '-v',       // 顯示詳細資訊
        '-f', outputPath
      ], env);
    } else if (format === 'sql') {
      // 純 SQL 格式，更易讀但可能較大
      await execCommand('pg_dump', [
        '-F', 'p',  // 純 SQL 格式
        '-v',       // 顯示詳細資訊
        '-f', outputPath
      ], env);
    } else {
      throw new Error(`不支援的匯出格式: ${format}`);
    }
    
    console.log(`✅ 資料庫匯出成功: ${outputPath}`);
  } catch (error) {
    console.error('❌ 資料庫匯出失敗:', error);
    throw error;
  }
}

// 從檔案匯入資料庫
async function importDatabase(options: { file?: string, format?: string, force?: boolean } = {}): Promise<void> {
  if (!options.file) {
    console.error('❌ 錯誤: 匯入時必須指定檔案路徑 (--file)');
    process.exit(1);
  }
  
  if (!fs.existsSync(options.file)) {
    console.error(`❌ 錯誤: 匯入檔案不存在: ${options.file}`);
    process.exit(1);
  }
  
  if (!options.force) {
    console.log('⚠️ 警告: 匯入會覆蓋現有資料庫資料');
    console.log('如果您確定要繼續，請加上 --force 選項');
    process.exit(1);
  }
  
  const format = options.format || (options.file.endsWith('.sql') ? 'sql' : 'dump');
  
  try {
    console.log('📥 開始匯入資料庫...');
    
    // 從 DATABASE_URL 解析資料庫資訊
    const url = new URL(process.env.DATABASE_URL!);
    const dbName = url.pathname.slice(1);
    const username = url.username;
    const password = url.password;
    const host = url.hostname;
    const port = url.port || '5432';
    
    // 設定環境變數供 psql/pg_restore 使用
    const env: NodeJS.ProcessEnv = {
      PGUSER: username,
      PGPASSWORD: password,
      PGHOST: host,
      PGPORT: port,
      PGDATABASE: dbName,
    };
    
    // 根據檔案格式決定使用哪個工具來匯入
    if (format === 'sql') {
      // 使用 psql 匯入 SQL 檔案
      await execCommand('psql', [
        '-f', options.file,
        '-v', 'ON_ERROR_STOP=1'
      ], env);
    } else {
      // 使用 pg_restore 匯入自定義格式
      await execCommand('pg_restore', [
        '--clean',         // 刪除資料庫中現有物件
        '--if-exists',     // 僅在物件存在時刪除
        '--no-owner',      // 不設定物件所有者
        '--no-privileges', // 不設定權限
        '-v',              // 顯示詳細資訊
        '-d', dbName,      // 資料庫名稱
        options.file       // 匯入檔案
      ], env);
    }
    
    console.log('✅ 資料庫匯入成功');
    
    // 同步權限
    console.log('🔄 同步權限...');
    await syncPermissions({ force: true });
  } catch (error) {
    console.error('❌ 資料庫匯入失敗:', error);
    throw error;
  }
}

// 主程式邏輯
(async function main() {
  try {
    switch (command) {
      case 'status':
        await checkDatabaseStatus();
        break;
        
      case 'create':
        await createMigration(options.name);
        break;
        
      case 'apply':
        await applyMigration({
          seed: options.seed,
          fullData: options['full-data'],
          skipPerms: options['skip-perms'],
          force: options.force
        });
        break;
        
      case 'reset':
        await resetDatabase({
          seed: options.seed !== false, // 預設執行種子
          fullData: options['full-data'],
          skipPerms: options['skip-perms'],
          force: options.force
        });
        break;
        
      case 'rollback':
        await rollbackMigration({ force: options.force });
        break;
        
      case 'sync-perms':
        await syncPermissions({
          dryRun: options['dry-run'],
          force: options.force
        });
        break;
        
      case 'export':
        await exportDatabase({
          output: options.output,
          format: options.format
        });
        break;
        
      case 'import':
        await importDatabase({
          file: options.file,
          format: options.format,
          force: options.force
        });
        break;
        
      default:
        console.error(`❌ 未知指令: ${command}`);
        console.log('執行 --help 查看可用指令');
        process.exit(1);
    }
    
    process.exit(0);
  } catch (error) {
    console.error('❌ 錯誤:', error);
    process.exit(1);
  }
})();
