import { PrismaClient } from "@prisma/client";

async function main() {
  const prisma = new PrismaClient();
  
  try {
    // 直接執行 SQL 查詢
    const result = await prisma.$queryRaw`
      SELECT
          t.typname,
          e.enumlabel
      FROM
          pg_type t
          JOIN pg_enum e ON t.oid = e.enumtypid
      WHERE
          t.typname = 'aibotscope'
      ORDER BY
          e.enumsortorder;
    `;
    
    console.log('AiBotScope 枚舉值:');
    console.log(result);
    
    // 查詢目前使用的範圍
    const botScopes = await prisma.$queryRaw`
      SELECT scope, COUNT(*)
      FROM ai_bots
      GROUP BY scope;
    `;
    
    console.log('\nAI Bots 範圍使用統計:');
    console.log(botScopes);
  } catch (error) {
    console.error('查詢錯誤:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
