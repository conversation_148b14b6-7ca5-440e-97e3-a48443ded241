# Backend Scripts 目錄說明

本目錄包含後端開發過程中使用的各種輔助腳本，主要用於簡化開發流程、管理資料庫和處理端口相關問題。

## 📋 檔案清單

### 🚀 開發相關
- **`dev-start.ts`** - 統一的開發服務器啟動腳本
  - 自動檢查端口占用
  - 詢問用戶是否清理占用進程
  - 無縫啟動 NestJS 開發服務器
  - 使用方式：`pnpm run start:dev`

### 🔧 端口管理
- **`port-manager.ts`** - 獨立的端口診斷工具
  - 檢查端口 4000 的使用狀況
  - 顯示占用進程的詳細信息
  - 使用方式：`pnpm run port:check`

### 🗄️ 資料庫管理
- **`migrate.ts`** - 資料庫遷移管理工具
  - 處理 Prisma 遷移操作
  - 支援多種資料庫操作命令
  - 整合權限同步功能

- **`check-env.ts`** - 環境變數檢查工具
  - 驗證必要的環境變數設定
  - 自動修復常見的環境設定問題
  - 提供完整的環境檢查報告

- **`check-enum.ts`** - 資料庫列舉類型檢查工具
  - 驗證 Prisma schema 中的列舉定義
  - 確保資料庫與 schema 的一致性

## 🎯 主要使用指令

### 開發啟動（推薦）
```bash
pnpm run start:dev    # 🚀 一鍵啟動，自動處理端口問題
```

### 端口管理
```bash
pnpm run port:check   # 🔍 診斷端口占用情況
```

### 資料庫操作
```bash
pnpm run db:auto-fix     # 🔄 自動修復資料庫狀態
pnpm run db:quick-reset  # ⚡ 快速重置資料庫
pnpm run db:status       # 📊 查看遷移狀態
pnpm run db:apply        # ✅ 應用待處理的遷移
```

### 環境檢查
```bash
pnpm run db:check-env    # 🧪 檢查環境變數
pnpm run db:sync-env     # 🔧 修復環境設定問題
pnpm run db:full-check   # 🔍 完整環境檢查
```

## 📝 設計理念

這些腳本遵循以下設計原則：

1. **🎯 單一職責**：每個腳本專注於特定的功能領域
2. **🔄 智能化**：自動處理常見問題，減少手動干預
3. **💬 友善提示**：提供清晰的用戶互動和錯誤訊息
4. **🛡️ 安全性**：在進行破壞性操作前進行確認
5. **⚡ 效率優化**：簡化開發流程，提升開發體驗

## 🎉 最新改進

### v2.0 統一啟動體驗
- **整合式端口檢查**：`start:dev` 現在包含智能端口管理
- **簡化指令集**：移除多餘的開發相關指令
- **一鍵解決**：無需記憶多個指令，`pnpm run start:dev` 解決所有問題

**現在開發者只需要記住：`pnpm run start:dev` 🚀**

---
