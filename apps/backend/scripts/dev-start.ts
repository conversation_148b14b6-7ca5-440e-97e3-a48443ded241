/**
 * 整合式開發服務器啟動工具
 * 功能：檢查端口占用 → 詢問用戶 → 清理端口 → 啟動 NestJS 服務
 * 統一在 start:dev 中使用，無需額外指令
 */

import { exec, spawn } from "child_process";
import { promisify } from "util";
import * as readline from "readline";
import { execSync } from "child_process";
import { PrismaClient } from "@prisma/client";
import chalk from "chalk";

const execAsync = promisify(exec);
const prisma = new PrismaClient();

interface ProcessInfo {
  pid: string;
  command: string;
  port: number;
}

class DevStarter {
  public defaultPort = 4000;

  async checkPort(port: number): Promise<ProcessInfo | null> {
    try {
      let pid: string;
      let command: string;
      if (process.platform === "win32") {
        const { stdout } = await execAsync(`netstat -ano | findstr :${port}`);
        const lines = stdout.trim().split("\n");
        if (lines.length === 0 || !lines[0]) {
          return null;
        }
        const match = lines[0].trim().split(/\s+/);
        pid = match[match.length - 1];
        command = `Port ${port} is in use by process ${pid}`; // Simplified
      } else {
        const { stdout: pidOutput } = await execAsync(
          `lsof -ti:${port} || true`
        ); //  || true to prevent error if port not in use
        pid = pidOutput.trim();

        if (!pid) {
          return null;
        }

        try {
          const { stdout: processInfoOutput } = await execAsync(
            `ps aux | grep ${pid} | grep -v grep | head -1 || true`
          );
          command = processInfoOutput.trim();
        } catch (psError) {
          command = `Process ${pid}`; // Fallback command
        }
      }

      return {
        pid,
        command: command || `Process ${pid}`,
        port,
      };
    } catch (error: any) {
      return null;
    }
  }

  async killProcess(pid: string): Promise<boolean> {
    try {
      console.log(
        `[INFO] Kill command: ${process.platform === "win32" ? `taskkill /F /PID ${pid}` : `kill -9 ${pid}`}`
      );
      if (process.platform === "win32") {
        await execAsync(`taskkill /F /PID ${pid}`);
      } else {
        await execAsync(`kill -9 ${pid}`);
      }
      await new Promise((resolve) => setTimeout(resolve, 1000));

      const stillRunning = await this.checkPort(this.defaultPort);
      if (stillRunning && stillRunning.pid === pid) {
        console.error(
          `[ERROR] 無法終止進程 ${pid} (端口 ${this.defaultPort} 仍被占用)`
        );
        return false;
      }
      console.log(`[INFO] 已成功終止進程 ${pid}`);
      return true;
    } catch (error: any) {
      console.error(`[ERROR] 終止進程 ${pid} 時出錯: ${error.message}`);
      return false;
    }
  }

  public async askUserConfirmation(message: string): Promise<boolean> {
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
    });

    return new Promise((resolve) => {
      rl.question(`[CONFIRM] ${message} (y/N): `, (answer) => {
        rl.close();
        resolve(answer.toLowerCase() === "y" || answer.toLowerCase() === "yes");
      });
    });
  }

  private async execPrismaCommand(args: string[]): Promise<{
    stdout: string;
    stderr: string;
    success: boolean;
    exitCode: number | null;
  }> {
    const commandString = `npx prisma ${args.join(" ")} --schema=./prisma/schema.prisma`;
    try {
      const { stdout, stderr } = await execAsync(commandString, {
        cwd: process.cwd(),
      });
      return { stdout, stderr, success: true, exitCode: 0 };
    } catch (error: any) {
      return {
        stdout: error.stdout || "",
        stderr:
          error.stderr || error.message || "Prisma command failed unexpectedly",
        success: false,
        exitCode: typeof error.code === "number" ? error.code : null,
      };
    }
  }

  private async checkDatabaseStatus(): Promise<boolean> {
    console.log("[INFO] 檢查資料庫狀態...");
    console.log("[INFO] 請確保資料庫服務（PostgreSQL）正在運行。");

    const { stdout, stderr, exitCode } = await this.execPrismaCommand([
      "migrate",
      "status",
    ]);

    // 檢查是否有嚴重的 migration 錯誤需要重置
    const criticalMigrationErrors = [
      "current transaction is aborted",
      "commands ignored until end of transaction block",
      "Migration failed",
      "database schema is not up to date",
      "Database is in a failed migration state",
      "Failed to apply migration",
    ];

    const errorOutput = `${stderr} ${stdout}`.toLowerCase();
    const hasCriticalError = criticalMigrationErrors.some((error) =>
      errorOutput.includes(error.toLowerCase())
    );

    if (hasCriticalError) {
      console.error(chalk.red("[ERROR] 偵測到嚴重的 migration 問題："));
      console.error(chalk.red(`  ${stderr || stdout}`));
      console.warn(
        chalk.yellow("[WARN] 這種問題通常需要執行 migration reset 來修復")
      );
      console.warn(
        chalk.yellow("[WARN] migration reset 會清空資料庫並重新建立所有資料")
      );

      const shouldReset = await this.askUserConfirmation(
        "是否要執行 'pnpm prisma migrate reset --force' 來修復問題？"
      );

      if (shouldReset) {
        console.log(chalk.yellow("[INFO] 正在執行 migration reset..."));
        try {
          execSync("pnpm prisma migrate reset --force", {
            stdio: "inherit",
            encoding: "utf8",
          });
          console.log(
            chalk.green("[INFO] Migration reset 完成，資料庫已重新建立")
          );
          return true;
        } catch (resetError: any) {
          console.error(
            chalk.red("[ERROR] Migration reset 失敗："),
            resetError.message
          );
          const continueAfterResetFail = await this.askUserConfirmation(
            "Migration reset 失敗，是否仍要嘗試啟動服務？"
          );
          return continueAfterResetFail;
        }
      } else {
        console.log(chalk.yellow("[INFO] 跳過 migration reset"));
        console.log(
          chalk.cyan(
            "[INFO] 您可以稍後手動執行：pnpm prisma migrate reset --force"
          )
        );
        const continueWithoutReset = await this.askUserConfirmation(
          "在不修復 migration 問題的情況下，是否仍要嘗試啟動服務？"
        );
        return continueWithoutReset;
      }
    }

    // 僅 exitCode !== 0 時才視為錯誤
    if (exitCode !== 0) {
      console.warn("[WARN] Prisma migrate status 執行時遇到問題。");
      if (stderr) {
        console.warn(`   錯誤訊息: ${stderr.trim().split("\n")[0]}`);
      } else {
        console.warn(`   Prisma 指令退出碼: ${exitCode}`);
      }
      const continueDespitePrismaError = await this.askUserConfirmation(
        "無法完全確認資料庫狀態，是否仍要嘗試啟動服務？"
      );
      if (!continueDespitePrismaError) {
        return false;
      }
      return true;
    }

    let migrationsPending = false;
    // 以 stdout 內容判斷 migration 狀態
    if (
      stdout.includes("Following migrations have not yet been applied:") ||
      stdout.includes("pending") ||
      stdout.includes("migrations are pending") ||
      stdout.includes("Drift detected")
    ) {
      migrationsPending = true;
      console.warn("[WARN] 資料庫結構可能不是最新的！");
      if (stdout.includes("Drift detected")) {
        console.warn(
          "  偵測到資料庫結構差異 (drift)。建議執行 'pnpm db:apply' 或 'pnpm prisma migrate dev --name <name>'。"
        );
      }
      if (
        stdout.includes("Following migrations have not yet been applied:") ||
        stdout.includes("pending") ||
        stdout.includes("migrations are pending")
      ) {
        console.warn(
          "  有待處理的 migration。建議執行 'pnpm db:apply' 或 'pnpm prisma migrate dev'."
        );
        // 新增互動：詢問是否要自動執行 migration
        const applyMigration = await this.askUserConfirmation(
          "偵測到有待處理的 migration，是否要立即執行 'pnpm db:apply' 以同步資料庫？"
        );
        if (applyMigration) {
          console.log("\n🔄 正在執行 'pnpm db:apply' ...");
          try {
            const {
              stdout: applyOut,
              stderr: applyErr,
              success: applySuccess,
            } = await this.execPrismaCommand(["migrate", "deploy"]);
            if (applySuccess) {
              console.log("✅ Migration 已成功套用！");
            } else {
              console.error("❌ Migration 套用失敗：", applyErr);
              const continueWithError = await this.askUserConfirmation(
                "Migration 執行失敗，是否仍要繼續啟動服務？"
              );
              if (!continueWithError) return false;
            }
          } catch (migrationError: any) {
            console.error(
              "❌ Migration 執行過程發生例外：",
              migrationError.message
            );
            const continueWithError = await this.askUserConfirmation(
              "Migration 執行過程發生例外，是否仍要繼續啟動服務？"
            );
            if (!continueWithError) return false;
          }
        }
      }
    } else if (stdout.includes("Database schema is up to date")) {
      console.log("[INFO] 資料庫結構已是最新狀態。");
    } else {
      console.log("[INFO] 未檢測到明確的資料庫遷移狀態，請手動確認。");
    }

    if (migrationsPending) {
      const continueWithPending = await this.askUserConfirmation(
        "資料庫結構可能不是最新的，是否仍要繼續啟動服務？"
      );
      if (!continueWithPending) {
        return false;
      }
    }

    return true;
  }

  private startNestJSServer(): void {
    console.log("[INFO] 準備啟動 NestJS 開發伺服器 (熱重載)...");
    const command = "npx";
    const args = ["nest", "start", "--watch"];
    console.log(`[INFO] 執行指令: ${command} ${args.join(" ")}`);
    console.log("[INFO] 初次啟動或大幅變更時需一些時間編譯...");
    console.log("[INFO] 熱重載已啟用，檔案修改時自動重新編譯與重啟伺服器");

    const nestProcess = spawn(command, args, {
      stdio: "inherit",
      cwd: process.cwd(),
      shell: process.platform === "win32",
      env: { ...process.env, NODE_NO_WARNINGS: "1" },
    });

    nestProcess.on("spawn", () => {
      console.log("[INFO] NestJS 伺服器程序已啟動，等待日誌輸出...");
    });

    nestProcess.on("close", (code) => {
      if (code !== 0) {
        console.error(`[ERROR] NestJS 伺服器異常退出，代碼: ${code}`);
      } else {
        console.log("[INFO] NestJS 伺服器已正常關閉。");
      }
      process.exit(code || 0);
    });

    nestProcess.on("error", (error) => {
      console.error(`[ERROR] 啟動 NestJS 伺服器時發生錯誤: ${error.message}`);
      process.exit(1);
    });

    const shutdown = () => {
      console.log("\n🛑 收到終止信號，正在嘗試優雅關閉 NestJS 服務器...");
      if (nestProcess && !nestProcess.killed) {
        const killed = nestProcess.kill("SIGINT");
        if (!killed) {
          console.warn("   發送 SIGINT 失敗，嘗試強制終止...");
          nestProcess.kill("SIGTERM");
        }
      }
      setTimeout(() => {
        console.log("   開發服務器退出。");
        process.exit(0);
      }, 2000);
    };

    process.on("SIGINT", shutdown);
    process.on("SIGTERM", shutdown);
  }

  public async run(): Promise<void> {
    console.clear();
    console.log("\x1b[36mHorizAI 後端啟動工具 v1.4\x1b[0m");
    console.log("啟動時間: \x1b[33m%s\x1b[0m", new Date().toISOString());

    const canProceedAfterDbCheck = await this.checkDatabaseStatus();
    if (!canProceedAfterDbCheck) {
      console.log("[INFO] 資料庫檢查未通過，用戶取消啟動。");
      process.exit(0);
    }

    console.log("\n----------------------------------------------");

    console.log(`[INFO] 檢查端口 ${this.defaultPort} 使用情況...`);
    const processInfo = await this.checkPort(this.defaultPort);

    if (!processInfo) {
      console.log(`[INFO] 端口 ${this.defaultPort} 可用。`);
      this.startNestJSServer();
    } else {
      console.warn(
        `[WARN] 檢測到進程 PID ${processInfo.pid} 使用端口 ${this.defaultPort}`
      );
      console.log(`  進程資訊: ${processInfo.command}`);

      const terminate = await this.askUserConfirmation(
        `是否要嘗試終止此進程 (PID: ${processInfo.pid}) 並釋放端口 ${this.defaultPort}？`
      );

      if (terminate) {
        console.log(`[INFO] 嘗試終止進程 ${processInfo.pid}...`);
        const killed = await this.killProcess(processInfo.pid);
        if (killed) {
          console.log(
            `[INFO] 進程 ${processInfo.pid} 已終止，端口 ${this.defaultPort} 可用。`
          );
          this.startNestJSServer();
        } else {
          console.error(`[ERROR] 無法自動終止進程 ${processInfo.pid}。`);
          console.error(`  請手動終止進程，或修改 port 設定後重試。`);
          process.exit(1);
        }
      } else {
        console.log("[INFO] 使用者取消終止進程，服務未啟動。");
        console.log(
          `  請手動終止進程 ${processInfo.pid}，或修改 defaultPort 使用其他端口。`
        );
        process.exit(0);
      }
    }
  }
}

(async () => {
  // 合併模式：支持 --port-check, --port-kill, --force 旗標
  const args = process.argv.slice(2);
  const checkPortOnly = args.includes("--port-check");
  const killPortOnly = args.includes("--port-kill");
  const forceKill = args.includes("--force");
  const portArg = args.find((a) => /^\d+$/.test(a));
  const starter = new DevStarter();
  if (portArg) starter.defaultPort = parseInt(portArg, 10);
  if (checkPortOnly) {
    console.log(`🔍 檢查端口 ${starter.defaultPort} 使用情況...`);
    const info = await starter.checkPort(starter.defaultPort);
    if (info) {
      console.log(`⚠️ 端口 ${starter.defaultPort} 被進程 PID ${info.pid} 使用`);
      process.exit(1);
    }
    console.log(`✅ 端口 ${starter.defaultPort} 可用`);
    process.exit(0);
  }
  if (killPortOnly) {
    console.log(`🔍 檢查端口 ${starter.defaultPort} 使用情況...`);
    const info = await starter.checkPort(starter.defaultPort);
    if (!info) {
      console.log(`✅ 端口 ${starter.defaultPort} 沒有被占用`);
      process.exit(0);
    }
    console.log(`⚠️ 進程 PID ${info.pid} 使用端口 ${starter.defaultPort}`);
    const shouldKill =
      forceKill ||
      (await starter.askUserConfirmation(`是否終止進程 ${info.pid}？`));
    if (shouldKill) {
      const ok = await starter.killProcess(info.pid);
      process.exit(ok ? 0 : 1);
    }
    process.exit(0);
  }
  // 默認：完整啟動流程
  await starter.run();
})().catch((error) => {
  console.error("執行腳本時發生錯誤:", error);
  process.exit(1);
});

export default DevStarter;
