#!/usr/bin/env ts-node
/**
 * 資料庫遷移工具 CLI 入口
 * 依據指令參數呼叫對應遷移流程
 *
 * 支援指令：status, list, migrate, quick-migrate
 *
 * <AUTHOR>
 */
import {
  fullMigration,
  migrateUsersSeparation,
  syncPermissions,
} from "./index";
import { MigrationManager } from "./migration-manager";

function printHelp() {
  console.log(`\nHorizAI SaaS 資料庫遷移工具 CLI\n`);
  console.log("可用指令:");
  console.log("  status                檢查資料庫狀態");
  console.log("  list                  列出所有可用的遷移步驟");
  // console.log('  health                執行資料庫健康檢查'); // 暫無 health 方法
  console.log("  migrate [--steps]     執行自訂遷移步驟");
  console.log(
    "  quick-migrate [--type] 快速遷移 (full, users-only, permissions-only)"
  );
  console.log("  --dry-run             預覽不執行");
  console.log("  --force               強制執行");
  console.log("  --skip-backup         跳過資料庫備份（開發環境推薦）");
  console.log("  -h, --help            顯示說明\n");
}

async function main() {
  const args = process.argv.slice(2);
  if (args.length === 0 || args.includes("-h") || args.includes("--help")) {
    printHelp();
    process.exit(0);
  }

  const dryRun = args.includes("--dry-run");
  const force = args.includes("--force");
  const skipBackup = args.includes("--skip-backup");

  try {
    switch (args[0]) {
      case "status": {
        console.log("\u001b[36m[執行] 檢查資料庫狀態...\u001b[0m");
        const manager = new MigrationManager({ dryRun, force });
        await manager.getStatus();
        console.log("\u001b[32m[完成] 資料庫狀態檢查結束\u001b[0m");
        break;
      }
      case "list": {
        console.log("\u001b[36m[執行] 列出所有可用的遷移步驟...\u001b[0m");
        const manager = new MigrationManager({ dryRun, force });
        await manager.listMigrationSteps();
        console.log("\u001b[32m[完成] 步驟列表輸出結束\u001b[0m");
        break;
      }
      case "migrate": {
        console.log("\u001b[36m[執行] 開始自訂遷移...\u001b[0m");
        const stepsArg = args.find((a) => a.startsWith("--steps"));
        let steps: string[] = [];
        if (stepsArg) {
          const idx = args.indexOf(stepsArg);
          const val = args[idx + 1] || "";
          steps = val
            .split(",")
            .map((s) => s.trim())
            .filter(Boolean);
        }
        const manager = new MigrationManager({ dryRun, force });
        await manager.executeMigration(steps.length ? steps : undefined);
        console.log("\u001b[32m[完成] 自訂遷移執行結束\u001b[0m");
        break;
      }
      case "quick-migrate": {
        const typeIdx = args.indexOf("--type");
        const type = typeIdx >= 0 ? args[typeIdx + 1] : "full";
        if (type === "users-only") {
          console.log("\u001b[36m[執行] 用戶資料表分離遷移...\u001b[0m");
          await migrateUsersSeparation({ dryRun, force });
          console.log("\u001b[32m[完成] 用戶資料表分離遷移結束\u001b[0m");
        } else if (type === "permissions-only") {
          console.log("\u001b[36m[執行] 權限同步...\u001b[0m");
          await syncPermissions({ dryRun, force });
          console.log("\u001b[32m[完成] 權限同步結束\u001b[0m");
        } else {
          console.log("\u001b[36m[執行] 完整遷移流程...\u001b[0m");
          await fullMigration({ dryRun, force, skipBackup });
          console.log("\u001b[32m[完成] 完整遷移流程結束\u001b[0m");
        }
        break;
      }
      default:
        printHelp();
        process.exit(1);
    }
    process.exit(0);
  } catch (err) {
    // 錯誤處理與日誌
    console.error(
      "\u001b[31m[遷移工具錯誤]\u001b[0m",
      err instanceof Error ? err.message : err
    );
    process.exit(1);
  }
}

main();
