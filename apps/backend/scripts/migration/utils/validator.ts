import { PrismaClient } from '@prisma/client';
import chalk from 'chalk';

export interface ValidationResult {
  success: boolean;
  errors: string[];
  warnings: string[];
  details: Record<string, any>;
}

export class DatabaseValidator {
  private prisma: PrismaClient;

  constructor() {
    this.prisma = new PrismaClient();
  }

  /**
   * 驗證資料庫連線
   */
  async validateConnection(): Promise<ValidationResult> {
    const result: ValidationResult = {
      success: false,
      errors: [],
      warnings: [],
      details: {}
    };

    try {
      await this.prisma.$connect();
      await this.prisma.$queryRaw`SELECT 1`;
      
      result.success = true;
      result.details.connection = 'OK';
      console.log(chalk.green('✅ 資料庫連線正常'));
    } catch (error) {
      result.errors.push(`資料庫連線失敗: ${error.message}`);
      console.error(chalk.red('❌ 資料庫連線失敗'), error);
    } finally {
      await this.prisma.$disconnect();
    }

    return result;
  }

  /**
   * 驗證用戶資料表分離
   */
  async validateUsersSeparation(): Promise<ValidationResult> {
    const result: ValidationResult = {
      success: false,
      errors: [],
      warnings: [],
      details: {}
    };

    try {
      // 檢查新資料表是否存在
      const systemUsersExists = await this.tableExists('system_users');
      const tenantUsersExists = await this.tableExists('tenant_users');
      const oldUsersExists = await this.tableExists('users');

      result.details.systemUsersExists = systemUsersExists;
      result.details.tenantUsersExists = tenantUsersExists;
      result.details.oldUsersExists = oldUsersExists;

      if (!systemUsersExists) {
        result.errors.push('system_users 資料表不存在');
      }

      if (!tenantUsersExists) {
        result.errors.push('tenant_users 資料表不存在');
      }

      if (oldUsersExists) {
        result.warnings.push('舊的 users 資料表仍然存在，建議移除');
      }

      // 檢查資料數量
      if (systemUsersExists) {
        const systemUsersCount = await this.prisma.system_users.count();
        result.details.systemUsersCount = systemUsersCount;
        console.log(chalk.blue(`📊 system_users 資料表有 ${systemUsersCount} 筆記錄`));
      }

      if (tenantUsersExists) {
        const tenantUsersCount = await this.prisma.tenant_users.count();
        result.details.tenantUsersCount = tenantUsersCount;
        console.log(chalk.blue(`📊 tenant_users 資料表有 ${tenantUsersCount} 筆記錄`));
      }

      // 檢查 refresh_tokens 資料表
      const refreshTokensValid = await this.validateRefreshTokens();
      result.details.refreshTokensValid = refreshTokensValid;

      if (!refreshTokensValid) {
        result.warnings.push('refresh_tokens 資料表可能需要更新');
      }

      result.success = result.errors.length === 0;

      if (result.success) {
        console.log(chalk.green('✅ 用戶資料表分離驗證通過'));
      } else {
        console.error(chalk.red('❌ 用戶資料表分離驗證失敗'));
      }
    } catch (error) {
      result.errors.push(`驗證過程發生錯誤: ${error.message}`);
      console.error(chalk.red('❌ 驗證過程發生錯誤'), error);
    }

    return result;
  }

  /**
   * 驗證權限同步
   */
  async validatePermissionsSync(): Promise<ValidationResult> {
    const result: ValidationResult = {
      success: false,
      errors: [],
      warnings: [],
      details: {}
    };

    try {
      // 檢查權限相關資料表
      const permissionsExists = await this.tableExists('permissions');
      const rolesExists = await this.tableExists('roles');
      const rolePermissionMappingExists = await this.tableExists('role_permission_mapping');

      result.details.permissionsExists = permissionsExists;
      result.details.rolesExists = rolesExists;
      result.details.rolePermissionMappingExists = rolePermissionMappingExists;

      if (!permissionsExists) {
        result.errors.push('permissions 資料表不存在');
      }

      if (!rolesExists) {
        result.errors.push('roles 資料表不存在');
      }

      if (!rolePermissionMappingExists) {
        result.errors.push('role_permission_mapping 資料表不存在');
      }

      // 檢查資料數量
      if (permissionsExists) {
        const permissionsCount = await this.prisma.permissions.count();
        result.details.permissionsCount = permissionsCount;
        console.log(chalk.blue(`📊 permissions 資料表有 ${permissionsCount} 筆記錄`));

        if (permissionsCount === 0) {
          result.warnings.push('permissions 資料表為空，可能需要執行權限同步');
        }
      }

      if (rolesExists) {
        const rolesCount = await this.prisma.roles.count();
        result.details.rolesCount = rolesCount;
        console.log(chalk.blue(`📊 roles 資料表有 ${rolesCount} 筆記錄`));

        if (rolesCount === 0) {
          result.warnings.push('roles 資料表為空，可能需要執行初始化');
        }
      }

      result.success = result.errors.length === 0;

      if (result.success) {
        console.log(chalk.green('✅ 權限同步驗證通過'));
      } else {
        console.error(chalk.red('❌ 權限同步驗證失敗'));
      }
    } catch (error) {
      result.errors.push(`驗證過程發生錯誤: ${error.message}`);
      console.error(chalk.red('❌ 驗證過程發生錯誤'), error);
    }

    return result;
  }

  /**
   * 驗證 Schema 狀態
   */
  async validateSchemaStatus(): Promise<ValidationResult> {
    const result: ValidationResult = {
      success: false,
      errors: [],
      warnings: [],
      details: {}
    };

    try {
      // 檢查 _prisma_migrations 資料表
      const migrationsExists = await this.tableExists('_prisma_migrations');
      result.details.migrationsExists = migrationsExists;

      if (!migrationsExists) {
        result.errors.push('_prisma_migrations 資料表不存在，可能需要初始化 Prisma');
      } else {
        // 檢查遷移狀態
        const migrations = await this.prisma.$queryRaw<any[]>`
          SELECT migration_name, finished_at, rolled_back_at 
          FROM "_prisma_migrations" 
          ORDER BY started_at DESC 
          LIMIT 10
        `;

        result.details.recentMigrations = migrations;
        
        const failedMigrations = migrations.filter(m => !m.finished_at && !m.rolled_back_at);
        if (failedMigrations.length > 0) {
          result.errors.push(`有 ${failedMigrations.length} 個遷移失敗`);
          result.details.failedMigrations = failedMigrations;
        }

        const rolledBackMigrations = migrations.filter(m => m.rolled_back_at);
        if (rolledBackMigrations.length > 0) {
          result.warnings.push(`有 ${rolledBackMigrations.length} 個遷移被回滾`);
          result.details.rolledBackMigrations = rolledBackMigrations;
        }

        console.log(chalk.blue(`📊 最近 ${migrations.length} 個遷移記錄已檢查`));
      }

      result.success = result.errors.length === 0;

      if (result.success) {
        console.log(chalk.green('✅ Schema 狀態驗證通過'));
      } else {
        console.error(chalk.red('❌ Schema 狀態驗證失敗'));
      }
    } catch (error) {
      result.errors.push(`驗證過程發生錯誤: ${error.message}`);
      console.error(chalk.red('❌ 驗證過程發生錯誤'), error);
    }

    return result;
  }

  /**
   * 執行完整的資料庫健康檢查
   */
  async performHealthCheck(): Promise<ValidationResult> {
    console.log(chalk.blue('🔍 開始執行資料庫健康檢查...'));

    const results = await Promise.all([
      this.validateConnection(),
      this.validateSchemaStatus(),
      this.validateUsersSeparation(),
      this.validatePermissionsSync()
    ]);

    const combinedResult: ValidationResult = {
      success: results.every(r => r.success),
      errors: results.flatMap(r => r.errors),
      warnings: results.flatMap(r => r.warnings),
      details: {
        connection: results[0].details,
        schema: results[1].details,
        usersSeparation: results[2].details,
        permissions: results[3].details
      }
    };

    // 輸出總結
    console.log(chalk.blue('\n📋 健康檢查總結:'));
    console.log(chalk.green(`✅ 成功項目: ${results.filter(r => r.success).length}/4`));
    
    if (combinedResult.errors.length > 0) {
      console.log(chalk.red(`❌ 錯誤: ${combinedResult.errors.length} 個`));
      combinedResult.errors.forEach(error => {
        console.log(chalk.red(`   • ${error}`));
      });
    }

    if (combinedResult.warnings.length > 0) {
      console.log(chalk.yellow(`⚠️  警告: ${combinedResult.warnings.length} 個`));
      combinedResult.warnings.forEach(warning => {
        console.log(chalk.yellow(`   • ${warning}`));
      });
    }

    return combinedResult;
  }

  /**
   * 檢查資料表是否存在
   */
  private async tableExists(tableName: string): Promise<boolean> {
    try {
      const result = await this.prisma.$queryRaw<any[]>`
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_schema = 'public' 
          AND table_name = ${tableName}
        );
      `;
      return result[0]?.exists || false;
    } catch (error) {
      console.error(chalk.red(`檢查資料表 ${tableName} 時發生錯誤:`), error);
      return false;
    }
  }

  /**
   * 驗證 refresh_tokens 資料表結構
   */
  private async validateRefreshTokens(): Promise<boolean> {
    try {
      // 檢查是否有新的欄位
      const columns = await this.prisma.$queryRaw<any[]>`
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name = 'refresh_tokens' 
        AND table_schema = 'public'
      `;

      const columnNames = columns.map(col => col.column_name);
      
      // 檢查必要的欄位
      const requiredColumns = ['systemUserId', 'tenantUserId', 'userType'];
      const hasNewColumns = requiredColumns.some(col => columnNames.includes(col));

      return hasNewColumns;
    } catch (error) {
      console.error(chalk.red('驗證 refresh_tokens 資料表時發生錯誤:'), error);
      return false;
    }
  }

  /**
   * 清理資源
   */
  async cleanup(): Promise<void> {
    await this.prisma.$disconnect();
  }
} 