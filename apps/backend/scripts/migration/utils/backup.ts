import { execSync } from 'child_process';
import * as fs from 'fs';
import * as path from 'path';
import * as zlib from 'zlib';
import chalk from 'chalk';

export interface BackupOptions {
  directory: string;
  filename?: string;
  compression: boolean;
  retentionDays: number;
}

export class BackupManager {
  private databaseUrl: string;

  constructor(databaseUrl: string) {
    this.databaseUrl = databaseUrl;
  }

  /**
   * 建立資料庫備份
   */
  async createBackup(options: BackupOptions): Promise<string> {
    console.log(chalk.blue('🔄 開始建立資料庫備份...'));

    // 確保備份目錄存在
    if (!fs.existsSync(options.directory)) {
      fs.mkdirSync(options.directory, { recursive: true });
    }

    // 生成備份檔案名稱
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = options.filename || `backup-${timestamp}.sql`;
    const backupPath = path.join(options.directory, filename);

    try {
      // 執行 pg_dump
      console.log(chalk.gray(`📁 備份檔案: ${backupPath}`));
      
      const command = `pg_dump "${this.databaseUrl}" --no-owner --no-privileges --clean --if-exists`;
      const backupData = execSync(command, { encoding: 'utf8' });

      if (options.compression) {
        // 壓縮備份
        const compressedPath = `${backupPath}.gz`;
        const compressed = zlib.gzipSync(backupData);
        fs.writeFileSync(compressedPath, compressed);
        console.log(chalk.green(`✅ 壓縮備份完成: ${compressedPath}`));
        
        // 清理舊備份
        await this.cleanOldBackups(options.directory, options.retentionDays);
        
        return compressedPath;
      } else {
        // 直接寫入備份
        fs.writeFileSync(backupPath, backupData);
        console.log(chalk.green(`✅ 備份完成: ${backupPath}`));
        
        // 清理舊備份
        await this.cleanOldBackups(options.directory, options.retentionDays);
        
        return backupPath;
      }
    } catch (error) {
      console.error(chalk.red('❌ 備份失敗:'), error);
      throw new Error(`備份失敗: ${error.message}`);
    }
  }

  /**
   * 還原資料庫備份
   */
  async restoreBackup(backupPath: string): Promise<void> {
    console.log(chalk.blue(`🔄 開始還原備份: ${backupPath}`));

    if (!fs.existsSync(backupPath)) {
      throw new Error(`備份檔案不存在: ${backupPath}`);
    }

    try {
      let backupData: string;

      if (backupPath.endsWith('.gz')) {
        // 解壓縮備份
        const compressed = fs.readFileSync(backupPath);
        backupData = zlib.gunzipSync(compressed).toString();
      } else {
        // 直接讀取備份
        backupData = fs.readFileSync(backupPath, 'utf8');
      }

      // 執行還原
      const command = `psql "${this.databaseUrl}"`;
      execSync(command, { input: backupData, encoding: 'utf8' });

      console.log(chalk.green('✅ 備份還原完成'));
    } catch (error) {
      console.error(chalk.red('❌ 還原失敗:'), error);
      throw new Error(`還原失敗: ${error.message}`);
    }
  }

  /**
   * 列出可用的備份檔案
   */
  listBackups(directory: string): string[] {
    if (!fs.existsSync(directory)) {
      return [];
    }

    const files = fs.readdirSync(directory);
    return files
      .filter(file => file.startsWith('backup-') && (file.endsWith('.sql') || file.endsWith('.sql.gz')))
      .sort()
      .reverse(); // 最新的在前面
  }

  /**
   * 清理舊的備份檔案
   */
  private async cleanOldBackups(directory: string, retentionDays: number): Promise<void> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

    const files = fs.readdirSync(directory);
    let deletedCount = 0;

    for (const file of files) {
      if (file.startsWith('backup-') && (file.endsWith('.sql') || file.endsWith('.sql.gz'))) {
        const filePath = path.join(directory, file);
        const stats = fs.statSync(filePath);

        if (stats.mtime < cutoffDate) {
          fs.unlinkSync(filePath);
          deletedCount++;
          console.log(chalk.gray(`🗑️  已刪除舊備份: ${file}`));
        }
      }
    }

    if (deletedCount > 0) {
      console.log(chalk.yellow(`🧹 已清理 ${deletedCount} 個舊備份檔案`));
    }
  }

  /**
   * 驗證備份檔案
   */
  async validateBackup(backupPath: string): Promise<boolean> {
    try {
      if (!fs.existsSync(backupPath)) {
        return false;
      }

      let content: string;
      if (backupPath.endsWith('.gz')) {
        const compressed = fs.readFileSync(backupPath);
        content = zlib.gunzipSync(compressed).toString();
      } else {
        content = fs.readFileSync(backupPath, 'utf8');
      }

      // 基本驗證：檢查是否包含 SQL 內容
      const hasCreateTable = content.includes('CREATE TABLE');
      const hasInsert = content.includes('INSERT INTO') || content.includes('COPY');
      
      return hasCreateTable || hasInsert;
    } catch (error) {
      console.error(chalk.red('備份驗證失敗:'), error);
      return false;
    }
  }

  /**
   * 取得備份檔案資訊
   */
  getBackupInfo(backupPath: string): { size: number; created: Date; compressed: boolean } | null {
    try {
      if (!fs.existsSync(backupPath)) {
        return null;
      }

      const stats = fs.statSync(backupPath);
      return {
        size: stats.size,
        created: stats.mtime,
        compressed: backupPath.endsWith('.gz')
      };
    } catch (error) {
      return null;
    }
  }
} 