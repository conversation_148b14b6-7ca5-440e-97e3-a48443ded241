import { PrismaClient } from "@prisma/client";
import { execSync } from "child_process";
import * as fs from "fs";
import * as path from "path";
import chalk from "chalk";

interface MigrationStep {
  id: string;
  name: string;
  description: string;
  execute: () => Promise<void>;
  rollback?: () => Promise<void>;
  validate?: () => Promise<boolean>;
}

interface MigrationConfig {
  backupBeforeMigration: boolean;
  validateAfterMigration: boolean;
  dryRun: boolean;
  force: boolean;
  environment: "development" | "staging" | "production";
}

export class MigrationManager {
  private prisma: PrismaClient;
  private config: MigrationConfig;
  private migrationSteps: Map<string, MigrationStep> = new Map();

  constructor(config: Partial<MigrationConfig> = {}) {
    this.prisma = new PrismaClient();
    this.config = {
      backupBeforeMigration: true,
      validateAfterMigration: true,
      dryRun: false,
      force: false,
      environment: "development",
      ...config,
    };

    this.registerMigrationSteps();
  }

  private registerMigrationSteps() {
    // 註冊所有可用的遷移步驟
    this.migrationSteps.set("users-separation", {
      id: "users-separation",
      name: "用戶資料表分離",
      description: "將舊的 users 資料表分離為 system_users 和 tenant_users",
      execute: this.migrateUsersSeparation.bind(this),
      validate: this.validateUsersSeparation.bind(this),
      rollback: this.rollbackUsersSeparation.bind(this),
    });

    this.migrationSteps.set("permissions-sync", {
      id: "permissions-sync",
      name: "權限同步",
      description: "同步權限定義到資料庫",
      execute: this.syncPermissions.bind(this),
      validate: this.validatePermissions.bind(this),
    });

    this.migrationSteps.set("schema-update", {
      id: "schema-update",
      name: "Schema 更新",
      description: "應用 Prisma schema 變更",
      execute: this.updateSchema.bind(this),
      validate: this.validateSchema.bind(this),
    });
  }

  async executeMigration(stepIds: string[] = []): Promise<void> {
    console.log(chalk.blue("🚀 開始執行資料庫遷移..."));
    console.log(chalk.gray(`環境: ${this.config.environment}`));
    console.log(chalk.gray(`乾跑模式: ${this.config.dryRun ? "是" : "否"}`));

    try {
      // 1. 檢查前置條件
      await this.checkPrerequisites();

      // 2. 備份資料庫（如果需要）
      if (this.config.backupBeforeMigration && !this.config.dryRun) {
        await this.createBackup();
      }

      // 3. 執行遷移步驟
      const stepsToExecute =
        stepIds.length > 0 ? stepIds : Array.from(this.migrationSteps.keys());

      for (const stepId of stepsToExecute) {
        await this.executeStep(stepId);
      }

      // 4. 驗證遷移結果
      if (this.config.validateAfterMigration) {
        await this.validateMigration(stepsToExecute);
      }

      console.log(chalk.green("✅ 遷移完成！"));
    } catch (error) {
      console.error(chalk.red("❌ 遷移失敗:"), error);
      throw error;
    } finally {
      await this.prisma.$disconnect();
    }
  }

  private async checkPrerequisites(): Promise<void> {
    console.log(chalk.blue("🔍 檢查前置條件..."));

    // 檢查資料庫連線
    try {
      await this.prisma.$connect();
      console.log(chalk.green("✓ 資料庫連線正常"));
    } catch (error) {
      throw new Error(`資料庫連線失敗: ${error}`);
    }

    // 檢查 Prisma CLI
    try {
      execSync("npx prisma --version", { stdio: "pipe" });
      console.log(chalk.green("✓ Prisma CLI 可用"));
    } catch (error) {
      throw new Error("Prisma CLI 不可用，請確保已安裝 Prisma");
    }

    // 檢查環境變數
    if (!process.env.DATABASE_URL) {
      throw new Error("DATABASE_URL 環境變數未設定");
    }
    console.log(chalk.green("✓ 環境變數檢查通過"));
  }

  private async createBackup(): Promise<string> {
    console.log(chalk.blue("💾 建立資料庫備份..."));

    const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
    const backupDir = path.join(__dirname, "../../backups");
    const backupFile = path.join(backupDir, `backup-${timestamp}.sql`);

    // 確保備份目錄存在
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir, { recursive: true });
    }

    try {
      // 使用 pg_dump 建立備份
      const dbUrl = new URL(process.env.DATABASE_URL!);
      const command = `pg_dump -h ${dbUrl.hostname} -p ${dbUrl.port} -U ${dbUrl.username} -d ${dbUrl.pathname.slice(1)} -f ${backupFile}`;

      execSync(command, {
        stdio: "pipe",
        env: { ...process.env, PGPASSWORD: dbUrl.password },
      });

      console.log(chalk.green(`✓ 備份已建立: ${backupFile}`));
      return backupFile;
    } catch (error) {
      throw new Error(`備份建立失敗: ${error}`);
    }
  }

  private async executeStep(stepId: string): Promise<void> {
    const step = this.migrationSteps.get(stepId);
    if (!step) {
      throw new Error(`未知的遷移步驟: ${stepId}`);
    }

    console.log(chalk.blue(`📋 執行步驟: ${step.name}`));
    console.log(chalk.gray(`   ${step.description}`));

    if (this.config.dryRun) {
      console.log(chalk.yellow("   (乾跑模式 - 跳過實際執行)"));
      return;
    }

    try {
      await step.execute();
      console.log(chalk.green(`✓ 步驟完成: ${step.name}`));
    } catch (error) {
      console.error(chalk.red(`✗ 步驟失敗: ${step.name}`), error);
      throw error;
    }
  }

  private async validateMigration(stepIds: string[]): Promise<void> {
    console.log(chalk.blue("🔍 驗證遷移結果..."));

    for (const stepId of stepIds) {
      const step = this.migrationSteps.get(stepId);
      if (step?.validate) {
        try {
          const isValid = await step.validate();
          if (isValid) {
            console.log(chalk.green(`✓ 驗證通過: ${step.name}`));
          } else {
            throw new Error(`驗證失敗: ${step.name}`);
          }
        } catch (error) {
          console.error(chalk.red(`✗ 驗證失敗: ${step.name}`), error);
          throw error;
        }
      }
    }
  }

  // 具體的遷移步驟實作
  private async migrateUsersSeparation(): Promise<void> {
    console.log("  正在遷移用戶資料...");

    // 檢查是否存在舊的 users 資料表
    const hasOldUsersTable = await this.checkTableExists("users");
    if (!hasOldUsersTable) {
      console.log(chalk.yellow("  舊的 users 資料表不存在，跳過遷移"));
      return;
    }

    // 遷移系統使用者
    const systemUsers = await this.prisma.$queryRaw`
      SELECT * FROM users WHERE role IN ('SUPER_ADMIN', 'SYSTEM_ADMIN')
    `;

    for (const user of systemUsers as any[]) {
      await this.prisma.system_users.upsert({
        where: { email: user.email },
        update: {},
        create: {
          id: user.id,
          email: user.email,
          password: user.password,
          name: user.name,
          role: user.role,
          status: user.status || "active",
          avatar: user.avatar,
          phone: user.phone,
          lastLoginAt: user.lastLoginAt,
          lastLoginIp: user.lastLoginIp,
          createdAt: user.createdAt,
          updatedAt: user.updatedAt,
        },
      });
    }

    // 遷移租戶使用者
    const tenantUsers = await this.prisma.$queryRaw`
      SELECT * FROM users WHERE role IN ('TENANT_ADMIN', 'TENANT_USER')
    `;

    for (const user of tenantUsers as any[]) {
      await this.prisma.tenant_users.upsert({
        where: { email: user.email },
        update: {},
        create: {
          id: user.id,
          email: user.email,
          password: user.password,
          name: user.name,
          tenantId: user.tenantId,
          role: user.role,
          status: "ACTIVE",
          avatar: user.avatar,
          phone: user.phone,
          title: user.title,
          department: user.department,
          lastLoginAt: user.lastLoginAt,
          lastLoginIp: user.lastLoginIp,
          createdAt: user.createdAt,
          updatedAt: user.updatedAt,
        },
      });
    }

    console.log(`  已遷移 ${(systemUsers as any[]).length} 個系統使用者`);
    console.log(`  已遷移 ${(tenantUsers as any[]).length} 個租戶使用者`);
  }

  private async validateUsersSeparation(): Promise<boolean> {
    // 檢查新資料表是否有資料
    const systemUsersCount = await this.prisma.system_users.count();
    const tenantUsersCount = await this.prisma.tenant_users.count();

    console.log(`  system_users: ${systemUsersCount} 筆記錄`);
    console.log(`  tenant_users: ${tenantUsersCount} 筆記錄`);

    // 檢查是否存在舊的 users 資料表
    const hasOldUsersTable = await this.checkTableExists("users");

    // 如果舊表不存在，表示沒有數據需要遷移，這是正常的
    if (!hasOldUsersTable) {
      return true;
    }

    // 如果舊表存在，則檢查是否成功遷移了數據
    return systemUsersCount > 0 || tenantUsersCount > 0;
  }

  private async rollbackUsersSeparation(): Promise<void> {
    console.log("  回滾用戶資料分離...");
    // 實作回滾邏輯（如果需要）
    console.log(chalk.yellow("  回滾功能尚未實作"));
  }

  private async syncPermissions(): Promise<void> {
    console.log("  同步權限定義...");
    try {
      execSync("npx ts-node prisma/templates/permission-sync.ts --force", {
        stdio: "inherit",
        cwd: path.join(__dirname, "../.."),
      });
    } catch (error) {
      throw new Error(`權限同步失敗: ${error}`);
    }
  }

  private async validatePermissions(): Promise<boolean> {
    const permissionsCount = await this.prisma.permissions.count();
    console.log(`  permissions: ${permissionsCount} 筆記錄`);
    return permissionsCount > 0;
  }

  private async updateSchema(): Promise<void> {
    console.log("  應用 Schema 變更...");
    try {
      execSync("npx prisma migrate deploy", {
        stdio: "inherit",
        cwd: path.join(__dirname, "../.."),
      });
    } catch (error) {
      throw new Error(`Schema 更新失敗: ${error}`);
    }
  }

  private async validateSchema(): Promise<boolean> {
    try {
      execSync("npx prisma migrate status", {
        stdio: "pipe",
        cwd: path.join(__dirname, "../.."),
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  private async checkTableExists(tableName: string): Promise<boolean> {
    try {
      const result = await this.prisma.$queryRaw`
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_schema = 'public' 
          AND table_name = ${tableName}
        );
      `;
      return (result as any[])[0]?.exists || false;
    } catch (error) {
      return false;
    }
  }

  // 公開方法
  async listMigrationSteps(): Promise<void> {
    console.log(chalk.blue("📋 可用的遷移步驟:"));
    for (const [id, step] of this.migrationSteps) {
      console.log(chalk.green(`  ${id}: ${step.name}`));
      console.log(chalk.gray(`    ${step.description}`));
    }
  }

  async getStatus(): Promise<void> {
    console.log(chalk.blue("📊 資料庫狀態:"));

    // 檢查資料表存在性
    const tables = ["users", "system_users", "tenant_users", "permissions"];
    for (const table of tables) {
      const exists = await this.checkTableExists(table);
      const status = exists ? chalk.green("存在") : chalk.red("不存在");
      console.log(`  ${table}: ${status}`);
    }

    // 檢查資料數量
    try {
      const systemUsersCount = await this.prisma.system_users.count();
      const tenantUsersCount = await this.prisma.tenant_users.count();
      const permissionsCount = await this.prisma.permissions.count();

      console.log(chalk.blue("\n📈 資料統計:"));
      console.log(`  system_users: ${systemUsersCount} 筆`);
      console.log(`  tenant_users: ${tenantUsersCount} 筆`);
      console.log(`  permissions: ${permissionsCount} 筆`);
    } catch (error) {
      console.log(chalk.yellow("  無法取得資料統計"));
    }
  }
}
