import * as path from 'path';

export interface DatabaseConfig {
  url: string;
  host?: string;
  port?: number;
  username?: string;
  password?: string;
  database?: string;
}

export interface BackupConfig {
  enabled: boolean;
  directory: string;
  retentionDays: number;
  compression: boolean;
}

export interface MigrationEnvironmentConfig {
  database: DatabaseConfig;
  backup: BackupConfig;
  validation: {
    enabled: boolean;
    strict: boolean;
  };
  logging: {
    level: 'debug' | 'info' | 'warn' | 'error';
    file?: string;
  };
}

export const migrationConfig: Record<string, MigrationEnvironmentConfig> = {
  development: {
    database: {
      url: process.env.DATABASE_URL || 'postgresql://localhost:5432/horizai_dev'
    },
    backup: {
      enabled: true,
      directory: path.join(__dirname, '../../backups/development'),
      retentionDays: 7,
      compression: false
    },
    validation: {
      enabled: true,
      strict: false
    },
    logging: {
      level: 'debug'
    }
  },

  staging: {
    database: {
      url: process.env.DATABASE_URL || 'postgresql://localhost:5432/horizai_staging'
    },
    backup: {
      enabled: true,
      directory: path.join(__dirname, '../../backups/staging'),
      retentionDays: 14,
      compression: true
    },
    validation: {
      enabled: true,
      strict: true
    },
    logging: {
      level: 'info',
      file: path.join(__dirname, '../../logs/migration-staging.log')
    }
  },

  production: {
    database: {
      url: process.env.DATABASE_URL || ''
    },
    backup: {
      enabled: true,
      directory: path.join(__dirname, '../../backups/production'),
      retentionDays: 30,
      compression: true
    },
    validation: {
      enabled: true,
      strict: true
    },
    logging: {
      level: 'warn',
      file: path.join(__dirname, '../../logs/migration-production.log')
    }
  }
};

/**
 * 取得指定環境的配置
 */
export function getEnvironmentConfig(environment: string): MigrationEnvironmentConfig {
  const config = migrationConfig[environment];
  if (!config) {
    throw new Error(`未找到環境配置: ${environment}`);
  }
  return config;
}

/**
 * 驗證配置是否有效
 */
export function validateConfig(config: MigrationEnvironmentConfig): boolean {
  // 檢查資料庫 URL
  if (!config.database.url) {
    throw new Error('資料庫 URL 未設定');
  }

  // 檢查備份目錄
  if (config.backup.enabled && !config.backup.directory) {
    throw new Error('備份目錄未設定');
  }

  return true;
}

/**
 * 預設的遷移步驟配置
 */
export const migrationStepsConfig = {
  'users-separation': {
    dependencies: [], // 沒有依賴
    timeout: 300000, // 5 分鐘
    retries: 1
  },
  'permissions-sync': {
    dependencies: [], // 沒有依賴
    timeout: 120000, // 2 分鐘
    retries: 2
  },
  'schema-update': {
    dependencies: [], // 沒有依賴
    timeout: 600000, // 10 分鐘
    retries: 0 // Schema 更新不重試
  }
};

/**
 * 取得遷移步驟配置
 */
export function getStepConfig(stepId: string) {
  return migrationStepsConfig[stepId as keyof typeof migrationStepsConfig] || {
    dependencies: [],
    timeout: 300000,
    retries: 1
  };
} 