import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

async function checkBotProvider() {
  const botId = '61776b43-a5ed-4707-85e2-d93cd23e76a9';
  
  try {
    const bot = await prisma.ai_bots.findUnique({
      where: { id: botId },
      include: {
        ai_key: true,
        ai_model: true
      }
    });

    if (!bot) {
      console.log('❌ Bot not found');
      return;
    }

    console.log('🤖 Bot Information:');
    console.log('ID:', bot.id);
    console.log('Name:', bot.name);
    console.log('Provider Type:', bot.provider_type);
    console.log('Key ID:', bot.key_id);
    
    if (bot.key) {
      console.log('\n🔑 API Key Information:');
      console.log('Key ID:', bot.key.id);
      console.log('Key Name:', bot.key.name);
      console.log('Key Provider:', bot.key.provider);
      console.log('Key Enabled:', bot.key.is_enabled);
    } else {
      console.log('❌ No API key associated');
    }

    if (bot.model) {
      console.log('\n🧠 Model Information:');
      console.log('Model ID:', bot.model.id);
      console.log('Model Name:', bot.model.model_name);
      console.log('Model Provider:', bot.model.provider);
    } else {
      console.log('❌ No model associated');
    }

    // 檢查是否有不一致
    if (bot.key && bot.key.provider !== getExpectedProvider(bot.provider_type)) {
      console.log('\n⚠️  MISMATCH DETECTED!');
      console.log('Bot provider_type:', bot.provider_type);
      console.log('Key provider:', bot.key.provider);
      console.log('Expected key provider for this bot:', getExpectedProvider(bot.provider_type));
    } else {
      console.log('\n✅ Provider types are consistent');
    }

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

function getExpectedProvider(providerType: string): string {
  switch (providerType) {
    case 'OPENAI':
      return 'openai';
    case 'CLAUDE':
      return 'anthropic';
    case 'GEMINI':
      return 'google-gemini';
    case 'OPENAI_COMPATIBLE':
      return 'openai-compatible';
    default:
      return 'unknown';
  }
}

checkBotProvider();
