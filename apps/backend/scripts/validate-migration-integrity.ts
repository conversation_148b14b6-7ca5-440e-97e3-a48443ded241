#!/usr/bin/env ts-node

/**
 * 資料完整性檢查腳本
 * 驗證使用者遷移後的資料完整性
 * 
 * 執行方式: pnpm tsx scripts/validate-migration-integrity.ts
 */

import { PrismaClient } from '@prisma/client';
import { Logger } from '@nestjs/common';

const prisma = new PrismaClient();
const logger = new Logger('MigrationValidation');

interface ValidationResult {
  passed: boolean;
  issues: string[];
  warnings: string[];
  summary: {
    originalUsers: number;
    systemUsers: number;
    tenantUsers: number;
    refreshTokens: {
      total: number;
      legacy: number;
      system: number;
      tenant: number;
    };
  };
}

/**
 * 檢查電子郵件唯一性
 */
async function checkEmailUniqueness(): Promise<string[]> {
  const issues: string[] = [];
  
  // 檢查 system_users 電子郵件重複
  const systemEmailDuplicates = await prisma.$queryRaw<{ email: string; count: bigint }[]>`
    SELECT email, COUNT(*) as count
    FROM system_users
    GROUP BY email
    HAVING COUNT(*) > 1
  `;
  
  if (systemEmailDuplicates.length > 0) {
    systemEmailDuplicates.forEach(dup => {
      issues.push(`系統使用者電子郵件重複: ${dup.email} (${dup.count} 次)`);
    });
  }
  
  // 檢查 tenant_users 電子郵件重複
  const tenantEmailDuplicates = await prisma.$queryRaw<{ email: string; count: bigint }[]>`
    SELECT email, COUNT(*) as count
    FROM tenant_users
    GROUP BY email
    HAVING COUNT(*) > 1
  `;
  
  if (tenantEmailDuplicates.length > 0) {
    tenantEmailDuplicates.forEach(dup => {
      issues.push(`租戶使用者電子郵件重複: ${dup.email} (${dup.count} 次)`);
    });
  }
  
  // 檢查跨表電子郵件重複
  const crossTableDuplicates = await prisma.$queryRaw<{ email: string }[]>`
    SELECT su.email
    FROM system_users su
    INNER JOIN tenant_users tu ON su.email = tu.email
  `;
  
  if (crossTableDuplicates.length > 0) {
    crossTableDuplicates.forEach(dup => {
      issues.push(`跨表電子郵件重複: ${dup.email} (同時存在於 system_users 和 tenant_users)`);
    });
  }
  
  return issues;
}

/**
 * 檢查外鍵關聯完整性
 */
async function checkForeignKeyIntegrity(): Promise<string[]> {
  const issues: string[] = [];
  
  // 檢查 tenant_users 的 tenantId 關聯
  const invalidTenantRefs = await prisma.$queryRaw<{ id: string; tenantId: string }[]>`
    SELECT tu.id, tu."tenantId"
    FROM tenant_users tu
    LEFT JOIN tenants t ON tu."tenantId" = t.id
    WHERE t.id IS NULL
  `;
  
  if (invalidTenantRefs.length > 0) {
    invalidTenantRefs.forEach(ref => {
      issues.push(`租戶使用者 ${ref.id} 引用了不存在的租戶 ${ref.tenantId}`);
    });
  }
  
  // 檢查 tenant_users 的 invitedBy 關聯
  const invalidInviterRefs = await prisma.$queryRaw<{ id: string; invitedBy: string }[]>`
    SELECT tu.id, tu."invitedBy"
    FROM tenant_users tu
    LEFT JOIN tenant_users inviter ON tu."invitedBy" = inviter.id
    WHERE tu."invitedBy" IS NOT NULL AND inviter.id IS NULL
  `;
  
  if (invalidInviterRefs.length > 0) {
    invalidInviterRefs.forEach(ref => {
      issues.push(`租戶使用者 ${ref.id} 的邀請人 ${ref.invitedBy} 不存在`);
    });
  }
  
  return issues;
}

/**
 * 檢查 RefreshToken 關聯
 */
async function checkRefreshTokenIntegrity(): Promise<{ issues: string[]; summary: any }> {
  const issues: string[] = [];
  
  // 統計 refresh token 類型分布
  const tokenStats = await prisma.$queryRaw<{ userType: string; count: bigint }[]>`
    SELECT "userType", COUNT(*) as count
    FROM refresh_tokens
    GROUP BY "userType"
  `;
  
  const summary = {
    total: 0,
    legacy: 0,
    system: 0,
    tenant: 0,
  };
  
  tokenStats.forEach(stat => {
    const count = Number(stat.count);
    summary.total += count;
    
    switch (stat.userType) {
      case 'legacy':
        summary.legacy = count;
        break;
      case 'system':
        summary.system = count;
        break;
      case 'tenant':
        summary.tenant = count;
        break;
    }
  });
  
  // 檢查 system refresh token 關聯
  const invalidSystemTokens = await prisma.$queryRaw<{ id: string; systemUserId: string }[]>`
    SELECT rt.id, rt."systemUserId"
    FROM refresh_tokens rt
    LEFT JOIN system_users su ON rt."systemUserId" = su.id
    WHERE rt."userType" = 'system' AND su.id IS NULL
  `;
  
  if (invalidSystemTokens.length > 0) {
    invalidSystemTokens.forEach(token => {
      issues.push(`系統 refresh token ${token.id} 引用了不存在的系統使用者 ${token.systemUserId}`);
    });
  }
  
  // 檢查 tenant refresh token 關聯
  const invalidTenantTokens = await prisma.$queryRaw<{ id: string; tenantUserId: string }[]>`
    SELECT rt.id, rt."tenantUserId"
    FROM refresh_tokens rt
    LEFT JOIN tenant_users tu ON rt."tenantUserId" = tu.id
    WHERE rt."userType" = 'tenant' AND tu.id IS NULL
  `;
  
  if (invalidTenantTokens.length > 0) {
    invalidTenantTokens.forEach(token => {
      issues.push(`租戶 refresh token ${token.id} 引用了不存在的租戶使用者 ${token.tenantUserId}`);
    });
  }
  
  return { issues, summary };
}

/**
 * 檢查資料數量一致性
 */
async function checkDataConsistency(): Promise<{ issues: string[]; summary: any }> {
  const issues: string[] = [];
  
  // 獲取原始使用者數量
  const originalUserCount = await prisma.user.count();
  
  // 獲取新表中的使用者數量
  const systemUserCount = await prisma.systemUser.count();
  const tenantUserCount = await prisma.tenantUser.count();
  const totalMigratedUsers = systemUserCount + tenantUserCount;
  
  const summary = {
    originalUsers: originalUserCount,
    systemUsers: systemUserCount,
    tenantUsers: tenantUserCount,
  };
  
  if (originalUserCount !== totalMigratedUsers) {
    issues.push(
      `使用者數量不一致: 原始 ${originalUserCount}, 遷移後 ${totalMigratedUsers} ` +
      `(系統: ${systemUserCount}, 租戶: ${tenantUserCount})`
    );
  }
  
  return { issues, summary };
}

/**
 * 檢查租戶唯一性約束
 */
async function checkTenantUniqueness(): Promise<string[]> {
  const issues: string[] = [];
  
  // 檢查 domain 唯一性
  const domainDuplicates = await prisma.$queryRaw<{ domain: string; count: bigint }[]>`
    SELECT domain, COUNT(*) as count
    FROM tenants
    WHERE domain IS NOT NULL
    GROUP BY domain
    HAVING COUNT(*) > 1
  `;
  
  if (domainDuplicates.length > 0) {
    domainDuplicates.forEach(dup => {
      issues.push(`租戶網域重複: ${dup.domain} (${dup.count} 次)`);
    });
  }
  
  // 檢查名稱 + 國家組合唯一性
  const nameCountryDuplicates = await prisma.$queryRaw<{ name: string; country: string; count: bigint }[]>`
    SELECT name, country, COUNT(*) as count
    FROM tenants
    GROUP BY name, country
    HAVING COUNT(*) > 1
  `;
  
  if (nameCountryDuplicates.length > 0) {
    nameCountryDuplicates.forEach(dup => {
      issues.push(`租戶名稱+國家組合重複: ${dup.name} (${dup.country}) (${dup.count} 次)`);
    });
  }
  
  return issues;
}

/**
 * 主要驗證函式
 */
async function validateMigration(): Promise<ValidationResult> {
  const result: ValidationResult = {
    passed: true,
    issues: [],
    warnings: [],
    summary: {
      originalUsers: 0,
      systemUsers: 0,
      tenantUsers: 0,
      refreshTokens: {
        total: 0,
        legacy: 0,
        system: 0,
        tenant: 0,
      },
    },
  };

  try {
    logger.log('🔍 開始資料完整性檢查...');
    
    // 1. 檢查電子郵件唯一性
    logger.log('📧 檢查電子郵件唯一性...');
    const emailIssues = await checkEmailUniqueness();
    result.issues.push(...emailIssues);
    
    // 2. 檢查外鍵關聯完整性
    logger.log('🔗 檢查外鍵關聯完整性...');
    const foreignKeyIssues = await checkForeignKeyIntegrity();
    result.issues.push(...foreignKeyIssues);
    
    // 3. 檢查 RefreshToken 關聯
    logger.log('🎫 檢查 RefreshToken 關聯...');
    const { issues: tokenIssues, summary: tokenSummary } = await checkRefreshTokenIntegrity();
    result.issues.push(...tokenIssues);
    result.summary.refreshTokens = tokenSummary;
    
    // 4. 檢查資料數量一致性
    logger.log('📊 檢查資料數量一致性...');
    const { issues: consistencyIssues, summary: consistencySummary } = await checkDataConsistency();
    result.issues.push(...consistencyIssues);
    Object.assign(result.summary, consistencySummary);
    
    // 5. 檢查租戶唯一性約束
    logger.log('🏢 檢查租戶唯一性約束...');
    const tenantIssues = await checkTenantUniqueness();
    result.issues.push(...tenantIssues);
    
    // 檢查是否有需要注意的情況
    if (result.summary.refreshTokens.legacy > 0) {
      result.warnings.push(`仍有 ${result.summary.refreshTokens.legacy} 個 legacy refresh token 未遷移`);
    }
    
    result.passed = result.issues.length === 0;
    
    logger.log('✅ 資料完整性檢查完成');
    
  } catch (error) {
    logger.error(`❌ 驗證過程中發生錯誤: ${error.message}`);
    result.issues.push(`驗證過程錯誤: ${error.message}`);
    result.passed = false;
  }

  return result;
}

/**
 * 產生驗證報告
 */
function generateValidationReport(result: ValidationResult) {
  logger.log('\n📋 驗證報告:');
  logger.log(`檢查結果: ${result.passed ? '✅ 通過' : '❌ 失敗'}`);
  
  logger.log('\n📊 資料摘要:');
  logger.log(`原始使用者: ${result.summary.originalUsers}`);
  logger.log(`系統使用者: ${result.summary.systemUsers}`);
  logger.log(`租戶使用者: ${result.summary.tenantUsers}`);
  logger.log(`RefreshToken 總數: ${result.summary.refreshTokens.total}`);
  logger.log(`  - Legacy: ${result.summary.refreshTokens.legacy}`);
  logger.log(`  - System: ${result.summary.refreshTokens.system}`);
  logger.log(`  - Tenant: ${result.summary.refreshTokens.tenant}`);
  
  if (result.warnings.length > 0) {
    logger.warn('\n⚠️ 警告:');
    result.warnings.forEach(warning => logger.warn(`  - ${warning}`));
  }
  
  if (result.issues.length > 0) {
    logger.error('\n❌ 發現問題:');
    result.issues.forEach(issue => logger.error(`  - ${issue}`));
  } else {
    logger.log('\n🎉 所有檢查都通過！');
  }
}

/**
 * 主函式
 */
async function main() {
  try {
    const result = await validateMigration();
    generateValidationReport(result);
    
    if (!result.passed) {
      process.exit(1);
    }
    
  } catch (error) {
    logger.error(`❌ 驗證失敗: ${error.message}`);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// 執行驗證
if (require.main === module) {
  main();
}

export { validateMigration, ValidationResult };
