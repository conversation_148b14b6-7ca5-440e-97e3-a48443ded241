# 權限同步報告

- **產生時間**: 2025-05-31T00:32:46.504Z
- **版本**: unknown
- **模式**: sync

## 統計摘要
- 總權限數: 32
- 按範圍:
  - SYSTEM: 20
  - TENANT: 8
  - WORKSPACE: 4
- 按分類:
  - system_management: 1
  - user_management: 10
  - tenant_management: 6
  - member_management: 4
  - ai_management: 9
  - log_management: 2

## 權限列表
- `Permission` `create` [SYSTEM/user_management]
- `Permission` `delete` [SYSTEM/user_management]
- `Permission` `manage` [SYSTEM/user_management]
- `Permission` `read` [SYSTEM/user_management]
- `Permission` `update` [SYSTEM/user_management]
- `Role` `create` [SYSTEM/user_management]
- `Role` `delete` [SYSTEM/user_management]
- `Role` `read` [SYSTEM/user_management]
- `Role` `update` [SYSTEM/user_management]
- `User` `manage` [SYSTEM/user_management]
- `Tenant` `manage` [SYSTEM/tenant_management]
- `Tenant` `read` [SYSTEM/tenant_management]
- `Workspace` `create` [TENANT/tenant_management]
- `Workspace` `delete` [TENANT/tenant_management]
- `Workspace` `read` [TENANT/tenant_management]
- `Workspace` `update` [TENANT/tenant_management]
- `Dashboard` `read` [SYSTEM/system_management]
- `User` `create` [WORKSPACE/member_management]
- `User` `delete` [WORKSPACE/member_management]
- `User` `read` [WORKSPACE/member_management]
- `User` `update` [WORKSPACE/member_management]
- `LineMessageLog` `read` [SYSTEM/log_management]
- `SystemLog` `read` [SYSTEM/log_management]
- `AiModel` `create` [SYSTEM/ai_management]
- `AiModel` `delete` [SYSTEM/ai_management]
- `AiModel` `manage` [SYSTEM/ai_management]
- `AiModel` `read` [SYSTEM/ai_management]
- `AiModel` `update` [SYSTEM/ai_management]
- `LineBot` `create` [TENANT/ai_management]
- `LineBot` `delete` [TENANT/ai_management]
- `LineBot` `read` [TENANT/ai_management]
- `LineBot` `update` [TENANT/ai_management]