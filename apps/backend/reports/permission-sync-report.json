{"timestamp": "2025-05-31T00:40:35.930Z", "version": "unknown", "mode": "dry-run", "scanResult": {"permissions": [{"action": "create", "subject": "Permission", "description": "允許建立新的權限定義", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/permissions/permissions.controller.ts", "lineNumber": 135, "name": "建立權限", "zone": "admin", "category": "user_management", "scope": "SYSTEM"}, {"action": "delete", "subject": "Permission", "description": "允許刪除權限定義", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/permissions/permissions.controller.ts", "lineNumber": 181, "name": "刪除權限", "zone": "admin", "category": "user_management", "scope": "SYSTEM"}, {"action": "manage", "subject": "Permission", "description": "完整管理權限的所有操作", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/permissions/permissions.controller.ts", "lineNumber": 195, "name": "管理權限", "zone": "admin", "category": "user_management", "scope": "SYSTEM"}, {"action": "read", "subject": "Permission", "description": "允許查看系統權限定義和設定", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/roles/roles.controller.ts", "lineNumber": 73, "name": "查看權限", "zone": "admin", "category": "user_management", "scope": "SYSTEM"}, {"action": "update", "subject": "Permission", "description": "允許修改現有的權限定義", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/permissions/permissions.controller.ts", "lineNumber": 157, "name": "修改權限", "zone": "admin", "category": "user_management", "scope": "SYSTEM"}, {"action": "create", "subject": "Role", "description": "允許建立新的系統角色", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/roles/roles.controller.ts", "lineNumber": 161, "name": "建立角色", "zone": "admin", "category": "user_management", "scope": "SYSTEM"}, {"action": "delete", "subject": "Role", "description": "允許刪除系統角色", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/roles/roles.controller.ts", "lineNumber": 227, "name": "刪除角色", "zone": "admin", "category": "user_management", "scope": "SYSTEM"}, {"action": "read", "subject": "Role", "description": "允許查看系統角色定義和權限分配", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/roles/roles.controller.ts", "lineNumber": 281, "name": "查看角色", "zone": "admin", "category": "user_management", "scope": "SYSTEM"}, {"action": "update", "subject": "Role", "description": "允許修改角色的權限分配和設定", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/roles/roles.controller.ts", "lineNumber": 249, "name": "修改角色", "zone": "admin", "category": "user_management", "scope": "SYSTEM"}, {"action": "manage", "subject": "User", "description": "完整管理系統層級使用者的所有操作", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/core/auth/auth.service.ts", "lineNumber": 680, "name": "管理使用者", "zone": "admin", "category": "user_management", "scope": "SYSTEM"}, {"action": "manage", "subject": "Tenant", "description": "完整管理租戶的建立、修改、刪除等所有操作", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/core/auth/auth.service.ts", "lineNumber": 681, "name": "管理租戶", "zone": "admin", "category": "tenant_management", "scope": "SYSTEM"}, {"action": "read", "subject": "Tenant", "description": "允許查看租戶列表和詳細資訊", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/core/auth/auth.service.ts", "lineNumber": 682, "name": "查看租戶", "zone": "admin", "category": "tenant_management", "scope": "SYSTEM"}, {"action": "create", "subject": "Workspace", "description": "允許為租戶建立新的工作區", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/workspaces/workspaces.controller.ts", "lineNumber": 38, "name": "建立工作區", "zone": "admin", "category": "tenant_management", "scope": "TENANT"}, {"action": "delete", "subject": "Workspace", "description": "允許刪除工作區", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/workspaces/workspaces.controller.ts", "lineNumber": 93, "name": "刪除工作區", "zone": "admin", "category": "tenant_management", "scope": "TENANT"}, {"action": "read", "subject": "Workspace", "description": "允許查看工作區列表和詳細資訊", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/workspaces/workspaces.controller.ts", "lineNumber": 132, "name": "查看工作區", "zone": "admin", "category": "tenant_management", "scope": "TENANT"}, {"action": "update", "subject": "Workspace", "description": "允許修改工作區的設定和配置", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/workspaces/workspaces.controller.ts", "lineNumber": 118, "name": "修改工作區", "zone": "admin", "category": "tenant_management", "scope": "TENANT"}, {"action": "read", "subject": "Dashboard", "description": "允許查看系統管理儀表板的統計資訊", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/dashboard/dashboard.controller.ts", "lineNumber": 179, "name": "查看管理儀表板", "zone": "admin", "category": "system_management", "scope": "SYSTEM"}, {"action": "create", "subject": "User", "description": "允許邀請新使用者加入工作區", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/users/users.controller.ts", "lineNumber": 43, "name": "建立使用者", "zone": "workspace", "category": "member_management", "scope": "WORKSPACE"}, {"action": "delete", "subject": "User", "description": "允許從工作區移除使用者", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/users/users.controller.ts", "lineNumber": 100, "name": "刪除使用者", "zone": "workspace", "category": "member_management", "scope": "WORKSPACE"}, {"action": "read", "subject": "User", "description": "允許查看工作區內的使用者列表", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/users/users.controller.ts", "lineNumber": 77, "name": "查看使用者", "zone": "workspace", "category": "member_management", "scope": "WORKSPACE"}, {"action": "update", "subject": "User", "description": "允許修改工作區使用者的資料和權限", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/users/users.controller.ts", "lineNumber": 87, "name": "修改使用者", "zone": "workspace", "category": "member_management", "scope": "WORKSPACE"}, {"action": "read", "subject": "LineMessageLog", "description": "允許查看LINE機器人的訊息交互日誌", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/line/controllers/message-log.controller.ts", "lineNumber": 28, "name": "查看LINE訊息日誌", "zone": "admin", "category": "log_management", "scope": "SYSTEM"}, {"action": "read", "subject": "SystemLog", "description": "允許查看系統操作和稽核日誌", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/system-logs/controllers/system-logs.controller.ts", "lineNumber": 88, "name": "查看系統日誌", "zone": "admin", "category": "log_management", "scope": "SYSTEM"}, {"action": "create", "subject": "AiModel", "description": "允許添加新的 AI 模型配置", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/ai/controllers/ai-models.controller.ts", "lineNumber": 54, "name": "建立 AI 模型", "zone": "admin", "category": "ai_management", "scope": "SYSTEM"}, {"action": "delete", "subject": "AiModel", "description": "允許刪除 AI 模型配置", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/ai/controllers/ai-models.controller.ts", "lineNumber": 75, "name": "刪除 AI 模型", "zone": "admin", "category": "ai_management", "scope": "SYSTEM"}, {"action": "manage", "subject": "AiModel", "description": "完整管理 AI 模型的所有操作", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/ai/controllers/ai-models.controller.ts", "lineNumber": 106, "name": "管理 AI 模型", "zone": "admin", "category": "ai_management", "scope": "SYSTEM"}, {"action": "read", "subject": "AiModel", "description": "允許查看 AI 模型列表和詳細配置", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/ai/controllers/ai-models.controller.ts", "lineNumber": 45, "name": "查看 AI 模型", "zone": "admin", "category": "ai_management", "scope": "SYSTEM"}, {"action": "update", "subject": "AiModel", "description": "允許修改 AI 模型的配置和參數", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/ai/controllers/ai-models.controller.ts", "lineNumber": 84, "name": "修改 AI 模型", "zone": "admin", "category": "ai_management", "scope": "SYSTEM"}, {"action": "create", "subject": "LineBot", "description": "允許建立新的 LINE 機器人配置", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/line/controllers/line-bot.controller.ts", "lineNumber": 125, "name": "建立 LINE 機器人", "zone": "admin", "category": "ai_management", "scope": "TENANT"}, {"action": "delete", "subject": "LineBot", "description": "允許刪除 LINE 機器人配置", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/line/controllers/line-bot.controller.ts", "lineNumber": 179, "name": "刪除 LINE 機器人", "zone": "admin", "category": "ai_management", "scope": "TENANT"}, {"action": "read", "subject": "LineBot", "description": "允許查看 LINE 機器人配置和狀態", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/line/controllers/line-bot.controller.ts", "lineNumber": 242, "name": "查看 LINE 機器人", "zone": "admin", "category": "ai_management", "scope": "TENANT"}, {"action": "update", "subject": "LineBot", "description": "允許修改 LINE 機器人的設定和配置", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/line/controllers/line-bot.controller.ts", "lineNumber": 203, "name": "修改 LINE 機器人", "zone": "admin", "category": "ai_management", "scope": "TENANT"}], "stats": {"totalFiles": 687, "scannedFiles": 686, "filesWithPermissions": 10, "totalPermissions": 115}, "errors": []}, "syncResult": {"created": 0, "updated": 0, "deprecated": 0, "errors": 0, "details": {"createdPermissions": [], "updatedPermissions": [], "deprecatedPermissions": [], "errorMessages": []}}, "summary": {"total": 32, "byScope": {"SYSTEM": 20, "TENANT": 8, "WORKSPACE": 4}, "byCategory": {"system_management": 1, "user_management": 10, "tenant_management": 6, "member_management": 4, "ai_management": 9, "log_management": 2}, "bySubject": {"Dashboard": 1, "Permission": 5, "Role": 4, "Workspace": 4, "User": 5, "Tenant": 2, "AiModel": 5, "LineBot": 4, "LineMessageLog": 1, "SystemLog": 1}}, "permissions": [{"action": "create", "subject": "Permission", "description": "允許建立新的權限定義", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/permissions/permissions.controller.ts", "lineNumber": 135, "name": "建立權限", "zone": "admin", "category": "user_management", "scope": "SYSTEM"}, {"action": "delete", "subject": "Permission", "description": "允許刪除權限定義", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/permissions/permissions.controller.ts", "lineNumber": 181, "name": "刪除權限", "zone": "admin", "category": "user_management", "scope": "SYSTEM"}, {"action": "manage", "subject": "Permission", "description": "完整管理權限的所有操作", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/permissions/permissions.controller.ts", "lineNumber": 195, "name": "管理權限", "zone": "admin", "category": "user_management", "scope": "SYSTEM"}, {"action": "read", "subject": "Permission", "description": "允許查看系統權限定義和設定", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/roles/roles.controller.ts", "lineNumber": 73, "name": "查看權限", "zone": "admin", "category": "user_management", "scope": "SYSTEM"}, {"action": "update", "subject": "Permission", "description": "允許修改現有的權限定義", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/permissions/permissions.controller.ts", "lineNumber": 157, "name": "修改權限", "zone": "admin", "category": "user_management", "scope": "SYSTEM"}, {"action": "create", "subject": "Role", "description": "允許建立新的系統角色", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/roles/roles.controller.ts", "lineNumber": 161, "name": "建立角色", "zone": "admin", "category": "user_management", "scope": "SYSTEM"}, {"action": "delete", "subject": "Role", "description": "允許刪除系統角色", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/roles/roles.controller.ts", "lineNumber": 227, "name": "刪除角色", "zone": "admin", "category": "user_management", "scope": "SYSTEM"}, {"action": "read", "subject": "Role", "description": "允許查看系統角色定義和權限分配", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/roles/roles.controller.ts", "lineNumber": 281, "name": "查看角色", "zone": "admin", "category": "user_management", "scope": "SYSTEM"}, {"action": "update", "subject": "Role", "description": "允許修改角色的權限分配和設定", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/roles/roles.controller.ts", "lineNumber": 249, "name": "修改角色", "zone": "admin", "category": "user_management", "scope": "SYSTEM"}, {"action": "manage", "subject": "User", "description": "完整管理系統層級使用者的所有操作", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/core/auth/auth.service.ts", "lineNumber": 680, "name": "管理使用者", "zone": "admin", "category": "user_management", "scope": "SYSTEM"}, {"action": "manage", "subject": "Tenant", "description": "完整管理租戶的建立、修改、刪除等所有操作", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/core/auth/auth.service.ts", "lineNumber": 681, "name": "管理租戶", "zone": "admin", "category": "tenant_management", "scope": "SYSTEM"}, {"action": "read", "subject": "Tenant", "description": "允許查看租戶列表和詳細資訊", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/core/auth/auth.service.ts", "lineNumber": 682, "name": "查看租戶", "zone": "admin", "category": "tenant_management", "scope": "SYSTEM"}, {"action": "create", "subject": "Workspace", "description": "允許為租戶建立新的工作區", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/workspaces/workspaces.controller.ts", "lineNumber": 38, "name": "建立工作區", "zone": "admin", "category": "tenant_management", "scope": "TENANT"}, {"action": "delete", "subject": "Workspace", "description": "允許刪除工作區", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/workspaces/workspaces.controller.ts", "lineNumber": 93, "name": "刪除工作區", "zone": "admin", "category": "tenant_management", "scope": "TENANT"}, {"action": "read", "subject": "Workspace", "description": "允許查看工作區列表和詳細資訊", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/workspaces/workspaces.controller.ts", "lineNumber": 132, "name": "查看工作區", "zone": "admin", "category": "tenant_management", "scope": "TENANT"}, {"action": "update", "subject": "Workspace", "description": "允許修改工作區的設定和配置", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/workspaces/workspaces.controller.ts", "lineNumber": 118, "name": "修改工作區", "zone": "admin", "category": "tenant_management", "scope": "TENANT"}, {"action": "read", "subject": "Dashboard", "description": "允許查看系統管理儀表板的統計資訊", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/dashboard/dashboard.controller.ts", "lineNumber": 179, "name": "查看管理儀表板", "zone": "admin", "category": "system_management", "scope": "SYSTEM"}, {"action": "create", "subject": "User", "description": "允許邀請新使用者加入工作區", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/users/users.controller.ts", "lineNumber": 43, "name": "建立使用者", "zone": "workspace", "category": "member_management", "scope": "WORKSPACE"}, {"action": "delete", "subject": "User", "description": "允許從工作區移除使用者", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/users/users.controller.ts", "lineNumber": 100, "name": "刪除使用者", "zone": "workspace", "category": "member_management", "scope": "WORKSPACE"}, {"action": "read", "subject": "User", "description": "允許查看工作區內的使用者列表", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/users/users.controller.ts", "lineNumber": 77, "name": "查看使用者", "zone": "workspace", "category": "member_management", "scope": "WORKSPACE"}, {"action": "update", "subject": "User", "description": "允許修改工作區使用者的資料和權限", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/users/users.controller.ts", "lineNumber": 87, "name": "修改使用者", "zone": "workspace", "category": "member_management", "scope": "WORKSPACE"}, {"action": "read", "subject": "LineMessageLog", "description": "允許查看LINE機器人的訊息交互日誌", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/line/controllers/message-log.controller.ts", "lineNumber": 28, "name": "查看LINE訊息日誌", "zone": "admin", "category": "log_management", "scope": "SYSTEM"}, {"action": "read", "subject": "SystemLog", "description": "允許查看系統操作和稽核日誌", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/system-logs/controllers/system-logs.controller.ts", "lineNumber": 88, "name": "查看系統日誌", "zone": "admin", "category": "log_management", "scope": "SYSTEM"}, {"action": "create", "subject": "AiModel", "description": "允許添加新的 AI 模型配置", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/ai/controllers/ai-models.controller.ts", "lineNumber": 54, "name": "建立 AI 模型", "zone": "admin", "category": "ai_management", "scope": "SYSTEM"}, {"action": "delete", "subject": "AiModel", "description": "允許刪除 AI 模型配置", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/ai/controllers/ai-models.controller.ts", "lineNumber": 75, "name": "刪除 AI 模型", "zone": "admin", "category": "ai_management", "scope": "SYSTEM"}, {"action": "manage", "subject": "AiModel", "description": "完整管理 AI 模型的所有操作", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/ai/controllers/ai-models.controller.ts", "lineNumber": 106, "name": "管理 AI 模型", "zone": "admin", "category": "ai_management", "scope": "SYSTEM"}, {"action": "read", "subject": "AiModel", "description": "允許查看 AI 模型列表和詳細配置", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/ai/controllers/ai-models.controller.ts", "lineNumber": 45, "name": "查看 AI 模型", "zone": "admin", "category": "ai_management", "scope": "SYSTEM"}, {"action": "update", "subject": "AiModel", "description": "允許修改 AI 模型的配置和參數", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/ai/controllers/ai-models.controller.ts", "lineNumber": 84, "name": "修改 AI 模型", "zone": "admin", "category": "ai_management", "scope": "SYSTEM"}, {"action": "create", "subject": "LineBot", "description": "允許建立新的 LINE 機器人配置", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/line/controllers/line-bot.controller.ts", "lineNumber": 125, "name": "建立 LINE 機器人", "zone": "admin", "category": "ai_management", "scope": "TENANT"}, {"action": "delete", "subject": "LineBot", "description": "允許刪除 LINE 機器人配置", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/line/controllers/line-bot.controller.ts", "lineNumber": 179, "name": "刪除 LINE 機器人", "zone": "admin", "category": "ai_management", "scope": "TENANT"}, {"action": "read", "subject": "LineBot", "description": "允許查看 LINE 機器人配置和狀態", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/line/controllers/line-bot.controller.ts", "lineNumber": 242, "name": "查看 LINE 機器人", "zone": "admin", "category": "ai_management", "scope": "TENANT"}, {"action": "update", "subject": "LineBot", "description": "允許修改 LINE 機器人的設定和配置", "isSystemDefined": true, "filePath": "/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/line/controllers/line-bot.controller.ts", "lineNumber": 203, "name": "修改 LINE 機器人", "zone": "admin", "category": "ai_management", "scope": "TENANT"}]}