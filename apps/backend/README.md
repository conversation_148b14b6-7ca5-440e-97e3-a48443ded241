# HorizAI Backend

此目錄包含 HorizAI SaaS 平台的後端程式碼，使用 **NestJS** 與 **Prisma** 實作。

## 目錄結構

```text
apps/backend/
├── src/
│   ├── app.module.ts      # 根模組
│   ├── main.ts            # 進入點
│   ├── casl/              # CASL 權限控制
│   ├── common/            # 共用工具與裝飾器
│   ├── modules/           # 各業務模組
│   ├── prisma/            # Prisma 服務
│   └── types/             # 內部型別定義
├── prisma/
│   ├── schema.prisma      # 資料庫結構
│   ├── migrations/        # 遷移檔
│   ├── seed.ts            # 種子資料腳本
│   ├── init-db.ts         # 初始化腳本
│   └── templates/         # 權限與角色範本
├── scripts/               # 資料庫與環境工具
├── docs/                  # 後端相關文件
└── .env.example           # 環境變數範例
```

## 開發指令

```bash
# 安裝相依套件
pnpm install

# 啟動開發伺服器 (http://localhost:4000)
pnpm start:dev

# 資料庫遷移範例
pnpm db:create --name init
pnpm db:apply --seed
```

## 環境設定

1. 將 `.env.example` 複製為 `.env`。
2. 設定 `DATABASE_URL`、`JWT_SECRET` 等必要變數。
3. 如需初始化資料庫，可執行 `pnpm prisma migrate reset` 或 `pnpm db:reset`。
