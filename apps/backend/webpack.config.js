const path = require('path');
const fs = require('fs');

module.exports = (options) => {
  const config = {
    ...options,
    externals: [
      '@mapbox/node-pre-gyp',
      'node-pre-gyp',
      'mock-aws-s3',
      'aws-sdk',
      'nock',
      'sqlite3',
      'pg',
      'mysql2',
      'oracledb',
      'mongodb',
      'redis',
      'sharp',
      'canvas',
      'serialport',
      'usb',
      'bluetooth-hci-socket',
      'node-hid',
      'ffi',
      'ref',
      'weak',
      function ({ context, request }, callback) {
        if (request && (
          request.includes('swagger-ui') ||
          request.includes('@nestjs/swagger') ||
          request.includes('@fastify/swagger') ||
          request.includes('swagger2openapi')
        )) {
          return callback();
        }
        if (request.includes('@mapbox/node-pre-gyp') || request.includes('node-pre-gyp')) {
          return callback(null, 'commonjs ' + request);
        }
        if (request.match(/^(sqlite3|pg|mysql2|oracledb|mongodb|redis|sharp|canvas|serialport|usb|bluetooth-hci-socket|node-hid|ffi|ref|weak)$/)) {
          return callback(null, 'commonjs ' + request);
        }
        if (request.endsWith('.node')) {
          return callback(null, 'commonjs ' + request);
        }
        if (request.includes('bindings')) {
          return callback(null, 'commonjs ' + request);
        }
        callback();
      },
    ],
    plugins: [
      ...options.plugins || [],
    ],
    resolve: {
      ...options.resolve,
      fallback: {
        fs: false,
        path: false,
        crypto: false,
        stream: false,
        util: false,
        buffer: false,
        process: false,
      },
    },
    module: {
      ...options.module,
      rules: [
        ...options.module.rules,
        {
          test: /\.css$/,
          use: ['style-loader', 'css-loader'],
        },
        {
          test: /\.(woff|woff2|eot|ttf|otf|png|jpg|gif|svg)$/,
          type: 'asset/resource',
        },
        {
          test: /\.(html|node)$/,
          type: 'asset/resource',
          generator: {
            emit: false,
          },
        },
      ],
    },
    ignoreWarnings: [
      {
        module: /node_modules\/\@mapbox\/node-pre-gyp/,
      },
      /Module not found.*mock-aws-s3/,
      /Module not found.*aws-sdk/,
      /Module not found.*nock/,
      /Module not found.*bufferutil/,
      /Module not found.*utf-8-validate/,
      /Module parse failed.*Unexpected token/,
      /Critical dependency.*the request of a dependency is an expression/,
    ],
  };
  return config;
};
