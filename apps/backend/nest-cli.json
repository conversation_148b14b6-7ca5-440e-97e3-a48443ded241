{"$schema": "https://json.schemastore.org/nest-cli", "collection": "@nestjs/schematics", "sourceRoot": "src", "entryFile": "main", "root": ".", "compilerOptions": {"deleteOutDir": true, "webpack": true, "webpackConfigPath": "webpack.config.js", "tsConfigPath": "tsconfig.build.json", "assets": [{"include": "**/*.{css,html,json,png,jpg,ico,svg,woff,woff2,eot,ttf,otf}", "exclude": "**/node_modules/**", "outDir": "dist"}, {"include": "swagger-ui/**/*", "watchAssets": false, "outDir": "dist"}], "watchAssets": false}}