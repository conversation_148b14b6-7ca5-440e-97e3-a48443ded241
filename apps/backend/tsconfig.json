{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2021", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": true, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": false, "esModuleInterop": true, "resolveJsonModule": true, "paths": {"@/*": ["src/*"], "@modules/*": ["src/modules/*"], "@auth/*": ["../../packages/@auth/*"], "@horizai/permissions": ["../../packages/permissions/src"], "@horizai/permissions/constants": ["../../packages/permissions/src/constants"], ".prisma/client": ["./node_modules/.prisma/client"]}}, "ts-node": {"require": ["tsconfig-paths/register"]}, "include": ["src/**/*", "prisma/**/*", "scripts/**/*", "../swagger-ui"], "exclude": ["node_modules", "dist"]}