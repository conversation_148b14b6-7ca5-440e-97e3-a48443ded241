import { PrismaClient } from "@prisma/client";
import * as bcrypt from "bcryptjs";
import { randomUUID } from "crypto";
import * as dotenv from "dotenv";

// 載入環境變數
dotenv.config();

const prisma = new PrismaClient();

async function main() {
  console.log("🚀 開始執行 seed 腳本...");

  try {
    // 清除現有資料（按依賴順序）
    console.log("清除現有資料...");

    // 使用 deleteMany 而非 TRUNCATE 來避免外鍵約束問題
    await prisma.system_user_roles.deleteMany({});
    await prisma.tenant_user_roles.deleteMany({});
    await prisma.role_permissions.deleteMany({});
    await prisma.permissions.deleteMany({});
    await prisma.roles.deleteMany({});
    await prisma.system_users.deleteMany({});
    await prisma.tenant_users.deleteMany({});
    await prisma.tenants.deleteMany({});
    await prisma.permission_categories.deleteMany({});
    await prisma.settings.deleteMany({});

    // 1. 建立系統管理員帳號（system_users）
    console.log("建立系統管理員帳號...");
    const sysAdminId = randomUUID();
    const sysAdminEmail = "<EMAIL>";
    const sysAdminPassword = await bcrypt.hash("Admin@123", 10);

    const systemAdmin = await prisma.system_users.create({
      data: {
        id: sysAdminId,
        email: sysAdminEmail,
        password: sysAdminPassword,
        name: "Super Admin",
        role: "SUPER_ADMIN",
        status: "active",
      },
    });
    console.log("✅ 系統管理員帳號建立完成");

    // 2. 建立系統角色
    console.log("建立系統角色...");

    const superAdminRole = await prisma.roles.create({
      data: {
        id: randomUUID(),
        name: "SUPER_ADMIN",
        displayName: "超級管理員",
        description: "擁有系統最高權限，可管理整個平台",
        scope: "SYSTEM",
        isSystem: true,
        updatedAt: new Date(),
      },
    });

    const systemAdminRole = await prisma.roles.create({
      data: {
        id: randomUUID(),
        name: "SYSTEM_ADMIN",
        displayName: "系統管理員",
        description: "管理系統級設定和租戶",
        scope: "SYSTEM",
        isSystem: true,
        updatedAt: new Date(),
      },
    });

    const systemModeratorRole = await prisma.roles.create({
      data: {
        id: randomUUID(),
        name: "SYSTEM_MODERATOR",
        displayName: "系統監控員",
        description: "監控系統狀態和日誌",
        scope: "SYSTEM",
        isSystem: true,
        updatedAt: new Date(),
      },
    });

    // 3. 關聯系統用戶與角色
    await prisma.system_user_roles.create({
      data: {
        id: randomUUID(),
        system_user_id: sysAdminId,
        role_id: superAdminRole.id,
      },
    });
    console.log("✅ 系統管理員角色與映射建立完成");

    // 4. 建立測試租戶
    console.log("建立租戶...");
    const tenantId = randomUUID();

    const tenant = await prisma.tenants.create({
      data: {
        id: tenantId,
        name: "HorizAI",
        domain: "horizai.com",
        departments: ["管理部", "技術部", "業務部"],
        status: "active",
        adminEmail: "<EMAIL>",
        adminName: "系統管理員",
        billingCycle: "monthly",
        companySize: "10-50",
        industry: "科技服務",
        maxUsers: 10,
        maxProjects: 20,
        maxStorage: 20,
        paymentStatus: "paid",
        updatedAt: new Date(),
      },
    });

    // 5. 建立租戶角色
    console.log("建立租戶角色...");

    const tenantAdminRole = await prisma.roles.create({
      data: {
        id: randomUUID(),
        name: "TENANT_ADMIN",
        displayName: "租戶管理員",
        description: "管理租戶內所有資源和用戶",
        scope: "TENANT",
        tenantId: tenantId,
        isSystem: false,
        updatedAt: new Date(),
      },
    });

    const tenantManagerRole = await prisma.roles.create({
      data: {
        id: randomUUID(),
        name: "TENANT_MANAGER",
        displayName: "租戶經理",
        description: "管理租戶專案和部分設定",
        scope: "TENANT",
        tenantId: tenantId,
        isSystem: false,
        updatedAt: new Date(),
      },
    });

    const tenantUserRole = await prisma.roles.create({
      data: {
        id: randomUUID(),
        name: "TENANT_USER",
        displayName: "租戶用戶",
        description: "使用租戶提供的功能",
        scope: "TENANT",
        tenantId: tenantId,
        isSystem: false,
        updatedAt: new Date(),
      },
    });

    const tenantViewerRole = await prisma.roles.create({
      data: {
        id: randomUUID(),
        name: "TENANT_VIEWER",
        displayName: "租戶觀察者",
        description: "只能查看租戶資訊",
        scope: "TENANT",
        tenantId: tenantId,
        isSystem: false,
        updatedAt: new Date(),
      },
    });

    // 6. 建立測試租戶使用者（tenant_users）
    console.log("建立測試租戶使用者...");

    const tenantAdminUser = await prisma.tenant_users.create({
      data: {
        id: randomUUID(),
        email: "<EMAIL>",
        password: await bcrypt.hash("Admin@123", 10),
        name: "租戶管理員",
        tenantId: tenantId,
        role: "TENANT_ADMIN",
        status: "ACTIVE",
        department: "管理部",
        title: "總經理",
      },
    });

    const managerUser = await prisma.tenant_users.create({
      data: {
        id: randomUUID(),
        email: "<EMAIL>",
        password: await bcrypt.hash("Manager@123", 10),
        name: "專案經理",
        tenantId: tenantId,
        role: "TENANT_MANAGER",
        status: "ACTIVE",
        department: "技術部",
        title: "專案經理",
      },
    });

    const normalUser = await prisma.tenant_users.create({
      data: {
        id: randomUUID(),
        email: "<EMAIL>",
        password: await bcrypt.hash("User@123", 10),
        name: "一般用戶",
        tenantId: tenantId,
        role: "TENANT_USER",
        status: "ACTIVE",
        department: "業務部",
        title: "業務專員",
      },
    });

    // 7. 關聯租戶用戶與角色
    console.log("關聯租戶用戶與角色...");

    await prisma.tenant_user_roles.create({
      data: {
        id: randomUUID(),
        tenant_user_id: tenantAdminUser.id,
        role_id: tenantAdminRole.id,
      },
    });

    await prisma.tenant_user_roles.create({
      data: {
        id: randomUUID(),
        tenant_user_id: managerUser.id,
        role_id: tenantManagerRole.id,
      },
    });

    await prisma.tenant_user_roles.create({
      data: {
        id: randomUUID(),
        tenant_user_id: normalUser.id,
        role_id: tenantUserRole.id,
      },
    });

    // 8. 建立基本權限分類
    console.log("建立權限分類...");

    await prisma.permission_categories.create({
      data: {
        id: randomUUID(),
        name: "USER_MANAGEMENT",
        description: "管理系統和租戶用戶",
        updatedAt: new Date(),
      },
    });

    await prisma.permission_categories.create({
      data: {
        id: randomUUID(),
        name: "TENANT_MANAGEMENT",
        description: "管理租戶設定和資源",
        updatedAt: new Date(),
      },
    });

    await prisma.permission_categories.create({
      data: {
        id: randomUUID(),
        name: "AI_MANAGEMENT",
        description: "管理 AI 功能和設定",
        updatedAt: new Date(),
      },
    });

    await prisma.permission_categories.create({
      data: {
        id: randomUUID(),
        name: "PROJECT_MANAGEMENT",
        description: "管理工作區專案",
        updatedAt: new Date(),
      },
    });

    // 9. 建立基本設定
    console.log("建立系統設定...");

    await prisma.settings.create({
      data: {
        id: randomUUID(),
        name: "aiGloballyEnabled",
        type: "system",
        value: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    });

    await prisma.settings.create({
      data: {
        id: randomUUID(),
        name: "maxUploadSize",
        type: "system",
        value: 10485760,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    });

    await prisma.settings.create({
      data: {
        id: randomUUID(),
        name: "defaultLanguage",
        type: "general",
        value: "zh-TW",
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    });

    // 10. 建立測試工作區
    console.log("建立測試工作區...");

    const workspace = await prisma.workspaces.create({
      data: {
        id: randomUUID(),
        name: "HorizAI 主工作區",
        description: "HorizAI 公司的主要工作區",
        status: "active",
        tenantId: tenantId,
        ownerId: tenantAdminUser.id,
        settings: {
          allowFileSharing: true,
          allowImageSharing: true,
          maxFileSize: 10485760,
          retentionDays: 30
        },
        updatedAt: new Date(),
      },
    });

    // 11. 建立工作區成員
    console.log("建立工作區成員...");

    await prisma.workspace_members.create({
      data: {
        id: randomUUID(),
        workspaceId: workspace.id,
        userId: tenantAdminUser.id,
        role: "admin",
        updatedAt: new Date(),
      },
    });

    await prisma.workspace_members.create({
      data: {
        id: randomUUID(),
        workspaceId: workspace.id,
        userId: managerUser.id,
        role: "member",
        updatedAt: new Date(),
      },
    });

    await prisma.workspace_members.create({
      data: {
        id: randomUUID(),
        workspaceId: workspace.id,
        userId: normalUser.id,
        role: "member",
        updatedAt: new Date(),
      },
    });

    console.log("🎉 Seed 腳本執行完成！");
    console.log("📋 建立的資料：");
    console.log(`   🔹 系統管理員: ${sysAdminEmail} (密碼: Admin@123)`);
    console.log(`   🔹 租戶管理員: <EMAIL> (密碼: Admin@123)`);
    console.log(`   🔹 專案經理: <EMAIL> (密碼: Manager@123)`);
    console.log(`   🔹 一般用戶: <EMAIL> (密碼: User@123)`);
    console.log(`   🔹 租戶名稱: HorizAI`);
    console.log(`   🔹 工作區: ${workspace.name} (ID: ${workspace.id})`);
    console.log(`   🔹 工作區成員: 3 個`);
    console.log(`   🔹 系統角色: 3 個`);
    console.log(`   🔹 租戶角色: 4 個`);
    console.log(`   🔹 權限分類: 4 個`);
    console.log(`   🔹 系統設定: 3 個`);
  } catch (error) {
    console.error("❌ Seed 腳本執行失敗：", error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

main();
