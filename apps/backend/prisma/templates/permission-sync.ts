import {
  PrismaClient,
  permissions,
  roles,
  role_permissions,
} from "@prisma/client";
import { randomUUID } from "crypto";
import {
  ADMIN_PERMISSIONS,
  Permission,
  createPermissionKey,
} from "./permissions.template";
import { ROLE_PERMISSIONS, RoleName } from "./role-permissions.template";

// 資料庫權限型別
type DBPermission = permissions & {
  deprecated: boolean;
};

// 同步選項介面
export interface SyncOptions {
  force?: boolean;
  dryRun?: boolean;
  prismaClient?: PrismaClient;
}

/**
 * 同步權限到資料庫
 * @param options 同步選項
 */
export async function syncPermissions(options: SyncOptions = {}) {
  const { force = false, dryRun = false, prismaClient } = options;
  const client = prismaClient ?? new PrismaClient();

  try {
    console.log("🔄 開始同步權限...");

    // 1. 讀取現有權限
    const existingPermissions = await client.permissions.findMany();
    const existingPermissionsMap = new Map<string, permissions>();
    existingPermissions.forEach((p) => {
      const key = createPermissionKey(p.action, p.subject);
      existingPermissionsMap.set(key, p);
    });

    // 2. 計算需要新增和標記為已廢棄的權限
    const newPermissions: any[] = [];
    const deprecatedPermissionIds: string[] = [];

    // 檢查新權限
    ADMIN_PERMISSIONS.forEach((permission) => {
      const key = createPermissionKey(permission.action, permission.subject);
      const existing = existingPermissionsMap.get(key);

      if (!existing) {
        newPermissions.push({
          id: randomUUID(),
          action: permission.action,
          subject: permission.subject,
          description: permission.description,
          createdAt: new Date(),
          updatedAt: new Date(),
        });
      }
    });

    // 檢查已廢棄的權限
    existingPermissionsMap.forEach((permission, key) => {
      const isDeprecated = !ADMIN_PERMISSIONS.some(
        (p) => createPermissionKey(p.action, p.subject) === key
      );

      if (isDeprecated) {
        deprecatedPermissionIds.push(permission.id);
      }
    });

    // 3. 輸出同步計畫
    console.log("📝 同步計畫：");
    console.log(`   - 新增 ${newPermissions.length} 個權限`);
    console.log(`   - 標記 ${deprecatedPermissionIds.length} 個已廢棄權限`);

    if (dryRun) {
      console.log("🏃 執行乾跑模式，不進行實際更改");
      return;
    }

    // 4. 執行同步
    // 新增新權限
    if (newPermissions.length > 0) {
      await client.permissions.createMany({
        data: newPermissions,
      });
      console.log("✅ 新權限已新增");
    }

    // 更新已廢棄權限狀態
    for (const id of deprecatedPermissionIds) {
      await client.permissions.update({
        where: { id },
        data: { description: "已廢棄" },
      });
    }
    if (deprecatedPermissionIds.length > 0) {
      console.log("✅ 已廢棄權限已更新");
    }

    // 5. 同步角色權限
    await syncRolePermissions(client);
  } catch (error) {
    console.error("❌ 權限同步失敗:", error);
    throw error;
  } finally {
    if (!prismaClient) {
      await client.$disconnect();
    }
  }
}

/**
 * 同步角色權限
 * @param client Prisma Client
 */
async function syncRolePermissions(client: PrismaClient) {
  try {
    console.log("🔄 開始同步角色權限...");

    // 1. 讀取所有有效權限
    const allPermissions = await client.permissions.findMany({
      where: {
        description: { not: "已廢棄" },
      },
    });

    // 建立權限映射
    const permissionMap = new Map<string, string>();
    allPermissions.forEach((p) => {
      const key = createPermissionKey(p.action, p.subject);
      permissionMap.set(key, p.id);
    });

    // 2. 讀取所有角色
    const roles = await client.roles.findMany({
      include: {
        role_permissions: true,
      },
    });

    // 3. 為每個角色更新權限
    for (const role of roles) {
      const roleName = role.name as RoleName;
      const rolePermissions = ROLE_PERMISSIONS[roleName];

      // 如果角色有 'ALL' 權限
      if (rolePermissions === "ALL") {
        // 給予所有未廢棄的權限
        const permissionIds = allPermissions.map((p) => p.id);
        await updateRolePermissions(client, role.id, permissionIds);
        continue;
      }

      // 否則，給予指定的權限
      if (Array.isArray(rolePermissions)) {
        const permissionIds = rolePermissions
          .map((key) => permissionMap.get(key))
          .filter((id): id is string => id !== undefined);

        await updateRolePermissions(client, role.id, permissionIds);
      }
    }

    console.log("✅ 角色權限同步完成");
  } catch (error) {
    console.error("❌ 角色權限同步失敗:", error);
    throw error;
  }
}

/**
 * 更新角色權限
 * @param client Prisma Client
 * @param roleId 角色 ID
 * @param permissionIds 權限 ID 列表
 */
async function updateRolePermissions(
  client: PrismaClient,
  roleId: string,
  permissionIds: string[]
) {
  // 1. 刪除現有的角色權限映射
  await client.role_permissions.deleteMany({
    where: {
      roleId: roleId,
    },
  });

  // 2. 建立新的角色權限映射
  if (permissionIds.length > 0) {
    await client.role_permissions.createMany({
      data: permissionIds.map((permissionId) => ({
        id: randomUUID(),
        roleId: roleId,
        permissionId,
        createdAt: new Date(),
        updatedAt: new Date(),
      })),
    });
  }
}

// 處理命令行參數
if (require.main === module) {
  const args = process.argv.slice(2);
  const options: SyncOptions = {
    force: args.includes("--force"),
    dryRun: args.includes("--dry-run"),
  };

  syncPermissions(options).catch((error) => {
    console.error("同步失敗:", error);
    process.exit(1);
  });
}
