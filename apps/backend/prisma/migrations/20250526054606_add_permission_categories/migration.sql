-- AlterTable
ALTER TABLE "permissions" ADD COLUMN     "categoryId" TEXT;

-- CreateTable
CREATE TABLE "permission_categories" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "icon" TEXT,
    "sortOrder" INTEGER NOT NULL DEFAULT 0,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "permission_categories_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "permission_categories_name_key" ON "permission_categories"("name");

-- AddForeignKey
ALTER TABLE "permissions" ADD CONSTRAINT "permissions_categoryId_fkey" FOREIGN KEY ("categoryId") REFERENCES "permission_categories"("id") ON DELETE SET NULL ON UPDATE CASCADE;
