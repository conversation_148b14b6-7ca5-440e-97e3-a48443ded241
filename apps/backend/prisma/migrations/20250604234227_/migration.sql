/*
  Warnings:

  - You are about to drop the column `systemUserId` on the `refresh_tokens` table. All the data in the column will be lost.
  - You are about to drop the column `tenantUserId` on the `refresh_tokens` table. All the data in the column will be lost.
  - You are about to drop the column `userType` on the `refresh_tokens` table. All the data in the column will be lost.
  - You are about to drop the column `country` on the `tenants` table. All the data in the column will be lost.
  - You are about to drop the `system_users` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `tenant_users` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "refresh_tokens" DROP CONSTRAINT "refresh_tokens_systemUserId_fkey";

-- DropForeignKey
ALTER TABLE "refresh_tokens" DROP CONSTRAINT "refresh_tokens_tenantUserId_fkey";

-- DropForeign<PERSON>ey
ALTER TABLE "tenant_users" DROP CONSTRAINT "tenant_users_invitedBy_fkey";

-- DropForeignKey
ALTER TABLE "tenant_users" DROP CONSTRAINT "tenant_users_tenantId_fkey";

-- DropIndex
DROP INDEX "refresh_tokens_systemUserId_idx";

-- DropIndex
DROP INDEX "refresh_tokens_tenantUserId_idx";

-- DropIndex
DROP INDEX "refresh_tokens_userType_idx";

-- DropIndex
DROP INDEX "tenants_name_country_key";

-- AlterTable
ALTER TABLE "permissions" ADD COLUMN     "name" TEXT,
ADD COLUMN     "zone" TEXT;

-- AlterTable
ALTER TABLE "refresh_tokens" DROP COLUMN "systemUserId",
DROP COLUMN "tenantUserId",
DROP COLUMN "userType";

-- AlterTable
ALTER TABLE "tenants" DROP COLUMN "country",
ALTER COLUMN "domain" DROP NOT NULL;

-- AlterTable
ALTER TABLE "users" ADD COLUMN     "mfaEnabled" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "mfaSecret" TEXT;

-- DropTable
DROP TABLE "system_users";

-- DropTable
DROP TABLE "tenant_users";

-- DropEnum
DROP TYPE "SystemUserRole";

-- DropEnum
DROP TYPE "TenantUserRole";

-- DropEnum
DROP TYPE "TenantUserStatus";
