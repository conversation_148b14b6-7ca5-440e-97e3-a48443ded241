/*
  Warnings:

  - You are about to drop the column `name` on the `permissions` table. All the data in the column will be lost.
  - You are about to drop the column `zone` on the `permissions` table. All the data in the column will be lost.
  - You are about to drop the column `mfaEnabled` on the `users` table. All the data in the column will be lost.
  - You are about to drop the column `mfaSecret` on the `users` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[name,country]` on the table `tenants` will be added. If there are existing duplicate values, this will fail.
  - Made the column `domain` on table `tenants` required. This step will fail if there are existing NULL values in that column.

*/
-- CreateEnum
CREATE TYPE "SystemUserRole" AS ENUM ('SUPER_ADMIN', 'SYSTEM_ADMIN', 'SYSTEM_MODERATOR');

-- CreateEnum
CREATE TYPE "TenantUserStatus" AS ENUM ('ACTIVE', 'INACTIVE', 'PENDING', 'LEFT_COMPANY', 'SUSPENDED');

-- Create<PERSON>num
CREATE TYPE "TenantUserRole" AS ENUM ('TENANT_ADMIN', 'TENANT_MANAGER', 'TENANT_USER', 'TENANT_VIEWER');

-- AlterTable
ALTER TABLE "permissions" DROP COLUMN "name",
DROP COLUMN "zone";

-- AlterTable
ALTER TABLE "refresh_tokens" ADD COLUMN     "systemUserId" TEXT,
ADD COLUMN     "tenantUserId" TEXT,
ADD COLUMN     "userType" TEXT NOT NULL DEFAULT 'legacy';

-- AlterTable
ALTER TABLE "tenants" ADD COLUMN     "country" TEXT DEFAULT 'TW',
ALTER COLUMN "domain" SET NOT NULL;

-- AlterTable
ALTER TABLE "users" DROP COLUMN "mfaEnabled",
DROP COLUMN "mfaSecret";

-- CreateTable
CREATE TABLE "system_users" (
    "id" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "password" TEXT NOT NULL,
    "name" TEXT,
    "role" "SystemUserRole" NOT NULL DEFAULT 'SYSTEM_ADMIN',
    "status" TEXT NOT NULL DEFAULT 'active',
    "avatar" TEXT,
    "phone" TEXT,
    "mfaEnabled" BOOLEAN NOT NULL DEFAULT false,
    "mfaSecret" TEXT,
    "lastLoginAt" TIMESTAMP(3),
    "lastLoginIp" TEXT,
    "passwordLastChangedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "system_users_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "tenant_users" (
    "id" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "password" TEXT NOT NULL,
    "name" TEXT,
    "tenantId" TEXT NOT NULL,
    "role" "TenantUserRole" NOT NULL DEFAULT 'TENANT_USER',
    "status" "TenantUserStatus" NOT NULL DEFAULT 'ACTIVE',
    "avatar" TEXT,
    "phone" TEXT,
    "title" TEXT,
    "department" TEXT,
    "lastLoginAt" TIMESTAMP(3),
    "lastLoginIp" TEXT,
    "passwordLastChangedAt" TIMESTAMP(3),
    "mfaEnabled" BOOLEAN NOT NULL DEFAULT false,
    "mfaSecret" TEXT,
    "invitedBy" TEXT,
    "leftCompanyAt" TIMESTAMP(3),
    "leftCompanyReason" TEXT,
    "dataTransferStatus" TEXT DEFAULT 'pending',
    "dataTransferNote" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "tenant_users_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "system_users_email_key" ON "system_users"("email");

-- CreateIndex
CREATE INDEX "system_users_role_idx" ON "system_users"("role");

-- CreateIndex
CREATE UNIQUE INDEX "tenant_users_email_key" ON "tenant_users"("email");

-- CreateIndex
CREATE INDEX "tenant_users_role_idx" ON "tenant_users"("role");

-- CreateIndex
CREATE INDEX "tenant_users_tenantId_idx" ON "tenant_users"("tenantId");

-- CreateIndex
CREATE INDEX "refresh_tokens_systemUserId_idx" ON "refresh_tokens"("systemUserId");

-- CreateIndex
CREATE INDEX "refresh_tokens_tenantUserId_idx" ON "refresh_tokens"("tenantUserId");

-- CreateIndex
CREATE INDEX "refresh_tokens_userType_idx" ON "refresh_tokens"("userType");

-- CreateIndex
CREATE UNIQUE INDEX "tenants_name_country_key" ON "tenants"("name", "country");

-- AddForeignKey
ALTER TABLE "tenant_users" ADD CONSTRAINT "tenant_users_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "tenants"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tenant_users" ADD CONSTRAINT "tenant_users_invitedBy_fkey" FOREIGN KEY ("invitedBy") REFERENCES "tenant_users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "refresh_tokens" ADD CONSTRAINT "refresh_tokens_systemUserId_fkey" FOREIGN KEY ("systemUserId") REFERENCES "system_users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "refresh_tokens" ADD CONSTRAINT "refresh_tokens_tenantUserId_fkey" FOREIGN KEY ("tenantUserId") REFERENCES "tenant_users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
