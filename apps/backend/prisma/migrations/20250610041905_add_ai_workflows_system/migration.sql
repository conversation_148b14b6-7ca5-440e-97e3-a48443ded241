/*
  Warnings:

  - You are about to drop the `ai_service_workflows` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "ai_service_workflows" DROP CONSTRAINT "ai_service_workflows_tenant_id_fkey";

-- DropForeignKey
ALTER TABLE "ai_service_workflows" DROP CONSTRAINT "ai_service_workflows_workspace_id_fkey";

-- DropForeignKey
ALTER TABLE "workflow_executions" DROP CONSTRAINT "workflow_executions_workflow_id_fkey";

-- DropForeignKey
ALTER TABLE "workflow_nodes" DROP CONSTRAINT "workflow_nodes_workflow_id_fkey";

-- DropTable
DROP TABLE "ai_service_workflows";

-- CreateTable
CREATE TABLE "ai_workflows" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "version" TEXT NOT NULL DEFAULT '1.0.0',
    "is_published" BOOLEAN NOT NULL DEFAULT false,
    "is_template" BOOLEAN NOT NULL DEFAULT false,
    "config" JSONB,
    "input_schema" JSONB,
    "output_schema" JSONB,
    "visibility" "WorkflowVisibility" NOT NULL DEFAULT 'PRIVATE',
    "created_by" TEXT NOT NULL,
    "updated_by" TEXT,
    "tenant_id" TEXT,
    "workspace_id" TEXT,
    "status" "WorkflowStatus" NOT NULL DEFAULT 'DRAFT',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "published_at" TIMESTAMP(3),

    CONSTRAINT "ai_workflows_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "ai_workflows_tenant_id_idx" ON "ai_workflows"("tenant_id");

-- CreateIndex
CREATE INDEX "ai_workflows_workspace_id_idx" ON "ai_workflows"("workspace_id");

-- CreateIndex
CREATE INDEX "ai_workflows_created_by_idx" ON "ai_workflows"("created_by");

-- CreateIndex
CREATE INDEX "ai_workflows_status_idx" ON "ai_workflows"("status");

-- CreateIndex
CREATE INDEX "ai_workflows_is_published_idx" ON "ai_workflows"("is_published");

-- AddForeignKey
ALTER TABLE "ai_workflows" ADD CONSTRAINT "ai_workflows_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ai_workflows" ADD CONSTRAINT "ai_workflows_workspace_id_fkey" FOREIGN KEY ("workspace_id") REFERENCES "workspaces"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "workflow_nodes" ADD CONSTRAINT "workflow_nodes_workflow_id_fkey" FOREIGN KEY ("workflow_id") REFERENCES "ai_workflows"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "workflow_executions" ADD CONSTRAINT "workflow_executions_workflow_id_fkey" FOREIGN KEY ("workflow_id") REFERENCES "ai_workflows"("id") ON DELETE CASCADE ON UPDATE CASCADE;
