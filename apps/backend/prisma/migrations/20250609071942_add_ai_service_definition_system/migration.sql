/*
  Warnings:

  - You are about to drop the column `action` on the `workspace_activity_logs` table. All the data in the column will be lost.
  - You are about to drop the column `createdAt` on the `workspace_activity_logs` table. All the data in the column will be lost.
  - You are about to drop the column `description` on the `workspace_activity_logs` table. All the data in the column will be lost.
  - You are about to drop the column `entityId` on the `workspace_activity_logs` table. All the data in the column will be lost.
  - You are about to drop the column `entityType` on the `workspace_activity_logs` table. All the data in the column will be lost.
  - You are about to drop the column `metadata` on the `workspace_activity_logs` table. All the data in the column will be lost.
  - You are about to drop the column `userId` on the `workspace_activity_logs` table. All the data in the column will be lost.
  - You are about to drop the column `workspaceId` on the `workspace_activity_logs` table. All the data in the column will be lost.
  - Added the required column `activity_type` to the `workspace_activity_logs` table without a default value. This is not possible if the table is not empty.
  - Added the required column `user_id` to the `workspace_activity_logs` table without a default value. This is not possible if the table is not empty.
  - Added the required column `workspace_id` to the `workspace_activity_logs` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "AiServiceStepType" AS ENUM ('PROMPT_TEMPLATE', 'BOT_EXECUTION', 'CODE_EXECUTION', 'API_CALL', 'DATA_TRANSFORMATION');

-- CreateEnum
CREATE TYPE "AiExecutionStatus" AS ENUM ('PENDING', 'RUNNING', 'COMPLETED', 'FAILED', 'CANCELLED');

-- AlterEnum
ALTER TYPE "AiBotProviderType" ADD VALUE 'CUSTOM';

-- DropForeignKey
ALTER TABLE "workspace_activity_logs" DROP CONSTRAINT "workspace_activity_logs_workspaceId_fkey";

-- DropIndex
DROP INDEX "workspace_activity_logs_action_idx";

-- DropIndex
DROP INDEX "workspace_activity_logs_createdAt_idx";

-- DropIndex
DROP INDEX "workspace_activity_logs_userId_idx";

-- DropIndex
DROP INDEX "workspace_activity_logs_workspaceId_idx";

-- AlterTable
ALTER TABLE "workspace_activity_logs" DROP COLUMN "action",
DROP COLUMN "createdAt",
DROP COLUMN "description",
DROP COLUMN "entityId",
DROP COLUMN "entityType",
DROP COLUMN "metadata",
DROP COLUMN "userId",
DROP COLUMN "workspaceId",
ADD COLUMN     "activity_type" TEXT NOT NULL,
ADD COLUMN     "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "details" JSONB,
ADD COLUMN     "user_id" TEXT NOT NULL,
ADD COLUMN     "workspace_id" TEXT NOT NULL;

-- CreateTable
CREATE TABLE "ai_service_definitions" (
    "id" TEXT NOT NULL,
    "key" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "is_enabled" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "created_by" TEXT NOT NULL,
    "updated_by" TEXT,

    CONSTRAINT "ai_service_definitions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ai_service_steps" (
    "id" TEXT NOT NULL,
    "service_id" TEXT NOT NULL,
    "step_order" INTEGER NOT NULL,
    "step_type" "AiServiceStepType" NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "config" JSONB NOT NULL,
    "is_enabled" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ai_service_steps_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ai_service_templates" (
    "id" TEXT NOT NULL,
    "service_id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "config_template" JSONB NOT NULL,
    "is_public" BOOLEAN NOT NULL DEFAULT false,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ai_service_templates_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ai_service_executions" (
    "id" TEXT NOT NULL,
    "service_id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "workspace_id" TEXT,
    "input_data" JSONB NOT NULL,
    "output_data" JSONB,
    "status" "AiExecutionStatus" NOT NULL,
    "started_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "completed_at" TIMESTAMP(3),
    "error_message" TEXT,

    CONSTRAINT "ai_service_executions_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "ai_service_definitions_key_key" ON "ai_service_definitions"("key");

-- CreateIndex
CREATE UNIQUE INDEX "ai_service_steps_service_id_step_order_key" ON "ai_service_steps"("service_id", "step_order");

-- CreateIndex
CREATE INDEX "ai_service_executions_service_id_user_id_idx" ON "ai_service_executions"("service_id", "user_id");

-- CreateIndex
CREATE INDEX "workspace_activity_logs_workspace_id_idx" ON "workspace_activity_logs"("workspace_id");

-- CreateIndex
CREATE INDEX "workspace_activity_logs_user_id_idx" ON "workspace_activity_logs"("user_id");

-- AddForeignKey
ALTER TABLE "workspace_activity_logs" ADD CONSTRAINT "workspace_activity_logs_workspace_id_fkey" FOREIGN KEY ("workspace_id") REFERENCES "workspaces"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ai_service_definitions" ADD CONSTRAINT "ai_service_definitions_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "system_users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ai_service_definitions" ADD CONSTRAINT "ai_service_definitions_updated_by_fkey" FOREIGN KEY ("updated_by") REFERENCES "system_users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ai_service_steps" ADD CONSTRAINT "ai_service_steps_service_id_fkey" FOREIGN KEY ("service_id") REFERENCES "ai_service_definitions"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ai_service_templates" ADD CONSTRAINT "ai_service_templates_service_id_fkey" FOREIGN KEY ("service_id") REFERENCES "ai_service_definitions"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ai_service_executions" ADD CONSTRAINT "ai_service_executions_service_id_fkey" FOREIGN KEY ("service_id") REFERENCES "ai_service_definitions"("id") ON DELETE CASCADE ON UPDATE CASCADE;
