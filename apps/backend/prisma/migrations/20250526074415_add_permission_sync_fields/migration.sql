/*
  Warnings:

  - A unique constraint covering the columns `[action,subject]` on the table `permissions` will be added. If there are existing duplicate values, this will fail.

*/
-- CreateEnum
CREATE TYPE "PermissionScope" AS ENUM ('SYSTEM', 'TENANT', 'WORKSPACE', 'G<PERSON><PERSON>BAL');

-- AlterTable
ALTER TABLE "permissions" ADD COLUMN     "isSystemDefined" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "scope" "PermissionScope";

-- CreateIndex
CREATE UNIQUE INDEX "permissions_action_subject_key" ON "permissions"("action", "subject");
