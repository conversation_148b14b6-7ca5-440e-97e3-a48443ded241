-- AlterTable
ALTER TABLE "projects" ADD COLUMN     "level" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "parentProjectId" TEXT,
ADD COLUMN     "path" TEXT;

-- CreateIndex
CREATE INDEX "projects_tenantId_idx" ON "projects"("tenantId");

-- CreateIndex
CREATE INDEX "projects_userId_idx" ON "projects"("userId");

-- CreateIndex
CREATE INDEX "projects_parentProjectId_idx" ON "projects"("parentProjectId");

-- CreateIndex
CREATE INDEX "projects_level_idx" ON "projects"("level");

-- CreateIndex
CREATE INDEX "projects_path_idx" ON "projects"("path");

-- AddForeignKey
ALTER TABLE "projects" ADD CONSTRAINT "projects_parentProjectId_fkey" FOREIGN KEY ("parentProjectId") REFERENCES "projects"("id") ON DELETE CASCADE ON UPDATE CASCADE;
