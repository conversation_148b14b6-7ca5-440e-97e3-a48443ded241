/*
  Warnings:

  - You are about to drop the column `user_id` on the `refresh_tokens` table. All the data in the column will be lost.
  - You are about to drop the `users` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "ai_bots" DROP CONSTRAINT "ai_bots_created_by_fkey";

-- DropForeignKey
ALTER TABLE "ai_bots" DROP CONSTRAINT "ai_bots_updated_by_fkey";

-- DropForeignKey
ALTER TABLE "albums" DROP CONSTRAINT "albums_userId_fkey";

-- DropForeignKey
ALTER TABLE "line_auth_states" DROP CONSTRAINT "line_auth_states_userId_fkey";

-- DropForeignKey
ALTER TABLE "line_group_verifications" DROP CONSTRAINT "line_group_verifications_verified_by_user_id_fkey";

-- DropForeignKey
ALTER TABLE "login_logs" DROP CONSTRAINT "login_logs_userId_fkey";

-- DropForeignKey
ALTER TABLE "photos" DROP CONSTRAINT "photos_userId_fkey";

-- DropForeignKey
ALTER TABLE "projects" DROP CONSTRAINT "projects_userId_fkey";

-- DropForeignKey
ALTER TABLE "refresh_tokens" DROP CONSTRAINT "refresh_tokens_user_id_fkey";

-- DropForeignKey
ALTER TABLE "settings" DROP CONSTRAINT "settings_createdBy_fkey";

-- DropForeignKey
ALTER TABLE "settings" DROP CONSTRAINT "settings_updatedBy_fkey";

-- DropForeignKey
ALTER TABLE "tenant_invitations" DROP CONSTRAINT "tenant_invitations_acceptedById_fkey";

-- DropForeignKey
ALTER TABLE "tenant_invitations" DROP CONSTRAINT "tenant_invitations_createdById_fkey";

-- DropForeignKey
ALTER TABLE "user_roles" DROP CONSTRAINT "user_roles_userId_fkey";

-- DropForeignKey
ALTER TABLE "users" DROP CONSTRAINT "users_tenantId_fkey";

-- DropForeignKey
ALTER TABLE "workspace_members" DROP CONSTRAINT "workspace_members_userId_fkey";

-- DropForeignKey
ALTER TABLE "workspaces" DROP CONSTRAINT "workspaces_ownerId_fkey";

-- DropIndex
DROP INDEX "refresh_tokens_user_id_idx";

-- AlterTable
ALTER TABLE "refresh_tokens" DROP COLUMN "user_id",
ALTER COLUMN "userType" SET DEFAULT 'system';

-- DropTable
DROP TABLE "users";

-- DropEnum
DROP TYPE "UserRole";
