generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model ai_bots {
  id                              String               @id
  name                            String
  description                     String?
  scene                           String?
  temperature                     Float?               @default(0.7)
  scope                           AiBotScope
  provider_type                   AiBotProviderType
  model_id                        String
  key_id                          String
  provider_config_override        Json?
  system_prompt                   String?
  max_tokens                      Int?
  response_format                 AiBotResponseFormat  @default(TEXT)
  is_enabled                      Boolean              @default(true)
  is_template                     Boolean              @default(false)
  tenant_id                       String?
  workspace_id                    String?
  created_at                      DateTime             @default(now())
  updated_at                      DateTime
  created_by                      String
  updated_by                      String?
  ai_keys                         ai_keys              @relation(fields: [key_id], references: [id])
  ai_models                       ai_models            @relation(fields: [model_id], references: [id])
  tenants                         tenants?             @relation(fields: [tenant_id], references: [id], onDelete: Cascade)
  workspaces                      workspaces?          @relation(fields: [workspace_id], references: [id], onDelete: Cascade)
  ai_feature_configs              ai_feature_configs[]
  ai_usage_logs                   ai_usage_logs[]

  @@index([scope, tenant_id, workspace_id])
}

model ai_feature_configs {
  id          String   @id
  feature_key String   @unique
  is_enabled  Boolean  @default(false)
  bot_id      String?
  created_at  DateTime @default(now())
  updated_at  DateTime
  ai_bots     ai_bots? @relation(fields: [bot_id], references: [id])
}

model ai_global_settings {
  id                          String   @id
  is_ai_globally_enabled      Boolean  @default(true)
  global_monthly_quota_tokens BigInt?
  global_monthly_quota_calls  Int?
  created_at                  DateTime @default(now())
  updated_at                  DateTime
}

model ai_keys {
  id            String          @id
  provider      String
  name          String
  api_key       String
  api_url       String?
  is_enabled    Boolean         @default(true)
  created_at    DateTime        @default(now())
  updated_at    DateTime
  last_test     DateTime?
  ai_bots       ai_bots[]
  ai_usage_logs ai_usage_logs[]
}

model ai_models {
  id                         String    @id
  provider                   String
  model_name                 String
  display_name               String
  is_enabled                 Boolean   @default(true)
  input_price_per_1k_tokens  Decimal   @default(0)
  output_price_per_1k_tokens Decimal   @default(0)
  currency                   String    @default("USD")
  price_last_updated_at      DateTime
  context_window_tokens      Int?
  notes                      String?
  created_at                 DateTime  @default(now())
  updated_at                 DateTime
  ai_bots                    ai_bots[]

  @@unique([provider, model_name])
}

model ai_price_sources {
  id         String   @id
  provider   String
  url        String
  created_at DateTime @default(now())
  updated_at DateTime
}

model ai_usage_logs {
  id                 String    @id
  user_id            String?
  tenant_id          String?
  bot_id             String
  feature_key        String?
  api_key_id         String
  provider           String
  model_name         String
  input_tokens       Int
  output_tokens      Int
  call_count         Int       @default(1)
  estimated_cost     Decimal
  request_timestamp  DateTime  @default(now())
  response_timestamp DateTime?
  is_success         Boolean
  error_message      String?
  created_at         DateTime  @default(now())
  ai_keys            ai_keys   @relation(fields: [api_key_id], references: [id])
  ai_bots            ai_bots   @relation(fields: [bot_id], references: [id])

  @@index([api_key_id])
  @@index([bot_id])
  @@index([feature_key])
  @@index([request_timestamp])
  @@index([tenant_id])
  @@index([user_id])
}

model ai_photo_analysis_results {
  id                   String   @id
  photo_url            String
  project_id           String
  tenant_id            String
  user_id              String
  analysis_type        String
  context              String?
  confidence           Decimal  @default(0.8)
  description          String
  progress_percentage  Int?
  quality_score        Int?
  safety_issues        String[]
  equipment_detected   String[]
  recommendations      String[]
  ai_model_used        String?
  processing_time_ms   Int?
  created_at           DateTime @default(now())
  updated_at           DateTime @updatedAt
  projects             projects @relation(fields: [project_id], references: [id], onDelete: Cascade)
  tenants              tenants  @relation(fields: [tenant_id], references: [id], onDelete: Cascade)

  @@index([project_id])
  @@index([tenant_id])
  @@index([user_id])
  @@index([analysis_type])
  @@index([created_at])
}

model albums {
  id          String   @id
  name        String
  description String?
  photosCount Int      @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime
  userId      String
  tenantId    String
  tenants     tenants  @relation(fields: [tenantId], references: [id])
  photos      photos[]

  @@index([tenantId])
  @@index([userId])
}

model line_auth_states {
  id        String   @id
  state     String   @unique
  userId    String
  expiresAt DateTime
  createdAt DateTime @default(now())


  @@index([state])
  @@index([userId])
}

model line_bots {
  id                                String                     @id
  name                              String
  description                       String?
  scope                             LineBotScope
  tenant_id                         String?
  bot_secret                        String?
  bot_token                         String?
  token_last_updated_at             DateTime?
  token_update_reminder_period_days Int?
  webhook_url                       String?
  is_enabled                        Boolean                    @default(true)
  created_at                        DateTime                   @default(now())
  updated_at                        DateTime
  needs_token_update_reminder       Boolean                    @default(false)
  workspace_id                      String?
  tenants                           tenants?                   @relation(fields: [tenant_id], references: [id], onDelete: Cascade)
  workspaces                        workspaces?                @relation(fields: [workspace_id], references: [id])
  line_group_verifications          line_group_verifications[]
  line_message_logs                 line_message_logs[]

  @@index([tenant_id])
  @@index([workspace_id])
}

model line_group_verifications {
  id                  String      @id
  bot_id              String
  group_id            String
  workspace_id        String?
  tenant_id           String?
  is_verified         Boolean     @default(false)
  verified_at         DateTime?
  verified_by_user_id String?
  created_at          DateTime    @default(now())
  updated_at          DateTime
  line_bots           line_bots   @relation(fields: [bot_id], references: [id], onDelete: Cascade)
  tenants             tenants?    @relation(fields: [tenant_id], references: [id], onDelete: Cascade)

  workspaces          workspaces? @relation(fields: [workspace_id], references: [id])

  @@unique([bot_id, group_id])
}

model line_message_logs {
  id              String               @id
  bot_id          String
  group_id        String?
  line_user_id    String
  message_id      String
  message_type    String
  message_content Json?
  direction       LineMessageDirection
  processed_at    DateTime             @default(now())
  workspace_id    String?
  tenant_id       String?
  created_at      DateTime             @default(now())
  updated_at      DateTime
  line_bots       line_bots            @relation(fields: [bot_id], references: [id], onDelete: Cascade)
  tenants         tenants?             @relation(fields: [tenant_id], references: [id], onDelete: Cascade)
  workspaces      workspaces?          @relation(fields: [workspace_id], references: [id], onDelete: Cascade)

  @@index([group_id])
  @@index([line_user_id])
  @@index([message_id])
  @@index([tenant_id])
  @@index([workspace_id])
}

model login_logs {
  id         String   @id
  userId     String?
  ipAddress  String?
  userAgent  String?
  loginAt    DateTime @default(now())
  success    Boolean
  failReason String?

}

model order_histories {
  id          String   @id
  type        String
  status      String
  description String
  by          String?
  orderId     String
  createdAt   DateTime @default(now())
  orders      orders   @relation(fields: [orderId], references: [id])
}

model orders {
  id                  String            @id
  tenantId            String
  planId              String
  tenantName          String
  planName            String
  amount              Float
  period              Int
  numberOfSubscribers Int
  startDate           DateTime
  endDate             DateTime
  status              String            @default("PENDING")
  remarks             String?
  billingCycle        String            @default("monthly")
  createdAt           DateTime          @default(now())
  updatedAt           DateTime
  order_histories     order_histories[]
  plans               plans             @relation(fields: [planId], references: [id])
  tenants             tenants           @relation(fields: [tenantId], references: [id])
  payments            payments?
}

model payments {
  id            String   @id
  method        String
  status        String
  transactionId String?
  amount        Float?
  currency      String?  @default("TWD")
  orderId       String   @unique
  createdAt     DateTime @default(now())
  updatedAt     DateTime
  orders        orders   @relation(fields: [orderId], references: [id])
}

model permission_categories {
  id          String        @id
  name        String        @unique
  description String?
  icon        String?
  sortOrder   Int           @default(0)
  isActive    Boolean       @default(true)
  createdAt   DateTime      @default(now())
  updatedAt   DateTime
  permissions permissions[]
}

model permissions {
  id                    String                 @id
  action                String
  subject               String
  conditions            Json?
  description           String?
  createdAt             DateTime               @default(now())
  updatedAt             DateTime
  deprecated            Boolean                @default(false)
  fields                String[]
  categoryId            String?
  isSystemDefined       Boolean                @default(false)
  scope                 PermissionScope?
  name                  String?
  zone                  String?
  permission_categories permission_categories? @relation(fields: [categoryId], references: [id])
  role_permissions      role_permissions[]

  @@unique([action, subject])
}

model photos {
  id          String        @id
  title       String
  description String?
  url         String
  category    PhotoCategory
  metadata    Json?
  createdAt   DateTime      @default(now())
  updatedAt   DateTime
  albumId     String
  userId      String
  tenantId    String
  projectId   String
  albums      albums        @relation(fields: [albumId], references: [id])
  projects    projects      @relation(fields: [projectId], references: [id])
  tenants     tenants       @relation(fields: [tenantId], references: [id])

  @@index([albumId])
  @@index([projectId])
  @@index([tenantId])
  @@index([userId])
}

model plans {
  id                    String          @id
  name                  String          @unique
  description           String
  price                 Float
  billingCycle          String          @default("monthly")
  features              Json
  maxUsers              Int
  maxProjects           Int
  maxStorage            Int
  isPopular             Boolean         @default(false)
  createdAt             DateTime        @default(now())
  updatedAt             DateTime
  monthlyAiCreditsLimit Decimal?        @default(0)
  orders                orders[]
  subscriptions         subscriptions[]
  tenants               tenants[]
}

model projects {
  id          String    @id @default(cuid())
  name        String
  description String?
  status      String    @default("planning")
  startDate   DateTime?
  endDate     DateTime?
  budget      Float?
  priority    String    @default("medium")
  tenantId    String
  userId      String
  workspaceId String?   // 工作區關聯
  parentProjectId String? // 支援專案階層結構
  level       Int       @default(0) // 專案層級，0為根專案
  path        String?   // 專案路徑，用於快速查詢階層
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  
  // 關聯關係
  photos      photos[]
  tasks       tasks[]
  progress_entries progress_entries[]
  project_milestones project_milestones[]
  progress_reports progress_reports[]
  ai_photo_analysis_results ai_photo_analysis_results[]
  tenants     tenants   @relation(fields: [tenantId], references: [id])
  workspaces  workspaces? @relation(fields: [workspaceId], references: [id], onDelete: SetNull)
  
  // 階層關係
  parentProject projects? @relation("ProjectHierarchy", fields: [parentProjectId], references: [id], onDelete: Cascade)
  subProjects   projects[] @relation("ProjectHierarchy")
  
  @@index([tenantId])
  @@index([userId])
  @@index([workspaceId])
  @@index([parentProjectId])
  @@index([level])
  @@index([path])
}

model tasks {
  id          String    @id @default(cuid())
  title       String
  description String?
  status      String    @default("todo")
  priority    String    @default("medium")
  dueDate     DateTime?
  startDate   DateTime?
  estimatedHours Float?
  actualHours    Float?
  projectId   String
  assigneeId  String?
  createdById String
  tenantId    String
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  project     projects  @relation(fields: [projectId], references: [id], onDelete: Cascade)
  tenants     tenants   @relation(fields: [tenantId], references: [id])
  progress_entries progress_entries[]

  @@index([projectId])
  @@index([assigneeId])
  @@index([tenantId])
  @@index([status])
}

model progress_entries {
  id          String    @id @default(cuid())
  title       String
  description String?
  progressType ProgressType @default(TASK_UPDATE)
  progressValue Float?   // 進度百分比 (0-100)
  status      String?   // 狀態更新
  notes       String?   // 備註
  photoUrls   String[]  // 相關照片 URLs
  metadata    Json?     // 額外的元數據
  
  // 關聯
  projectId   String?
  taskId      String?
  userId      String    // 記錄者
  tenantId    String
  
  // 時間戳
  recordedAt  DateTime  @default(now()) // 實際發生時間
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  
  // 關聯關係
  project     projects? @relation(fields: [projectId], references: [id], onDelete: Cascade)
  task        tasks?    @relation(fields: [taskId], references: [id], onDelete: Cascade)
  tenants     tenants   @relation(fields: [tenantId], references: [id])
  
  @@index([projectId])
  @@index([taskId])
  @@index([tenantId])
  @@index([userId])
  @@index([recordedAt])
}

model project_milestones {
  id          String    @id @default(cuid())
  title       String
  description String?
  targetDate  DateTime
  completedAt DateTime?
  status      MilestoneStatus @default(PENDING)
  priority    String    @default("medium")
  
  // 關聯
  projectId   String
  tenantId    String
  createdById String
  
  // 時間戳
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  
  // 關聯關係
  project     projects  @relation(fields: [projectId], references: [id], onDelete: Cascade)
  tenants     tenants   @relation(fields: [tenantId], references: [id])
  
  @@index([projectId])
  @@index([tenantId])
  @@index([targetDate])
}

model progress_reports {
  id          String    @id @default(cuid())
  title       String
  reportType  ReportType @default(WEEKLY)
  period      String    // 報告期間 (如 "2024-W01")
  
  // 統計數據
  totalTasks  Int       @default(0)
  completedTasks Int    @default(0)
  inProgressTasks Int   @default(0)
  overdueTasks Int     @default(0)
  completionRate Float @default(0) // 完成率
  
  // 預測數據 (為 AI 分析預留)
  predictedCompletionDate DateTime?
  riskLevel   String?   // 風險等級
  recommendations Json? // AI 建議
  
  // 關聯
  projectId   String?   // 可以是項目報告或整體報告
  tenantId    String
  generatedBy String    // 生成者
  
  // 時間戳
  reportDate  DateTime  @default(now())
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  
  // 關聯關係
  project     projects? @relation(fields: [projectId], references: [id], onDelete: Cascade)
  tenants     tenants   @relation(fields: [tenantId], references: [id])
  
  @@index([projectId])
  @@index([tenantId])
  @@index([reportDate])
  @@unique([projectId, reportType, period])
}

model refresh_tokens {
  id             String        @id
  token          String        @unique
  systemUserId   String?
  tenantUserId   String?
  userType       String
  isValid        Boolean       @default(true) @map("is_valid")
  deviceInfo     String?       @map("device_info")
  expiresAt      DateTime      @map("expires_at")
  revokedAt      DateTime?     @map("revoked_at")
  createdAt      DateTime      @default(now()) @map("created_at")
  system_user    system_users? @relation(fields: [systemUserId], references: [id], onDelete: Cascade)
  tenant_user    tenant_users? @relation(fields: [tenantUserId], references: [id], onDelete: Cascade)

  @@index([systemUserId])
  @@index([tenantUserId])
  @@index([userType])
  @@map("refresh_tokens")
}

model password_reset_tokens {
  id          String   @id @default(cuid())
  email       String
  token       String   @unique
  user_type   String   // "system" | "tenant"
  user_id     String
  expires_at  DateTime
  used_at     DateTime?
  created_at  DateTime @default(now())

  @@index([email])
  @@index([token])
  @@index([user_id])
  @@map("password_reset_tokens")
}

model role_permissions {
  id           String      @id
  roleId       String
  permissionId String
  createdAt    DateTime    @default(now())
  updatedAt    DateTime
  permissions  permissions @relation(fields: [permissionId], references: [id], onDelete: Cascade)
  roles        roles       @relation(fields: [roleId], references: [id], onDelete: Cascade)

  @@unique([roleId, permissionId])
}

model roles {
  id                 String               @id
  name               String               @unique
  displayName        String
  description        String?
  isSystem           Boolean              @default(false)
  tenantId           String?
  createdAt          DateTime             @default(now())
  updatedAt          DateTime
  parentRoleId       String?
  scope              RoleScope            @default(SYSTEM)
  role_permissions   role_permissions[]
  roles              roles?               @relation("rolesToroles", fields: [parentRoleId], references: [id])
  other_roles        roles[]              @relation("rolesToroles")
  tenants            tenants?             @relation(fields: [tenantId], references: [id])
  tenant_invitations tenant_invitations[]
  system_user_roles  system_user_roles[]  @relation("SystemUserRoles")
  tenant_user_roles  tenant_user_roles[]  @relation("TenantUserRoles")
}

model settings {
  id                              String   @id
  type                            String
  createdAt                       DateTime @default(now())
  updatedAt                       DateTime
  createdBy                       String?
  name                            String
  updatedBy                       String?
  value                           Json


  @@unique([name, type])
  @@index([createdBy])
  @@index([type])
  @@index([updatedBy])
}

model subscriptions {
  id                  String    @id
  tenantId            String?
  planId              String?
  tenantName          String
  planName            String
  amount              Float
  period              Int
  numberOfSubscribers Int
  startDate           DateTime
  endDate             DateTime?
  status              String    @default("PENDING")
  remarks             String?
  paymentMethod       String?
  transactionId       String?
  createdAt           DateTime  @default(now())
  updatedAt           DateTime
  plans               plans?    @relation(fields: [planId], references: [id])
  tenants             tenants?  @relation(fields: [tenantId], references: [id])

  @@index([planId])
  @@index([tenantId])
}

model system_logs {
  id               String   @id
  level            String
  message          String
  stack            String?
  path             String?
  method           String?
  userId           String?
  ip               String?
  createdAt        DateTime @default(now())
  action           String?
  details          Json?
  errorMessage     String?
  status           String?
  targetResource   String?
  targetResourceId String?
  tenantId         String?
}

model tenant_credit_purchases {
  id          String   @id
  tenantId    String
  amount      Decimal
  pricePaid   Decimal
  currency    String
  paymentId   String?
  purchasedAt DateTime @default(now())
  notes       String?
  tenants     tenants  @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@index([tenantId])
}

model tenant_invitations {
  id                                           String    @id
  email                                        String
  tenantId                                     String
  roleId                                       String
  status                                       String    @default("pending")
  token                                        String    @unique
  createdAt                                    DateTime  @default(now())
  expiresAt                                    DateTime
  createdById                                  String?
  acceptedById                                 String?
  acceptedAt                                   DateTime?
  roles                                        roles     @relation(fields: [roleId], references: [id])
  tenants                                      tenants   @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@unique([email, tenantId, status])
  @@index([email])
  @@index([roleId])
  @@index([tenantId])
  @@index([token])
}

model tenants {
  id                       String                     @id
  name                     String
  domain                   String?                    @unique
  departments              String[]                   @default([])
  createdAt                DateTime                   @default(now())
  updatedAt                DateTime
  status                   String                     @default("pending")
  adminEmail               String?
  adminName                String?
  companySize              String?                    @default("1-10")
  industry                 String?
  maxProjects              Int?                       @default(10)
  maxStorage               Int?                       @default(10)
  maxUsers                 Int?                       @default(5)
  nextBillingDate          DateTime?
  paymentStatus            String?                    @default("unpaid")
  planId                   String?
  contactEmail             String?
  contactName              String?
  billingCycle             String?                    @default("monthly")
  currentAiCredits         Decimal?                   @default(0)
  ai_bots                  ai_bots[]
  ai_photo_analysis_results ai_photo_analysis_results[]
  albums                   albums[]
  line_bots                line_bots[]
  line_group_verifications line_group_verifications[]
  line_message_logs        line_message_logs[]
  orders                   orders[]
  photos                   photos[]
  projects                 projects[]
  roles                    roles[]
  tasks                    tasks[]
  progress_entries         progress_entries[]
  project_milestones       project_milestones[]
  progress_reports         progress_reports[]
  subscriptions            subscriptions[]
  tenant_credit_purchases  tenant_credit_purchases[]
  tenant_invitations       tenant_invitations[]
  tenant_lifecycle_events  tenant_lifecycle_events[]
  workspace_templates      workspace_templates[]
  comments                 comments[]
  comment_reactions        comment_reactions[]
  comment_mentions         comment_mentions[]
  shared_files             shared_files[]
  file_permissions         file_permissions[]
  file_shares              file_shares[]
  file_access_logs         file_access_logs[]
  message_conversations    message_conversations[]
  message_center_messages  message_center_messages[]
  message_center_notifications message_center_notifications[]
  ai_workflows     ai_workflows[]
  workflow_executions      workflow_executions[]
  plans                    plans?                     @relation(fields: [planId], references: [id])
  tenant_users             tenant_users[]
  workspaces               workspaces[]
}

model system_user_roles {
  id              String       @id
  system_user_id  String  
  role_id         String
  created_at      DateTime     @default(now())
  updated_at      DateTime     @updatedAt
  
  system_user     system_users @relation(fields: [system_user_id], references: [id], onDelete: Cascade)
  role            roles        @relation("SystemUserRoles", fields: [role_id], references: [id], onDelete: Cascade)

  @@unique([system_user_id, role_id])
}

model tenant_user_roles {
  id              String       @id
  tenant_user_id  String  
  role_id         String
  created_at      DateTime     @default(now())
  updated_at      DateTime     @updatedAt
  
  tenant_user     tenant_users @relation(fields: [tenant_user_id], references: [id], onDelete: Cascade)
  role            roles        @relation("TenantUserRoles", fields: [role_id], references: [id], onDelete: Cascade)

  @@unique([tenant_user_id, role_id])
}

// Legacy users model removed - replaced by system_users and tenant_users

model workspace_members {
  id          String       @id
  workspaceId String
  userId      String
  role        String       @default("member")
  createdAt   DateTime     @default(now())
  updatedAt   DateTime
  workspaces  workspaces   @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  tenant_user tenant_users @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([workspaceId, userId])
  @@index([userId])
  @@index([workspaceId])
}

model workspaces {
  id                       String                     @id
  name                     String
  description              String?
  status                   String                     @default("active")
  settings                 Json?
  tenantId                 String
  ownerId                  String
  createdAt                DateTime                   @default(now())
  updatedAt                DateTime
  ai_bots                  ai_bots[]
  line_bots                line_bots[]
  line_group_verifications line_group_verifications[]
  line_message_logs        line_message_logs[]
  workspace_members        workspace_members[]
  workspace_invitations    workspace_invitations[]
  workspace_activity_logs  workspace_activity_logs[]
  projects                 projects[]
  comments                 comments[]
  comment_reactions        comment_reactions[]
  comment_mentions         comment_mentions[]
  shared_files             shared_files[]
  message_conversations    message_conversations[]
  message_center_notifications message_center_notifications[]
  conversations            conversations[]            @relation("ChatConversations")
  ai_workflows     ai_workflows[]
  workflow_executions      workflow_executions[]
  tenants                  tenants                    @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@index([ownerId])
  @@index([tenantId])
}

model workspace_templates {
  id              String    @id @default(cuid())
  name            String
  description     String?
  defaultSettings Json?
  defaultMemberRole String @default("member")
  isSystem        Boolean   @default(false)
  tenantId        String?
  createdBy       String
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
  tenants         tenants?  @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@index([tenantId])
  @@index([isSystem])
}

model workspace_invitations {
  id          String    @id @default(cuid())
  workspaceId String
  email       String
  role        String    @default("member")
  invitedBy   String
  token       String    @unique
  status      String    @default("pending") // pending, accepted, rejected, expired
  expiresAt   DateTime
  acceptedAt  DateTime?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  workspaces  workspaces @relation(fields: [workspaceId], references: [id], onDelete: Cascade)

  @@index([workspaceId])
  @@index([email])
  @@index([token])
  @@index([status])
}

model workspace_activity_logs {
  id            String      @id
  workspace_id  String
  user_id       String
  activity_type String
  details       Json?
  created_at    DateTime    @default(now())
  workspaces    workspaces? @relation(fields: [workspace_id], references: [id], onDelete: Cascade)

  @@index([workspace_id])
  @@index([user_id])
}

// ==========================================
// AI Service Definition System
// ==========================================
model ai_service_definitions {
  id           String    @id @default(cuid())
  key          String    @unique // Unique key for the service, e.g., "codeGeneration"
  name         String    // Human-readable name, e.g., "Code Generation Service"
  description  String?
  is_enabled   Boolean   @default(true)
  created_at   DateTime  @default(now())
  updated_at   DateTime  @updatedAt
  created_by   String
  updated_by   String?
  creator      system_users @relation("CreatedAiServiceDefinitions", fields: [created_by], references: [id])
  updater      system_users? @relation("UpdatedAiServiceDefinitions", fields: [updated_by], references: [id])

  // Relations
  steps        ai_service_steps[]
  templates    ai_service_templates[]
  executions   ai_service_executions[]

  @@map("ai_service_definitions")
}

model ai_service_steps {
  id                  String             @id @default(cuid())
  service_id          String
  step_order          Int                // Order of execution
  step_type           AiServiceStepType  // e.g., PROMPT, CODE_EXECUTION, API_CALL
  name                String
  description         String?
  config              Json               // Configuration for the step (e.g., prompt content, API endpoint)
  is_enabled          Boolean            @default(true)
  created_at          DateTime           @default(now())
  updated_at          DateTime           @updatedAt

  // Relations
  service_definition  ai_service_definitions @relation(fields: [service_id], references: [id], onDelete: Cascade)

  @@unique([service_id, step_order])
  @@map("ai_service_steps")
}

model ai_service_templates {
  id                  String    @id @default(cuid())
  service_id          String
  name                String
  description         String?
  config_template     Json      // A template of the entire service config for easy setup
  is_public           Boolean   @default(false)
  created_at          DateTime  @default(now())
  updated_at          DateTime  @updatedAt

  // Relations
  service_definition  ai_service_definitions @relation(fields: [service_id], references: [id], onDelete: Cascade)
  
  @@map("ai_service_templates")
}

model ai_service_executions {
  id                  String             @id @default(cuid())
  service_id          String
  user_id             String
  workspace_id        String?
  input_data          Json
  output_data         Json?
  status              AiExecutionStatus
  started_at          DateTime           @default(now())
  completed_at        DateTime?
  error_message       String?
  
  // Relations
  service_definition  ai_service_definitions @relation(fields: [service_id], references: [id], onDelete: Cascade)
  
  @@index([service_id, user_id])
  @@map("ai_service_executions")
}

// ==========================================
// AI Service Workflow System (Creator Studio)
// ==========================================
model ai_workflows {
  id              String    @id @default(cuid())
  name            String
  description     String?
  version         String    @default("1.0.0")
  is_published    Boolean   @default(false)
  is_template     Boolean   @default(false)
  
  // 工作流程配置
  config          Json?     // 全域配置參數
  input_schema    Json?     // 輸入資料結構定義
  output_schema   Json?     // 輸出資料結構定義
  
  // 權限與可見性
  visibility      WorkflowVisibility @default(PRIVATE)
  
  // 作者資訊
  created_by      String
  updated_by      String?
  
  // 租戶隔離
  tenant_id       String?
  workspace_id    String?
  
  // 狀態
  status          WorkflowStatus @default(DRAFT)
  
  // 時間戳
  created_at      DateTime  @default(now())
  updated_at      DateTime  @updatedAt
  published_at    DateTime?
  
  // 關聯關係
  nodes           workflow_nodes[]
  executions      workflow_executions[]
  tenants         tenants?  @relation(fields: [tenant_id], references: [id], onDelete: Cascade)
  workspaces      workspaces? @relation(fields: [workspace_id], references: [id], onDelete: Cascade)
  
  @@index([tenant_id])
  @@index([workspace_id])
  @@index([created_by])
  @@index([status])
  @@index([is_published])
  @@map("ai_workflows")
}

model workflow_nodes {
  id              String    @id @default(cuid())
  workflow_id     String
  
  // 節點基本資訊
  name            String
  description     String?
  node_type       WorkflowNodeType
  
  // 節點位置 (用於前端 Canvas 顯示)
  position_x      Float     @default(0)
  position_y      Float     @default(0)
  
  // 節點配置
  config          Json      // 節點特定配置 (如 AI Bot ID、提示範本等)
  
  // 執行順序與依賴
  execution_order Int?      // 執行順序 (可選，支援平行執行)
  
  // 狀態
  is_enabled      Boolean   @default(true)
  
  // 時間戳
  created_at      DateTime  @default(now())
  updated_at      DateTime  @updatedAt
  
  // 關聯關係
  workflow        ai_workflows @relation(fields: [workflow_id], references: [id], onDelete: Cascade)
  input_connections  node_connections[] @relation("NodeInputs")
  output_connections node_connections[] @relation("NodeOutputs")
  
  @@index([workflow_id])
  @@index([node_type])
  @@index([execution_order])
  @@map("workflow_nodes")
}

model node_connections {
  id              String    @id @default(cuid())
  
  // 連接的節點
  source_node_id  String
  target_node_id  String
  
  // 連接端口資訊
  source_port     String    @default("output") // 輸出端口名稱
  target_port     String    @default("input")  // 輸入端口名稱
  
  // 連接配置
  config          Json?     // 連接特定配置 (如資料轉換規則)
  
  // 狀態
  is_enabled      Boolean   @default(true)
  
  // 時間戳
  created_at      DateTime  @default(now())
  updated_at      DateTime  @updatedAt
  
  // 關聯關係
  source_node     workflow_nodes @relation("NodeOutputs", fields: [source_node_id], references: [id], onDelete: Cascade)
  target_node     workflow_nodes @relation("NodeInputs", fields: [target_node_id], references: [id], onDelete: Cascade)
  
  @@unique([source_node_id, target_node_id, source_port, target_port])
  @@index([source_node_id])
  @@index([target_node_id])
  @@map("node_connections")
}

model workflow_executions {
  id              String    @id @default(cuid())
  workflow_id     String
  
  // 執行資訊
  input_data      Json      // 執行輸入
  output_data     Json?     // 執行輸出
  execution_log   Json?     // 執行日誌
  
  // 執行狀態
  status          WorkflowExecutionStatus @default(PENDING)
  error_message   String?
  
  // 執行者資訊
  executed_by     String
  executor_type   String    // "system" | "tenant"
  
  // 租戶隔離
  tenant_id       String?
  workspace_id    String?
  
  // 時間戳
  started_at      DateTime  @default(now())
  completed_at    DateTime?
  
  // 關聯關係
  workflow        ai_workflows @relation(fields: [workflow_id], references: [id], onDelete: Cascade)
  tenants         tenants?  @relation(fields: [tenant_id], references: [id], onDelete: Cascade)
  workspaces      workspaces? @relation(fields: [workspace_id], references: [id], onDelete: Cascade)
  
  @@index([workflow_id])
  @@index([tenant_id])
  @@index([workspace_id])
  @@index([executed_by, executor_type])
  @@index([status])
  @@index([started_at])
  @@map("workflow_executions")
}

// Workflow 相關枚舉
enum WorkflowVisibility {
  PRIVATE       // 僅作者可見
  WORKSPACE     // 工作區成員可見
  TENANT        // 租戶內可見
  PUBLIC        // 公開 (模板)
}

enum WorkflowStatus {
  DRAFT         // 草稿
  PUBLISHED     // 已發布
  ARCHIVED      // 已歸檔
  DEPRECATED    // 已棄用
}

enum WorkflowNodeType {
  // 輸入/輸出節點
  INPUT         // 輸入節點
  OUTPUT        // 輸出節點
  
  // AI 相關節點
  AI_BOT        // AI Bot 執行節點
  AI_ANALYSIS   // AI 分析節點 (專案分析、照片分析等)
  PROMPT_TEMPLATE // 提示範本節點
  
  // 資料處理節點
  DATA_TRANSFORM // 資料轉換節點
  DATA_FILTER   // 資料篩選節點
  DATA_MERGE    // 資料合併節點
  
  // 控制流程節點
  CONDITION     // 條件判斷節點
  LOOP          // 迴圈節點
  PARALLEL      // 平行執行節點
  
  // 外部整合節點
  API_CALL      // API 呼叫節點
  DATABASE      // 資料庫操作節點
  FILE_OPERATION // 檔案操作節點
  
  // 通知節點
  NOTIFICATION  // 通知節點
  EMAIL         // 郵件節點
  WEBHOOK       // Webhook 節點
}

enum WorkflowExecutionStatus {
  PENDING       // 等待執行
  RUNNING       // 執行中
  COMPLETED     // 執行完成
  FAILED        // 執行失敗
  CANCELLED     // 已取消
  TIMEOUT       // 執行逾時
}

enum AiBotProviderType {
  OPENAI
  CLAUDE
  GEMINI
  OPENAI_COMPATIBLE
  CUSTOM       // 自定義
}

enum AiBotResponseFormat {
  TEXT
  JSON_OBJECT
}

enum AiBotScope {
  SYSTEM
  WORKSPACE
  TENANT_TEMPLATE
}

enum LineBotScope {
  SYSTEM
  TENANT
}

enum LineMessageDirection {
  INCOMING
  OUTGOING
}

enum PermissionScope {
  SYSTEM
  TENANT
  WORKSPACE
  GLOBAL
}

enum PhotoCategory {
  SITE
  PROFILE
  PRODUCT
  OTHER
}

enum RoleScope {
  SYSTEM
  TENANT
  WORKSPACE
}

// Legacy UserRole enum removed - replaced by SystemUserRole and TenantUserRole

enum SystemUserRole {
  SUPER_ADMIN
  SYSTEM_ADMIN
  SYSTEM_MODERATOR
}

enum TenantUserStatus {
  ACTIVE
  INACTIVE
  PENDING
  LEFT_COMPANY
  SUSPENDED
}

enum TenantUserRole {
  TENANT_ADMIN
  TENANT_MANAGER
  TENANT_USER
  TENANT_VIEWER
}

enum ProgressType {
  TASK_UPDATE      // 任務更新
  MILESTONE        // 里程碑
  PHOTO_EVIDENCE   // 照片證據
  STATUS_CHANGE    // 狀態變更
  QUALITY_CHECK    // 質量檢查
  RESOURCE_UPDATE  // 資源更新
  RISK_IDENTIFIED  // 風險識別
  ISSUE_REPORTED   // 問題報告
}

enum MilestoneStatus {
  PENDING      // 待處理
  IN_PROGRESS  // 進行中
  COMPLETED    // 已完成
  DELAYED      // 延遲
  CANCELLED    // 已取消
}

enum ReportType {
  DAILY        // 日報
  WEEKLY       // 週報
  MONTHLY      // 月報
  QUARTERLY    // 季報
  PROJECT      // 項目報告
  CUSTOM       // 自定義
}

enum AiServiceStepType {
  PROMPT_TEMPLATE   // Uses a template to generate a prompt for an AI model
  BOT_EXECUTION     // Executes a pre-configured AiBot
  CODE_EXECUTION    // Executes a snippet of code (e.g., JavaScript)
  API_CALL          // Makes an external API call
  DATA_TRANSFORMATION // Transforms data from one step to be used in another
}

enum AiExecutionStatus {
  PENDING
  RUNNING
  COMPLETED
  FAILED
  CANCELLED
}

model system_users {
  id                    String         @id @default(cuid())
  email                 String         @unique
  password              String
  name                  String?
  role                  SystemUserRole @default(SYSTEM_ADMIN)
  status                String         @default("active")
  avatar                String?
  phone                 String?
  mfaEnabled            Boolean        @default(false)
  mfaSecret             String?
  lastLoginAt           DateTime?
  lastLoginIp           String?
  lastLogoutAt          DateTime?
  passwordLastChangedAt DateTime?
  createdAt             DateTime       @default(now())
  updatedAt             DateTime       @updatedAt

  // Relations
  refresh_tokens     refresh_tokens[]
  system_user_roles  system_user_roles[]
  created_ai_service_definitions ai_service_definitions[] @relation("CreatedAiServiceDefinitions")
  updated_ai_service_definitions ai_service_definitions[] @relation("UpdatedAiServiceDefinitions")

  @@index([role])
  @@map("system_users")
}

model tenant_users {
  id                    String           @id @default(cuid())
  email                 String           @unique
  password              String
  name                  String?
  tenantId              String
  role                  TenantUserRole   @default(TENANT_USER)
  status                TenantUserStatus @default(ACTIVE)
  avatar                String?
  phone                 String?
  title                 String?
  department            String?
  lastLoginAt           DateTime?
  lastLoginIp           String?
  lastLogoutAt          DateTime?
  passwordLastChangedAt DateTime?
  mfaEnabled            Boolean          @default(false)
  mfaSecret             String?
  invitedBy             String?
  leftCompanyAt         DateTime?
  leftCompanyReason     String?
  dataTransferStatus    String?          @default("pending")
  dataTransferNote      String?
  createdAt             DateTime         @default(now())
  updatedAt             DateTime         @updatedAt

  // Relations
  tenant                tenants           @relation(fields: [tenantId], references: [id], onDelete: Restrict)
  inviter               tenant_users?     @relation("TenantUserInviter", fields: [invitedBy], references: [id])
  invited_users         tenant_users[]    @relation("TenantUserInviter")
  refresh_tokens        refresh_tokens[]
  tenant_user_roles     tenant_user_roles[]
  workspace_members     workspace_members[]

  @@index([role])
  @@index([tenantId])
  @@map("tenant_users")
}

model tenant_lifecycle_events {
  id          String    @id @default(cuid())
  tenantId    String
  eventType   String    // created, suspended, reactivated, deleted, etc.
  reason      String?
  metadata    Json?
  triggeredBy String?
  createdAt   DateTime  @default(now())
  tenants     tenants   @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@index([tenantId])
  @@index([eventType])
  @@index([createdAt])
}

model comments {
  id          String    @id @default(cuid())
  content     String
  contentType CommentContentType @default(TEXT)
  
  // 關聯的實體 (可以是專案、任務、進度記錄等)
  entityType  CommentEntityType
  entityId    String
  
  // 回覆功能
  parentId    String?   // 父評論 ID，支援巢狀回覆
  threadId    String?   // 討論串 ID，用於快速查詢整個討論串
  
  // 作者資訊
  authorId    String
  authorType  String    // "system" | "tenant"
  
  // 狀態
  isEdited    Boolean   @default(false)
  isDeleted   Boolean   @default(false)
  deletedAt   DateTime?
  
  // 租戶隔離
  tenantId    String
  workspaceId String?
  
  // 時間戳
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  
  // 關聯關係
  parent      comments? @relation("CommentReplies", fields: [parentId], references: [id], onDelete: Cascade)
  replies     comments[] @relation("CommentReplies")
  tenants     tenants   @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  workspaces  workspaces? @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  reactions   comment_reactions[]
  mentions    comment_mentions[]
  
  @@index([entityType, entityId])
  @@index([tenantId])
  @@index([workspaceId])
  @@index([authorId])
  @@index([parentId])
  @@index([threadId])
  @@index([createdAt])
}

model comment_reactions {
  id          String    @id @default(cuid())
  commentId   String
  userId      String
  userType    String    // "system" | "tenant"
  reaction    CommentReactionType
  tenantId    String
  workspaceId String?
  createdAt   DateTime  @default(now())
  
  comment     comments  @relation(fields: [commentId], references: [id], onDelete: Cascade)
  tenants     tenants   @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  workspaces  workspaces? @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  
  @@unique([commentId, userId, userType])
  @@index([commentId])
  @@index([userId])
  @@index([tenantId])
  @@index([workspaceId])
}

model comment_mentions {
  id          String    @id @default(cuid())
  commentId   String
  userId      String
  userType    String    // "system" | "tenant"
  tenantId    String
  workspaceId String?
  createdAt   DateTime  @default(now())
  
  comment     comments  @relation(fields: [commentId], references: [id], onDelete: Cascade)
  tenants     tenants   @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  workspaces  workspaces? @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  
  @@unique([commentId, userId, userType])
  @@index([commentId])
  @@index([userId])
  @@index([tenantId])
  @@index([workspaceId])
}

enum CommentContentType {
  TEXT
  MARKDOWN
  HTML
}

enum CommentEntityType {
  PROJECT
  TASK
  PROGRESS_ENTRY
  MILESTONE
  PHOTO
  DOCUMENT
  SHARED_FILE
}

enum CommentReactionType {
  LIKE
  LOVE
  LAUGH
  ANGRY
  SAD
  THUMBS_UP
  THUMBS_DOWN
}

// 檔案分享系統相關模型
model shared_files {
  id              String    @id @default(cuid())
  name            String
  originalName    String
  description     String?
  fileType        String    // MIME type
  fileExtension   String
  fileSize        Int       // bytes
  filePath        String    // 儲存路徑
  fileUrl         String?   // 公開存取 URL（如果適用）
  
  // 檔案分類
  category        FileCategory @default(DOCUMENT)
  
  // 關聯實體
  entityType      FileEntityType?
  entityId        String?
  
  // 版本控制
  version         Int       @default(1)
  parentFileId    String?   // 指向原始檔案
  isLatestVersion Boolean   @default(true)
  
  // 預覽相關
  thumbnailPath   String?   // 縮圖路徑
  previewPath     String?   // 預覽檔案路徑
  metadata        Json?     // 檔案元數據（尺寸、時長等）
  
  // 權限設定
  visibility      FileVisibility @default(PRIVATE)
  allowDownload   Boolean   @default(true)
  allowComment    Boolean   @default(true)
  expiresAt       DateTime? // 分享過期時間
  
  // 上傳者資訊
  uploaderId      String
  uploaderType    String    // "system" | "tenant"
  
  // 租戶隔離
  tenantId        String
  workspaceId     String?
  
  // 狀態
  status          FileStatus @default(ACTIVE)
  isDeleted       Boolean   @default(false)
  deletedAt       DateTime?
  
  // 時間戳
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
  
  // 關聯關係
  parentFile      shared_files? @relation("FileVersions", fields: [parentFileId], references: [id], onDelete: Cascade)
  versions        shared_files[] @relation("FileVersions")
  tenants         tenants   @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  workspaces      workspaces? @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  permissions     file_permissions[]
  shares          file_shares[]
  access_logs     file_access_logs[]
  
  @@index([tenantId])
  @@index([workspaceId])
  @@index([uploaderId, uploaderType])
  @@index([entityType, entityId])
  @@index([parentFileId])
  @@index([status])
  @@index([createdAt])
}

model file_permissions {
  id          String    @id @default(cuid())
  fileId      String
  userId      String
  userType    String    // "system" | "tenant"
  permission  FilePermission
  grantedBy   String
  grantedAt   DateTime  @default(now())
  expiresAt   DateTime?
  tenantId    String
  
  file        shared_files @relation(fields: [fileId], references: [id], onDelete: Cascade)
  tenants     tenants   @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  
  @@unique([fileId, userId, userType])
  @@index([fileId])
  @@index([userId, userType])
  @@index([tenantId])
}

model file_shares {
  id          String    @id @default(cuid())
  fileId      String
  shareToken  String    @unique
  shareType   ShareType @default(LINK)
  
  // 分享設定
  allowDownload Boolean  @default(true)
  allowComment  Boolean  @default(false)
  requireAuth   Boolean  @default(false)
  password      String?  // 分享密碼
  
  // 限制設定
  maxDownloads  Int?     // 最大下載次數
  currentDownloads Int   @default(0)
  expiresAt     DateTime?
  
  // 分享者資訊
  sharedBy      String
  sharedByType  String   // "system" | "tenant"
  
  // 租戶隔離
  tenantId      String
  
  // 狀態
  isActive      Boolean  @default(true)
  
  // 時間戳
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  
  // 關聯關係
  file          shared_files @relation(fields: [fileId], references: [id], onDelete: Cascade)
  tenants       tenants   @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  access_logs   file_access_logs[]
  
  @@index([fileId])
  @@index([shareToken])
  @@index([tenantId])
  @@index([expiresAt])
}

model file_access_logs {
  id          String    @id @default(cuid())
  fileId      String
  shareId     String?   // 如果是通過分享連結存取
  
  // 存取者資訊
  userId      String?
  userType    String?   // "system" | "tenant"
  ipAddress   String
  userAgent   String?
  
  // 存取類型
  accessType  AccessType
  
  // 租戶隔離
  tenantId    String
  
  // 時間戳
  accessedAt  DateTime  @default(now())
  
  // 關聯關係
  file        shared_files @relation(fields: [fileId], references: [id], onDelete: Cascade)
  share       file_shares? @relation(fields: [shareId], references: [id], onDelete: Cascade)
  tenants     tenants   @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  
  @@index([fileId])
  @@index([shareId])
  @@index([userId, userType])
  @@index([tenantId])
  @@index([accessedAt])
  @@index([accessType])
}

// 檔案系統相關枚舉
enum FileCategory {
  DOCUMENT      // 文件
  IMAGE         // 圖片
  VIDEO         // 影片
  AUDIO         // 音訊
  ARCHIVE       // 壓縮檔
  SPREADSHEET   // 試算表
  PRESENTATION  // 簡報
  CODE          // 程式碼
  OTHER         // 其他
}

enum FileEntityType {
  PROJECT
  TASK
  PROGRESS_ENTRY
  MILESTONE
  COMMENT
  WORKSPACE
  USER_PROFILE
}

enum FileVisibility {
  PRIVATE       // 私人（僅上傳者可見）
  WORKSPACE     // 工作區成員可見
  PROJECT       // 專案成員可見
  TENANT        // 租戶內可見
  PUBLIC        // 公開（有連結即可存取）
}

enum FileStatus {
  ACTIVE        // 正常
  ARCHIVED      // 已歸檔
  QUARANTINED   // 隔離（安全掃描中）
  BLOCKED       // 已封鎖
}

enum FilePermission {
  VIEW          // 檢視
  DOWNLOAD      // 下載
  COMMENT       // 評論
  EDIT          // 編輯（重新上傳版本）
  DELETE        // 刪除
  SHARE         // 分享
  MANAGE        // 管理（所有權限）
}

enum ShareType {
  LINK          // 連結分享
  EMAIL         // 郵件分享
  EMBED         // 嵌入分享
}

enum AccessType {
  VIEW          // 檢視
  DOWNLOAD      // 下載
  PREVIEW       // 預覽
  COMMENT       // 評論
  SHARE         // 分享
}

// Message Center 相關模型
model message_conversations {
  id          String    @id @default(cuid())
  title       String
  type        ConversationType @default(DIRECT)
  
  // 對話參與者
  participantIds Json    // 存儲參與者 ID 陣列
  
  // 最後訊息資訊
  lastMessageId String?
  lastMessageAt DateTime?
  
  // 租戶隔離
  tenantId    String
  workspaceId String?
  
  // 狀態
  isActive    Boolean   @default(true)
  isArchived  Boolean   @default(false)
  
  // 時間戳
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  createdBy   String
  
  // 關聯關係
  tenants     tenants   @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  workspaces  workspaces? @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  messages    message_center_messages[]
  
  @@index([tenantId])
  @@index([workspaceId])
  @@index([createdBy])
  @@index([lastMessageAt])
}

model message_center_messages {
  id              String    @id @default(cuid())
  conversationId  String
  content         String
  contentType     MessageContentType @default(TEXT)
  
  // 發送者資訊
  senderId        String
  senderType      String    // "system" | "tenant"
  senderName      String
  
  // 回覆訊息
  replyToMessageId String?
  
  // 附件
  attachments     Json?     // 存儲附件資訊陣列
  
  // 狀態
  isRead          Boolean   @default(false)
  isEdited        Boolean   @default(false)
  isDeleted       Boolean   @default(false)
  deletedAt       DateTime?
  
  // 租戶隔離
  tenantId        String
  
  // 時間戳
  sentAt          DateTime  @default(now())
  editedAt        DateTime?
  readAt          DateTime?
  
  // 關聯關係
  conversation    message_conversations @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  replyToMessage  message_center_messages? @relation("MessageReplies", fields: [replyToMessageId], references: [id])
  replies         message_center_messages[] @relation("MessageReplies")
  tenants         tenants   @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  
  @@index([conversationId])
  @@index([senderId, senderType])
  @@index([tenantId])
  @@index([sentAt])
  @@index([replyToMessageId])
}

model message_center_notifications {
  id          String    @id @default(cuid())
  title       String
  message     String
  type        NotificationType @default(INFO)
  priority    NotificationPriority @default(NORMAL)
  
  // 接收者資訊
  recipientId String
  recipientType String  // "system" | "tenant"
  
  // 關聯資源
  entityType  String?   // "project", "task", "message", etc.
  entityId    String?
  actionUrl   String?   // 點擊通知的跳轉連結
  
  // 狀態
  isRead      Boolean   @default(false)
  isArchived  Boolean   @default(false)
  
  // 租戶隔離
  tenantId    String
  workspaceId String?
  
  // 時間戳
  createdAt   DateTime  @default(now())
  readAt      DateTime?
  archivedAt  DateTime?
  expiresAt   DateTime? // 通知過期時間
  
  // 關聯關係
  tenants     tenants   @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  workspaces  workspaces? @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  
  @@index([recipientId, recipientType])
  @@index([tenantId])
  @@index([workspaceId])
  @@index([createdAt])
  @@index([isRead])
  @@index([entityType, entityId])
}

// Message Center 相關枚舉
enum ConversationType {
  DIRECT        // 私人對話
  GROUP         // 群組對話
  ANNOUNCEMENT  // 公告
  SYSTEM        // 系統訊息
}

enum MessageContentType {
  TEXT          // 純文字
  RICH_TEXT     // 富文本
  FILE          // 檔案
  IMAGE         // 圖片
  LINK          // 連結
  SYSTEM        // 系統訊息
}

enum NotificationType {
  INFO          // 資訊
  SUCCESS       // 成功
  WARNING       // 警告
  ERROR         // 錯誤
  REMINDER      // 提醒
  ANNOUNCEMENT  // 公告
}

enum NotificationPriority {
  LOW           // 低
  NORMAL        // 一般
  HIGH          // 高
  URGENT        // 緊急
}

// Chat 相關模型
model conversations {
  id              String    @id @default(cuid())
  type            ConversationType @default(DIRECT)
  name            String?
  description     String?
  avatar          String?
  isPrivate       Boolean   @default(false)
  isArchived      Boolean   @default(false)
  
  // 工作區關聯
  workspaceId     String
  workspaces      workspaces @relation("ChatConversations", fields: [workspaceId], references: [id], onDelete: Cascade)
  
  // 創建者
  createdBy       String
  
  // 最後活動
  lastMessageId   String?
  lastActivityAt  DateTime?
  
  // 對話設定
  allowFileSharing    Boolean @default(true)
  allowImageSharing   Boolean @default(true)
  allowVideoSharing   Boolean @default(true)
  allowAudioSharing   Boolean @default(true)
  maxFileSize         Int     @default(10485760) // 10MB
  retentionDays       Int?
  isEncrypted         Boolean @default(false)
  requireApprovalForNewMembers Boolean @default(false)
  
  // 時間戳
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
  
  // 關聯
  participants    conversation_participants[]
  messages        messages[]
  lastMessage     messages? @relation("ConversationLastMessage", fields: [lastMessageId], references: [id])
  
  @@index([workspaceId])
  @@index([type])
  @@index([createdBy])
  @@index([lastActivityAt])
}

model conversation_participants {
  id              String    @id @default(cuid())
  conversationId  String
  userId          String
  role            ParticipantRole @default(MEMBER)
  status          ParticipantStatus @default(ACTIVE)
  
  // 讀取狀態
  lastSeenAt      DateTime?
  lastReadMessageId String?
  lastReadAt      DateTime?
  
  // 個人設定
  isMuted         Boolean   @default(false)
  mutedUntil      DateTime?
  isPinned        Boolean   @default(false)
  allowNotifications Boolean @default(true)
  
  // 時間戳
  joinedAt        DateTime  @default(now())
  leftAt          DateTime?
  updatedAt       DateTime  @updatedAt
  
  // 關聯
  conversation    conversations @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  
  @@unique([conversationId, userId])
  @@index([conversationId])
  @@index([userId])
  @@index([status])
}

model messages {
  id              String    @id @default(cuid())
  conversationId  String
  senderId        String
  type            MessageType @default(TEXT)
  content         String
  
  // 回覆功能
  replyToId       String?
  replyToMessage  messages? @relation("MessageReplies", fields: [replyToId], references: [id])
  replies         messages[] @relation("MessageReplies")
  
  // 狀態
  status          MessageStatus @default(SENT)
  isEdited        Boolean   @default(false)
  editedAt        DateTime?
  isDeleted       Boolean   @default(false)
  deletedAt       DateTime?
  
  // 提及
  mentions        Json?     // 提及的用戶 ID 列表
  
  // 元數據
  metadata        Json?
  
  // 時間戳
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
  
  // 關聯
  conversation    conversations @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  reactions       message_reactions[]
  attachments     message_attachments[]
  conversationAsLastMessage conversations[] @relation("ConversationLastMessage")
  
  @@index([conversationId])
  @@index([senderId])
  @@index([type])
  @@index([createdAt])
}

model message_reactions {
  id        String    @id @default(cuid())
  messageId String
  userId    String
  emoji     String
  createdAt DateTime  @default(now())
  
  // 關聯
  message   messages  @relation(fields: [messageId], references: [id], onDelete: Cascade)
  
  @@unique([messageId, userId, emoji])
  @@index([messageId])
  @@index([userId])
}

model message_attachments {
  id            String    @id @default(cuid())
  messageId     String
  name          String
  type          String    // MIME type
  size          BigInt
  url           String
  thumbnailUrl  String?
  previewUrl    String?
  
  // 檔案元數據
  metadata      Json?
  isProcessed   Boolean   @default(false)
  processingError String?
  
  createdAt     DateTime  @default(now())
  
  // 關聯
  message       messages  @relation(fields: [messageId], references: [id], onDelete: Cascade)
  
  @@index([messageId])
  @@index([type])
}

// Chat 相關枚舉
enum ParticipantRole {
  OWNER
  ADMIN
  MEMBER
  GUEST
}

enum ParticipantStatus {
  ACTIVE
  INACTIVE
  BANNED
  LEFT
}

enum MessageType {
  TEXT
  IMAGE
  FILE
  AUDIO
  VIDEO
  SYSTEM
}

enum MessageStatus {
  SENDING
  SENT
  DELIVERED
  READ
  FAILED
}

// OAuth 帳號模型 - 用於存儲外部認證提供商的用戶帳號
model oauth_accounts {
  id            String   @id @default(cuid())
  provider      String   // 'google', 'facebook', 'github', 'line', etc.
  providerId    String   // 外部提供商的用戶 ID
  userId        String   // 本地用戶 ID
  userType      String   // 'system' 或 'tenant'
  email         String?  // 提供商提供的電子郵件
  profile       Json?    // 存儲提供商提供的完整個人資料
  accessToken   String?  // 存儲加密的訪問令牌
  refreshToken  String?  // 存儲加密的刷新令牌
  tokenExpires  DateTime? // 令牌過期時間
  lastLogin     DateTime // 最後登入時間
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  system_user   system_users? @relation(fields: [userId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  tenant_user   tenant_users? @relation(fields: [userId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  @@unique([provider, providerId])
  @@index([userId, userType])
  @@index([email])
  @@map("oauth_accounts")
}
