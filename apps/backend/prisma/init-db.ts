import { PrismaClient } from "@prisma/client";
import * as bcrypt from "bcryptjs";
import { randomUUID } from "crypto";
import { syncPermissions } from "./templates/permission-sync";
import * as dotenv from "dotenv";

// 載入環境變數
dotenv.config();

// 手動定義枚舉，因為 Prisma 客戶端當前不導出這些
enum UserRole {
  SUPER_ADMIN = "SUPER_ADMIN",
  SYSTEM_ADMIN = "SYSTEM_ADMIN",
  TENANT_ADMIN = "TENANT_ADMIN",
  TENANT_USER = "TENANT_USER",
}

enum PhotoCategory {
  SITE = "SITE",
  PROFILE = "PROFILE",
  PRODUCT = "PRODUCT",
  OTHER = "OTHER",
}

// 手動擴充 PrismaClient 型別
export interface ExtendedPrismaClient extends PrismaClient {
  subscription: any;
  tenantInvitation: any;
  settings: any;
  role: any;
  permission: any;
  album: any;
  userRoleMapping: any;
  rolePermissionMapping: any;
  lineAuthState: any;
  order: any;
  orderHistory: any;
  payment: any;
  aiBot: any;
}

// 初始化選項介面
export interface InitDatabaseOptions {
  force?: boolean;
  fullData?: boolean;
  prismaClient?: ExtendedPrismaClient;
}

const prisma = new PrismaClient() as ExtendedPrismaClient;

// 檢查是否需要強制初始化
const shouldForceInit = process.argv.includes("--force");

/**
 * 執行更新腳本
 * 當資料庫已有資料時，執行此函數以更新資料
 */
async function executeUpdateScript(options: InitDatabaseOptions = {}) {
  const client: ExtendedPrismaClient = options.prismaClient ?? prisma;

  console.log("🔄 執行資料庫更新腳本...");

  try {
    // 檢查是否存在訂閱資料表
    const subscriptionCount = await client.subscription.count().catch(() => -1);

    if (subscriptionCount === -1) {
      console.log("⚠️ 訂閱資料表尚未建立，請先執行資料庫遷移");
      console.log("提示: 執行 npx prisma migrate dev 來更新資料庫結構");
      return;
    }

    // 檢查是否存在租戶邀請資料表
    const invitationCount = await client.tenantInvitation
      .count()
      .catch(() => -1);

    if (invitationCount === -1) {
      console.log("⚠️ 租戶邀請資料表尚未建立，請先執行資料庫遷移");
      console.log("提示: 執行 npx prisma migrate dev 來更新資料庫結構");
      return;
    }

    // 檢查是否存在訂單資料表
    const orderCount = await client.order.count().catch(() => -1);

    if (orderCount === -1) {
      console.log("⚠️ 訂單資料表尚未建立，請先執行資料庫遷移");
      console.log("提示: 執行 npx prisma migrate dev 來更新資料庫結構");
      return;
    }

    // 檢查是否需要更新現有方案
    const plans = await client.plan.findMany();

    if (plans.length > 0) {
      console.log("🔄 更新現有方案資料...");

      // 確保方案有正確的 features 格式
      for (const plan of plans) {
        // 檢查 features 是否為字串，若是則不需更新
        if (typeof plan.features === "string") {
          continue;
        }

        // 將 features 轉為字串格式
        await client.plan.update({
          where: { id: plan.id },
          data: {
            features: JSON.stringify(plan.features || []),
          },
        });
      }
    }

    // 檢查訂閱狀態並更新
    const tenants = await client.tenant.findMany({
      include: {
        // @ts-ignore - 忽略 TypeScript 錯誤，因為我們知道這個關聯存在
        subscriptions: true,
      },
    });

    for (const tenant of tenants) {
      // @ts-ignore - 忽略 TypeScript 錯誤，因為我們知道這個屬性存在
      if (tenant.subscriptions.length === 0 && tenant.planId) {
        console.log(`🔄 為租戶 ${tenant.name} 建立訂閱記錄...`);

        const plan = await client.plan.findUnique({
          where: { id: tenant.planId },
        });

        if (plan) {
          // 建立訂閱記錄
          await client.subscription.create({
            data: {
              id: randomUUID(),
              tenant_id: tenant.id,
              planId: plan.id,
              tenantName: tenant.name,
              planName: plan.name,
              amount: plan.price,
              period: 1,
              numberOfSubscribers: 1,
              startDate: new Date(),
              endDate:
                tenant.nextBillingDate ||
                new Date(new Date().setMonth(new Date().getMonth() + 1)),
              status: tenant.paymentStatus === "paid" ? "ACTIVE" : "PENDING",
              paymentMethod: "credit_card",
              createdAt: new Date(),
              updatedAt: new Date(),
            },
          });
        }
      }
    }

    // 確保所有使用者都有角色映射
    const usersWithoutRoles = await client.user.findMany({
      where: {
        userRoles: {
          none: {},
        },
      } as any,
      include: {
        tenants: {
          select: {
            id: true,
            name: true,
            status: true,
            domain: true,
          },
        },
      },
    });

    if (usersWithoutRoles.length > 0) {
      console.log(`🔄 更新 ${usersWithoutRoles.length} 位使用者的角色...`);

      for (const user of usersWithoutRoles) {
        // 根據使用者的角色欄位找到對應的角色記錄
        let roleName = "";

        switch (user.role) {
          case "SUPER_ADMIN":
            roleName = "SUPER_ADMIN";
            break;
          case "SYSTEM_ADMIN":
            roleName = "SYSTEM_ADMIN";
            break;
          case "TENANT_ADMIN": {
            // 使用 include 查詢出來的 tenant 關聯
            const tenantDomain = user.tenant?.domain;
            roleName =
              tenantDomain === "horizai.com"
                ? "HORIZAI_TENANT_ADMIN"
                : "DEMO_TENANT_ADMIN";
            break;
          }
          case "TENANT_USER": {
            // 使用 include 查詢出來的 tenant 關聯
            const tenantDomain = user.tenant?.domain;
            roleName =
              tenantDomain === "horizai.com"
                ? "HORIZAI_TENANT_USER"
                : "DEMO_TENANT_USER";
            break;
          }
          default:
            roleName = "DEMO_TENANT_USER";
        }

        const role = await (client as any).role.findFirst({
          where: {
            name: roleName,
          },
        });

        if (role) {
          await (client as any).userRoleMapping.create({
            data: {
              id: randomUUID(),
              user_id: user.id,
              roleId: role.id,
              createdAt: new Date(),
              updatedAt: new Date(),
            },
          });
        }
      }
    }

    console.log("✅ 資料庫更新完成！");
  } catch (error) {
    console.error("❌ 資料庫更新失敗:", error);
    throw error;
  }
}

/**
 * 初始化資料庫腳本
 * 1. 建立基本租戶
 * 2. 建立系統管理員角色
 * 3. 建立基本權限
 * 4. 建立超級管理員帳號
 * 5. 建立租戶與角色關聯
 */
export async function initializeDatabase(options: InitDatabaseOptions = {}) {
  const client: ExtendedPrismaClient = options.prismaClient ?? prisma;
  const { force = false, fullData = false } = options;

  console.log("🚀 開始初始化資料庫...");

  // 檢查是否有管理員帳號
  const adminExists = await client.user.findFirst({
    where: {
      role: "super_admin",
    },
  });

  // 若已有管理員帳號且不是強制初始化，僅執行更新腳本
  if (adminExists && !force && !shouldForceInit) {
    console.log("👨‍💼 管理員帳號已存在，僅執行更新腳本");
    await executeUpdateScript(options);
    return;
  }

  try {
    // 清除資料庫中的現有資料
    console.log("🧹 清除現有資料...");

    // 依照資料表依賴關係的順序刪除資料
    await client.lineAuthState
      .deleteMany({})
      .catch(() => console.log("清除 lineAuthState 失敗或資料表不存在"));
    await client.photo
      .deleteMany({})
      .catch(() => console.log("清除 photo 失敗或資料表不存在"));
    await client.project
      .deleteMany({})
      .catch(() => console.log("清除 project 失敗或資料表不存在"));
    await client.album
      .deleteMany({})
      .catch(() => console.log("清除 album 失敗或資料表不存在"));

    // 清除 AI Bot 相關資料
    await client.$executeRaw`TRUNCATE TABLE "ai_bots" CASCADE`.catch(() =>
      console.log("清除 ai_bots 失敗或資料表不存在")
    );

    // 清除訂單相關資料
    await client.payment
      .deleteMany({})
      .catch(() => console.log("清除 payment 失敗或資料表不存在"));
    await client.orderHistory
      .deleteMany({})
      .catch(() => console.log("清除 orderHistory 失敗或資料表不存在"));
    await client.order
      .deleteMany({})
      .catch(() => console.log("清除 order 失敗或資料表不存在"));

    // 清除訂閱相關資料
    await client.subscription
      .deleteMany({})
      .catch(() => console.log("清除 subscription 失敗或資料表不存在"));
    await client.tenantInvitation
      .deleteMany({})
      .catch(() => console.log("清除 tenantInvitation 失敗或資料表不存在"));

    // 清除角色和權限關聯
    await client.$executeRaw`TRUNCATE TABLE "role_permissions" CASCADE`.catch(
      () => console.log("清除 role_permissions 失敗或資料表不存在")
    );
    await client.$executeRaw`TRUNCATE TABLE "user_roles" CASCADE`.catch(() =>
      console.log("清除 user_roles 失敗或資料表不存在")
    );

    // 清除權限和角色資料
    await client.$executeRaw`TRUNCATE TABLE "permissions" CASCADE`.catch(() =>
      console.log("清除 permissions 失敗或資料表不存在")
    );
    await client.$executeRaw`TRUNCATE TABLE "roles" CASCADE`.catch(() =>
      console.log("清除 roles 失敗或資料表不存在")
    );

    // 清除使用者和租戶資料
    await client.$executeRaw`TRUNCATE TABLE "workspace_members" CASCADE`.catch(
      () => console.log("清除 workspace_members 失敗或資料表不存在")
    );
    await client.$executeRaw`TRUNCATE TABLE "workspaces" CASCADE`.catch(() =>
      console.log("清除 workspaces 失敗或資料表不存在")
    );
    await client.$executeRaw`TRUNCATE TABLE "users" CASCADE`.catch(() =>
      console.log("清除 users 失敗或資料表不存在")
    );
    await client.$executeRaw`TRUNCATE TABLE "tenants" CASCADE`.catch(() =>
      console.log("清除 tenants 失敗或資料表不存在")
    );

    // 清除設定和方案資料
    await client.$executeRaw`TRUNCATE TABLE "settings" CASCADE`.catch(() =>
      console.log("清除 settings 失敗或資料表不存在")
    );
    await client.$executeRaw`TRUNCATE TABLE "plans" CASCADE`.catch(() =>
      console.log("清除 plans 失敗或資料表不存在")
    );

    // 建立基本方案
    console.log("📋 建立基本方案...");

    const planIds = {
      free: randomUUID(),
      starter: randomUUID(),
      professional: randomUUID(),
      enterprise: randomUUID(),
    };

    // 預先處理 JSON 字串
    const freeFeaturesJson = JSON.stringify([
      "1 名使用者",
      "3 個專案",
      "1GB 儲存空間",
    ]);
    const starterFeaturesJson = JSON.stringify([
      "5 名使用者",
      "10 個專案",
      "5GB 儲存空間",
    ]);
    const proFeaturesJson = JSON.stringify([
      "20 名使用者",
      "無限專案",
      "20GB 儲存空間",
      "優先客服支援",
    ]);
    const enterpriseFeaturesJson = JSON.stringify([
      "無限使用者",
      "無限專案",
      "無限儲存空間",
      "24/7 專屬客服",
      "企業級安全防護",
    ]);

    // 分別建立每個方案
    await client.plan.create({
      data: {
        id: planIds.free,
        name: "免費方案",
        description: "適合小型專案的免費方案",
        price: 0,
        billingCycle: "monthly",
        features: freeFeaturesJson,
        maxUsers: 1,
        maxProjects: 3,
        maxStorage: 1,
        isPopular: false,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    });

    await client.plan.create({
      data: {
        id: planIds.starter,
        name: "入門方案",
        description: "適合小型團隊的入門方案",
        price: 199,
        billingCycle: "monthly",
        features: starterFeaturesJson,
        maxUsers: 5,
        maxProjects: 10,
        maxStorage: 5,
        isPopular: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    });

    await client.plan.create({
      data: {
        id: planIds.professional,
        name: "專業方案",
        description: "適合中型團隊的專業方案",
        price: 499,
        billingCycle: "monthly",
        features: proFeaturesJson,
        maxUsers: 20,
        maxProjects: -1, // 無限
        maxStorage: 20,
        isPopular: false,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    });

    await client.plan.create({
      data: {
        id: planIds.enterprise,
        name: "企業方案",
        description: "適合大型企業的高級方案",
        price: 999,
        billingCycle: "monthly",
        features: enterpriseFeaturesJson,
        maxUsers: -1, // 無限
        maxProjects: -1, // 無限
        maxStorage: -1, // 無限
        isPopular: false,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    });

    // 创建测试租户
    console.log("🏢 建立測試租戶...");

    // HorizAI 官方租戶
    const horizaiTenantId = randomUUID();
    const demoTenantId = randomUUID();

    await client.tenant.createMany({
      data: [
        {
          id: horizaiTenantId,
          name: "HorizAI",
          domain: "horizai.com",
          departments: ["管理部", "技術部", "業務部", "客服部"],
          status: "active",
          adminEmail: "<EMAIL>",
          adminName: "系統管理員",
          companySize: "10-50",
          industry: "科技服務",
          maxUsers: 20,
          maxProjects: -1,
          maxStorage: 20,
          paymentStatus: "paid",
          planId: planIds.professional,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: demoTenantId,
          name: "Demo Company",
          domain: "demo.com",
          departments: ["管理部", "技術部", "業務部"],
          status: "active",
          adminEmail: "<EMAIL>",
          adminName: "Demo Admin",
          companySize: "1-10",
          industry: "教育",
          maxUsers: 5,
          maxProjects: 10,
          maxStorage: 5,
          paymentStatus: "paid",
          planId: planIds.starter,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ],
    });

    // 建立角色
    console.log("👑 建立角色...");

    const roleIds = {
      superAdmin: randomUUID(),
      systemAdmin: randomUUID(),
      horizaiTenantAdmin: randomUUID(),
      horizaiTenantUser: randomUUID(),
      demoTenantAdmin: randomUUID(),
      demoTenantUser: randomUUID(),
    };

    await client.role.createMany({
      data: [
        {
          id: roleIds.superAdmin,
          name: "SUPER_ADMIN",
          displayName: "超級管理員",
          description: "擁有系統最高權限",
          isSystem: true,
          scope: "SYSTEM",
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: roleIds.systemAdmin,
          name: "SYSTEM_ADMIN",
          displayName: "系統管理員",
          description: "管理系統設定與租戶",
          isSystem: true,
          scope: "SYSTEM",
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: roleIds.horizaiTenantAdmin,
          name: "HORIZAI_TENANT_ADMIN",
          displayName: "租戶管理員",
          description: "管理租戶內所有資源",
          isSystem: false,
          tenantId: horizaiTenantId,
          scope: "TENANT",
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: roleIds.horizaiTenantUser,
          name: "HORIZAI_TENANT_USER",
          displayName: "租戶使用者",
          description: "租戶一般使用者",
          isSystem: false,
          tenantId: horizaiTenantId,
          scope: "TENANT",
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: roleIds.demoTenantAdmin,
          name: "DEMO_TENANT_ADMIN",
          displayName: "租戶管理員",
          description: "管理租戶內所有資源",
          isSystem: false,
          tenantId: demoTenantId,
          scope: "TENANT",
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: roleIds.demoTenantUser,
          name: "DEMO_TENANT_USER",
          displayName: "租戶使用者",
          description: "租戶一般使用者",
          isSystem: false,
          tenantId: demoTenantId,
          scope: "TENANT",
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ],
    });

    // 在建立角色之後，同步權限
    console.log("🔒 同步權限系統...");
    await syncPermissions({ force: true, prismaClient: client });

    // 建立使用者
    console.log("👤 建立使用者...");
    const hashedPassword = await bcrypt.hash("Admin@123", 10);

    const userIds = {
      superAdmin: randomUUID(),
      horizaiAdmin: randomUUID(),
      demoAdmin: randomUUID(),
    };

    await client.user.createMany({
      data: [
        {
          id: userIds.superAdmin,
          email: "<EMAIL>",
          password: hashedPassword,
          name: "Super Admin",
          role: "super_admin",
          tenantId: horizaiTenantId,
          title: "系統開發者",
          department: "技術部",
          status: "active",
          lineConnected: false,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: userIds.horizaiAdmin,
          email: "<EMAIL>",
          password: hashedPassword,
          name: "HorizAI Admin",
          role: "admin",
          tenantId: horizaiTenantId,
          title: "租戶管理員",
          department: "管理部",
          status: "active",
          lineConnected: false,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: userIds.demoAdmin,
          email: "<EMAIL>",
          password: hashedPassword,
          name: "Demo Admin",
          role: "admin",
          tenantId: demoTenantId,
          title: "租戶管理員",
          department: "管理部",
          status: "active",
          lineConnected: false,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ],
    });

    // 使用者角色關聯
    console.log("🔗 建立使用者角色關聯...");

    await client.userRoleMapping.createMany({
      data: [
        {
          id: randomUUID(),
          userId: userIds.superAdmin,
          roleId: roleIds.superAdmin,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: randomUUID(),
          userId: userIds.horizaiAdmin,
          roleId: roleIds.horizaiTenantAdmin,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: randomUUID(),
          userId: userIds.demoAdmin,
          roleId: roleIds.demoTenantAdmin,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ],
    });

    // 建立系統設定
    console.log("⚙️ 建立系統設定...");

    // 預先處理所有 JSON 值
    const settingsValues = {
      // 一般設定
      siteNameValue: JSON.stringify("HorizAI 管理系統"),
      siteDescValue: JSON.stringify("企業級 SaaS 應用程式平台"),
      logoUrlValue: JSON.stringify("/images/horizai-logo.svg"),
      siteUrlValue: JSON.stringify("https://horizai.com"),
      faviconUrlValue: JSON.stringify("/favicon.ico"),
      primaryColorValue: JSON.stringify("#4F46E5"),
      secondaryColorValue: JSON.stringify("#1E293B"),

      // 郵件設定
      emailProviderValue: JSON.stringify("smtp"),
      emailHostValue: JSON.stringify("smtp.example.com"),
      emailPortValue: JSON.stringify(587),
      emailSecureValue: JSON.stringify(false),
      emailAuthUserValue: JSON.stringify("<EMAIL>"),
      emailAuthPassValue: JSON.stringify("password"),
      emailFromValue: JSON.stringify("HorizAI <<EMAIL>>"),

      // 儲存設定
      storageProviderValue: JSON.stringify("local"),
      storagePathValue: JSON.stringify("./uploads"),

      // 系統設定
      maintenanceModeValue: JSON.stringify(false),
      debugModeValue: JSON.stringify(false),
      allowRegistrationValue: JSON.stringify(true),
      defaultLanguageValue: JSON.stringify("zh-TW"),
      timeZoneValue: JSON.stringify("Asia/Taipei"),
    };

    // 分別建立每個設定
    // 一般設定
    await client.settings.create({
      data: {
        id: "general_siteName",
        type: "general",
        name: "siteName",
        value: settingsValues.siteNameValue,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    });

    await client.settings.create({
      data: {
        id: "general_siteDescription",
        type: "general",
        name: "siteDescription",
        value: settingsValues.siteDescValue,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    });

    await client.settings.create({
      data: {
        id: "general_logoUrl",
        type: "general",
        name: "logoUrl",
        value: settingsValues.logoUrlValue,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    });

    await client.settings.create({
      data: {
        id: "general_siteUrl",
        type: "general",
        name: "siteUrl",
        value: settingsValues.siteUrlValue,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    });

    await client.settings.create({
      data: {
        id: "general_faviconUrl",
        type: "general",
        name: "faviconUrl",
        value: settingsValues.faviconUrlValue,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    });

    await client.settings.create({
      data: {
        id: "general_primaryColor",
        type: "general",
        name: "primaryColor",
        value: settingsValues.primaryColorValue,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    });

    await client.settings.create({
      data: {
        id: "general_secondaryColor",
        type: "general",
        name: "secondaryColor",
        value: settingsValues.secondaryColorValue,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    });

    await client.settings.create({
      data: {
        id: "general_defaultLanguage",
        type: "general",
        name: "defaultLanguage",
        value: settingsValues.defaultLanguageValue,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    });

    await client.settings.create({
      data: {
        id: "general_timeZone",
        type: "general",
        name: "timeZone",
        value: settingsValues.timeZoneValue,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    });

    // 郵件設定
    await client.settings.create({
      data: {
        id: "email_provider",
        type: "email",
        name: "provider",
        value: settingsValues.emailProviderValue,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    });

    await client.settings.create({
      data: {
        id: "email_host",
        type: "email",
        name: "host",
        value: settingsValues.emailHostValue,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    });

    await client.settings.create({
      data: {
        id: "email_port",
        type: "email",
        name: "port",
        value: settingsValues.emailPortValue,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    });

    await client.settings.create({
      data: {
        id: "email_secure",
        type: "email",
        name: "secure",
        value: settingsValues.emailSecureValue,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    });

    await client.settings.create({
      data: {
        id: "email_auth_user",
        type: "email",
        name: "auth.user",
        value: settingsValues.emailAuthUserValue,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    });

    await client.settings.create({
      data: {
        id: "email_auth_pass",
        type: "email",
        name: "auth.pass",
        value: settingsValues.emailAuthPassValue,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    });

    await client.settings.create({
      data: {
        id: "email_from",
        type: "email",
        name: "from",
        value: settingsValues.emailFromValue,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    });

    // 儲存設定
    await client.settings.create({
      data: {
        id: "storage_provider",
        type: "storage",
        name: "provider",
        value: settingsValues.storageProviderValue,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    });

    await client.settings.create({
      data: {
        id: "storage_path",
        type: "storage",
        name: "path",
        value: settingsValues.storagePathValue,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    });

    // 系統設定
    await client.settings.create({
      data: {
        id: "system_maintenanceMode",
        type: "system",
        name: "maintenanceMode",
        value: settingsValues.maintenanceModeValue,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    });

    await client.settings.create({
      data: {
        id: "system_debugMode",
        type: "system",
        name: "debugMode",
        value: settingsValues.debugModeValue,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    });

    await client.settings.create({
      data: {
        id: "system_allowRegistration",
        type: "system",
        name: "allowRegistration",
        value: settingsValues.allowRegistrationValue,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    });

    // 若需要建立更多測試數據
    if (fullData) {
      // 這裡可以加入更多測試資料的建立邏輯
      console.log("📊 建立更多測試數據...");

      // 建立訂單測試資料
      console.log("📝 建立訂單測試資料...");

      // 讀取第一個租戶和方案
      const firstTenant = await client.tenant.findFirst({
        where: { status: "active" },
      });

      const plans = await client.plan.findMany({
        take: 3,
      });

      if (firstTenant && plans.length > 0) {
        // 建立訂單 A - 已完成狀態
        const orderIdA = randomUUID();
        const orderAHistoryId1 = randomUUID();
        const orderAHistoryId2 = randomUUID();
        const orderAHistoryId3 = randomUUID();
        const orderAPaymentId = randomUUID();

        await client.order.create({
          data: {
            id: orderIdA,
            tenant_id: firstTenant.id,
            planId: plans[0].id,
            tenantName: firstTenant.name,
            planName: plans[0].name,
            amount: plans[0].price,
            period: 12,
            numberOfSubscribers: 5,
            startDate: new Date(new Date().setMonth(new Date().getMonth() - 1)),
            endDate: new Date(new Date().setMonth(new Date().getMonth() + 11)),
            status: "COMPLETED",
            remarks: "年付訂閱",
            createdAt: new Date(new Date().setMonth(new Date().getMonth() - 1)),
            updatedAt: new Date(),
            orderHistory: {
              createMany: {
                data: [
                  {
                    id: orderAHistoryId1,
                    type: "info",
                    status: "訂單已建立",
                    description: "訂單已成功建立，等待處理",
                    by: null,
                    createdAt: new Date(
                      new Date().setDate(new Date().getDate() - 30)
                    ),
                  },
                  {
                    id: orderAHistoryId2,
                    type: "success",
                    status: "付款已完成",
                    description: "已透過信用卡完成付款",
                    by: null,
                    createdAt: new Date(
                      new Date().setDate(new Date().getDate() - 29)
                    ),
                  },
                  {
                    id: orderAHistoryId3,
                    type: "success",
                    status: "訂單已完成",
                    description: "訂單已成功處理完成",
                    by: "系統管理員",
                    createdAt: new Date(
                      new Date().setDate(new Date().getDate() - 28)
                    ),
                  },
                ],
              },
            },
            payment: {
              create: {
                id: orderAPaymentId,
                method: "信用卡",
                status: "paid",
                transactionId: `txn_${randomUUID().substring(0, 8)}`,
                amount: plans[0].price * 12 * 0.9, // 年付優惠 90%
                currency: "TWD",
                createdAt: new Date(
                  new Date().setDate(new Date().getDate() - 29)
                ),
                updatedAt: new Date(
                  new Date().setDate(new Date().getDate() - 29)
                ),
              },
            },
          },
        });

        // 建立訂單 B - 待處理狀態
        const orderIdB = randomUUID();
        const orderBHistoryId1 = randomUUID();
        const orderBPaymentId = randomUUID();

        await client.order.create({
          data: {
            id: orderIdB,
            tenant_id: firstTenant.id,
            planId: plans[1].id,
            tenantName: firstTenant.name,
            planName: plans[1].name,
            amount: plans[1].price,
            period: 1,
            numberOfSubscribers: 15,
            startDate: new Date(),
            endDate: new Date(new Date().setMonth(new Date().getMonth() + 1)),
            status: "PENDING",
            remarks: "月付訂閱",
            orderHistory: {
              create: {
                id: orderBHistoryId1,
                type: "info",
                status: "訂單已建立",
                description: "訂單已成功建立，等待處理",
                by: null,
              },
            },
            payment: {
              create: {
                id: orderBPaymentId,
                method: "銀行轉帳",
                status: "pending",
                amount: plans[1].price,
                currency: "TWD",
              },
            },
          },
        });

        // 建立訂單 C - 已取消狀態
        const orderIdC = randomUUID();
        const orderCHistoryId1 = randomUUID();
        const orderCHistoryId2 = randomUUID();
        const orderCHistoryId3 = randomUUID();
        const orderCPaymentId = randomUUID();

        await client.order.create({
          data: {
            id: orderIdC,
            tenant_id: firstTenant.id,
            planId: plans[2].id,
            tenantName: firstTenant.name,
            planName: plans[2].name,
            amount: plans[2].price,
            period: 6,
            numberOfSubscribers: 50,
            startDate: new Date(new Date().setDate(new Date().getDate() - 10)),
            endDate: new Date(new Date().setMonth(new Date().getMonth() + 6)),
            status: "CANCELLED",
            remarks: "半年付訂閱，已取消",
            createdAt: new Date(new Date().setDate(new Date().getDate() - 10)),
            updatedAt: new Date(new Date().setDate(new Date().getDate() - 5)),
            orderHistory: {
              createMany: {
                data: [
                  {
                    id: orderCHistoryId1,
                    type: "info",
                    status: "訂單已建立",
                    description: "訂單已成功建立，等待處理",
                    by: null,
                    createdAt: new Date(
                      new Date().setDate(new Date().getDate() - 10)
                    ),
                  },
                  {
                    id: orderCHistoryId2,
                    type: "danger",
                    status: "付款失敗",
                    description: "透過信用卡付款失敗，卡片已過期",
                    by: null,
                    createdAt: new Date(
                      new Date().setDate(new Date().getDate() - 7)
                    ),
                  },
                  {
                    id: orderCHistoryId3,
                    type: "danger",
                    status: "訂單已取消",
                    description: "因付款失敗，訂單已被取消處理",
                    by: "系統管理員",
                    createdAt: new Date(
                      new Date().setDate(new Date().getDate() - 5)
                    ),
                  },
                ],
              },
            },
            payment: {
              create: {
                id: orderCPaymentId,
                method: "信用卡",
                status: "failed",
                amount: plans[2].price * 6,
                currency: "TWD",
                createdAt: new Date(
                  new Date().setDate(new Date().getDate() - 7)
                ),
                updatedAt: new Date(
                  new Date().setDate(new Date().getDate() - 5)
                ),
              },
            },
          },
        });

        console.log("✅ 訂單測試資料建立成功");
      } else {
        console.log("⚠️ 找不到租戶或方案資料，無法建立訂單測試資料");
      }
    }

    // 為每個租戶建立基本 AI Bot 資料
    console.log("🤖 建立 AI Bot 測試資料...");

    // 為 HorizAI 租戶建立 AI Bot
    await client.aiBot
      .createMany({
        data: [
          {
            id: randomUUID(),
            name: "OpenAI 助手",
            description: "基本的 OpenAI 聊天助手",
            scene: "一般對話",
            scope: "WORKSPACE",
            provider: "openai",
            model: "gpt-4",
            prompt: "你是一個有用的助手，盡可能精確地回答問題",
            temperature: 0.7,
            isTemplate: false,
            isEnabled: true,
            tenantId: horizaiTenantId,
            createdBy: userIds.horizaiAdmin,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            id: randomUUID(),
            name: "Claude 助手",
            description: "基本的 Claude 聊天助手",
            scene: "複雜思考",
            scope: "WORKSPACE",
            provider: "claude",
            model: "claude-3-opus-20240229",
            prompt:
              "你是一個有用的助手，擅長複雜思考任務。請提供詳細且結構化的回答。",
            temperature: 0.8,
            isTemplate: false,
            isEnabled: true,
            tenantId: horizaiTenantId,
            createdBy: userIds.horizaiAdmin,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
        ],
      })
      .catch((err) => {
        console.log("建立 AI Bot 資料失敗:", err.message);
      });

    // 為 Demo 租戶建立 AI Bot
    await client.aiBot
      .createMany({
        data: [
          {
            id: randomUUID(),
            name: "Demo 對話助手",
            description: "示範用的 AI 對話助手",
            scene: "客戶服務",
            scope: "WORKSPACE",
            provider: "openai",
            model: "gpt-3.5-turbo",
            prompt: "你是一個客戶服務助手，以友好的態度解答客戶問題。",
            temperature: 0.6,
            isTemplate: false,
            isEnabled: true,
            tenantId: demoTenantId,
            createdBy: userIds.demoAdmin,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
        ],
      })
      .catch((err) => {
        console.log("建立 Demo AI Bot 資料失敗:", err.message);
      });

    // 建立系統範本 AI Bot（不屬於特定租戶）
    await client.aiBot
      .createMany({
        data: [
          {
            id: randomUUID(),
            name: "系統通用助手範本",
            description: "可作為新機器人的基礎範本",
            scene: "通用場景",
            scope: "SYSTEM",
            provider: "openai",
            model: "gpt-4",
            prompt:
              "你是一個有用的 AI 助手，根據使用者的問題提供準確、有幫助的回答。",
            temperature: 0.7,
            isTemplate: true,
            isEnabled: true,
            tenantId: null, // 系統級範本沒有所屬租戶
            createdBy: userIds.superAdmin,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
        ],
      })
      .catch((err) => {
        console.log("建立系統範本 AI Bot 資料失敗:", err.message);
      });

    console.log("✅ AI Bot 測試資料建立完成");
    console.log("✅ 資料庫初始化完成！");
    console.log("🔑 超級管理員登入資訊:");
    console.log("   Email: <EMAIL>");
    console.log("   密碼: Admin@123");

    console.log("🔑 HorizAI 租戶管理員登入資訊:");
    console.log("   Email: <EMAIL>");
    console.log("   密碼: Admin@123");

    console.log("🔑 Demo 租戶管理員登入資訊:");
    console.log("   Email: <EMAIL>");
    console.log("   密碼: Admin@123");
  } catch (error) {
    console.error("❌ 資料庫初始化失敗:", error);
    throw error;
  }
}

// 如果直接執行此腳本，則初始化資料庫
if (require.main === module) {
  initializeDatabase({ force: shouldForceInit })
    .catch((e) => {
      console.error(e);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
    });
}
