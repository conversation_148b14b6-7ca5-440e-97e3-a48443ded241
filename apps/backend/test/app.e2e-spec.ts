import { INestApplication, ValidationPipe } from "@nestjs/common";
import { Test, TestingModule } from "@nestjs/testing";
import request from "supertest";
import { AppModule } from "../src/app.module";
import { PrismaService } from "../src/modules/core/prisma/prisma.service";

describe("Auth and Permissions (e2e)", () => {
  let app: INestApplication;
  let prisma: PrismaService;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(
      new ValidationPipe({
        whitelist: true,
        forbidNonWhitelisted: true,
        transform: true,
      })
    );
    await app.init();

    prisma = app.get(PrismaService);
    
    // 清理測試數據 - 按外鍵依賴順序清理 (最深層的表先清理)
    await prisma.aiUsageLog.deleteMany({});
    await prisma.aiBot.deleteMany({});
    await prisma.aiKey.deleteMany({});
    await prisma.refreshToken.deleteMany({});
    await prisma.systemLog.deleteMany({});
    await prisma.loginLog.deleteMany({});
    await prisma.userRoleMapping.deleteMany({});
    await prisma.rolePermissionMapping.deleteMany({});
    await prisma.orderHistory.deleteMany({});
    await prisma.payment.deleteMany({});
    await prisma.user.deleteMany({});
    await prisma.order.deleteMany({});
    await prisma.role.deleteMany({});
    await prisma.permission.deleteMany({});
    await prisma.tenant.deleteMany({});
  });

  afterAll(async () => {
    await app.close();
  });

  describe("Authentication Tests", () => {
    it("GET /auth/me (unauthorized) -> 401", () => {
      return request(app.getHttpServer())
        .get("/auth/me")
        .expect(401);
    });

    it("POST /auth/register, login and GET /auth/me (authorized)", async () => {
      const email = `test+${Date.now()}@testcompany.com`;
      const password = "Password123!";
      const name = "Test User";

      // 註冊 - 系統會根據郵箱域名自動創建租戶，第一個使用者會成為 TENANT_ADMIN
      const registerResponse = await request(app.getHttpServer())
        .post("/auth/register")
        .send({ 
          email, 
          password, 
          name
        })
        .expect(201);

      // 查找並啟用自動創建的租戶
      const userTenant = await prisma.user.findUnique({
        where: { id: registerResponse.body.id },
        include: { tenant: true }
      });

      if (userTenant?.tenant) {
        await prisma.tenant.update({
          where: { id: userTenant.tenant.id },
          data: { status: "active" }
        });
      }

      // 登入
      const loginRes = await request(app.getHttpServer())
        .post("/auth/login")
        .send({ email, password })
        .expect(200);

      expect(loginRes.body.accessToken).toBeDefined();
      const token = loginRes.body.accessToken;

      // 取得個人資料
      const meRes = await request(app.getHttpServer())
        .get("/auth/me")
        .set("Authorization", `Bearer ${token}`)
        .expect(200);

      expect(meRes.body.email).toEqual(email);
      expect(meRes.body.name).toEqual(name);
      expect(Array.isArray(meRes.body.permissions)).toBe(true);
    });

    it("POST /auth/login with invalid credentials -> 401", async () => {
      return request(app.getHttpServer())
        .post("/auth/login")
        .send({ 
          email: "<EMAIL>", 
          password: "wrongpassword" 
        })
        .expect(401);
    });

    it("POST /auth/register with invalid data -> 400", async () => {
      return request(app.getHttpServer())
        .post("/auth/register")
        .send({ 
          email: "invalid-email", 
          password: "123", // 太短
          name: "" // 空名稱
        })
        .expect(400);
    });

    it("POST /auth/logout should succeed with valid token", async () => {
      const email = `testlogout+${Date.now()}@testcompany.com`;
      const password = "Password123!";
      const name = "Test Logout User";

      // 註冊並啟用租戶
      const registerResponse = await request(app.getHttpServer())
        .post("/auth/register")
        .send({ email, password, name })
        .expect(201);

      const userTenant = await prisma.user.findUnique({
        where: { id: registerResponse.body.id },
        include: { tenant: true }
      });

      if (userTenant?.tenant) {
        await prisma.tenant.update({
          where: { id: userTenant.tenant.id },
          data: { status: "active" }
        });
      }

      // 登入
      const loginRes = await request(app.getHttpServer())
        .post("/auth/login")
        .send({ email, password })
        .expect(200);

      const token = loginRes.body.accessToken;

      // 登出
      return request(app.getHttpServer())
        .post("/auth/logout")
        .set("Authorization", `Bearer ${token}`)
        .expect(201);
    });
  });

  describe("Permission Tests", () => {
    let adminToken: stringlet regularUserToken: stringlet adminUserId: stringlet testTenantId: stringbeforeAll(async () => {
      // 1. 創建基本權限
      const systemLogPermission = await prisma.permission.create({
        data: {
          action: "read",
          subject: "SystemLog",
          description: "讀取系統日誌權限",
          deprecated: false,
        }
      });

      const userManagePermission = await prisma.permission.create({
        data: {
          action: "manage",
          subject: "User",
          description: "管理用戶權限",
          deprecated: false,
        }
      });

      const roleReadPermission = await prisma.permission.create({
        data: {
          action: "read",
          subject: "",
          description: "讀取角色權限",
          deprecated: false,
        }
      });

      const roleCreatePermission = await prisma.permission.create({
        data: {
          action: "create",
          subject: "",
          description: "創建角色權限",
          deprecated: false,
        }
      });

      const permissionReadPermission = await prisma.permission.create({
        data: {
          action: "read",
          subject: "Permission",
          description: "讀取權限權限",
          deprecated: false,
        }
      });

      // 2. 創建系統級角色
      const adminRole = await prisma.role.create({
        data: {
          name: "TEST_ADMIN",
          displayName: "測試管理員",
          description: "測試管理員角色",
          scope: "SYSTEM",
          isSystem: false,
        }
      });

      // 3. 將權限分配給角色
      await prisma.rolePermissionMapping.createMany({
        data: [
          { roleId: adminRole.id, permissionId: systemLogPermission.id },
          { roleId: adminRole.id, permissionId: userManagePermission.id },
          { roleId: adminRole.id, permissionId: roleReadPermission.id },
          { roleId: adminRole.id, permissionId: roleCreatePermission.id },
          { roleId: adminRole.id, permissionId: permissionReadPermission.id },
        ]
      });

      // 4. 創建測試租戶
      const testTenant = await prisma.tenant.create({
        data: {
          name: "Test Company",
          domain: "testadmin.com",
          status: "active",
        }
      });
      testTenantId = testTenant.id;

      // 5. 創建管理員用戶
      const testUser = await prisma.system_users.findUnique({ where: { email: "<EMAIL>" } });
      const hashedPassword = await import('bcryptjs').then(bcrypt =>
        bcrypt.hash("Admin@123", 10)
      );
      
      const adminUser = await prisma.user.create({
        data: {
          email: "<EMAIL>",
          password: hashedPassword,
          name: "Test Admin",
          role: "TENANT_ADMIN",
          tenantId: testTenant.id,
        }
      });
      adminUserId = adminUser.id;

      // 6. 將角色分配給管理員
      await prisma.userRoleMapping.create({
        data: {
          userId: adminUser.id,
          roleId: adminRole.id,
        }
      });

      // 7. 管理員登入
      const adminLoginRes = await request(app.getHttpServer())
        .post("/auth/login")
        .send({ 
          email: "<EMAIL>", 
          password: "Admin@123" 
        })
        .expect(200);
      adminToken = adminLoginRes.body.accessToken;

      // 8. 創建一般使用者（不分配特殊權限）
      const userEmail = `user+${Date.now()}@testcompany.com`;
      const userRegisterRes = await request(app.getHttpServer())
        .post("/auth/register")
        .send({ 
          email: userEmail, 
          password: "Password123!", 
          name: "Regular User"
        })
        .expect(201);

      // 啟用用戶的租戶
      const userTenant = await prisma.user.findUnique({
        where: { id: userRegisterRes.body.id },
        include: { tenant: true }
      });

      if (userTenant?.tenant) {
        await prisma.tenant.update({
          where: { id: userTenant.tenant.id },
          data: { status: "active" }
        });
      }

      // 一般使用者登入
      const userLoginRes = await request(app.getHttpServer())
        .post("/auth/login")
        .send({ email: userEmail, password: "Password123!" })
        .expect(200);
      regularUserToken = userLoginRes.body.accessToken;
    });

    it("Admin can access admin endpoints", async () => {
      // 測試管理員應該能存取系統日誌
      const response = await request(app.getHttpServer())
        .get("/admin/system-logs")
        .set("Authorization", `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('total');
      expect(response.body).toHaveProperty('logs');
      expect(Array.isArray(response.body.logs)).toBe(true);
    });

    it("Regular user cannot access admin endpoints", async () => {
      // 一般使用者不應該能存取系統日誌
      return request(app.getHttpServer())
        .get("/admin/system-logs")
        .set("Authorization", `Bearer ${regularUserToken}`)
        .expect(403);
    });

    it("Admin can access role management endpoints", async () => {
      // 測試管理員可以讀取角色列表
      const response = await request(app.getHttpServer())
        .get("/admin/roles")
        .set("Authorization", `Bearer ${adminToken}`)
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
    });

    it("Regular user cannot access role management endpoints", async () => {
      // 一般使用者不應該能存取角色管理
      return request(app.getHttpServer())
        .get("/admin/roles")
        .set("Authorization", `Bearer ${regularUserToken}`)
        .expect(403);
    });
  });

  describe("Role and Permission Management Tests", () => {
    let adminToken: stringbeforeAll(async () => {
      // 使用之前創建的管理員登入
      const adminLoginRes = await request(app.getHttpServer())
        .post("/auth/login")
        .send({ 
          email: "<EMAIL>", 
          password: "Admin@123" 
        })
        .expect(200);
      adminToken = adminLoginRes.body.accessToken;
    });

    it("Admin can create new role", async () => {
      const timestamp = Date.now();
      const newRole = {
        name: `TEST_ROLE_${timestamp}`,
        display_name: "測試角色",
        description: "用於測試的新角色",
        scope: "SYSTEM"
      };

      const response = await request(app.getHttpServer())
        .post("/admin/roles")
        .set("Authorization", `Bearer ${adminToken}`)
        .send(newRole)
        .expect(201);

      expect(response.body.name).toBe(newRole.name);
      expect(response.body.displayName).toBe(newRole.display_name);
      expect(response.body.scope).toBe(newRole.scope);
    });

    it("Admin can view permissions list", async () => {
      const response = await request(app.getHttpServer())
        .get("/admin/permissions")
        .set("Authorization", `Bearer ${adminToken}`)
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
      
      // 檢查權限結構
      const permission = response.body[0];
      expect(permission).toHaveProperty('action');
      expect(permission).toHaveProperty('subject');
    });

    it("Admin can export system logs", async () => {
      const response = await request(app.getHttpServer())
        .get("/admin/system-logs/export?format=csv")
        .set("Authorization", `Bearer ${adminToken}`)
        .expect(200);

      expect(response.header['content-type']).toContain('text/csv');
      expect(response.header['content-disposition']).toContain('attachment');
    });
  });

  describe("Data Security and Isolation Tests", () => {
    it("Users can only access their own tenant data", async () => {
      // 此測試確保租戶間的數據隔離
      // 創建兩個不同租戶的用戶，確保他們無法存取對方的數據
      
      const tenant1Email = `tenant1+${Date.now()}@company1.com`;
      const tenant2Email = `tenant2+${Date.now()}@company2.com`;
      
      // 註冊第一個租戶的用戶
      const user1Response = await request(app.getHttpServer())
        .post("/auth/register")
        .send({ 
          email: tenant1Email, 
          password: "Password123!", 
          name: "Tenant 1 User"
        })
        .expect(201);

      // 註冊第二個租戶的用戶
      const user2Response = await request(app.getHttpServer())
        .post("/auth/register")
        .send({ 
          email: tenant2Email, 
          password: "Password123!", 
          name: "Tenant 2 User"
        })
        .expect(201);

      // 啟用兩個租戶
      const user1Tenant = await prisma.user.findUnique({
        where: { id: user1Response.body.id },
        include: { tenant: true }
      });

      const user2Tenant = await prisma.user.findUnique({
        where: { id: user2Response.body.id },
        include: { tenant: true }
      });

      if (user1Tenant?.tenant) {
        await prisma.tenant.update({
          where: { id: user1Tenant.tenant.id },
          data: { status: "active" }
        });
      }

      if (user2Tenant?.tenant) {
        await prisma.tenant.update({
          where: { id: user2Tenant.tenant.id },
          data: { status: "active" }
        });
      }

      // 兩個用戶登入
      const user1LoginRes = await request(app.getHttpServer())
        .post("/auth/login")
        .send({ email: tenant1Email, password: "Password123!" })
        .expect(200);

      const user2LoginRes = await request(app.getHttpServer())
        .post("/auth/login")
        .send({ email: tenant2Email, password: "Password123!" })
        .expect(200);

      // 驗證用戶資訊中包含正確的租戶 ID，且不同
      const user1Me = await request(app.getHttpServer())
        .get("/auth/me")
        .set("Authorization", `Bearer ${user1LoginRes.body.accessToken}`)
        .expect(200);

      const user2Me = await request(app.getHttpServer())
        .get("/auth/me")
        .set("Authorization", `Bearer ${user2LoginRes.body.accessToken}`)
        .expect(200);

      expect(user1Me.body.tenantId).toBeDefined();
      expect(user2Me.body.tenantId).toBeDefined();
      expect(user1Me.body.tenantId).not.toEqual(user2Me.body.tenantId);
    });
  });
}); 