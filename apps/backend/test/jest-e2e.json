{"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testRegex": ".e2e-spec.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "moduleNameMapper": {"^@horizai/permissions$": "<rootDir>/../../../packages/permissions/src", "^@horizai/permissions/(.*)$": "<rootDir>/../../../packages/permissions/src/$1", "^@/(.*)$": "<rootDir>/../src/$1", "^@modules/(.*)$": "<rootDir>/../src/modules/$1"}, "testEnvironment": "node", "setupFilesAfterEnv": ["<rootDir>/setup-e2e.ts"]}