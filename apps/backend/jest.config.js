/**
 * @type {import('ts-jest/dist/types').InitialOptionsTsJest}
 */
module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  rootDir: '.',
  roots: ['<rootDir>/src', '<rootDir>/scripts/permission-sync'],
  testRegex: '(/__tests__/.*|(\\.|/)(spec|test))\\.ts$',
  moduleFileExtensions: ['ts', 'js', 'json'],
  transform: {
    '^.+\\.ts$': 'ts-jest'
  },
  globals: {
    'ts-jest': {
      tsconfig: 'tsconfig.json'
    }
  },
  moduleNameMapper: {
    "^@horizai/permissions$": "<rootDir>/../../../packages/permissions/src",
    "^@horizai/permissions/(.*)$": "<rootDir>/../../../packages/permissions/src/$1",
    "^@/(.*)$": "<rootDir>/src/$1",
    "^@modules/(.*)$": "<rootDir>/src/modules/$1"
  },
  transformIgnorePatterns: [
    "node_modules/(?!(@horizai)/)",
  ],
};
