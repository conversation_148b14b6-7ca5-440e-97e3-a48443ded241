# HorizAI SaaS Backend - 開發指南

本文件提供 HorizAI SaaS 後端開發的快速入門指引，著重於日常開發流程、自動化檢查工具，以及確保開發環境穩定性的最佳實踐。

## 🚀 快速開始

本專案使用 **pnpm** 作為套件管理工具。

### 基本開發流程

1.  **安裝依賴**

    ```bash
    pnpm install
    ```

2.  **設定環境變數**
    請參考 `apps/backend/docs/database-and-environment-management-guide.md` 文件中的「2.2. 設定環境變數」一節。

3.  **啟動開發服務器 (推薦)**

    ```bash
    pnpm run start:dev
    ```

    此指令會自動執行開發前檢查，確保：

    - 資料庫連線正常
    - 所有資料庫遷移已套用（詳細操作請參考 `database-and-environment-management-guide.md`）
    - Prisma Client 是最新版本
    - Prisma Schema 驗證通過

## 🛠️ 核心開發指令與自動檢查系統

HorizAI SaaS 後端配備了智能開發前檢查系統（v2.0+），旨在確保開發環境的一致性和穩定性，並提升啟動效率。

### 開發啟動相關指令

- `pnpm run start:dev`: **標準開發啟動指令 (推薦)**。包含完整的預檢查，並利用智能緩存優化啟動速度。
- `pnpm run dev:safe`: 手動執行一次完整的開發前檢查後，再啟動開發服務器。
- `pnpm run dev:fresh`: 清理開發環境相關快照（如 Prisma Client、可能的舊 build），重置資料庫（套用所有遷移並可選 seeding），然後啟動開發服務器。適用於環境發生嚴重不一致或需要全新開始時。
  - **注意**: `dev:fresh` 涉及資料庫重置，詳細的資料庫重置操作和選項請參考 `database-and-environment-management-guide.md` 的「3.4. 重置資料庫」一節。
- `pnpm run dev:clean`: 功能上類似 `dev:fresh`，清理並重新準備環境後啟動。具體行為請依據專案內部 `package.json` 腳本定義為準。

### 開發前檢查系統 (`dev:check`)

您可以透過 `pnpm run dev:check` 手動觸發此檢查。

#### 檢查項目

1.  **資料庫連線**: 驗證 `DATABASE_URL` 設定和資料庫可用性。
2.  **遷移狀態**: 檢查是否有待套用的資料庫遷移。
3.  **Prisma Client**: 確保 Prisma Client 是最新生成的。
4.  **Schema 驗證**: 驗證 `prisma/schema.prisma` 語法正確性。

#### 自動修正功能

當透過 `start:dev` 或特定修復指令觸發時，檢查系統具備自動修正能力：

- **待處理遷移**: 自動執行遷移部署 (相當於 `pnpm db:apply`)。
- **過期 Prisma Client**: 自動重新生成 (`pnpm prisma generate`)。
- **Schema 問題**: 可能提供修正建議。

#### 智能緩存與效能

- **自動緩存**: 檢查結果會自動緩存（預設有效期 5 分鐘），當 Schema 和環境變數無變化時，使用快速模式，大幅縮短後續啟動時間。
- **緩存管理**:
  - `pnpm run dev:cache-clear`: 清除檢查緩存。
  - `pnpm run dev:cache-info`: 查看緩存資訊。
- **效能監控**: 系統會顯示各檢查項目的執行時間和總體檢查時間。

### 資料庫相關輔助指令

- `pnpm run db:auto-fix`: 嘗試自動修正偵測到的資料庫相關問題（如套用遷移、生成 Client）。

**註**: 關於更詳細的資料庫遷移 (`pnpm db:apply`, `pnpm db:create`, `pnpm db:status` 等)、資料庫重置、資料匯出/匯入、環境同步 (`pnpm db:check-env`, `pnpm db:sync-env`) 等操作，請參閱 `apps/backend/docs/database-and-environment-management-guide.md`。

## ⚙️ 環境設定

### 必要環境變數範例

```env
DATABASE_URL="postgresql://user:password@localhost:5432/horizai_db?schema=public"
JWT_SECRET="your-very-strong-secret-key-for-jwt"
JWT_REFRESH_SECRET="your-very-strong-secret-key-for-refresh-tokens"
ENCRYPTION_KEY="a-64-character-long-hex-string-for-data-encryption"
# ... 其他專案必要的環境變數
```

請參考 `.env.example` 獲取完整的變數列表。

### 開發依賴

- Node.js (版本請參考專案 `package.json` 的 `engines` 設定，通常為 >= 18)
- PostgreSQL (版本建議 >= 13)
- pnpm (版本請參考專案 `package.json` 的 `packageManager` 設定)

## 🔄 開發工作流程建議

1.  **每日開發**: `git pull` -> (若有依賴變更) `pnpm install` -> `pnpm run start:dev`。
2.  **切換分支後**: 建議執行 `pnpm run dev:fresh` 或至少 `pnpm run start:dev` 以確保環境與新分支的程式碼和遷移同步。
3.  **遇到問題時**:
    1.  首先嘗試 `pnpm run dev:check` 來診斷問題。
    2.  如果問題與環境或資料庫狀態有關，可考慮 `pnpm run dev:fresh` （**注意會重置資料庫**）。
    3.  查閱 `apps/backend/docs/database-and-environment-management-guide.md` 中的「常見問題與排解」章節。

## 📝 最佳實踐

1.  **優先使用 `pnpm run start:dev`**: 這是啟動後端開發服務的標準方式，它包含了重要的自動檢查和修正流程。
2.  **理解自動檢查**: 熟悉 `dev:check` 的檢查項目，有助於快速定位問題。
3.  **定期同步**: 保持程式碼庫為最新，並在切換分支或合併主要分支更新後，確保本機環境同步。
4.  **查閱專門指南**: 對於資料庫遷移、備份、環境同步等特定操作，請參考 `database-and-environment-management-guide.md`。

## 🎯 預防策略總結

開發前檢查系統旨在預防以下常見問題：

- 資料庫 schema 與程式碼定義不同步導致的執行時錯誤。
- Prisma Client 過期或未生成導致的型別錯誤或執行時錯誤。
- 未套用最新的資料庫遷移導致的資料不一致或功能異常。
- 基礎環境配置問題導致的啟動失敗。

透過在每次 `start:dev` 前自動執行檢查與修正，可以大幅減少開發過程中的環境問題，提升開發效率和體驗。
