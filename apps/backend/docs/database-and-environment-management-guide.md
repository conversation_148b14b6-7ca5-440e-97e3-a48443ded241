# HorizAI SaaS 資料庫與環境管理指南

本文件是 HorizAI SaaS 平台管理資料庫結構、資料遷移、資料備份與還原，以及確保多個開發環境一致性的綜合指南。它整合了先前關於資料庫遷移、匯出/匯入和環境同步的獨立文件。

## 1. 核心概念與工具特色

HorizAI SaaS 平台的資料庫與環境管理主要依賴封裝了 Prisma 功能的 `pnpm db:*` 系列指令，並提供以下特色：

- **資料庫遷移管理**:
  - 建立和套用遷移
  - 資料庫狀態檢查
  - 資料庫重置與回滾
- **資料庫備份與還原**:
  - 匯出資料庫到檔案 (多種格式)
  - 從檔案匯入資料庫
- **環境一致性管理**:
  - 檢查不同環境之間的配置差異
  - 自動修復環境問題
- **通用功能**:
  - 多環境配置支援 (開發/測試/生產)
  - 權限同步整合
  - 種子資料建立 (基本資料或完整展示資料)

## 2. 環境設定與準備

### 2.1. 安裝相依套件

確保已安裝所需相依套件：

```bash
pnpm install
```

### 2.2. 設定環境變數

複製 `apps/backend/.env.example` 檔案為 `apps/backend/.env`，並根據您的本機或目標環境修改設定，特別是資料庫連線資訊：

```bash
# 進入後端目錄
cd apps/backend
# 複製範例檔案 (Windows 使用 copy, Linux/macOS 使用 cp)
# copy .env.example .env
# cp .env.example .env
```

編輯 `.env` 檔案，關鍵變數：
`DATABASE_URL="postgresql://使用者名稱:密碼@主機:埠號/資料庫名稱?schema=public"`

### 2.3. 多環境配置檔案

為了支援多環境開發，您可以為不同環境建立獨立的配置檔案：

- `apps/backend/.env.dev` - 開發環境
- `apps/backend/.env.staging` - 測試環境
- `apps/backend/.env.prod` - 生產環境

使用 `--env` 選項指定要使用的環境，例如：

```bash
# 在測試環境套用遷移
pnpm db:apply --env staging
```

工具會嘗試依序讀取 `.env.{env}.local`、`.env.{env}`、`.env.local`、`.env` 來載入環境變數。

## 3. 資料庫遷移管理 (Schema Migrations)

### 3.1. 檢查資料庫遷移狀態

顯示目前資料庫的遷移狀態，包括已套用和未套用的遷移：

```bash
pnpm db:status
```

### 3.2. 建立新的遷移檔案

當您修改了 `prisma/schema.prisma` 檔案以變更資料庫結構時，使用此指令建立新的遷移檔案：

```bash
# 遷移名稱應簡潔描述變更內容
pnpm db:create --name add_user_phone_number
```

建立遷移後，系統會在 `prisma/migrations/` 目錄下產生一個新的遷移資料夾，其中包含 SQL 檔案。建議檢視此檔案以確認變更符合預期。

### 3.3. 套用遷移

套用所有尚未在目標資料庫中執行的遷移：

```bash
# 僅套用遷移
pnpm db:apply

# 套用遷移並執行種子資料建立 (用於填充基礎資料)
pnpm db:apply --seed

# 套用遷移並建立完整展示資料 (用於演示或完整測試環境)
pnpm db:apply --seed --full-data
```

### 3.4. 重置資料庫

重置資料庫會刪除所有現有資料，然後重新套用所有遷移：

```bash
# 重置資料庫並執行種子資料建立 (預設行為)
pnpm db:reset

# 重置資料庫但不執行種子資料建立
pnpm db:reset --seed false

# 重置資料庫並建立完整展示資料
pnpm db:reset --full-data
```

**警告**: `db:reset` 是破壞性操作，請謹慎在開發環境以外使用。

### 3.5. 回滾最近的遷移 (謹慎使用)

如果最近的遷移有問題，可以嘗試回滾。但 Prisma 的回滾機制是基於將資料庫狀態解析到某個歷史遷移點，並非所有操作都能完美"撤銷"。

```bash
pnpm db:rollback --force
```

如果回滾後發現問題，最好的方式通常是建立一個新的遷移來修正問題。

### 3.6. 顯示遷移工具說明

```bash
pnpm db:migrate help
```

## 4. 資料庫匯出與匯入 (Backup & Restore)

### 4.1. 匯出資料庫

將資料庫內容匯出到檔案，以便備份或在不同環境間移轉。

#### 4.1.1. 基本用法

執行以下命令將資料庫匯出到預設位置 (`apps/backend/backup/horizai_backup_[時間戳].dump`):

```bash
# 確保您在 apps/backend 目錄下
pnpm db:export
```

#### 4.1.2. 指定輸出位置與格式

```bash
# 指定輸出檔案路徑
pnpm db:export --output=/path/to/your_backup.dump

# 匯出為純 SQL 格式 (預設為 PostgreSQL 自定義 dump 格式)
pnpm db:export --format=sql --output=./backup.sql
```

支援的格式:

- `dump` (預設): PostgreSQL 自定義格式，最適合用於備份和還原。
- `sql`: 純 SQL 格式，更易讀但可能較大。

#### 4.1.3. 在特定環境匯出

```bash
# 從測試環境匯出
pnpm db:export --env=staging
```

### 4.2. 匯入資料庫

從先前匯出的檔案還原資料庫。

#### 4.2.1. 基本用法

```bash
# 匯入資料庫 (需要使用 --force 選項確認覆蓋現有資料)
pnpm db:import --file=./backup.dump --force
```

**警告**: 匯入操作會覆蓋目標資料庫中的現有資料，請務必小心並在操作前進行備份。

#### 4.2.2. 選擇匯入格式

工具會自動根據檔案副檔名決定格式，但您也可以明確指定:

```bash
# 匯入 SQL 格式
pnpm db:import --file=./backup.sql --format=sql --force
```

#### 4.2.3. 在特定環境匯入

```bash
# 匯入到測試環境
pnpm db:import --file=./backup.dump --env=staging --force
```

### 4.3. 範例：環境間資料移轉

從開發環境匯出並匯入到測試環境的完整流程:

```bash
# 1. 從開發環境匯出資料庫 (假設在 apps/backend 目錄下)
pnpm db:export --env=dev --output=./dev_backup.dump

# 2. 匯入到測試環境
pnpm db:import --env=staging --file=./dev_backup.dump --force
```

### 4.4. 備份排程建議

建議設定定期備份機制，例如使用 cron 作業每天執行一次:

```bash
# 範例: 每天凌晨 3 點執行備份
0 3 * * * cd /path/to/project/apps/backend && pnpm db:export --output=/path/to/backups/horizai_backup_$(date +\%Y\%m\%d).dump
```

### 4.5. 匯入注意事項

1.  匯入操作會覆蓋目標資料庫中的現有資料。
2.  建議在匯入前對目標資料庫進行備份。
3.  在不同 PostgreSQL 版本間遷移時可能會遇到相容性問題，建議使用相同版本。
4.  對於生產環境，建議在執行重要操作前停止相關服務，並在維護時段進行。

## 5. 環境同步與一致性管理

確保所有團隊成員的開發環境配置相同，或確保不同部署環境（開發、測試、生產）之間的一致性，對於避免因環境差異導致的問題至關重要。

### 5.1. 環境同步工具

HorizAI SaaS 平台提供了專用的環境同步工具，位於 `apps/backend` 目錄下，可以幫助您檢查和修復不同環境之間的差異。

### 5.2. 檢查環境一致性

```bash
# 檢查開發環境和測試環境之間的一致性 (比較 .env 檔案內容和資料庫狀態)
# 確保在 apps/backend 目錄下執行
pnpm db:check-env dev,staging

# 執行更完整的檢查，包括資料庫遷移狀態、Prisma Client 版本等
pnpm db:full-check dev,staging

# 檢查所有已知的環境 (假設 dev, staging, prod 已在某處定義或工具能推斷)
# pnpm db:full-check dev,staging,prod
```

### 5.3. 自動修復環境問題

```bash
# 嘗試自動修復環境差異 (例如，同步 .env 檔案中缺失的鍵)
pnpm db:sync-env dev,staging

# 執行完整檢查並嘗試修復
pnpm db:sync-env dev,staging --full
```

### 5.4. 完整環境同步流程（新成員或重設環境）

1.  **拉取最新程式碼**:
    ```bash
    git pull origin main # 或您的主要分支
    ```
2.  **安裝相依套件**:
    ```bash
    pnpm install
    ```
3.  **設定環境變數**:
    參考 2.2. 節。
4.  **(若需) 建立資料庫**:
    如果資料庫不存在，您可能需要手動建立它 (例如使用 `createdb horizai_saas`)。
5.  **套用資料庫遷移**:
    ```bash
    cd apps/backend
    pnpm db:apply --seed # 通常開發環境需要種子資料
    ```
6.  **檢查環境一致性**:
    ```bash
    pnpm db:full-check dev # 與一個參考環境比較，或檢查本機設定是否完整
    ```
7.  **啟動開發伺服器**:
    參考專案的後端開發指南 (`development-guide.md`)。

## 6. 其他相關工具指令

### 6.1. 權限同步

資料庫操作（如匯入、重置）後，系統通常會自動執行權限同步。您也可以單獨執行：

```bash
# 預覽權限變更 (不實際修改資料庫)
pnpm db:sync-perms --dry-run

# 強制執行權限同步，將程式碼中定義的權限同步到資料庫
pnpm db:sync-perms --force
```

這確保 `Permission` 表與程式碼宣告 (源自 `@horizai/permissions` 和後端控制器/服務的權限使用點) 一致。

### 6.2. 跳過權限同步

在某些特定操作（如套用遷移）中，若想跳過自動的權限同步：

```bash
pnpm db:apply --skip-perms
```

## 7. 常見問題與排解

### 7.1. 資料庫連線問題

- 確保 `DATABASE_URL` 在對應的 `.env` 檔案中格式正確且包含有效憑證。
- 確認 PostgreSQL 服務正在運行且網路可達。
- 檢查防火牆設定。

### 7.2. PostgreSQL 工具找不到

執行匯出/匯入時，錯誤訊息提示 `pg_dump`, `pg_restore`, 或 `psql` 找不到：

- 確保 PostgreSQL 客戶端工具已安裝。
- 確保這些工具所在的目錄已加入系統的 `PATH` 環境變數中。

### 7.3. 環境變數不一致

使用環境同步工具檢查並修復：

```bash
pnpm db:sync-env dev,staging
```

### 7.4. 遷移衝突或錯誤

- 仔細閱讀錯誤訊息。
- 使用 `pnpm db:status` 檢查當前遷移狀態。
- 如果遷移檔案有語法錯誤，修改後重新執行 `pnpm db:apply`。
- 嚴重情況下，考慮備份資料後，使用 `pnpm db:reset` 重置資料庫（僅限開發環境或確認資料可重建）。
- 參考 3.5. 節關於回滾的說明。

### 7.5. 權限問題 (非資料庫權限，指應用程式角色權限)

如果應用程式中的權限設定不一致或未按預期工作：

- 執行權限同步：`pnpm db:sync-perms --force`。
- 檢查 `prisma/templates/` 或 `@horizai/permissions` 套件中權限定義檔案是否正確。

## 8. 最佳實踐

1.  **版本控制**: `prisma/schema.prisma` 和 `prisma/migrations/` 目錄必須納入版本控制。`.env` 檔案不應納入版本控制。
2.  **定期檢查**: 經常執行 `pnpm db:status` 和 `pnpm db:check-env` (若適用) 確保環境一致性。
3.  **遷移先行**: 在提交任何會影響資料庫結構的程式碼之前，先建立、測試並提交對應的遷移檔案。
4.  **團隊協作**: 在團隊間共享環境設定檔案 (`.env.example`) 的變更，並確保所有成員了解如何設定其本機環境。
5.  **備份**: 定期備份重要環境的資料庫，尤其是在執行重大變更之前。

## 9. 說明與聯絡資訊

如果您在執行這些操作時遇到任何問題或需要協助，請聯繫 HorizAI 開發團隊或參考相關的內部文件。
