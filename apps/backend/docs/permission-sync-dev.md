# HorizAI SaaS 權限自動化同步機制開發說明

## 1. 目標

- 建立一套自動化權限同步機制，確保後端程式碼中宣告的權限（透過靜態常數與裝飾器，其中靜態常數主要來自 `@horizai/permissions`）、共享權限包 (`@horizai/permissions`) 中的核心定義，與資料庫中的權限列表 (`Permission` 表) 嚴格一致。
- 降低人為遺漏與安全風險，確保權限管理的統一性與可追溯性，作為整體認證與權限架構的基石。
- 提供 CLI 工具與 CI/CD 整合，支援 dry-run、force 覆蓋、報告產生等功能，並輔助前端權限控制的實現 (透過確保後端 `/api/auth/me` 能基於同步後的權限數據生成正確的 CASL 規則)。

## 2. 主要功能模組

### 2.1 權限掃描（scanner） - `apps/backend/scripts/permission-sync/scanner.ts`

- **掃描來源**:
  - **主要來源**: 共享權限套件 `@horizai/permissions` (例如 `packages/permissions/src/constants.ts`) 中定義的所有權限標識符 (Action:Subject)。
  - 後端 Controller/Service 中的 `@CheckPolicies()` 裝飾器引用的權限常數 (這些常數**必須**來自 `@horizai/permissions`)。
  - (未來可擴展) 前端路由 meta、`$can`/`useAbility().can` 調用點 (需更複雜的靜態分析，且確保其引用的權限常數也來自 `@horizai/permissions`)。
  - (未來可擴展) Seeder 檔案中直接使用的權限字串 (應避免，強烈推薦引用 `@horizai/permissions` 中的常數)。
- **核心功能**:
  - 解析權限標識符 (Action:Subject 格式)。
  - 自動推斷或根據配置映射權限的 `scope` (SYSTEM, TENANT, WORKSPACE) 和 `category` (模組分類，如 AI, Project, UserManagement)。
  - 支援快取機制 (如 `.cache/permissions.hash`)，提升重複掃描效率。

### 2.2 權限同步（syncer） - `apps/backend/scripts/permission-sync/syncer.ts`

- **核心功能**:
  - 比對 `scanner` 掃描到的權限列表與資料庫 `Permission` 表中的記錄。
  - **新增權限**: 對於掃描到但在資料庫中不存在的權限，自動在 `Permission` 表中創建新記錄。
  - **標記廢棄**: 對於資料庫中存在但未在最新掃描結果中出現的權限（可能已從程式碼中移除或重命名），可選擇將其標記為廢棄 (例如，更新其 `description` 欄位或設定 `is_deprecated` 旗標)，而非直接刪除，以供審計或逐步遷移。
  - **支援 `--force` 參數**: 強制覆蓋資料庫中現有權限的某些屬性（如 `description`, `category`, `scope`，需謹慎使用）。
  - **權限定義來源**: 強調權限的「真實來源 (Source of Truth)」是程式碼中的靜態宣告（主要在 `@horizai/permissions` 和後端控制器）。
  - (未來可擴展) 同步權限分類 (`PermissionCategory` 表)，自動建立新分類。

### 2.3 報告產生（reporter） - `apps/backend/scripts/permission-sync/reporter.ts`

- **核心功能**:
  - 產生 JSON 和/或 Markdown 格式的同步報告。
  - 報告內容應包含：本次同步新增的權限、被標記為廢棄的權限、更新的權限（若 `--force` 使用）、掃描過程中遇到的錯誤或警告（例如，掃描到未使用 `@horizai/permissions` 的裸權限字串）。
  - 報告預設存於 `apps/backend/reports/permission-sync-report.json`。
  - Console 應輸出簡潔的同步摘要。

### 2.4 CLI 與 CI/CD 整合

- **CLI 指令**:
  - `pnpm db:scan-perms [--no-cache]`: 僅執行權限掃描，可輸出掃描結果。
  - `pnpm db:sync-perms --dry-run`: 執行掃描和比對，模擬同步操作並產生報告，但不實際修改資料庫。
  - `pnpm db:sync-perms [--force]`: 執行完整的掃描和同步流程，更新資料庫。
  - `pnpm db:report-perms --format json|markdown [--output <path>]`: 產生特定格式的最新同步報告。
- **CI/CD 整合**:
  - 在 CI Pipeline (如 GitHub Actions) 中加入步驟，於每次提交或 Pull Request 時執行 `pnpm db:sync-perms --dry-run`。
  - 若 `--dry-run` 偵測到程式碼中的權限定義與資料庫可能不一致（例如，有新的權限需要同步，或有應被廢棄的權限），CI 流程應發出警告或中斷，強制開發者先執行同步。
  - 成功的同步操作（非 `--dry-run`）應在特定分支合併或部署前由授權人員執行或自動化執行。

## 3. 開發步驟建議 (迭代完善)

1.  **鞏固權限常數中心**: 確保所有權限標識符統一在 `@horizai/permissions` 共享套件中定義，並被前後端正確引用。`Scanner` 應能識別並警告未使用常數的硬編碼權限字串。
2.  **增強 Scanner**: 提升 `scanner.ts` 的準確性和覆蓋範圍，特別是對於解析共享套件中的權限定義。考慮加入對程式碼風格的檢查，例如 `@CheckPolicies` 是否總是引用 `@horizai/permissions` 中的常數。
3.  **完善 Syncer 邏輯**: 優化新增和標記廢棄的邏輯，考慮更細緻的更新策略，避免誤操作。記錄同步操作到審計日誌。
4.  **優化 Reporter**: 使報告更易讀，包含更多上下文信息（如權限被引用的文件位置），輔助開發者理解權限變更。
5.  **強化 CLI**: 提供更豐富的 CLI 選項，例如指定掃描特定模組、按分類篩選報告等。
6.  **前端權限對齊**:
    - 雖然本工具主要同步後端權限到資料庫，但其產生的權限列表是前端 `AppAbility` 規則設定的基礎。
    - 後端 `/api/auth/me` 應基於同步後的 `Permission` 表和用戶角色來生成 CASL 規則，供前端 `authStore` 使用。
    - 確保 `@horizai/permissions` 在前端被正確且一致地使用。
7.  **編寫詳盡測試**: 為 `scanner`, `syncer`, `reporter` 編寫單元測試和整合測試。

## 4. 常用指令

```bash
# 掃描權限定義 (建議加上 --no-cache 以獲取最新結果)
pnpm db:scan-perms --no-cache

# 預覽權限變更（不實際修改資料庫）
pnpm db:sync-perms --dry-run

# 執行權限同步（建議先 dry-run 確認無誤；如需強制覆蓋屬性加 --force）
pnpm db:sync-perms
# pnpm db:sync-perms --force

# 產生同步報告（支援 json 與 markdown，可輸出到指定路徑或同時輸出到控制台和文件）
pnpm db:report-perms --format markdown # 輸出到控制台
pnpm db:report-perms --format json --output ./reports/latest-sync.json # 輸出到文件
```

## 5. 參考文件

- **核心規則**:
  - `.cursor/rules/AuthAndAccessControlGuide.mdc` (定義整體認證與權限架構)
  - `.cursor/rules/ApplicationArchitectureGuide.mdc` (定義 `@horizai/permissions` 等共享包的角色)
- 內部 PRD: `apps/backend/docs/permission-sync-system.md`
- Prisma Schema: `apps/backend/prisma/schema.prisma` (特別是 `Permission`, `RolePermissionMapping` 模型)
- CLI 腳本入口: `apps/backend/scripts/permission-sync/cli.ts` (或類似的主要執行檔案)
- 程式碼模組: `apps/backend/scripts/permission-sync/` 目錄下的 `scanner.ts`, `syncer.ts`, `reporter.ts`
- **共享權限包 (唯一真實來源)**: `packages/permissions/` (尤其是 `constants.ts`)

---

本文檔描述了權限自動化同步機制的理想狀態和開發方向。實際執行中請參考最新的程式碼實作和相關 TODO 文件 (`apps/backend/docs/permission-sync-todo.md`)。
