# HorizAI SaaS 權限架構圖

## 1. 整體系統架構

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                           HorizAI SaaS 權限管理系統                          │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐          │
│  │   前端 (Vue 3)   │    │ 後端 (NestJS)   │    │ 資料庫 (PostgreSQL) │          │
│  │                 │    │                 │    │                 │          │
│  │ • UI 權限控制    │◄──►│ • API 權限驗證   │◄──►│ • 權限資料儲存   │          │
│  │ • 路由守衛      │    │ • CASL 整合      │    │ • 角色權限映射   │          │
│  │ • 元素顯隱      │    │ • JWT 驗證       │    │ • 使用者角色     │          │
│  └─────────────────┘    └─────────────────┘    └─────────────────┘          │
│           │                       │                       │                 │
│           └───────────────────────┼───────────────────────┘                 │
│                                   │                                         │
│  ┌─────────────────────────────────┼─────────────────────────────────────┐   │
│  │             共享權限套件 (@horizai/permissions)                │   │
│  │                                 │                                         │
│  │ • 權限常數定義                                                            │
│  │ • 中文化對應                                                              │
│  │ • 類型定義                                                                │
│  │ • 權限分類                                                                │
│  └─────────────────────────────────────────────────────────────────────────┘   │
│                                   │                                         │
│  ┌─────────────────────────────────┼─────────────────────────────────────┐   │
│  │                權限同步工具 (permission-sync)                 │   │
│  │                                 │                                         │
│  │ • 程式碼掃描                                                              │
│  │ • 資料庫同步                                                              │
│  │ • 報告生成                                                                │
│  └─────────────────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 2. 權限定義與管理流程

```
┌────────────────────────────────────────────────────────────────────────────┐
│                              權限定義流程                                   │
└────────────────────────────────────────────────────────────────────────────┘

  ┌─────────────────────┐
  │ 開發者定義權限       │
  │ (@horizai/permissions)│
  │                     │
  │ • Actions 常數       │
  │ • Subjects 常數      │
  │ • 權限組合定義       │
  │ • 中文名稱對應       │
  │ • 分類與描述         │
  └──────────┬──────────┘
             │
             ▼
  ┌─────────────────────┐
  │ 程式碼中使用         │
  │                     │
  │ • @CheckPolicies     │
  │ • ability.can()      │
  │ • $can() / <Can>     │
  │ • 路由守衛           │
  └──────────┬──────────┘
             │
             ▼
  ┌─────────────────────┐
  │ 權限掃描器           │
  │ (permission-sync)    │
  │                     │
  │ • 掃描程式碼引用     │
  │ • 識別權限使用       │
  │ • 生成權限清單       │
  │ • 中文化處理         │
  └──────────┬──────────┘
             │
             ▼
  ┌─────────────────────┐
  │ 權限同步器           │
  │                     │
  │ • 比對資料庫         │
  │ • 新增/更新權限      │
  │ • 清理廢棄權限       │
  │ • 生成同步報告       │
  └──────────┬──────────┘
             │
             ▼
  ┌─────────────────────┐
  │ 資料庫權限表         │
  │                     │
  │ • permissions       │
  │ • roles             │
  │ • user_role_mapping │
  │ • role_permission_  │
  │   mapping           │
  └─────────────────────┘
```

## 3. 權限驗證流程

```
┌────────────────────────────────────────────────────────────────────────────┐
│                              權限驗證流程                                   │
└────────────────────────────────────────────────────────────────────────────┘

前端請求權限驗證:
┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│    用戶      │    │  前端應用    │    │  後端 API    │    │   資料庫     │
│   操作      │    │            │    │            │    │            │
└──────┬──────┘    └──────┬──────┘    └──────┬──────┘    └──────┬──────┘
       │                  │                  │                  │
       │ 1. 觸發操作        │                  │                  │
       ├─────────────────►│                  │                  │
       │                  │                  │                  │
       │                  │ 2. 檢查本地權限   │                  │
       │                  │   ($can/ability) │                  │
       │                  │                  │                  │
       │                  │ 3. 發送 API 請求  │                  │
       │                  ├─────────────────►│                  │
       │                  │   (with JWT)     │                  │
       │                  │                  │                  │
       │                  │                  │ 4. JWT 驗證       │
       │                  │                  │   (JwtAuthGuard) │
       │                  │                  │                  │
       │                  │                  │ 5. 權限檢查       │
       │                  │                  │   (PoliciesGuard)│
       │                  │                  │                  │
       │                  │                  │ 6. 查詢用戶權限   │
       │                  │                  ├─────────────────►│
       │                  │                  │                  │
       │                  │                  │ 7. 返回權限資料   │
       │                  │                  │◄─────────────────┤
       │                  │                  │                  │
       │                  │                  │ 8. CASL 權限判斷  │
       │                  │                  │   (能力檢查)      │
       │                  │                  │                  │
       │                  │ 9. 返回結果       │                  │
       │                  │◄─────────────────┤                  │
       │                  │   (200/403)      │                  │
       │                  │                  │                  │
       │ 10. 顯示結果/錯誤  │                  │                  │
       │◄─────────────────┤                  │                  │
```

## 4. 資料庫設計架構

```
┌────────────────────────────────────────────────────────────────────────────┐
│                               資料庫設計                                    │
└────────────────────────────────────────────────────────────────────────────┘

┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     users       │    │     tenants     │    │   workspaces    │
│                 │    │                 │    │                 │
│ • id            │    │ • id            │    │ • id            │
│ • email         │    │ • name          │    │ • name          │
│ • name          │    │ • status        │    │ • tenant_id ────┼──┐
│ • tenant_id ────┼────┤ • created_at    │    │ • created_at    │  │
│ • status        │    │ • updated_at    │    │ • updated_at    │  │
│ • created_at    │    └─────────────────┘    └─────────────────┘  │
│ • updated_at    │                                               │
└──────┬──────────┘                                               │
       │                                                          │
       │  ┌─────────────────────────────────────────────────────┘
       │  │
       ▼  ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ user_role_mapping│   │     roles       │    │     permissions │
│                 │    │                 │    │                 │
│ • id            │    │ • id            │    │ • id            │
│ • user_id ──────┼────┤ • name          │    │ • action        │
│ • role_id ──────┼───►│ • description   │    │ • subject       │
│ • tenant_id     │    │ • scope         │    │ • name (中文)    │
│ • workspace_id  │    │ • is_system_    │    │ • description   │
│ • created_at    │    │   defined       │    │ • zone          │
│ • updated_at    │    │ • tenant_id     │    │ • category      │
└─────────────────┘    │ • created_at    │    │ • scope         │
                       │ • updated_at    │    │ • is_system_    │
                       └──────┬──────────┘    │   defined       │
                              │               │ • created_at    │
                              │               │ • updated_at    │
                              │               └──────┬──────────┘
                              │                      │
                              ▼                      │
                       ┌─────────────────┐           │
                       │role_permission_ │           │
                       │mapping          │           │
                       │                 │           │
                       │ • id            │           │
                       │ • role_id ──────┼───────────┘
                       │ • permission_id ├───────────┘
                       │ • created_at    │
                       │ • updated_at    │
                       └─────────────────┘
```

## 5. 權限分類架構

```
┌────────────────────────────────────────────────────────────────────────────┐
│                              權限分類架構                                   │
└────────────────────────────────────────────────────────────────────────────┘

權限區域 (Zone)
├── admin (管理後台)
│   ├── system_management (系統管理)
│   │   ├── access:ADMIN_PANEL
│   │   ├── read:DASHBOARD
│   │   └── read/update:SYSTEM_SETTINGS
│   │
│   ├── tenant_management (租戶管理)
│   │   ├── manage/create/read/update/delete:TENANT
│   │   └── 租戶相關操作權限
│   │
│   ├── user_management (用戶管理)
│   │   ├── manage/read:SYSTEM_USER
│   │   ├── manage/read/invite:TENANT_USER
│   │   └── 用戶相關操作權限
│   │
│   ├── role_management (角色管理)
│   │   ├── manage/create/read/update/delete:ROLE
│   │   ├── manage/read:PERMISSION
│   │   └── 角色權限相關操作
│   │
│   ├── ai_management (AI 管理)
│   │   ├── manage/read:AI_MODEL
│   │   ├── manage:AI_KEY
│   │   └── read/update:AI_GLOBAL_SETTING
│   │
│   ├── subscription_management (訂閱管理)
│   │   ├── manage:PLAN
│   │   └── read:ORDER
│   │
│   ├── audit_management (稽核管理)
│   │   ├── read:SYSTEM_LOG
│   │   └── 審計相關權限
│   │
│   └── log_management (日誌管理)
│       ├── read:LOGIN_LOG
│       ├── read:LINE_MESSAGE_LOG
│       └── 各類日誌相關權限
│
└── workspace (工作區)
    ├── workspace_settings (工作區設定)
    │   ├── read/update:WORKSPACE
    │   └── 工作區設定相關權限
    │
    ├── project_management (專案管理)
    │   ├── manage/create/read/update/delete:PROJECT
    │   └── 專案相關操作權限
    │
    ├── member_management (成員管理)
    │   ├── manage/invite/read:WORKSPACE_MEMBER
    │   └── 成員管理相關權限
    │
    ├── resource_management (資源管理)
    │   ├── manage/create/read:CLIENT
    │   ├── manage/create/read:FORM
    │   └── 資源管理相關權限
    │
    ├── ai_features (AI 功能)
    │   ├── manage/execute:AI_BOT
    │   ├── read:AI_USAGE_LOG
    │   └── AI 功能相關權限
    │
    └── collaboration (協作功能)
        └── 協作相關權限
```

## 6. 前端權限控制架構

```
┌────────────────────────────────────────────────────────────────────────────┐
│                            前端權限控制架構                                  │
└────────────────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────────────────┐
│                              Vue 3 應用                                     │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐          │
│  │   路由守衛       │    │   UI 元件控制    │    │   狀態管理       │          │
│  │                 │    │                 │    │   (Pinia)       │          │
│  │ • beforeEach     │    │ • v-if="$can()" │    │                 │          │
│  │ • 權限檢查       │    │ • <Can> 組件     │    │ ┌─────────────┐ │          │
│  │ • 重定向邏輯     │    │ • 按鈕禁用       │    │ │ authStore   │ │          │
│  └─────────────────┘    │ • 欄位隱藏       │    │ │             │ │          │
│                         └─────────────────┘    │ │ • user      │ │          │
│  ┌─────────────────────────────────────────┐    │ │ • ability   │ │          │
│  │            權限檢查方法                 │    │ │ • tokens    │ │          │
│  │                                         │    │ │ • actions   │ │          │
│  │ • $can(action, subject)                 │    │ └─────────────┘ │          │
│  │ • useAbility().can()                    │    └─────────────────┘          │
│  │ • 來自 @casl/vue                        │                                 │
│  │ • 基於後端同步的 CASL 規則              │                                 │
│  └─────────────────────────────────────────┘                                 │
│                                                                             │
├─────────────────────────────────────────────────────────────────────────────┤
│                          權限常數引用層                                      │
│                                                                             │
│  從 @horizai/permissions 引入:                                               │
│  • Actions 常數                                                             │
│  • Subjects 常數                                                            │
│  • 中文化標籤                                                               │
│  • 權限檢查函數                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 7. 權限同步工具架構

```
┌────────────────────────────────────────────────────────────────────────────┐
│                           權限同步工具架構                                   │
└────────────────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────────────────┐
│                        permission-sync 工具                                │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐          │
│  │   掃描器         │    │   同步器         │    │   報告器         │          │
│  │   (Scanner)     │    │   (Syncer)      │    │   (Reporter)    │          │
│  │                 │    │                 │    │                 │          │
│  │ • 掃描後端程式碼 │────►│ • 比對權限差異   │────►│ • 生成 JSON 報告 │          │
│  │ • 掃描前端程式碼 │    │ • 新增新權限     │    │ • 生成 MD 報告   │          │
│  │ • 掃描 Seed 檔案 │    │ • 標記廢棄權限   │    │ • 控制台輸出     │          │
│  │ • 權限常數識別   │    │ • 更新中文名稱   │    │ • 統計資訊       │          │
│  │ • 中文化對應     │    │ • 分類更新       │    └─────────────────┘          │
│  │ • 快取機制       │    │ • 事務處理       │                                 │
│  └─────────────────┘    └─────────────────┘                                 │
│                                                                             │
├─────────────────────────────────────────────────────────────────────────────┤
│                              CLI 介面                                       │
│                                                                             │
│  • pnpm db:scan-perms [--no-cache]                                          │
│  • pnpm db:sync-perms [--dry-run] [--force]                                 │
│  • pnpm db:report-perms [--format] [--output]                               │
│                                                                             │
├─────────────────────────────────────────────────────────────────────────────┤
│                             推論引擎                                         │
│                                                                             │
│  • 區域推論 (admin/workspace)                                                │
│  • 分類推論 (基於主體關鍵字)                                                 │
│  • 範圍推論 (SYSTEM/TENANT/WORKSPACE)                                        │
│  • 中文名稱生成                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 8. 開發與維護工作流程

```
┌────────────────────────────────────────────────────────────────────────────┐
│                          開發與維護工作流程                                  │
└────────────────────────────────────────────────────────────────────────────┘

開發階段:
┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│ 1. 需求分析  │───►│ 2. 權限設計  │───►│ 3. 常數定義  │───►│ 4. 程式實作  │
│             │    │             │    │             │    │             │
│ • 功能需求   │    │ • 權限規劃   │    │ • Actions   │    │ • 後端 API   │
│ • 角色分析   │    │ • 分類設計   │    │ • Subjects  │    │ • 前端 UI    │
│ • 安全考量   │    │ • 範圍定義   │    │ • 組合定義   │    │ • 權限檢查   │
└─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘
       │                   │                   │                   │
       └───────────────────┼───────────────────┼───────────────────┘
                           │                   │
                           ▼                   ▼
                    ┌─────────────┐    ┌─────────────┐
                    │ 5. 權限掃描  │───►│ 6. 資料同步  │
                    │             │    │             │
                    │ • 程式碼掃描 │    │ • 資料庫更新 │
                    │ • 常數識別   │    │ • 中文化同步 │
                    │ • 報告生成   │    │ • 分類更新   │
                    └─────────────┘    └─────────────┘

維護階段:
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│ 定期檢查     │───►│ 權限審計     │───►│ 系統優化     │
│             │    │             │    │             │
│ • 權限使用量 │    │ • 廢棄權限   │    │ • 效能優化   │
│ • 安全日誌   │    │ • 重複權限   │    │ • 架構調整   │
│ • 用戶反饋   │    │ • 權限衝突   │    │ • 新功能支援 │
└─────────────┘    └─────────────┘    └─────────────┘
```

這個權限架構圖全面展示了 HorizAI SaaS 系統的權限管理架構，包括：

1. **整體系統架構** - 展示前後端、資料庫、共享套件的關係
2. **權限定義流程** - 從開發者定義到資料庫同步的完整流程
3. **權限驗證流程** - 前端到後端的權限檢查流程
4. **資料庫設計** - 權限相關表的結構和關係
5. **權限分類架構** - 按區域和分類組織的權限結構
6. **前端控制架構** - Vue 3 應用中的權限控制機制
7. **同步工具架構** - permission-sync 工具的內部架構
8. **開發維護流程** - 完整的開發和維護工作流程

這個架構圖可以幫助開發團隊更好地理解整個權限系統的設計和運作方式。
