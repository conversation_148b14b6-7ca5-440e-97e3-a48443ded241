# Application
NODE_ENV=development
PORT=4000
HOST=localhost
API_PREFIX=api
API_BASE_URL=https://dev.horizai.com

# Cookie Settings
COOKIE_DOMAIN=localhost          # 開發環境使用 localhost
COOKIE_SECURE=false             # 開發環境可以設為 false
COOKIE_SAME_SITE=lax           # 預設使用 lax
COOKIE_MAX_AGE=86400000        # 24 小時，以毫秒為單位

# Rate Limiting
RATE_LIMIT_WINDOW=15             # 時間窗口 (分鐘)
RATE_LIMIT_MAX_REQUESTS=100      # 該時間窗口內最大請求數

# Logging
LOG_LEVEL=debug

# CORS
ALLOWED_ORIGINS=http://localhost:3000,https://dev.horizai.com,http://localhost:5173

# Encryption Key (32 bytes hex) 用於資料庫敏感欄位的加解密
# node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"
ENCRYPTION_KEY=YOUR_64_HEX_CHARACTERS_HEX_KEY

# Database
# 當使用 Prisma Accelerate 時，這個 URL 格式會是: prisma://accelerate.prisma-data.net/?api_key=YOUR_API_KEY
DATABASE_URL=postgresql://user:password@localhost:5432/horizai?schema=public

# Authentication
JWT_ACCESS_SECRET=your_jwt_secret_key
JWT_ACCESS_EXPIRATION_TIME=1h
REFRESH_TOKEN_SECRET=your_refresh_token_secret
REFRESH_TOKEN_EXPIRY_DAYS=7
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
GOOGLE_CALLBACK_URL=http://localhost:3333/api/auth/google/callback # 開發時，端口號需與後端服務一致
GOOGLE_SIGNUP_DEFAULT_TENANT_ID=your_default_tenant_id_for_new_google_users # 新 Google 用戶註冊時預設加入的租戶 ID
FRONTEND_URL=http://localhost:5173 # 前端基礎 URL，用於回調後的重導
FRONTEND_GOOGLE_SUCCESS_REDIRECT_URL=http://localhost:5173/dashboard # Google 登入成功後，後端重導向的前端 URL

# Email Configuration
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USER=your_smtp_username
SMTP_PASSWORD=your_smtp_password
EMAIL_FROM=<EMAIL>

# AWS S3 (若啟用文件存儲)
AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
AWS_REGION=your_aws_region
AWS_BUCKET_NAME=your_s3_bucket_name

# 第三方 API 金鑰
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret