---
description: 
globs: 
alwaysApply: true
---
# Prisma 模型與欄位命名規範

為了確保資料庫互動的一致性並減少因命名不匹配導致的錯誤，本專案中所有 Prisma 相關的命名遵循以下規範：

## 1. Prisma Schema (`schema.prisma`)

### 1.1 模型命名規則
- **模型名稱 (Model Names)**: 必須使用 **`snake_case`**，複數形式。
  - 範例: `system_users`, `tenant_users`, `system_user_roles`, `tenant_user_roles`, `ai_bots`, `permission_categories`
  - **注意**: 不使用單數形式如 `user`, `role`

### 1.2 欄位命名規則
- **欄位名稱 (Field Names)**: 必須使用 **`snake_case`**。
  - 範例: `user_id`, `created_at`, `updated_at`, `is_active`, `tenant_id`, `system_user_id`
- **時間欄位**: 統一使用 `created_at`, `updated_at`, `deleted_at`, `last_login_at`
- **ID 欄位**: 統一使用 `id` 作為主鍵，外鍵使用 `模型名_id` 格式
- **布林欄位**: 使用 `is_` 或 `has_` 前綴，如 `is_active`, `is_enabled`, `has_permission`
- **狀態欄位**: 使用 `status` 而非 `state`

### 1.3 關聯命名規則
- **關聯欄位名稱**: 保持與對應模型的 `snake_case` 一致。
- **反向關聯**: 使用複數形式，如 `system_user_roles[]`, `tenant_user_roles[]`
- **多對多關聯表**: 使用 `表1_表2` 格式，如 `system_user_roles`, `tenant_user_roles`

### 1.4 枚舉和其他規則
- **枚舉名稱 (Enum Names)**: 使用 `PascalCase`，如 `SystemUserRole`, `TenantUserStatus`
- **枚舉值**: 使用 `UPPER_SNAKE_CASE`，如 `SUPER_ADMIN`, `TENANT_USER`, `ACTIVE`
- **表格映射**: 使用 `@@map("table_name")` 確保資料庫表名與 Prisma 模型名一致

## 2. 用戶分離架構範例

本專案採用分離用戶架構，以下是具體的命名範例：

### 2.1 用戶模型
```prisma
// 系統管理員用戶
model system_users {
  id                    String         @id @default(cuid())
  email                 String         @unique
  password              String
  name                  String?
  role                  SystemUserRole @default(SYSTEM_ADMIN)
  status                String         @default("active")
  created_at            DateTime       @default(now())
  updated_at            DateTime       @updatedAt
  last_login_at         DateTime?
  
  // 關聯
  system_user_roles     system_user_roles[]
  refresh_tokens        refresh_tokens[]

  @@map("system_users")
}

// 租戶用戶
model tenant_users {
  id                    String           @id @default(cuid())
  email                 String           @unique
  password              String
  name                  String?
  tenant_id             String
  role                  TenantUserRole   @default(TENANT_USER)
  status                TenantUserStatus @default(ACTIVE)
  created_at            DateTime         @default(now())
  updated_at            DateTime         @updatedAt
  
  // 關聯
  tenant                tenants          @relation(fields: [tenant_id], references: [id])
  tenant_user_roles     tenant_user_roles[]
  refresh_tokens        refresh_tokens[]

  @@map("tenant_users")
}
```

### 2.2 角色關聯模型
```prisma
// 系統用戶角色關聯
model system_user_roles {
  id              String       @id
  system_user_id  String  
  role_id         String
  created_at      DateTime     @default(now())
  updated_at      DateTime     @updatedAt
  
  system_user     system_users @relation(fields: [system_user_id], references: [id], onDelete: Cascade)
  role            roles        @relation(fields: [role_id], references: [id], onDelete: Cascade)

  @@unique([system_user_id, role_id])
}

// 租戶用戶角色關聯
model tenant_user_roles {
  id              String       @id
  tenant_user_id  String  
  role_id         String
  created_at      DateTime     @default(now())
  updated_at      DateTime     @updatedAt
  
  tenant_user     tenant_users @relation(fields: [tenant_user_id], references: [id], onDelete: Cascade)
  role            roles        @relation(fields: [role_id], references: [id], onDelete: Cascade)

  @@unique([tenant_user_id, role_id])
}
```

## 3. TypeScript/JavaScript 程式碼中的 Prisma Client 使用

### 3.1 模型存取規則
- **模型引用**: 使用 Prisma Schema 中定義的 **`snake_case`** 模型名稱。
  ```typescript
  // ✅ 正確
  await this.prisma.system_users.create({...})
  await this.prisma.tenant_users.findMany({...})
  await this.prisma.system_user_roles.create({...})
  
  // ❌ 錯誤
  await this.prisma.systemUsers.create({...})
  await this.prisma.userRoles.create({...})
  ```

### 3.2 欄位引用規則
- **查詢欄位**: 在 `data`, `where`, `select`, `include` 等物件中使用 **`snake_case`**。
  ```typescript
  // ✅ 正確
  await this.prisma.tenant_users.findFirst({
    where: { 
      tenant_id: tenantId,
      status: 'ACTIVE'
    },
    select: {
      id: true,
      email: true,
      created_at: true,
      last_login_at: true
    }
  })
  
  // ❌ 錯誤
  await this.prisma.tenant_users.findFirst({
    where: { 
      tenantId: tenantId,  // 應該是 tenant_id
      status: 'ACTIVE'
    },
    select: {
      id: true,
      email: true,
      createdAt: true,     // 應該是 created_at
      lastLoginAt: true    // 應該是 last_login_at
    }
  })
  ```

### 3.3 關聯查詢
```typescript
// ✅ 正確的關聯查詢
const systemUser = await this.prisma.system_users.findUnique({
  where: { id: userId },
  include: {
    system_user_roles: {
      include: {
        role: true
      }
    }
  }
})

const tenantUser = await this.prisma.tenant_users.findUnique({
  where: { id: userId },
  include: {
    tenant: true,
    tenant_user_roles: {
      include: {
        role: true
      }
    }
  }
})
```

## 4. 類型定義規則

### 4.1 Prisma 生成類型
- 使用 Prisma 生成的 `snake_case` 類型：
  ```typescript
  import { Prisma } from '@prisma/client'
  
  // ✅ 正確
  type SystemUserCreateInput = Prisma.system_usersCreateInput
  type TenantUserUpdateInput = Prisma.tenant_usersUpdateInput
  type SystemUserRoleCreateInput = Prisma.system_user_rolesCreateInput
  
  // ❌ 錯誤
  type SystemUserCreateInput = Prisma.SystemUsersCreateInput
  ```

### 4.2 自定義類型
```typescript
// ✅ 正確的自定義類型定義
interface CreateSystemUserDto {
  email: string
  password: string
  name?: string
  role: SystemUserRole
}

interface CreateTenantUserDto {
  email: string
  password: string
  name?: string
  tenant_id: string
  role: TenantUserRole
  department?: string
}
```

## 5. Migration 和 Seed 檔案規則

### 5.1 Migration 檔案
- Migration 檔案中的 SQL 語句必須使用 `snake_case` 表名和欄位名
- 確保與 Prisma Schema 完全一致

### 5.2 Seed 檔案
```typescript
// ✅ 正確的 seed 檔案寫法
await prisma.system_users.create({
  data: {
    email: '<EMAIL>',
    password: hashedPassword,
    name: 'System Admin',
    role: 'SUPER_ADMIN'
  }
})

await prisma.system_user_roles.create({
  data: {
    system_user_id: userId,
    role_id: roleId
  }
})
```

## 6. 重要提醒和最佳實踐

### 6.1 一致性檢查
- **開發時**: 使用 TypeScript 嚴格模式確保類型安全
- **程式碼審查**: 特別檢查 Prisma 相關的命名是否符合規範
- **測試**: 確保所有資料庫操作測試都使用正確的命名

### 6.2 常見錯誤避免
1. **混用命名風格**: 不要在同一專案中混用 `camelCase` 和 `snake_case`
2. **關聯錯誤**: 確保關聯欄位名稱與對應的模型 ID 欄位一致
3. **類型錯誤**: 使用 Prisma 生成的正確類型，不要自行定義不匹配的類型

### 6.3 工具和檢查
- 使用 Prisma Studio 檢查資料庫結構
- 定期執行 `prisma validate` 驗證 schema 正確性
- 使用 ESLint 規則檢查命名一致性

## 7. 參考檔案

- **主要 Schema**: `apps/backend/prisma/schema.prisma`
- **Seed 檔案**: `apps/backend/prisma/seed.ts`
- **Migration 檔案**: `apps/backend/prisma/migrations/`

遵循此命名規範有助於：
- 減少因大小寫或命名風格不匹配導致的執行時錯誤
- 提高程式碼的可讀性和可維護性
- 確保開發團隊對資料庫結構有統一的理解
- 支援分離用戶架構的複雜關聯查詢

**最後更新**: 2025年6月5日 - 添加用戶分離架構範例和完整的命名規範
