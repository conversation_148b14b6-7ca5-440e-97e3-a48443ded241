import { defineStore, DefineStoreOptions, _GettersTree } from "pinia";
import { computed } from "vue";
import { MongoAbility, AbilityTuple, MongoQuery } from "@casl/ability";
import type {
  AuthStoreState,
  CurrentUser,
  SystemUser,
  TenantUser,
  UserType,
  LoginDto,
  AuthResponse,
  UseAuthReturn,
  AuthError,
  AuthConfig,
} from "../types/auth.types";
import type {
  Action,
  Subject,
  AppAbility,
  Permission,
} from "../types/ability.types";
import type { TokenService } from "../types/token.types";
import type { HttpService } from "../types/http.types";
import { createAuthService } from "../services/auth.service";
import { createTokenService } from "../services/token.service";
import { createHttpService as createDefaultHttpService } from "../services/http.service";
import {
  AbilityService,
  type PermissionRule,
} from "../services/ability.service";
import { getAuthConfig } from "../config";

// Singleton services, to avoid recreating them on every operation
let _httpService: HttpService | null = null;
let _tokenService: TokenService | null = null;
let _authService: any | null = null;

// Utility function to get service instances
function getServices() {
  const config = getAuthConfig();

  if (!_tokenService) {
    _tokenService = createTokenService(config);
    console.debug("AuthStore: Created Token service instance");
  }
  if (!_httpService) {
    // Always create and use the default HttpService from within the @horizai/auth package
    // The factory function will create its own tokenService internally
    _httpService = createDefaultHttpService(config.baseURL, config);
    console.debug(
      "AuthStore: Created and using internal default HTTP service instance for authentication."
    );
  }

  if (!_authService) {
    // Ensure _httpService is not null before passing to createAuthService
    if (!_httpService) {
      // This case should ideally not happen if the logic above is correct
      console.error(
        "AuthStore: HttpService is null, cannot create AuthService"
      );
      throw new Error(
        "HttpService not initialized before AuthService creation"
      );
    }
    _authService = createAuthService(config, _httpService, _tokenService);
    console.debug("AuthStore: Created Authentication service instance");
  }

  return {
    httpService: _httpService, // This will now be the potentially shared instance
    tokenService: _tokenService,
    authService: _authService,
  };
}

interface AuthState {
  user: CurrentUser;
  isAuthenticated: boolean;
  ability: AppAbility | null;
  loading: boolean;
  error: AuthError | null;
  // 權限相關狀態
  permissionRules: PermissionRule[];
}

type AuthGetters = {
  currentUser: (state: AuthState) => CurrentUser;
  isLoggedIn: (state: AuthState) => boolean;
  userAbility: (state: AuthState) => AppAbility | null;
  currentAbility: (state: AuthState) => AppAbility | null;
  isLoading: (state: AuthState) => boolean;
  hasError: (state: AuthState) => boolean;
  errorMessage: (state: AuthState) => string | null;
  // 新增的 getters
  userType: (state: AuthState) => UserType | null;
  isSystemUser: (state: AuthState) => boolean;
  isTenantUser: (state: AuthState) => boolean;
  tenantId: (state: AuthState) => string | null;
  userRole: (state: AuthState) => string | null;
  accessToken: (state: AuthState) => string | null;
};

interface AuthActions {
  setUser(user: CurrentUser): void;
  setAbility(ability: AppAbility | null): void;
  setLoading(loading: boolean): void;
  setError(error: AuthError | null): void;
  clearError(): void;
  resetState(): void;
  login(credentials: LoginDto): Promise<AuthResponse>;
  logout(): Promise<void>;
  refreshToken(forceRefresh?: boolean): Promise<{ accessToken: string }>;
  getUser(): Promise<CurrentUser>;
  // 新增的 actions
  updatePermissions(rules: PermissionRule[]): void;
  rebuildAbility(): void;
  checkPermission(action: Action, subject: Subject, field?: string): boolean;
  initAuth(): Promise<void>;
}

const storeDefinition: DefineStoreOptions<
  "auth",
  AuthState,
  _GettersTree<AuthState>,
  AuthActions
> = {
  id: "auth",
  state: () => ({
    user: null,
    isAuthenticated: false,
    ability: null,
    loading: false,
    error: null,
    permissionRules: [],
  }),
  getters: {
    currentUser: (state) => state.user,
    isLoggedIn: (state) => state.isAuthenticated,
    userAbility: (state) => state.ability,
    currentAbility: (state) => state.ability,
    isLoading: (state) => state.loading,
    hasError: (state) => !!state.error,
    errorMessage: (state) => state.error?.message || null,

    // 新增的 getters
    userType: (state) => state.user?.userType || null,
    isSystemUser: (state) => state.user?.userType === "system",
    isTenantUser: (state) => state.user?.userType === "tenant",
    tenantId: (state) =>
      state.user?.userType === "tenant"
        ? (state.user as TenantUser).tenantId
        : null,
    userRole: (state) => state.user?.role || null,
    accessToken: (state) => {
      const { tokenService } = getServices();
      return tokenService.getAuthToken();
    },
  },
  actions: {
    setUser(user: CurrentUser) {
      const previousUser = this.user;
      const previousUserType = previousUser?.userType;
      const newUserType = user?.userType;

      this.user = user;
      this.isAuthenticated = !!user;

      // 如果 userType 發生變化，自動重建 ability
      if (previousUserType !== newUserType) {
        console.debug(
          "AuthStore: User type changed from",
          previousUserType,
          "to",
          newUserType,
          "- rebuilding ability"
        );
        this.rebuildAbility();
      }
    },

    setAbility(ability: AppAbility | null) {
      this.ability = ability;
    },

    setLoading(loading: boolean) {
      this.loading = loading;
    },

    setError(error: AuthError | null) {
      this.error = error;
    },

    clearError() {
      this.error = null;
    },

    resetState() {
      this.user = null;
      this.isAuthenticated = false;
      this.ability = null;
      this.loading = false;
      this.error = null;
      this.permissionRules = [];
    },

    async login(credentials: LoginDto): Promise<AuthResponse> {
      const { authService } = getServices();

      try {
        this.setLoading(true);
        const response = await authService.login(credentials);

        // 設定使用者，這會觸發 userType 變化檢查
        this.setUser(response.user);

        // 如果回應包含權限規則，更新權限
        if (response.abilityRules) {
          this.updatePermissions(response.abilityRules);
        }

        return response;
      } catch (error) {
        this.setError(error as AuthError);
        throw error;
      } finally {
        this.setLoading(false);
      }
    },

    async logout(): Promise<void> {
      const { authService } = getServices();

      try {
        await authService.logout();
      } catch (error) {
        console.warn('AuthStore: 登出 API 呼叫失敗，但繼續清除前端狀態', error);
        // 不拋出錯誤，確保前端狀態能夠被清除
      } finally {
        // 無論 API 呼叫是否成功，都要重置前端狀態
        this.resetState();

        // 清除錯誤狀態
        this.error = null;

        console.debug('AuthStore: 登出完成，狀態已重置');
      }
    },

    async refreshToken(forceRefresh = false): Promise<{ accessToken: string }> {
      const { authService } = getServices();
      try {
        const response = await authService.refreshToken(forceRefresh);

        console.debug(
          "AuthStore: refreshToken action completed. Raw response:",
          response
        );

        return response;
      } catch (error) {
        this.setError(error as AuthError);
        throw error;
      }
    },

    async getUser(): Promise<CurrentUser> {
      const { authService } = getServices();

      try {
        const userData = await authService.getUser();

        // 檢查回應是否包含權限規則
        let user: CurrentUser = null;
        let abilityRules: PermissionRule[] = [];

        if (userData && typeof userData === "object") {
          // 如果 userData 包含 user 和 abilityRules 屬性
          if ("user" in userData && "abilityRules" in userData) {
            user = userData.user as CurrentUser;
            abilityRules = userData.abilityRules as PermissionRule[];
          } else {
            // 假設 userData 就是 user
            user = userData as CurrentUser;
          }
        }

        // 設定使用者，這會觸發 userType 變化檢查
        this.setUser(user);

        // 更新權限規則
        if (abilityRules.length > 0) {
          this.updatePermissions(abilityRules);
        } else {
          // 如果沒有特定的權限規則，重建基本權限
          this.rebuildAbility();
        }

        return user;
      } catch (error) {
        this.setError(error as AuthError);
        throw error;
      }
    },

    /**
     * 更新權限規則並重建 ability
     */
    updatePermissions(rules: PermissionRule[]) {
      this.permissionRules = rules;
      this.rebuildAbility();
    },

    /**
     * 根據當前使用者和權限規則重建 ability
     */
    rebuildAbility() {
      const newAbility = AbilityService.createAbility(
        this.user,
        this.permissionRules
      );
      this.setAbility(newAbility);

      console.debug(
        "AuthStore: Ability rebuilt for user type:",
        this.userType,
        "with",
        this.permissionRules.length,
        "permission rules"
      );
    },

    /**
     * 檢查特定權限
     */
    checkPermission(action: Action, subject: Subject, field?: string): boolean {
      if (!this.ability) return false;
      return field
        ? this.ability.can(action, subject, field)
        : this.ability.can(action, subject);
    },

    /**
     * 初始化認證狀態
     * 通常在應用啟動時調用
     */
    async initAuth(): Promise<void> {
      try {
        this.setLoading(true);
        await this.getUser();
        console.debug("AuthStore: Authentication initialized successfully");
      } catch (error) {
        console.debug(
          "AuthStore: Authentication initialization failed:",
          error
        );
        // 初始化失敗時重置狀態，但不拋出錯誤
        this.resetState();
      } finally {
        this.setLoading(false);
      }
    },
  },
};

export const useAuthStore = defineStore(storeDefinition);

export type AuthStoreType = ReturnType<typeof useAuthStore>;
export const createAuthStore = (): AuthStoreType => useAuthStore();

/**
 * 工具函式：建立 auth store 的 composable wrapper
 * 提供更方便的 API 存取
 */
export function useAuth() {
  const store = useAuthStore();

  return {
    // State
    user: computed(() => store.currentUser),
    isAuthenticated: computed(() => store.isLoggedIn),
    ability: computed(() => store.currentAbility),
    isLoading: computed(() => store.isLoading),
    error: computed(() => store.errorMessage),
    userType: computed(() => store.userType),
    isSystemUser: computed(() => store.isSystemUser),
    isTenantUser: computed(() => store.isTenantUser),
    tenantId: computed(() => store.tenantId),
    userRole: computed(() => store.userRole),
    accessToken: computed(() => store.accessToken),

    // Actions
    login: store.login,
    logout: store.logout,
    refreshToken: store.refreshToken,
    getUser: store.getUser,
    checkPermission: store.checkPermission,
    initAuth: store.initAuth,

    // Shortcuts
    can: (action: Action, subject: Subject, field?: string) =>
      store.checkPermission(action, subject, field),
    cannot: (action: Action, subject: Subject, field?: string) =>
      !store.checkPermission(action, subject, field),
  };
}
