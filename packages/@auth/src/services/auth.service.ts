import type {
  AuthConfig,
  AuthResponse,
  User,
  LoginDto,
  AuthError,
  AcceptInvitationDto,
  InvitationInfo,
} from '../types/auth.types';
import type { HttpService } from '../types/http.types';
import type { TokenService } from '../types/token.types';
import { AxiosError } from 'axios';

export class AuthService {
  private readonly logger = console;
  private isGettingUser = false;
  private userPromise: Promise<User> | null = null;
  private lastGetUserAttempt: number | null = null;

  constructor(
    private readonly config: AuthConfig,
    private readonly httpService: HttpService,
    private readonly tokenService: TokenService,
  ) {
    this.logger.debug('AuthService: 初始化');
  }

  /**
   * 使用者登入
   */
  async login(loginDto: LoginDto): Promise<AuthResponse> {
    try {
      this.logger.debug('AuthService: 發送登入請求');

      // 後端回應的格式 (注意：httpService.post 已經返回 response.data，不是完整的 response)
      interface BackendLoginResponse {
        accessToken: string;
        user: any; // 後端使用者物件
      }

      // httpService.post 已經通過響應攔截器返回了 response.data
      const backendResponse = await this.httpService.post<BackendLoginResponse>(
        this.config.loginEndpoint,
        loginDto,
      );

      this.logger.debug('AuthService: 登入成功，收到後端數據:', backendResponse);

      // 檢查後端回應格式
      if (!backendResponse) {
        throw new Error('後端回應為空');
      }

      if (!backendResponse.accessToken) {
        throw new Error('後端回應中缺少 accessToken');
      }

      if (!backendResponse.user) {
        throw new Error('後端回應中缺少 user 資訊');
      }

      // 轉換為前端期望的 AuthResponse 格式
      const response: AuthResponse = {
        accessToken: backendResponse.accessToken,
        user: backendResponse.user,
        abilityRules: [], // 可在此處添加權限規則處理邏輯
      };

      this.logger.debug('AuthService: 轉換後的 AuthResponse:', response);

      // 設置本地存儲標記，表示已登入
      try {
        if (typeof window !== 'undefined' && window.localStorage) {
          localStorage.setItem('auth.session.exists', 'true');
        }
      } catch (err) {
        this.logger.warn('AuthService: 無法設置本地存儲標記', err);
      }

      // 重置令牌刷新狀態
      if (typeof this.httpService.resetRefreshState === 'function') {
        this.httpService.resetRefreshState();
      }

      // 觸發登入成功回調
      if (this.config.onLoginSuccess) {
        this.config.onLoginSuccess(response);
      }

      // 觸發認證狀態變更回調
      if (this.config.onAuthStateChange) {
        this.config.onAuthStateChange(true);
      }

      return response;
    } catch (error) {
      this.logger.error('AuthService: 登入失敗', error);

      // 轉換錯誤為 AuthError 類型
      const authError: AuthError = {
        name: 'AuthError',
        message: error instanceof Error ? error.message : '登入失敗',
        code: error instanceof AxiosError ? error.code || 'LOGIN_FAILED' : 'LOGIN_FAILED',
        statusCode: error instanceof AxiosError ? error.response?.status || 401 : 401,
        details: error instanceof AxiosError ? error.response?.data || {} : {},
      };

      // 觸發登入失敗回調
      if (this.config.onLoginError) {
        this.config.onLoginError(authError);
      }

      throw authError;
    }
  }

  /**
   * 使用者登出
   */
  async logout(): Promise<void> {
    // 設置登出狀態，防止在登出過程中觸發 token 刷新
    if (typeof this.httpService.setLoggingOutState === 'function') {
      this.httpService.setLoggingOutState(true);
    }

    try {
      this.logger.debug('AuthService: 發送登出請求');

      // 嘗試呼叫後端登出 API
      try {
        await this.httpService.post(this.config.logoutEndpoint);
        this.logger.debug('AuthService: 後端登出成功');
      } catch (apiError) {
        this.logger.warn('AuthService: 後端登出失敗，但繼續清除前端狀態', apiError);
        // 不拋出錯誤，繼續清除前端狀態
      }

      // 執行完整的前端狀態清除
      this.performCompleteCleanup();

      this.logger.debug('AuthService: 登出成功');

    } catch (error) {
      this.logger.error('AuthService: 登出過程中發生錯誤', error);

      // 即使發生錯誤，也要執行完整的前端狀態清除
      this.performCompleteCleanup();

      // 不拋出錯誤，確保登出流程能夠完成
    } finally {
      // 重置登出狀態
      if (typeof this.httpService.setLoggingOutState === 'function') {
        this.httpService.setLoggingOutState(false);
      }
    }
  }

  /**
   * 執行完整的前端狀態清除
   */
  private performCompleteCleanup(): void {
    try {
      // 清除前端認證狀態
      this.tokenService.clearAuth();

      // 清除所有本地存儲
      this.clearAllLocalStorage();

      // 重置令牌刷新狀態
      if (typeof this.httpService.resetRefreshState === 'function') {
        this.httpService.resetRefreshState();
      }

      // 觸發登出成功回調
      if (this.config.onLogoutSuccess) {
        this.config.onLogoutSuccess();
      }

      // 觸發認證狀態變更回調
      if (this.config.onAuthStateChange) {
        this.config.onAuthStateChange(false);
      }

      // 發送自定義事件通知其他組件
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('auth:logout-complete'));
      }

    } catch (cleanupError) {
      this.logger.error('AuthService: 清除狀態時發生錯誤', cleanupError);
    }
  }

  /**
   * 清除所有本地存儲
   */
  private clearAllLocalStorage(): void {
    try {
      if (typeof window !== 'undefined') {
        const keysToRemove = [
          'auth.session.exists',
          'auth.user',
          'auth.token',
          'user',
          'auth_token',
          'access_token',
          'refresh_token',
          'remember_me',
          'auth.remember_me',
          'currentWorkspaceId',
          'selectedTenantId',
        ];

        // 清除 localStorage
        keysToRemove.forEach(key => {
          try {
            localStorage.removeItem(key);
          } catch (err) {
            this.logger.warn(`AuthService: 無法清除 localStorage key: ${key}`, err);
          }
        });

        // 清除 sessionStorage
        keysToRemove.forEach(key => {
          try {
            sessionStorage.removeItem(key);
          } catch (err) {
            this.logger.warn(`AuthService: 無法清除 sessionStorage key: ${key}`, err);
          }
        });
      }
    } catch (error) {
      this.logger.warn('AuthService: 清除本地存儲時發生錯誤', error);
    }
  }

  /**
   * 檢查使用者是否已認證
   */
  isAuthenticated(): boolean {
    return this.tokenService.isAuthenticated() && !this.tokenService.isTokenExpired();
  }

  /**
   * 讀取使用者資訊
   */
  async getUser(): Promise<User> {
    if (!this.config.meEndpoint) {
      throw new Error('meEndpoint 未配置');
    }

    // 去重：若已有進行中請求，重用該 Promise
    if (this.isGettingUser && this.userPromise) {
      this.logger.debug('AuthService: 重用正在進行的使用者資訊請求');
      return this.userPromise;
    }

    // 新增防抖延遲，防止頻繁調用
    if (this.lastGetUserAttempt) {
      const timeSinceLastAttempt = Date.now() - this.lastGetUserAttempt;
      const minDelay = 3000; // 增加到最少3秒間隔

      if (timeSinceLastAttempt < minDelay) {
        this.logger.debug(
          `AuthService: 請求頻率過高，延遲執行 (${Math.round((minDelay - timeSinceLastAttempt) / 1000)}秒)`,
        );
        await new Promise((resolve) => setTimeout(resolve, minDelay - timeSinceLastAttempt));
      }
    }

    try {
      this.isGettingUser = true;
      this.lastGetUserAttempt = Date.now();
      this.logger.debug('AuthService: 讀取使用者資訊');
      // 保存 Promise 以供重用
      this.userPromise = this.httpService.get<User>(this.config.meEndpoint);
      const user = await this.userPromise;

      // 讀取使用者資訊成功，重置本地存儲標記，表示已登入
      try {
        if (typeof window !== 'undefined' && window.localStorage) {
          localStorage.setItem('auth.session.exists', 'true');
        }
      } catch (err) {
        this.logger.warn('AuthService: 無法設置本地存儲標記', err);
      }

      return user;
    } catch (error) {
      this.logger.error('AuthService: 讀取使用者資訊失敗', error);

      // 處理429錯誤 - 返回一個假的錯誤對象，但包含正確的狀態碼
      if (error instanceof AxiosError && error.response?.status === 429) {
        const rateError: AuthError = {
          name: 'RateLimitError',
          message: '請求過於頻繁，請稍後再試',
          code: 'RATE_LIMIT_EXCEEDED',
          statusCode: 429,
          details: { retryAfter: error.response?.headers?.['retry-after'] || '5' },
        };
        throw rateError;
      }

      throw error;
    } finally {
      // 延遲重置狀態，避免立即重複請求
      setTimeout(() => {
        this.isGettingUser = false;
        this.userPromise = null;
      }, 5000); // 增加延遲時間到 5000ms
    }
  }

  /**
   * 刷新 Token
   */
  async refreshToken(forceRefresh = false): Promise<{ accessToken: string }> {
    if (!this.config.refreshTokenEndpoint) {
      this.logger.error('AuthService: refreshTokenEndpoint is not configured.');
      throw new Error('refreshTokenEndpoint is not configured');
    }
    try {
      this.logger.debug(`AuthService: Attempting to refresh token (forceRefresh: ${forceRefresh})`);

      // Backend returns { accessToken: string }
      const response = await this.httpService.post<{ accessToken: string }>(
        this.config.refreshTokenEndpoint,
        {}, // No body needed as refresh token is in httpOnly cookie
      );

      this.logger.debug('AuthService: Token refresh API call successful.');

      // The primary outcome is that the httpOnly auth_token cookie is set by the backend.
      // The accessToken in the response body can be used if JS needs to be aware of it,
      // but for cookie-based auth, it's not strictly necessary for the auth flow itself.

      // If there's a global HttpService instance, its internal state regarding tokens
      // might need resetting if it caches tokens in JS.
      // Assuming httpService handles its own state post-successful-refresh via interceptors.
      if (typeof this.httpService.resetRefreshState === 'function') {
        this.httpService.resetRefreshState(); // Reset retry counters etc. in httpService
      }

      if (this.config.onAuthStateChange) {
        this.config.onAuthStateChange(true); // Indicate auth state might have changed to authenticated
      }

      return response; // Returns { accessToken: string }
    } catch (error) {
      this.logger.error('AuthService: Token refresh failed', error);
      // Ensure onAuthStateChange is called appropriately on failure too,
      // for example, if it leads to definite de-authentication.
      // if (this.config.onAuthStateChange) {
      //   this.config.onAuthStateChange(false); // This depends on the desired behavior on refresh failure
      // }
      if (
        this.config.tokenExpiryHandler &&
        error instanceof AxiosError &&
        error.response?.status === 401
      ) {
        this.logger.warn(
          'AuthService: Refresh token is likely invalid or expired, calling tokenExpiryHandler.',
        );
        this.config.tokenExpiryHandler();
      }
      throw error;
    }
  }

  /**
   * 驗證邀請令牌
   */
  async verifyInvitation(token: string): Promise<InvitationInfo> {
    try {
      this.logger.debug('AuthService: 驗證邀請令牌');

      const response = await this.httpService.get<InvitationInfo>(
        `/auth/invitations/verify?token=${encodeURIComponent(token)}`,
      );

      this.logger.debug('AuthService: 邀請驗證成功', response);
      return response;
    } catch (error) {
      this.logger.error('AuthService: 邀請驗證失敗', error);

      const authError: AuthError = {
        name: 'InvitationError',
        message: error instanceof Error ? error.message : '邀請驗證失敗',
        code:
          error instanceof AxiosError
            ? error.code || 'INVITATION_VERIFY_FAILED'
            : 'INVITATION_VERIFY_FAILED',
        statusCode: error instanceof AxiosError ? error.response?.status || 400 : 400,
        details: error instanceof AxiosError ? error.response?.data || {} : {},
      };

      throw authError;
    }
  }

  /**
   * 接受邀請並註冊
   */
  async acceptInvitation(dto: AcceptInvitationDto): Promise<User> {
    try {
      this.logger.debug('AuthService: 接受邀請並註冊');

      const response = await this.httpService.post<User>('/auth/invitations/accept', dto);

      this.logger.debug('AuthService: 邀請接受成功', response);
      return response;
    } catch (error) {
      this.logger.error('AuthService: 接受邀請失敗', error);

      const authError: AuthError = {
        name: 'InvitationAcceptError',
        message: error instanceof Error ? error.message : '接受邀請失敗',
        code:
          error instanceof AxiosError
            ? error.code || 'INVITATION_ACCEPT_FAILED'
            : 'INVITATION_ACCEPT_FAILED',
        statusCode: error instanceof AxiosError ? error.response?.status || 400 : 400,
        details: error instanceof AxiosError ? error.response?.data || {} : {},
      };

      throw authError;
    }
  }
}

// 工廠函數
export const createAuthService = (
  config: AuthConfig,
  httpService: HttpService,
  tokenService: TokenService,
): AuthService => {
  return new AuthService(config, httpService, tokenService);
};
