import { TokenService } from '../types/token.types'
import type { AuthConfig } from '../types/auth.types'

export class DefaultTokenService implements TokenService {
  private readonly logger = console

  constructor(private readonly config: AuthConfig) {
    this.logger.debug('TokenService: 初始化')
  }

  /**
   * 檢查是否已認證（改為永遠回 true，狀態交由 /auth/me 決定）
   */
  isAuthenticated(): boolean {
    return true;
  }

  /**
   * 解析 JWT Token
   */
  getTokenPayload<T = any>(token: string): T | null {
    try {
      const base64Url = token.split('.')[1]
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/')
      const jsonPayload = decodeURIComponent(
        atob(base64)
          .split('')
          .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
          .join('')
      )
      return JSON.parse(jsonPayload)
    } catch (error) {
      this.logger.error('TokenService: Token 解析失敗', error)
      return null
    }
  }

  /**
   * 從 Cookie 中讀取 Token
   */
  getAuthToken(): string | null {
    try {
      const cookies = document.cookie.split(';')
      const authCookie = cookies.find(cookie => cookie.trim().startsWith('auth_token='))
      if (!authCookie) return null
      return authCookie.split('=')[1]
    } catch (error) {
      this.logger.error('TokenService: 無法從 Cookie 讀取 Token', error);
      return null;
    }
  }

  /**
   * 檢查 Token 是否過期（改為永遠回 false，狀態交由 /auth/me 決定）
   */
  isTokenExpired(): boolean {
    return false;
  }

  /**
   * 清除認證相關的 Cookie 和本地儲存
   * 注意：HTTP-only Cookie 只能由伺服器清除，這裡的操作僅作為備用清理
   */
  clearAuth(): void {
    try {
      // 清除所有可能的認證相關Cookie（包括不同路徑和域名）
      const cookiesToClear = ['auth_token', 'auth.token', 'access_token', 'refresh_token'];
      const paths = ['/', '/auth', '/api'];
      const domains = [window.location.hostname, `.${window.location.hostname}`];

      cookiesToClear.forEach(cookieName => {
        // 清除不同路徑的 cookie
        paths.forEach(path => {
          document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=${path}`;
          // 也嘗試清除帶域名的 cookie
          domains.forEach(domain => {
            document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=${path}; domain=${domain}`;
          });
        });
      });

      // 清除所有認證相關的 localStorage 和 sessionStorage
      try {
        const keysToRemove = [
          'auth.session.exists',
          'auth.user',
          'auth.token',
          'user',
          'auth_token',
          'access_token',
          'refresh_token',
          'remember_me',
          'auth.remember_me'
        ];

        keysToRemove.forEach(key => {
          localStorage.removeItem(key);
          sessionStorage.removeItem(key);
        });
      } catch (err) {
        this.logger.warn('TokenService: 無法清除本地儲存', err);
      }

      this.logger.debug('TokenService: 已清除所有認證相關資訊');
    } catch (error) {
      this.logger.error('TokenService: 清除認證資訊失敗', error);
    }
  }

  /**
   * 設置認證 Token（不再需要，由伺服器通過 Set-Cookie 頭設定）
   */
  setTokens(accessToken: string, refreshToken: string): void {
    // 由於我們使用 Cookie-based 認證，這個方法實際上不需要做任何事情
    // Token 的設置由後端通過 Set-Cookie header 處理
    this.logger.debug('TokenService: Token 設置由後端處理');
  }

  /**
   * 清除所有認證相關的 Token
   */
  clearTokens(): void {
    this.clearAuth();
  }
}

// 工廠函數
export const createTokenService = (config: AuthConfig): TokenService => {
  return new DefaultTokenService(config)
}